# Script để kiểm tra trạng thái tất cả microservices
Write-Host "=== CHECKING MICROSERVICES STATUS ===" -ForegroundColor Green

$services = @(
    @{ Name = "API Gateway"; Url = "http://localhost:8080/actuator/health"; Port = 8080 },
    @{ Name = "Customer Service"; Url = "http://localhost:8081/actuator/health"; Port = 8081 },
    @{ Name = "Job Service"; Url = "http://localhost:8082/actuator/health"; Port = 8082 },
    @{ Name = "Customer Contract Service"; Url = "http://localhost:8083/actuator/health"; Port = 8083 },
    @{ Name = "Customer Payment Service"; Url = "http://localhost:8084/actuator/health"; Port = 8084 },
    @{ Name = "Customer Statistics Service"; Url = "http://localhost:8085/actuator/health"; Port = 8085 },
    @{ Name = "Frontend"; Url = "http://localhost:3000"; Port = 3000 }
)

function Test-ServiceHealth {
    param(
        [string]$ServiceName,
        [string]$Url,
        [int]$Port
    )
    
    Write-Host "`nTesting $ServiceName..." -ForegroundColor Cyan
    Write-Host "URL: $Url" -ForegroundColor Gray
    
    try {
        # Check if port is listening
        $portTest = Test-NetConnection -ComputerName localhost -Port $Port -InformationLevel Quiet -WarningAction SilentlyContinue
        
        if (-not $portTest) {
            Write-Host "❌ $ServiceName - Port $Port is not listening" -ForegroundColor Red
            return $false
        }
        
        # Test HTTP endpoint
        $response = Invoke-RestMethod -Uri $Url -Method GET -TimeoutSec 5 -ErrorAction Stop
        
        if ($ServiceName -eq "Frontend") {
            Write-Host "✅ $ServiceName - OK (Frontend is accessible)" -ForegroundColor Green
            return $true
        } elseif ($response.status -eq "UP") {
            Write-Host "✅ $ServiceName - UP" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️ $ServiceName - Response received but status is not UP" -ForegroundColor Yellow
            Write-Host "Response: $($response | ConvertTo-Json -Compress)" -ForegroundColor Gray
            return $false
        }
    }
    catch {
        Write-Host "❌ $ServiceName - ERROR: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test all services
$results = @()
foreach ($service in $services) {
    $isHealthy = Test-ServiceHealth -ServiceName $service.Name -Url $service.Url -Port $service.Port
    $results += @{
        Name = $service.Name
        Port = $service.Port
        Healthy = $isHealthy
    }
}

# Summary
Write-Host "`n=== SUMMARY ===" -ForegroundColor Magenta
$healthyCount = ($results | Where-Object { $_.Healthy }).Count
$totalCount = $results.Count

Write-Host "Healthy Services: $healthyCount/$totalCount" -ForegroundColor $(if ($healthyCount -eq $totalCount) { "Green" } else { "Yellow" })

foreach ($result in $results) {
    $status = if ($result.Healthy) { "✅ UP" } else { "❌ DOWN" }
    $color = if ($result.Healthy) { "Green" } else { "Red" }
    Write-Host "$status $($result.Name) (Port $($result.Port))" -ForegroundColor $color
}

# Test specific contract endpoints
if (($results | Where-Object { $_.Name -eq "Customer Contract Service" -and $_.Healthy }).Count -gt 0) {
    Write-Host "`n=== TESTING CONTRACT ENDPOINTS ===" -ForegroundColor Cyan
    
    $contractEndpoints = @(
        "http://localhost:8083/api/contracts",
        "http://localhost:8080/api/customer-contract"
    )
    
    foreach ($endpoint in $contractEndpoints) {
        try {
            Write-Host "Testing GET $endpoint..." -ForegroundColor Gray
            $response = Invoke-RestMethod -Uri $endpoint -Method GET -TimeoutSec 5 -ErrorAction Stop
            Write-Host "✅ $endpoint - OK (Returned $($response.Count) contracts)" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ $endpoint - ERROR: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Recommendations
Write-Host "`n=== RECOMMENDATIONS ===" -ForegroundColor Yellow
if ($healthyCount -lt $totalCount) {
    Write-Host "Some services are down. Please check:" -ForegroundColor Yellow
    $downServices = $results | Where-Object { -not $_.Healthy }
    foreach ($service in $downServices) {
        Write-Host "- Start $($service.Name) on port $($service.Port)" -ForegroundColor White
    }
    Write-Host "`nTo start all services, run:" -ForegroundColor Yellow
    Write-Host "docker-compose up -d" -ForegroundColor White
} else {
    Write-Host "All services are healthy! ✅" -ForegroundColor Green
    Write-Host "You can now test contract creation in the frontend." -ForegroundColor White
}

Write-Host "`nFrontend URL: http://localhost:3000/contracts/create" -ForegroundColor Cyan
Write-Host "API Gateway URL: http://localhost:8080" -ForegroundColor Cyan
