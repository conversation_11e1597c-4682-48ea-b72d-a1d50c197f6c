<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Cache và Test Contract Creation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #2196F3;
            background-color: #f8f9fa;
        }
        .button {
            background-color: #2196F3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background-color: #1976D2;
        }
        .button.danger {
            background-color: #f44336;
        }
        .button.danger:hover {
            background-color: #d32f2f;
        }
        .button.success {
            background-color: #4CAF50;
        }
        .button.success:hover {
            background-color: #45a049;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Clear Cache và Test Contract Creation</h1>
        <p>Trang này giúp bạn clear cache và test việc tạo hợp đồng để xác định nguồn gốc lỗi "Vui lòng nhập địa chỉ hợp đồng".</p>

        <div class="step">
            <h3>Bước 1: Clear Browser Cache</h3>
            <p>Click các nút dưới đây để clear cache:</p>
            <button class="button danger" onclick="clearAllCache()">Clear All Cache</button>
            <button class="button danger" onclick="clearLocalStorage()">Clear LocalStorage</button>
            <button class="button danger" onclick="clearSessionStorage()">Clear SessionStorage</button>
            <div id="cacheResult"></div>
        </div>

        <div class="step">
            <h3>Bước 2: Test API Connectivity</h3>
            <p>Kiểm tra kết nối đến các microservices:</p>
            <button class="button" onclick="testAPIConnectivity()">Test API Connectivity</button>
            <div id="apiResult"></div>
        </div>

        <div class="step">
            <h3>Bước 3: Test Contract Creation</h3>
            <p>Test tạo hợp đồng với minimal data:</p>
            <button class="button success" onclick="testContractCreation()">Test Contract Creation</button>
            <div id="contractResult"></div>
        </div>

        <div class="step">
            <h3>Bước 4: Navigate to Contract Creation Page</h3>
            <p>Sau khi clear cache, mở trang tạo hợp đồng:</p>
            <button class="button success" onclick="navigateToContractPage()">Open Contract Creation Page</button>
        </div>

        <div class="step">
            <h3>Bước 5: Debug Information</h3>
            <button class="button" onclick="showDebugInfo()">Show Debug Info</button>
            <div id="debugResult"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function clearAllCache() {
            try {
                // Clear localStorage
                localStorage.clear();
                
                // Clear sessionStorage
                sessionStorage.clear();
                
                // Clear cookies (limited by same-origin policy)
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });

                showResult('cacheResult', '✅ All cache cleared successfully!', 'success');
            } catch (error) {
                showResult('cacheResult', `❌ Error clearing cache: ${error.message}`, 'error');
            }
        }

        function clearLocalStorage() {
            try {
                localStorage.clear();
                showResult('cacheResult', '✅ LocalStorage cleared!', 'success');
            } catch (error) {
                showResult('cacheResult', `❌ Error clearing localStorage: ${error.message}`, 'error');
            }
        }

        function clearSessionStorage() {
            try {
                sessionStorage.clear();
                showResult('cacheResult', '✅ SessionStorage cleared!', 'success');
            } catch (error) {
                showResult('cacheResult', `❌ Error clearing sessionStorage: ${error.message}`, 'error');
            }
        }

        async function testAPIConnectivity() {
            const apis = [
                { name: 'API Gateway', url: 'http://localhost:8080/api/customer-contract' },
                { name: 'Customer Contract Service', url: 'http://localhost:8083/api/contracts' },
                { name: 'Customer Service', url: 'http://localhost:8081/api/customers' },
                { name: 'Job Service', url: 'http://localhost:8082/api/job-categories' }
            ];

            let results = '<h4>API Connectivity Test Results:</h4>';
            
            for (const api of apis) {
                try {
                    const response = await fetch(api.url, { method: 'GET' });
                    if (response.ok || response.status === 405) { // 405 = Method Not Allowed is OK for POST endpoints
                        results += `<div class="result success">✅ ${api.name}: Connected</div>`;
                    } else {
                        results += `<div class="result error">❌ ${api.name}: HTTP ${response.status}</div>`;
                    }
                } catch (error) {
                    results += `<div class="result error">❌ ${api.name}: ${error.message}</div>`;
                }
            }
            
            document.getElementById('apiResult').innerHTML = results;
        }

        async function testContractCreation() {
            const testContract = {
                customerId: 1,
                startingDate: "2024-12-20",
                endingDate: "2024-12-31",
                totalAmount: 1000000,
                description: "Test contract - no address required",
                jobDetails: [
                    {
                        jobCategoryId: 1,
                        startDate: "2024-12-20",
                        endDate: "2024-12-31",
                        // workLocation: intentionally omitted
                        workShifts: [
                            {
                                startTime: "08:00",
                                endTime: "17:00",
                                numberOfWorkers: 1,
                                salary: 500000,
                                workingDays: "1,2,3,4,5"
                            }
                        ]
                    }
                ]
            };

            try {
                showResult('contractResult', '🔄 Testing contract creation...', 'info');
                
                const response = await fetch('http://localhost:8080/api/customer-contract', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testContract)
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult('contractResult', `✅ Contract created successfully! ID: ${result.id}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult('contractResult', `❌ Contract creation failed: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult('contractResult', `❌ Network error: ${error.message}`, 'error');
            }
        }

        function navigateToContractPage() {
            window.open('http://localhost:3000/contracts/create', '_blank');
        }

        function showDebugInfo() {
            const info = {
                userAgent: navigator.userAgent,
                localStorage: Object.keys(localStorage).length,
                sessionStorage: Object.keys(sessionStorage).length,
                cookies: document.cookie.split(';').length,
                currentURL: window.location.href,
                timestamp: new Date().toISOString()
            };

            let debugHTML = '<h4>Debug Information:</h4>';
            for (const [key, value] of Object.entries(info)) {
                debugHTML += `<div class="result info"><strong>${key}:</strong> ${value}</div>`;
            }

            document.getElementById('debugResult').innerHTML = debugHTML;
        }

        // Auto-run some tests on page load
        window.onload = function() {
            showResult('cacheResult', 'Ready to clear cache...', 'info');
            showResult('apiResult', 'Ready to test API connectivity...', 'info');
            showResult('contractResult', 'Ready to test contract creation...', 'info');
        };
    </script>
</body>
</html>
