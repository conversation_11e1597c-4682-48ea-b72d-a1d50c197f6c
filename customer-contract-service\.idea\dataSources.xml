<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="customercontractdb@localhost" uuid="64a95713-f025-433b-96c2-11ff5ea34914">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <imported>true</imported>
      <remarks>$PROJECT_DIR$/src/main/resources/application.properties</remarks>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>***************************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="customercontracdb@postgres" uuid="f2553492-298a-49c1-92c8-8cd79c77cd02">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <imported>true</imported>
      <remarks>$PROJECT_DIR$/src/main/resources/application-docker.properties</remarks>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>*************************************************</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.host.port" />
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
        <property name="com.intellij.clouds.kubernetes.db.container.port" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>