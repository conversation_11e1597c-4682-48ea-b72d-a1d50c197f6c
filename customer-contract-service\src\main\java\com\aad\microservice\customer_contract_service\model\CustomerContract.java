package com.aad.microservice.customer_contract_service.model;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "customer_contracts")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustomerContract {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private LocalDate startingDate;
    private LocalDate endingDate;

    private Double totalAmount;
    private Double totalPaid;
    private String description;

    @OneToMany(mappedBy = "contract", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonManagedReference
    @Builder.Default
    private List<JobDetail> jobDetails = new ArrayList<>();

    private Integer status;          // Trạng thái hợp đồng (0: <PERSON><PERSON>ử lý, 1: <PERSON><PERSON> hoạt động, 2: <PERSON><PERSON><PERSON>, 3: <PERSON><PERSON> hủy)
    private Boolean isDeleted;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    private Long customerId;

    public void addJobDetail(JobDetail jobDetail) {
        jobDetails.add(jobDetail);
        jobDetail.setContract(this);
    }

    public void removeJobDetail(JobDetail jobDetail) {
        jobDetails.remove(jobDetail);
        jobDetail.setContract(null);
    }
}
