spring.application.name=customer-contract-service

# PostgreSQL database configuration for Docker environment
spring.datasource.url=**************************************************************
spring.datasource.username=postgres
spring.datasource.password=1234
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA configurations - validate existing schema, don't modify
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=true

# Disable data initialization to prevent conflicts
spring.sql.init.mode=never

# Allow bean definition overriding
spring.main.allow-bean-definition-overriding=true

# Server
server.port=8083

# Service URLs for Docker environment
customer.service.url=http://customer-service:8081/api/customer
job.service.url=http://job-service:8082/api/job
job-category.service.url=http://job-service:8082/api/job-category

# Management endpoints
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
