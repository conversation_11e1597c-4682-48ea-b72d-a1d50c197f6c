<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="customerdb@postgres" uuid="2f06c282-7945-4ff8-914b-f549eeadc709">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <imported>true</imported>
      <remarks>$PROJECT_DIR$/src/main/resources/application-docker.properties</remarks>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>******************************************</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.host.port" />
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
        <property name="com.intellij.clouds.kubernetes.db.container.port" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="customerdb@localhost" uuid="89c8231b-0d1d-43e8-a6ba-a4a7f13a2910">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <imported>true</imported>
      <remarks>$PROJECT_DIR$/src/main/resources/application.properties</remarks>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>******************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>