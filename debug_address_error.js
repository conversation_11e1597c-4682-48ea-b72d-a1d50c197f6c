// Script để debug lỗi "Vui lòng nhập địa chỉ hợp đồng"
// Paste script này vào browser console để tìm nguồn gốc lỗi

console.log('🔍 Starting address error debug...');

// 1. <PERSON><PERSON><PERSON> tra tất cả error messages trong DOM
function findErrorMessages() {
  console.log('📋 Searching for error messages in DOM...');
  
  const errorSelectors = [
    '.MuiAlert-message',
    '.error-message',
    '.MuiFormHelperText-root',
    '[role="alert"]',
    '.Mui-error',
    '.error',
    '.alert'
  ];
  
  errorSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach((el, index) => {
      if (el.textContent.includes('địa chỉ') || el.textContent.includes('address')) {
        console.log(`❌ Found error message [${selector}][${index}]:`, el.textContent);
        console.log('Element:', el);
        console.log('Parent:', el.parentElement);
      }
    });
  });
}

// 2. <PERSON><PERSON>m tra validation functions
function checkValidationFunctions() {
  console.log('📋 Checking validation functions...');
  
  // Tìm tất cả functions có chứa "địa chỉ"
  const scripts = document.querySelectorAll('script');
  scripts.forEach((script, index) => {
    if (script.textContent.includes('địa chỉ') || script.textContent.includes('address')) {
      console.log(`📜 Found script [${index}] containing address validation:`, script.textContent.substring(0, 200) + '...');
    }
  });
}

// 3. Kiểm tra React component state
function checkReactState() {
  console.log('📋 Checking React component state...');
  
  // Tìm React Fiber nodes
  const reactElements = document.querySelectorAll('[data-reactroot], [data-react-checksum]');
  reactElements.forEach((el, index) => {
    const fiber = el._reactInternalFiber || el._reactInternalInstance;
    if (fiber) {
      console.log(`⚛️ Found React element [${index}]:`, fiber);
    }
  });
}

// 4. Kiểm tra localStorage và sessionStorage
function checkStorage() {
  console.log('📋 Checking storage...');
  
  console.log('LocalStorage:');
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    const value = localStorage.getItem(key);
    if (value && (value.includes('địa chỉ') || value.includes('address'))) {
      console.log(`💾 LocalStorage [${key}]:`, value);
    }
  }
  
  console.log('SessionStorage:');
  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    const value = sessionStorage.getItem(key);
    if (value && (value.includes('địa chỉ') || value.includes('address'))) {
      console.log(`💾 SessionStorage [${key}]:`, value);
    }
  }
}

// 5. Kiểm tra network requests
function monitorNetworkRequests() {
  console.log('📋 Setting up network monitoring...');
  
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    console.log('🌐 Fetch request:', args);
    return originalFetch.apply(this, args)
      .then(response => {
        console.log('🌐 Fetch response:', response);
        return response;
      })
      .catch(error => {
        console.log('🌐 Fetch error:', error);
        throw error;
      });
  };
  
  // Monitor XMLHttpRequest
  const originalXHR = window.XMLHttpRequest;
  window.XMLHttpRequest = function() {
    const xhr = new originalXHR();
    const originalSend = xhr.send;
    
    xhr.send = function(data) {
      console.log('🌐 XHR request:', data);
      return originalSend.apply(this, arguments);
    };
    
    return xhr;
  };
}

// 6. Test contract creation với minimal data
function testMinimalContract() {
  console.log('📋 Testing minimal contract creation...');
  
  const minimalContract = {
    customerId: 1,
    startingDate: "2024-12-20",
    endingDate: "2024-12-31",
    totalAmount: 1000000,
    description: "Test minimal contract",
    jobDetails: [
      {
        jobCategoryId: 1,
        startDate: "2024-12-20",
        endDate: "2024-12-31",
        // workLocation: intentionally omitted
        workShifts: [
          {
            startTime: "08:00",
            endTime: "17:00",
            numberOfWorkers: 1,
            salary: 500000,
            workingDays: "1,2,3,4,5"
          }
        ]
      }
    ]
  };
  
  console.log('📋 Minimal contract data:', minimalContract);
  
  // Try to find validation function in global scope
  if (window.validateContract) {
    console.log('📋 Found global validateContract function');
    try {
      const result = window.validateContract(minimalContract);
      console.log('📋 Validation result:', result);
    } catch (error) {
      console.log('❌ Validation error:', error);
    }
  } else {
    console.log('📋 No global validateContract function found');
  }
}

// Run all debug functions
console.log('🎯 Running all debug functions...');
findErrorMessages();
checkValidationFunctions();
checkReactState();
checkStorage();
monitorNetworkRequests();
testMinimalContract();

console.log('✅ Debug script completed. Check the logs above for any issues.');
console.log('💡 If you still see the error, try:');
console.log('   1. Clear browser cache (Ctrl+Shift+R)');
console.log('   2. Check Network tab for failed requests');
console.log('   3. Look for JavaScript errors in Console tab');
console.log('   4. Try creating a contract step by step');
