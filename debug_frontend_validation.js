// Script để debug frontend validation
// Paste script này vào browser console để test

console.log('🔍 Starting frontend validation debug...');

// Test contract data
const testContract = {
  customerId: 1,
  startingDate: "2024-12-20",
  endingDate: "2024-12-31",
  totalAmount: 5000000,
  address: "Test Address",
  description: "Test Description",
  jobDetails: [
    {
      jobCategoryId: 1,
      startDate: "2024-12-20",
      endDate: "2024-12-31",
      workLocation: "Test Location",
      workShifts: [
        {
          startTime: "08:00",
          endTime: "17:00",
          numberOfWorkers: 3,
          salary: 500000,
          workingDays: "1,2,3,4,5"
        }
      ]
    }
  ]
};

console.log('📋 Test contract data:', testContract);

// Function to validate contract (copy from CreateContractPage)
function validateTestContract(contract) {
  console.log('🔍 Starting validation...');
  
  if (!contract.customerId || contract.customerId === 0) {
    console.log('❌ Validation failed: Customer ID missing');
    return false;
  }
  console.log('✅ Customer ID valid:', contract.customerId);

  if (!contract.jobDetails || contract.jobDetails.length === 0) {
    console.log('❌ Validation failed: No job details');
    return false;
  }
  console.log('✅ Job details count:', contract.jobDetails.length);

  // Validate each job detail
  for (let i = 0; i < contract.jobDetails.length; i++) {
    const jobDetail = contract.jobDetails[i];
    console.log(`🔍 Validating job detail ${i}:`, jobDetail);
    
    if (!jobDetail.jobCategoryId || jobDetail.jobCategoryId === 0) {
      console.log(`❌ Validation failed: Job detail ${i} missing job category ID`);
      return false;
    }
    console.log(`✅ Job detail ${i} job category ID valid:`, jobDetail.jobCategoryId);

    if (!jobDetail.startDate) {
      console.log(`❌ Validation failed: Job detail ${i} missing start date`);
      return false;
    }
    console.log(`✅ Job detail ${i} start date valid:`, jobDetail.startDate);

    if (!jobDetail.endDate) {
      console.log(`❌ Validation failed: Job detail ${i} missing end date`);
      return false;
    }
    console.log(`✅ Job detail ${i} end date valid:`, jobDetail.endDate);

    if (!jobDetail.workShifts || jobDetail.workShifts.length === 0) {
      console.log(`❌ Validation failed: Job detail ${i} has no work shifts`);
      return false;
    }
    console.log(`✅ Job detail ${i} work shifts count:`, jobDetail.workShifts.length);

    // Validate each work shift
    for (let j = 0; j < jobDetail.workShifts.length; j++) {
      const workShift = jobDetail.workShifts[j];
      console.log(`🔍 Validating work shift ${i}-${j}:`, workShift);
      
      if (!workShift.startTime) {
        console.log(`❌ Validation failed: Work shift ${i}-${j} missing start time`);
        return false;
      }
      console.log(`✅ Work shift ${i}-${j} start time valid:`, workShift.startTime);

      if (!workShift.endTime) {
        console.log(`❌ Validation failed: Work shift ${i}-${j} missing end time`);
        return false;
      }
      console.log(`✅ Work shift ${i}-${j} end time valid:`, workShift.endTime);

      if (!workShift.numberOfWorkers || workShift.numberOfWorkers <= 0) {
        console.log(`❌ Validation failed: Work shift ${i}-${j} invalid number of workers:`, workShift.numberOfWorkers);
        return false;
      }
      console.log(`✅ Work shift ${i}-${j} number of workers valid:`, workShift.numberOfWorkers);

      if (workShift.salary === undefined || workShift.salary < 0) {
        console.log(`❌ Validation failed: Work shift ${i}-${j} invalid salary:`, workShift.salary);
        return false;
      }
      console.log(`✅ Work shift ${i}-${j} salary valid:`, workShift.salary);

      if (!workShift.workingDays) {
        console.log(`❌ Validation failed: Work shift ${i}-${j} missing working days`);
        return false;
      }
      console.log(`✅ Work shift ${i}-${j} working days valid:`, workShift.workingDays);
    }
  }

  console.log('✅ All validation passed!');
  return true;
}

// Test validation
console.log('🚀 Testing validation...');
const isValid = validateTestContract(testContract);
console.log('📊 Validation result:', isValid);

// Test API call directly
async function testDirectAPICall() {
  console.log('🚀 Testing direct API call...');
  
  try {
    const response = await fetch('http://localhost:8080/api/customer-contract', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(testContract)
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

    if (response.ok) {
      const data = await response.json();
      console.log('✅ API call successful:', data);
      return data;
    } else {
      const errorText = await response.text();
      console.log('❌ API call failed:', response.status, errorText);
      return null;
    }
  } catch (error) {
    console.error('❌ API call error:', error);
    return null;
  }
}

// Test localStorage issues
function checkLocalStorage() {
  console.log('🔍 Checking localStorage...');
  
  const lastSubmission = localStorage.getItem('lastContractSubmission');
  const lastSubmissionKey = localStorage.getItem('lastContractSubmissionKey');
  
  console.log('📦 Last submission timestamp:', lastSubmission);
  console.log('📦 Last submission key:', lastSubmissionKey);
  
  if (lastSubmission) {
    const timeDiff = Date.now() - parseInt(lastSubmission);
    console.log('⏰ Time since last submission:', timeDiff, 'ms');
    
    if (timeDiff < 2000) {
      console.log('⚠️ WARNING: Last submission was less than 2 seconds ago - this might block new submissions');
    }
    
    if (timeDiff < 60000) {
      console.log('⚠️ WARNING: Last submission was less than 60 seconds ago - duplicate detection might trigger');
    }
  }
  
  // Clear localStorage to allow testing
  console.log('🧹 Clearing localStorage to allow testing...');
  localStorage.removeItem('lastContractSubmission');
  localStorage.removeItem('lastContractSubmissionKey');
  console.log('✅ localStorage cleared');
}

// Run all tests
console.log('🎯 Running all debug tests...');
checkLocalStorage();

console.log('\n📋 To test API call, run: testDirectAPICall()');
console.log('📋 To test validation again, run: validateTestContract(testContract)');

// Make functions available globally
window.testDirectAPICall = testDirectAPICall;
window.validateTestContract = validateTestContract;
window.testContract = testContract;
