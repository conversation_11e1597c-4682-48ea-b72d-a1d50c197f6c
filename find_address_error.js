// Script để tìm nguồn gốc lỗi "<PERSON>ui lòng nhập địa chỉ hợp đồng"
// Ch<PERSON>y trong browser console

console.log('🔍 Searching for address error source...');

// 1. T<PERSON><PERSON> tất cả text nodes c<PERSON> chứa "địa chỉ hợp đồng"
function findTextNodes() {
    console.log('📋 Searching for text nodes...');
    
    const walker = document.createTreeWalker(
        document.body,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );
    
    let node;
    const foundNodes = [];
    
    while (node = walker.nextNode()) {
        if (node.textContent.includes('địa chỉ') || node.textContent.includes('hợp đồng')) {
            foundNodes.push({
                text: node.textContent,
                element: node.parentElement,
                path: getElementPath(node.parentElement)
            });
        }
    }
    
    console.log('📋 Found text nodes:', foundNodes);
    return foundNodes;
}

// 2. Lấy path của element
function getElementPath(element) {
    const path = [];
    while (element && element !== document.body) {
        let selector = element.tagName.toLowerCase();
        if (element.id) {
            selector += '#' + element.id;
        }
        if (element.className) {
            selector += '.' + element.className.split(' ').join('.');
        }
        path.unshift(selector);
        element = element.parentElement;
    }
    return path.join(' > ');
}

// 3. Tìm tất cả error elements
function findErrorElements() {
    console.log('📋 Searching for error elements...');
    
    const errorSelectors = [
        '.MuiAlert-message',
        '.error-message', 
        '.MuiFormHelperText-root',
        '[role="alert"]',
        '.Mui-error',
        '.error',
        '.alert',
        '.MuiAlert-root',
        '.MuiSnackbar-root'
    ];
    
    const foundErrors = [];
    
    errorSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((el, index) => {
            if (el.textContent.trim()) {
                foundErrors.push({
                    selector: selector,
                    index: index,
                    text: el.textContent,
                    element: el,
                    path: getElementPath(el)
                });
            }
        });
    });
    
    console.log('📋 Found error elements:', foundErrors);
    return foundErrors;
}

// 4. Kiểm tra React props và state
function checkReactComponents() {
    console.log('📋 Checking React components...');
    
    // Tìm tất cả elements có React fiber
    const reactElements = [];
    
    function findReactElements(element) {
        const fiberKey = Object.keys(element).find(key => 
            key.startsWith('__reactInternalInstance') || 
            key.startsWith('__reactFiber')
        );
        
        if (fiberKey) {
            const fiber = element[fiberKey];
            if (fiber && fiber.memoizedProps) {
                reactElements.push({
                    element: element,
                    fiber: fiber,
                    props: fiber.memoizedProps,
                    state: fiber.memoizedState
                });
            }
        }
        
        for (let child of element.children) {
            findReactElements(child);
        }
    }
    
    findReactElements(document.body);
    
    console.log('📋 Found React elements:', reactElements);
    return reactElements;
}

// 5. Kiểm tra network requests
function monitorNetworkRequests() {
    console.log('📋 Setting up network monitoring...');
    
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        console.log('🌐 Fetch request:', args);
        return originalFetch.apply(this, args)
            .then(response => {
                console.log('🌐 Fetch response:', response);
                if (!response.ok) {
                    return response.text().then(text => {
                        console.log('🌐 Error response text:', text);
                        throw new Error(text);
                    });
                }
                return response;
            })
            .catch(error => {
                console.log('🌐 Fetch error:', error);
                throw error;
            });
    };
}

// 6. Kiểm tra console errors
function monitorConsoleErrors() {
    console.log('📋 Setting up console error monitoring...');
    
    const originalError = console.error;
    console.error = function(...args) {
        console.log('🚨 Console error captured:', args);
        return originalError.apply(this, args);
    };
    
    const originalWarn = console.warn;
    console.warn = function(...args) {
        console.log('⚠️ Console warning captured:', args);
        return originalWarn.apply(this, args);
    };
}

// 7. Tìm validation functions
function findValidationFunctions() {
    console.log('📋 Searching for validation functions...');
    
    // Tìm trong window object
    for (let key in window) {
        if (typeof window[key] === 'function' && key.toLowerCase().includes('valid')) {
            console.log(`📋 Found validation function: ${key}`, window[key]);
        }
    }
    
    // Tìm trong React DevTools nếu có
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
        console.log('📋 React DevTools detected');
    }
}

// Chạy tất cả checks
console.log('🎯 Running all checks...');

const textNodes = findTextNodes();
const errorElements = findErrorElements();
const reactComponents = checkReactComponents();

monitorNetworkRequests();
monitorConsoleErrors();
findValidationFunctions();

// Tạo summary
console.log('\n📊 SUMMARY:');
console.log(`Text nodes found: ${textNodes.length}`);
console.log(`Error elements found: ${errorElements.length}`);
console.log(`React components found: ${reactComponents.length}`);

// Tìm cụ thể error message
const addressErrors = errorElements.filter(err => 
    err.text.includes('địa chỉ') && err.text.includes('hợp đồng')
);

if (addressErrors.length > 0) {
    console.log('\n🎯 FOUND ADDRESS CONTRACT ERRORS:');
    addressErrors.forEach((err, index) => {
        console.log(`Error ${index + 1}:`, err);
    });
} else {
    console.log('\n✅ No "địa chỉ hợp đồng" errors found in current DOM');
}

// Export functions for manual use
window.findAddressError = {
    findTextNodes,
    findErrorElements,
    checkReactComponents,
    getElementPath
};

console.log('\n💡 Functions available: window.findAddressError');
console.log('💡 To re-run checks: window.findAddressError.findErrorElements()');
