spring.application.name=job-service
# PostgreSQL database configuration
spring.datasource.url=**************************************
spring.datasource.username=postgres
spring.datasource.password=1234
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA configurations
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# Data initialization
spring.sql.init.mode=always
spring.sql.init.data-locations=classpath:data.sql
spring.sql.init.continue-on-error=true

# Server
server.port=8082

# DevTools configuration
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
spring.devtools.restart.poll-interval=2s
spring.devtools.restart.quiet-period=1s
