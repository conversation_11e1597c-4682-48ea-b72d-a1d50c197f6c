{"ast": null, "code": "export { default } from \"./shadows.js\";", "map": {"version": 3, "names": ["default"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/system/esm/shadows/index.js"], "sourcesContent": ["export { default } from \"./shadows.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}