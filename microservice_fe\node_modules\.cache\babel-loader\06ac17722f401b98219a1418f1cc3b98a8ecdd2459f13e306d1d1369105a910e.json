{"ast": null, "code": "/**\n * WARNING: Don't import this directly. It's imported by the code generated by\n * `@mui/interal-babel-plugin-minify-errors`. Make sure to always use string literals in `Error`\n * constructors to ensure the plugin works as expected. Supported patterns include:\n *   throw new Error('My message');\n *   throw new Error(`My message: ${foo}`);\n *   throw new Error(`My message: ${foo}` + 'another string');\n *   ...\n * @param {number} code\n */\nexport default function formatMuiErrorMessage(code) {\n  const url = new URL(\"https://mui.com/production-error/?code=\".concat(code));\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  args.forEach(arg => url.searchParams.append('args[]', arg));\n  return \"Minified MUI error #\".concat(code, \"; visit \").concat(url, \" for the full message.\");\n}", "map": {"version": 3, "names": ["formatMuiErrorMessage", "code", "url", "URL", "concat", "_len", "arguments", "length", "args", "Array", "_key", "for<PERSON>ach", "arg", "searchParams", "append"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/utils/esm/formatMuiErrorMessage/formatMuiErrorMessage.js"], "sourcesContent": ["/**\n * WARNING: Don't import this directly. It's imported by the code generated by\n * `@mui/interal-babel-plugin-minify-errors`. Make sure to always use string literals in `Error`\n * constructors to ensure the plugin works as expected. Supported patterns include:\n *   throw new Error('My message');\n *   throw new Error(`My message: ${foo}`);\n *   throw new Error(`My message: ${foo}` + 'another string');\n *   ...\n * @param {number} code\n */\nexport default function formatMuiErrorMessage(code, ...args) {\n  const url = new URL(`https://mui.com/production-error/?code=${code}`);\n  args.forEach(arg => url.searchParams.append('args[]', arg));\n  return `Minified MUI error #${code}; visit ${url} for the full message.`;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,qBAAqBA,CAACC,IAAI,EAAW;EAC3D,MAAMC,GAAG,GAAG,IAAIC,GAAG,2CAAAC,MAAA,CAA2CH,IAAI,CAAE,CAAC;EAAC,SAAAI,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADjBC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAEzDF,IAAI,CAACG,OAAO,CAACC,GAAG,IAAIV,GAAG,CAACW,YAAY,CAACC,MAAM,CAAC,QAAQ,EAAEF,GAAG,CAAC,CAAC;EAC3D,8BAAAR,MAAA,CAA8BH,IAAI,cAAAG,MAAA,CAAWF,GAAG;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}