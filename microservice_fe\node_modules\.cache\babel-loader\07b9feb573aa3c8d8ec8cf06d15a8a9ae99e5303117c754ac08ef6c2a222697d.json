{"ast": null, "code": "'use client';\n\nimport _defineProperty from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\n/**\n * Lazy initialization container for the Ripple instance. This improves\n * performance by delaying mounting the ripple until it's needed.\n */\nexport class LazyRipple {\n  /** React ref to the ripple instance */\n\n  /** If the ripple component should be mounted */\n\n  /** Promise that resolves when the ripple component is mounted */\n\n  /** If the ripple component has been mounted */\n\n  /** React state hook setter */\n\n  static create() {\n    return new LazyRipple();\n  }\n  static use() {\n    /* eslint-disable */\n    const ripple = useLazyRef(LazyRipple.create).current;\n    const [shouldMount, setShouldMount] = React.useState(false);\n    ripple.shouldMount = shouldMount;\n    ripple.setShouldMount = setShouldMount;\n    React.useEffect(ripple.mountEffect, [shouldMount]);\n    /* eslint-enable */\n\n    return ripple;\n  }\n  constructor() {\n    _defineProperty(this, \"mountEffect\", () => {\n      if (this.shouldMount && !this.didMount) {\n        if (this.ref.current !== null) {\n          this.didMount = true;\n          this.mounted.resolve();\n        }\n      }\n    });\n    this.ref = {\n      current: null\n    };\n    this.mounted = null;\n    this.didMount = false;\n    this.shouldMount = false;\n    this.setShouldMount = null;\n  }\n  mount() {\n    if (!this.mounted) {\n      this.mounted = createControlledPromise();\n      this.shouldMount = true;\n      this.setShouldMount(this.shouldMount);\n    }\n    return this.mounted;\n  }\n  /* Ripple API */\n\n  start() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    this.mount().then(() => {\n      var _this$ref$current;\n      return (_this$ref$current = this.ref.current) === null || _this$ref$current === void 0 ? void 0 : _this$ref$current.start(...args);\n    });\n  }\n  stop() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    this.mount().then(() => {\n      var _this$ref$current2;\n      return (_this$ref$current2 = this.ref.current) === null || _this$ref$current2 === void 0 ? void 0 : _this$ref$current2.stop(...args);\n    });\n  }\n  pulsate() {\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    this.mount().then(() => {\n      var _this$ref$current3;\n      return (_this$ref$current3 = this.ref.current) === null || _this$ref$current3 === void 0 ? void 0 : _this$ref$current3.pulsate(...args);\n    });\n  }\n}\nexport default function useLazyRipple() {\n  return LazyRipple.use();\n}\nfunction createControlledPromise() {\n  let resolve;\n  let reject;\n  const p = new Promise((resolveFn, rejectFn) => {\n    resolve = resolveFn;\n    reject = rejectFn;\n  });\n  p.resolve = resolve;\n  p.reject = reject;\n  return p;\n}", "map": {"version": 3, "names": ["_defineProperty", "React", "useLazyRef", "<PERSON>zyR<PERSON>ple", "create", "use", "ripple", "current", "shouldMount", "setShouldMount", "useState", "useEffect", "mountEffect", "constructor", "didMount", "ref", "mounted", "resolve", "mount", "createControlledPromise", "start", "_len", "arguments", "length", "args", "Array", "_key", "then", "_this$ref$current", "stop", "_len2", "_key2", "_this$ref$current2", "pulsate", "_len3", "_key3", "_this$ref$current3", "useLazyRipple", "reject", "p", "Promise", "resolveFn", "rejectFn"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/useLazyRipple/useLazyRipple.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\n/**\n * Lazy initialization container for the Ripple instance. This improves\n * performance by delaying mounting the ripple until it's needed.\n */\nexport class LazyRipple {\n  /** React ref to the ripple instance */\n\n  /** If the ripple component should be mounted */\n\n  /** Promise that resolves when the ripple component is mounted */\n\n  /** If the ripple component has been mounted */\n\n  /** React state hook setter */\n\n  static create() {\n    return new LazyRipple();\n  }\n  static use() {\n    /* eslint-disable */\n    const ripple = useLazyRef(LazyRipple.create).current;\n    const [shouldMount, setShouldMount] = React.useState(false);\n    ripple.shouldMount = shouldMount;\n    ripple.setShouldMount = setShouldMount;\n    React.useEffect(ripple.mountEffect, [shouldMount]);\n    /* eslint-enable */\n\n    return ripple;\n  }\n  constructor() {\n    this.ref = {\n      current: null\n    };\n    this.mounted = null;\n    this.didMount = false;\n    this.shouldMount = false;\n    this.setShouldMount = null;\n  }\n  mount() {\n    if (!this.mounted) {\n      this.mounted = createControlledPromise();\n      this.shouldMount = true;\n      this.setShouldMount(this.shouldMount);\n    }\n    return this.mounted;\n  }\n  mountEffect = () => {\n    if (this.shouldMount && !this.didMount) {\n      if (this.ref.current !== null) {\n        this.didMount = true;\n        this.mounted.resolve();\n      }\n    }\n  };\n\n  /* Ripple API */\n\n  start(...args) {\n    this.mount().then(() => this.ref.current?.start(...args));\n  }\n  stop(...args) {\n    this.mount().then(() => this.ref.current?.stop(...args));\n  }\n  pulsate(...args) {\n    this.mount().then(() => this.ref.current?.pulsate(...args));\n  }\n}\nexport default function useLazyRipple() {\n  return LazyRipple.use();\n}\nfunction createControlledPromise() {\n  let resolve;\n  let reject;\n  const p = new Promise((resolveFn, rejectFn) => {\n    resolve = resolveFn;\n    reject = rejectFn;\n  });\n  p.resolve = resolve;\n  p.reject = reject;\n  return p;\n}"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,eAAA;AAEb,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,uBAAuB;AAC9C;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,CAAC;EACtB;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA,OAAOC,MAAMA,CAAA,EAAG;IACd,OAAO,IAAID,UAAU,CAAC,CAAC;EACzB;EACA,OAAOE,GAAGA,CAAA,EAAG;IACX;IACA,MAAMC,MAAM,GAAGJ,UAAU,CAACC,UAAU,CAACC,MAAM,CAAC,CAACG,OAAO;IACpD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,KAAK,CAACS,QAAQ,CAAC,KAAK,CAAC;IAC3DJ,MAAM,CAACE,WAAW,GAAGA,WAAW;IAChCF,MAAM,CAACG,cAAc,GAAGA,cAAc;IACtCR,KAAK,CAACU,SAAS,CAACL,MAAM,CAACM,WAAW,EAAE,CAACJ,WAAW,CAAC,CAAC;IAClD;;IAEA,OAAOF,MAAM;EACf;EACAO,WAAWA,CAAA,EAAG;IAAAb,eAAA,sBAiBA,MAAM;MAClB,IAAI,IAAI,CAACQ,WAAW,IAAI,CAAC,IAAI,CAACM,QAAQ,EAAE;QACtC,IAAI,IAAI,CAACC,GAAG,CAACR,OAAO,KAAK,IAAI,EAAE;UAC7B,IAAI,CAACO,QAAQ,GAAG,IAAI;UACpB,IAAI,CAACE,OAAO,CAACC,OAAO,CAAC,CAAC;QACxB;MACF;IACF,CAAC;IAvBC,IAAI,CAACF,GAAG,GAAG;MACTR,OAAO,EAAE;IACX,CAAC;IACD,IAAI,CAACS,OAAO,GAAG,IAAI;IACnB,IAAI,CAACF,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACN,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;EACAS,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACF,OAAO,EAAE;MACjB,IAAI,CAACA,OAAO,GAAGG,uBAAuB,CAAC,CAAC;MACxC,IAAI,CAACX,WAAW,GAAG,IAAI;MACvB,IAAI,CAACC,cAAc,CAAC,IAAI,CAACD,WAAW,CAAC;IACvC;IACA,OAAO,IAAI,CAACQ,OAAO;EACrB;EAUA;;EAEAI,KAAKA,CAAA,EAAU;IAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IACX,IAAI,CAACR,KAAK,CAAC,CAAC,CAACS,IAAI,CAAC;MAAA,IAAAC,iBAAA;MAAA,QAAAA,iBAAA,GAAM,IAAI,CAACb,GAAG,CAACR,OAAO,cAAAqB,iBAAA,uBAAhBA,iBAAA,CAAkBR,KAAK,CAAC,GAAGI,IAAI,CAAC;IAAA,EAAC;EAC3D;EACAK,IAAIA,CAAA,EAAU;IAAA,SAAAC,KAAA,GAAAR,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAK,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJP,IAAI,CAAAO,KAAA,IAAAT,SAAA,CAAAS,KAAA;IAAA;IACV,IAAI,CAACb,KAAK,CAAC,CAAC,CAACS,IAAI,CAAC;MAAA,IAAAK,kBAAA;MAAA,QAAAA,kBAAA,GAAM,IAAI,CAACjB,GAAG,CAACR,OAAO,cAAAyB,kBAAA,uBAAhBA,kBAAA,CAAkBH,IAAI,CAAC,GAAGL,IAAI,CAAC;IAAA,EAAC;EAC1D;EACAS,OAAOA,CAAA,EAAU;IAAA,SAAAC,KAAA,GAAAZ,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAS,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJX,IAAI,CAAAW,KAAA,IAAAb,SAAA,CAAAa,KAAA;IAAA;IACb,IAAI,CAACjB,KAAK,CAAC,CAAC,CAACS,IAAI,CAAC;MAAA,IAAAS,kBAAA;MAAA,QAAAA,kBAAA,GAAM,IAAI,CAACrB,GAAG,CAACR,OAAO,cAAA6B,kBAAA,uBAAhBA,kBAAA,CAAkBH,OAAO,CAAC,GAAGT,IAAI,CAAC;IAAA,EAAC;EAC7D;AACF;AACA,eAAe,SAASa,aAAaA,CAAA,EAAG;EACtC,OAAOlC,UAAU,CAACE,GAAG,CAAC,CAAC;AACzB;AACA,SAASc,uBAAuBA,CAAA,EAAG;EACjC,IAAIF,OAAO;EACX,IAAIqB,MAAM;EACV,MAAMC,CAAC,GAAG,IAAIC,OAAO,CAAC,CAACC,SAAS,EAAEC,QAAQ,KAAK;IAC7CzB,OAAO,GAAGwB,SAAS;IACnBH,MAAM,GAAGI,QAAQ;EACnB,CAAC,CAAC;EACFH,CAAC,CAACtB,OAAO,GAAGA,OAAO;EACnBsB,CAAC,CAACD,MAAM,GAAGA,MAAM;EACjB,OAAOC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}