{"ast": null, "code": "import style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nimport { handleBreakpoints, values as breakpointsValues } from \"../breakpoints/index.js\";\nexport function sizingTransform(value) {\n  return value <= 1 && value !== 0 ? \"\".concat(value * 100, \"%\") : value;\n}\nexport const width = style({\n  prop: 'width',\n  transform: sizingTransform\n});\nexport const maxWidth = props => {\n  if (props.maxWidth !== undefined && props.maxWidth !== null) {\n    const styleFromPropValue = propValue => {\n      var _props$theme, _props$theme2;\n      const breakpoint = ((_props$theme = props.theme) === null || _props$theme === void 0 || (_props$theme = _props$theme.breakpoints) === null || _props$theme === void 0 || (_props$theme = _props$theme.values) === null || _props$theme === void 0 ? void 0 : _props$theme[propValue]) || breakpointsValues[propValue];\n      if (!breakpoint) {\n        return {\n          maxWidth: sizingTransform(propValue)\n        };\n      }\n      if (((_props$theme2 = props.theme) === null || _props$theme2 === void 0 || (_props$theme2 = _props$theme2.breakpoints) === null || _props$theme2 === void 0 ? void 0 : _props$theme2.unit) !== 'px') {\n        return {\n          maxWidth: \"\".concat(breakpoint).concat(props.theme.breakpoints.unit)\n        };\n      }\n      return {\n        maxWidth: breakpoint\n      };\n    };\n    return handleBreakpoints(props, props.maxWidth, styleFromPropValue);\n  }\n  return null;\n};\nmaxWidth.filterProps = ['maxWidth'];\nexport const minWidth = style({\n  prop: 'minWidth',\n  transform: sizingTransform\n});\nexport const height = style({\n  prop: 'height',\n  transform: sizingTransform\n});\nexport const maxHeight = style({\n  prop: 'maxHeight',\n  transform: sizingTransform\n});\nexport const minHeight = style({\n  prop: 'minHeight',\n  transform: sizingTransform\n});\nexport const sizeWidth = style({\n  prop: 'size',\n  cssProperty: 'width',\n  transform: sizingTransform\n});\nexport const sizeHeight = style({\n  prop: 'size',\n  cssProperty: 'height',\n  transform: sizingTransform\n});\nexport const boxSizing = style({\n  prop: 'boxSizing'\n});\nconst sizing = compose(width, maxWidth, minWidth, height, maxHeight, minHeight, boxSizing);\nexport default sizing;", "map": {"version": 3, "names": ["style", "compose", "handleBreakpoints", "values", "breakpointsValues", "sizingTransform", "value", "concat", "width", "prop", "transform", "max<PERSON><PERSON><PERSON>", "props", "undefined", "styleFromPropValue", "propValue", "_props$theme", "_props$theme2", "breakpoint", "theme", "breakpoints", "unit", "filterProps", "min<PERSON><PERSON><PERSON>", "height", "maxHeight", "minHeight", "sizeWidth", "cssProperty", "sizeHeight", "boxSizing", "sizing"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/system/esm/sizing/sizing.js"], "sourcesContent": ["import style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nimport { handleBreakpoints, values as breakpointsValues } from \"../breakpoints/index.js\";\nexport function sizingTransform(value) {\n  return value <= 1 && value !== 0 ? `${value * 100}%` : value;\n}\nexport const width = style({\n  prop: 'width',\n  transform: sizingTransform\n});\nexport const maxWidth = props => {\n  if (props.maxWidth !== undefined && props.maxWidth !== null) {\n    const styleFromPropValue = propValue => {\n      const breakpoint = props.theme?.breakpoints?.values?.[propValue] || breakpointsValues[propValue];\n      if (!breakpoint) {\n        return {\n          maxWidth: sizingTransform(propValue)\n        };\n      }\n      if (props.theme?.breakpoints?.unit !== 'px') {\n        return {\n          maxWidth: `${breakpoint}${props.theme.breakpoints.unit}`\n        };\n      }\n      return {\n        maxWidth: breakpoint\n      };\n    };\n    return handleBreakpoints(props, props.maxWidth, styleFromPropValue);\n  }\n  return null;\n};\nmaxWidth.filterProps = ['maxWidth'];\nexport const minWidth = style({\n  prop: 'minWidth',\n  transform: sizingTransform\n});\nexport const height = style({\n  prop: 'height',\n  transform: sizingTransform\n});\nexport const maxHeight = style({\n  prop: 'maxHeight',\n  transform: sizingTransform\n});\nexport const minHeight = style({\n  prop: 'minHeight',\n  transform: sizingTransform\n});\nexport const sizeWidth = style({\n  prop: 'size',\n  cssProperty: 'width',\n  transform: sizingTransform\n});\nexport const sizeHeight = style({\n  prop: 'size',\n  cssProperty: 'height',\n  transform: sizingTransform\n});\nexport const boxSizing = style({\n  prop: 'boxSizing'\n});\nconst sizing = compose(width, maxWidth, minWidth, height, maxHeight, minHeight, boxSizing);\nexport default sizing;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AACrC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,iBAAiB,EAAEC,MAAM,IAAIC,iBAAiB,QAAQ,yBAAyB;AACxF,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,KAAK,CAAC,MAAAC,MAAA,CAAMD,KAAK,GAAG,GAAG,SAAMA,KAAK;AAC9D;AACA,OAAO,MAAME,KAAK,GAAGR,KAAK,CAAC;EACzBS,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEL;AACb,CAAC,CAAC;AACF,OAAO,MAAMM,QAAQ,GAAGC,KAAK,IAAI;EAC/B,IAAIA,KAAK,CAACD,QAAQ,KAAKE,SAAS,IAAID,KAAK,CAACD,QAAQ,KAAK,IAAI,EAAE;IAC3D,MAAMG,kBAAkB,GAAGC,SAAS,IAAI;MAAA,IAAAC,YAAA,EAAAC,aAAA;MACtC,MAAMC,UAAU,GAAG,EAAAF,YAAA,GAAAJ,KAAK,CAACO,KAAK,cAAAH,YAAA,gBAAAA,YAAA,GAAXA,YAAA,CAAaI,WAAW,cAAAJ,YAAA,gBAAAA,YAAA,GAAxBA,YAAA,CAA0Bb,MAAM,cAAAa,YAAA,uBAAhCA,YAAA,CAAmCD,SAAS,CAAC,KAAIX,iBAAiB,CAACW,SAAS,CAAC;MAChG,IAAI,CAACG,UAAU,EAAE;QACf,OAAO;UACLP,QAAQ,EAAEN,eAAe,CAACU,SAAS;QACrC,CAAC;MACH;MACA,IAAI,EAAAE,aAAA,GAAAL,KAAK,CAACO,KAAK,cAAAF,aAAA,gBAAAA,aAAA,GAAXA,aAAA,CAAaG,WAAW,cAAAH,aAAA,uBAAxBA,aAAA,CAA0BI,IAAI,MAAK,IAAI,EAAE;QAC3C,OAAO;UACLV,QAAQ,KAAAJ,MAAA,CAAKW,UAAU,EAAAX,MAAA,CAAGK,KAAK,CAACO,KAAK,CAACC,WAAW,CAACC,IAAI;QACxD,CAAC;MACH;MACA,OAAO;QACLV,QAAQ,EAAEO;MACZ,CAAC;IACH,CAAC;IACD,OAAOhB,iBAAiB,CAACU,KAAK,EAAEA,KAAK,CAACD,QAAQ,EAAEG,kBAAkB,CAAC;EACrE;EACA,OAAO,IAAI;AACb,CAAC;AACDH,QAAQ,CAACW,WAAW,GAAG,CAAC,UAAU,CAAC;AACnC,OAAO,MAAMC,QAAQ,GAAGvB,KAAK,CAAC;EAC5BS,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEL;AACb,CAAC,CAAC;AACF,OAAO,MAAMmB,MAAM,GAAGxB,KAAK,CAAC;EAC1BS,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEL;AACb,CAAC,CAAC;AACF,OAAO,MAAMoB,SAAS,GAAGzB,KAAK,CAAC;EAC7BS,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEL;AACb,CAAC,CAAC;AACF,OAAO,MAAMqB,SAAS,GAAG1B,KAAK,CAAC;EAC7BS,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEL;AACb,CAAC,CAAC;AACF,OAAO,MAAMsB,SAAS,GAAG3B,KAAK,CAAC;EAC7BS,IAAI,EAAE,MAAM;EACZmB,WAAW,EAAE,OAAO;EACpBlB,SAAS,EAAEL;AACb,CAAC,CAAC;AACF,OAAO,MAAMwB,UAAU,GAAG7B,KAAK,CAAC;EAC9BS,IAAI,EAAE,MAAM;EACZmB,WAAW,EAAE,QAAQ;EACrBlB,SAAS,EAAEL;AACb,CAAC,CAAC;AACF,OAAO,MAAMyB,SAAS,GAAG9B,KAAK,CAAC;EAC7BS,IAAI,EAAE;AACR,CAAC,CAAC;AACF,MAAMsB,MAAM,GAAG9B,OAAO,CAACO,KAAK,EAAEG,QAAQ,EAAEY,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEI,SAAS,CAAC;AAC1F,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}