{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport FormControlContext from \"./FormControlContext.js\";\nexport default function useFormControl() {\n  return React.useContext(FormControlContext);\n}", "map": {"version": 3, "names": ["React", "FormControlContext", "useFormControl", "useContext"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/FormControl/useFormControl.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport FormControlContext from \"./FormControlContext.js\";\nexport default function useFormControl() {\n  return React.useContext(FormControlContext);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,eAAe,SAASC,cAAcA,CAAA,EAAG;EACvC,OAAOF,KAAK,CAACG,UAAU,CAACF,kBAAkB,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}