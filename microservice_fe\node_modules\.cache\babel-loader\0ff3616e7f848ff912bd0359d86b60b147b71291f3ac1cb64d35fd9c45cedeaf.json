{"ast": null, "code": "export default function shouldSkipGeneratingVar(keys) {\n  var _keys$;\n  return !!keys[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/) || !!keys[0].match(/sxConfig$/) ||\n  // ends with sxConfig\n  keys[0] === 'palette' && !!((_keys$ = keys[1]) !== null && _keys$ !== void 0 && _keys$.match(/(mode|contrastThreshold|tonalOffset)/));\n}", "map": {"version": 3, "names": ["shouldSkipGeneratingVar", "keys", "_keys$", "match"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/styles/shouldSkipGeneratingVar.js"], "sourcesContent": ["export default function shouldSkipGeneratingVar(keys) {\n  return !!keys[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/) || !!keys[0].match(/sxConfig$/) ||\n  // ends with sxConfig\n  keys[0] === 'palette' && !!keys[1]?.match(/(mode|contrastThreshold|tonalOffset)/);\n}"], "mappings": "AAAA,eAAe,SAASA,uBAAuBA,CAACC,IAAI,EAAE;EAAA,IAAAC,MAAA;EACpD,OAAO,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,qGAAqG,CAAC,IAAI,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,WAAW,CAAC;EAC7J;EACAF,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,GAAAC,MAAA,GAACD,IAAI,CAAC,CAAC,CAAC,cAAAC,MAAA,eAAPA,MAAA,CAASC,KAAK,CAAC,sCAAsC,CAAC;AACnF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}