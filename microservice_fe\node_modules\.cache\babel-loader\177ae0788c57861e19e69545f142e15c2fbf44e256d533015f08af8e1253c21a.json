{"ast": null, "code": "'use client';\n\nimport _objectSpread from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"action\", \"anchorEl\", \"anchorOrigin\", \"anchorPosition\", \"anchorReference\", \"children\", \"className\", \"container\", \"elevation\", \"marginThreshold\", \"open\", \"PaperProps\", \"slots\", \"slotProps\", \"transformOrigin\", \"TransitionComponent\", \"transitionDuration\", \"TransitionProps\", \"disableScrollLock\"],\n  _excluded2 = [\"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport refType from '@mui/utils/refType';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport isHostComponent from \"../utils/isHostComponent.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport debounce from \"../utils/debounce.js\";\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport ownerWindow from \"../utils/ownerWindow.js\";\nimport Grow from \"../Grow/index.js\";\nimport Modal from \"../Modal/index.js\";\nimport PaperBase from \"../Paper/index.js\";\nimport { getPopoverUtilityClass } from \"./popoverClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { mergeSlotProps } from \"../utils/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function getOffsetTop(rect, vertical) {\n  let offset = 0;\n  if (typeof vertical === 'number') {\n    offset = vertical;\n  } else if (vertical === 'center') {\n    offset = rect.height / 2;\n  } else if (vertical === 'bottom') {\n    offset = rect.height;\n  }\n  return offset;\n}\nexport function getOffsetLeft(rect, horizontal) {\n  let offset = 0;\n  if (typeof horizontal === 'number') {\n    offset = horizontal;\n  } else if (horizontal === 'center') {\n    offset = rect.width / 2;\n  } else if (horizontal === 'right') {\n    offset = rect.width;\n  }\n  return offset;\n}\nfunction getTransformOriginValue(transformOrigin) {\n  return [transformOrigin.horizontal, transformOrigin.vertical].map(n => typeof n === 'number' ? \"\".concat(n, \"px\") : n).join(' ');\n}\nfunction resolveAnchorEl(anchorEl) {\n  return typeof anchorEl === 'function' ? anchorEl() : anchorEl;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    paper: ['paper']\n  };\n  return composeClasses(slots, getPopoverUtilityClass, classes);\n};\nexport const PopoverRoot = styled(Modal, {\n  name: 'MuiPopover',\n  slot: 'Root'\n})({});\nexport const PopoverPaper = styled(PaperBase, {\n  name: 'MuiPopover',\n  slot: 'Paper'\n})({\n  position: 'absolute',\n  overflowY: 'auto',\n  overflowX: 'hidden',\n  // So we see the popover when it's empty.\n  // It's most likely on issue on userland.\n  minWidth: 16,\n  minHeight: 16,\n  maxWidth: 'calc(100% - 32px)',\n  maxHeight: 'calc(100% - 32px)',\n  // We disable the focus ring for mouse, touch and keyboard users.\n  outline: 0\n});\nconst Popover = /*#__PURE__*/React.forwardRef(function Popover(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPopover'\n  });\n  const {\n      action,\n      anchorEl,\n      anchorOrigin = {\n        vertical: 'top',\n        horizontal: 'left'\n      },\n      anchorPosition,\n      anchorReference = 'anchorEl',\n      children,\n      className,\n      container: containerProp,\n      elevation = 8,\n      marginThreshold = 16,\n      open,\n      PaperProps: PaperPropsProp = {},\n      // TODO: remove in v7\n      slots = {},\n      slotProps = {},\n      transformOrigin = {\n        vertical: 'top',\n        horizontal: 'left'\n      },\n      TransitionComponent,\n      // TODO: remove in v7\n      transitionDuration: transitionDurationProp = 'auto',\n      TransitionProps = {},\n      // TODO: remove in v7\n      disableScrollLock = false\n    } = props,\n    other = _objectWithoutProperties(props, _excluded);\n  const paperRef = React.useRef();\n  const ownerState = _objectSpread(_objectSpread({}, props), {}, {\n    anchorOrigin,\n    anchorReference,\n    elevation,\n    marginThreshold,\n    transformOrigin,\n    TransitionComponent,\n    transitionDuration: transitionDurationProp,\n    TransitionProps\n  });\n  const classes = useUtilityClasses(ownerState);\n\n  // Returns the top/left offset of the position\n  // to attach to on the anchor element (or body if none is provided)\n  const getAnchorOffset = React.useCallback(() => {\n    if (anchorReference === 'anchorPosition') {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!anchorPosition) {\n          console.error('MUI: You need to provide a `anchorPosition` prop when using ' + '<Popover anchorReference=\"anchorPosition\" />.');\n        }\n      }\n      return anchorPosition;\n    }\n    const resolvedAnchorEl = resolveAnchorEl(anchorEl);\n\n    // If an anchor element wasn't provided, just use the parent body element of this Popover\n    const anchorElement = resolvedAnchorEl && resolvedAnchorEl.nodeType === 1 ? resolvedAnchorEl : ownerDocument(paperRef.current).body;\n    const anchorRect = anchorElement.getBoundingClientRect();\n    if (process.env.NODE_ENV !== 'production') {\n      const box = anchorElement.getBoundingClientRect();\n      if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n        console.warn(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n      }\n    }\n    return {\n      top: anchorRect.top + getOffsetTop(anchorRect, anchorOrigin.vertical),\n      left: anchorRect.left + getOffsetLeft(anchorRect, anchorOrigin.horizontal)\n    };\n  }, [anchorEl, anchorOrigin.horizontal, anchorOrigin.vertical, anchorPosition, anchorReference]);\n\n  // Returns the base transform origin using the element\n  const getTransformOrigin = React.useCallback(elemRect => {\n    return {\n      vertical: getOffsetTop(elemRect, transformOrigin.vertical),\n      horizontal: getOffsetLeft(elemRect, transformOrigin.horizontal)\n    };\n  }, [transformOrigin.horizontal, transformOrigin.vertical]);\n  const getPositioningStyle = React.useCallback(element => {\n    const elemRect = {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    };\n\n    // Get the transform origin point on the element itself\n    const elemTransformOrigin = getTransformOrigin(elemRect);\n    if (anchorReference === 'none') {\n      return {\n        top: null,\n        left: null,\n        transformOrigin: getTransformOriginValue(elemTransformOrigin)\n      };\n    }\n\n    // Get the offset of the anchoring element\n    const anchorOffset = getAnchorOffset();\n\n    // Calculate element positioning\n    let top = anchorOffset.top - elemTransformOrigin.vertical;\n    let left = anchorOffset.left - elemTransformOrigin.horizontal;\n    const bottom = top + elemRect.height;\n    const right = left + elemRect.width;\n\n    // Use the parent window of the anchorEl if provided\n    const containerWindow = ownerWindow(resolveAnchorEl(anchorEl));\n\n    // Window thresholds taking required margin into account\n    const heightThreshold = containerWindow.innerHeight - marginThreshold;\n    const widthThreshold = containerWindow.innerWidth - marginThreshold;\n\n    // Check if the vertical axis needs shifting\n    if (marginThreshold !== null && top < marginThreshold) {\n      const diff = top - marginThreshold;\n      top -= diff;\n      elemTransformOrigin.vertical += diff;\n    } else if (marginThreshold !== null && bottom > heightThreshold) {\n      const diff = bottom - heightThreshold;\n      top -= diff;\n      elemTransformOrigin.vertical += diff;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (elemRect.height > heightThreshold && elemRect.height && heightThreshold) {\n        console.error(['MUI: The popover component is too tall.', \"Some part of it can not be seen on the screen (\".concat(elemRect.height - heightThreshold, \"px).\"), 'Please consider adding a `max-height` to improve the user-experience.'].join('\\n'));\n      }\n    }\n\n    // Check if the horizontal axis needs shifting\n    if (marginThreshold !== null && left < marginThreshold) {\n      const diff = left - marginThreshold;\n      left -= diff;\n      elemTransformOrigin.horizontal += diff;\n    } else if (right > widthThreshold) {\n      const diff = right - widthThreshold;\n      left -= diff;\n      elemTransformOrigin.horizontal += diff;\n    }\n    return {\n      top: \"\".concat(Math.round(top), \"px\"),\n      left: \"\".concat(Math.round(left), \"px\"),\n      transformOrigin: getTransformOriginValue(elemTransformOrigin)\n    };\n  }, [anchorEl, anchorReference, getAnchorOffset, getTransformOrigin, marginThreshold]);\n  const [isPositioned, setIsPositioned] = React.useState(open);\n  const setPositioningStyles = React.useCallback(() => {\n    const element = paperRef.current;\n    if (!element) {\n      return;\n    }\n    const positioning = getPositioningStyle(element);\n    if (positioning.top !== null) {\n      element.style.setProperty('top', positioning.top);\n    }\n    if (positioning.left !== null) {\n      element.style.left = positioning.left;\n    }\n    element.style.transformOrigin = positioning.transformOrigin;\n    setIsPositioned(true);\n  }, [getPositioningStyle]);\n  React.useEffect(() => {\n    if (disableScrollLock) {\n      window.addEventListener('scroll', setPositioningStyles);\n    }\n    return () => window.removeEventListener('scroll', setPositioningStyles);\n  }, [anchorEl, disableScrollLock, setPositioningStyles]);\n  const handleEntering = () => {\n    setPositioningStyles();\n  };\n  const handleExited = () => {\n    setIsPositioned(false);\n  };\n  React.useEffect(() => {\n    if (open) {\n      setPositioningStyles();\n    }\n  });\n  React.useImperativeHandle(action, () => open ? {\n    updatePosition: () => {\n      setPositioningStyles();\n    }\n  } : null, [open, setPositioningStyles]);\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n    const handleResize = debounce(() => {\n      setPositioningStyles();\n    });\n    const containerWindow = ownerWindow(resolveAnchorEl(anchorEl));\n    containerWindow.addEventListener('resize', handleResize);\n    return () => {\n      handleResize.clear();\n      containerWindow.removeEventListener('resize', handleResize);\n    };\n  }, [anchorEl, open, setPositioningStyles]);\n  let transitionDuration = transitionDurationProp;\n  const externalForwardedProps = {\n    slots: _objectSpread({\n      transition: TransitionComponent\n    }, slots),\n    slotProps: _objectSpread({\n      transition: TransitionProps,\n      paper: PaperPropsProp\n    }, slotProps)\n  };\n  const [TransitionSlot, transitionSlotProps] = useSlot('transition', {\n    elementType: Grow,\n    externalForwardedProps,\n    ownerState,\n    getSlotProps: handlers => _objectSpread(_objectSpread({}, handlers), {}, {\n      onEntering: (element, isAppearing) => {\n        var _handlers$onEntering;\n        (_handlers$onEntering = handlers.onEntering) === null || _handlers$onEntering === void 0 || _handlers$onEntering.call(handlers, element, isAppearing);\n        handleEntering();\n      },\n      onExited: element => {\n        var _handlers$onExited;\n        (_handlers$onExited = handlers.onExited) === null || _handlers$onExited === void 0 || _handlers$onExited.call(handlers, element);\n        handleExited();\n      }\n    }),\n    additionalProps: {\n      appear: true,\n      in: open\n    }\n  });\n  if (transitionDurationProp === 'auto' && !TransitionSlot.muiSupportAuto) {\n    transitionDuration = undefined;\n  }\n\n  // If the container prop is provided, use that\n  // If the anchorEl prop is provided, use its parent body element as the container\n  // If neither are provided let the Modal take care of choosing the container\n  const container = containerProp || (anchorEl ? ownerDocument(resolveAnchorEl(anchorEl)).body : undefined);\n  const [RootSlot, _ref] = useSlot('root', {\n      ref,\n      elementType: PopoverRoot,\n      externalForwardedProps: _objectSpread(_objectSpread({}, externalForwardedProps), other),\n      shouldForwardComponentProp: true,\n      additionalProps: {\n        slots: {\n          backdrop: slots.backdrop\n        },\n        slotProps: {\n          backdrop: mergeSlotProps(typeof slotProps.backdrop === 'function' ? slotProps.backdrop(ownerState) : slotProps.backdrop, {\n            invisible: true\n          })\n        },\n        container,\n        open\n      },\n      ownerState,\n      className: clsx(classes.root, className)\n    }),\n    {\n      slots: rootSlotsProp,\n      slotProps: rootSlotPropsProp\n    } = _ref,\n    rootProps = _objectWithoutProperties(_ref, _excluded2);\n  const [PaperSlot, paperProps] = useSlot('paper', {\n    ref: paperRef,\n    className: classes.paper,\n    elementType: PopoverPaper,\n    externalForwardedProps,\n    shouldForwardComponentProp: true,\n    additionalProps: {\n      elevation,\n      style: isPositioned ? undefined : {\n        opacity: 0\n      }\n    },\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(RootSlot, _objectSpread(_objectSpread(_objectSpread({}, rootProps), !isHostComponent(RootSlot) && {\n    slots: rootSlotsProp,\n    slotProps: rootSlotPropsProp,\n    disableScrollLock\n  }), {}, {\n    children: /*#__PURE__*/_jsx(TransitionSlot, _objectSpread(_objectSpread({}, transitionSlotProps), {}, {\n      timeout: transitionDuration,\n      children: /*#__PURE__*/_jsx(PaperSlot, _objectSpread(_objectSpread({}, paperProps), {}, {\n        children: children\n      }))\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Popover.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A ref for imperative actions.\n   * It currently only supports updatePosition() action.\n   */\n  action: refType,\n  /**\n   * An HTML element, [PopoverVirtualElement](https://mui.com/material-ui/react-popover/#virtual-element),\n   * or a function that returns either.\n   * It's used to set the position of the popover.\n   */\n  anchorEl: chainPropTypes(PropTypes.oneOfType([HTMLElementType, PropTypes.func]), props => {\n    if (props.open && (!props.anchorReference || props.anchorReference === 'anchorEl')) {\n      const resolvedAnchorEl = resolveAnchorEl(props.anchorEl);\n      if (resolvedAnchorEl && resolvedAnchorEl.nodeType === 1) {\n        const box = resolvedAnchorEl.getBoundingClientRect();\n        if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n          return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n        }\n      } else {\n        return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', \"It should be an Element or PopoverVirtualElement instance but it's `\".concat(resolvedAnchorEl, \"` instead.\")].join('\\n'));\n      }\n    }\n    return null;\n  }),\n  /**\n   * This is the point on the anchor where the popover's\n   * `anchorEl` will attach to. This is not used when the\n   * anchorReference is 'anchorPosition'.\n   *\n   * Options:\n   * vertical: [top, center, bottom];\n   * horizontal: [left, center, right].\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'left',\n   * }\n   */\n  anchorOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOfType([PropTypes.oneOf(['center', 'left', 'right']), PropTypes.number]).isRequired,\n    vertical: PropTypes.oneOfType([PropTypes.oneOf(['bottom', 'center', 'top']), PropTypes.number]).isRequired\n  }),\n  /**\n   * This is the position that may be used to set the position of the popover.\n   * The coordinates are relative to the application's client area.\n   */\n  anchorPosition: PropTypes.shape({\n    left: PropTypes.number.isRequired,\n    top: PropTypes.number.isRequired\n  }),\n  /**\n   * This determines which anchor prop to refer to when setting\n   * the position of the popover.\n   * @default 'anchorEl'\n   */\n  anchorReference: PropTypes.oneOf(['anchorEl', 'anchorPosition', 'none']),\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   *   overridesResolver: (props, styles) => {\n   *     return styles.backdrop;\n   *   },\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Backdrop`](/material-ui/api/backdrop/) element.\n   * @deprecated Use `slotProps.backdrop` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * An HTML element, component instance, or function that returns either.\n   * The `container` will passed to the Modal component.\n   *\n   * By default, it uses the body of the anchorEl's top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * The elevation of the popover.\n   * @default 8\n   */\n  elevation: integerPropType,\n  /**\n   * Specifies how close to the edge of the window the popover can appear.\n   * If null, the popover will not be constrained by the window.\n   * @default 16\n   */\n  marginThreshold: PropTypes.number,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * Props applied to the [`Paper`](https://mui.com/material-ui/api/paper/) element.\n   *\n   * This prop is an alias for `slotProps.paper` and will be overriden by it if both are used.\n   * @deprecated Use `slotProps.paper` instead.\n   *\n   * @default {}\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef\n  }),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * This is the point on the popover which\n   * will attach to the anchor's origin.\n   *\n   * Options:\n   * vertical: [top, center, bottom, x(px)];\n   * horizontal: [left, center, right, x(px)].\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'left',\n   * }\n   */\n  transformOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOfType([PropTypes.oneOf(['center', 'left', 'right']), PropTypes.number]).isRequired,\n    vertical: PropTypes.oneOfType([PropTypes.oneOf(['bottom', 'center', 'top']), PropTypes.number]).isRequired\n  }),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @deprecated use the `slots.transition` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default Grow\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Set to 'auto' to automatically calculate transition time based on height.\n   * @default 'auto'\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @deprecated use the `slotProps.transition` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Popover;", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "_excluded2", "React", "PropTypes", "clsx", "composeClasses", "HTMLElementType", "refType", "elementTypeAcceptingRef", "integerPropType", "chainPropTypes", "isHostComponent", "styled", "useDefaultProps", "debounce", "ownerDocument", "ownerWindow", "Grow", "Modal", "PaperBase", "getPopoverUtilityClass", "useSlot", "mergeSlotProps", "jsx", "_jsx", "getOffsetTop", "rect", "vertical", "offset", "height", "getOffsetLeft", "horizontal", "width", "getTransformOriginValue", "transform<PERSON><PERSON>in", "map", "n", "concat", "join", "resolveAnchorEl", "anchorEl", "useUtilityClasses", "ownerState", "classes", "slots", "root", "paper", "PopoverRoot", "name", "slot", "PopoverPaper", "position", "overflowY", "overflowX", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "outline", "Popover", "forwardRef", "inProps", "ref", "props", "action", "anchor<PERSON><PERSON><PERSON>", "anchorPosition", "anchorReference", "children", "className", "container", "containerProp", "elevation", "marginT<PERSON><PERSON>old", "open", "PaperProps", "PaperPropsProp", "slotProps", "TransitionComponent", "transitionDuration", "transitionDurationProp", "TransitionProps", "disableScrollLock", "other", "paperRef", "useRef", "getAnchorOffset", "useCallback", "process", "env", "NODE_ENV", "console", "error", "resolvedAnchorEl", "anchorElement", "nodeType", "current", "body", "anchorRect", "getBoundingClientRect", "box", "top", "left", "right", "bottom", "warn", "getTransformOrigin", "elemRect", "getPositioningStyle", "element", "offsetWidth", "offsetHeight", "elemTransformOrigin", "anchorOffset", "containerWindow", "heightThreshold", "innerHeight", "widthThreshold", "innerWidth", "diff", "Math", "round", "isPositioned", "setIsPositioned", "useState", "setPositioningStyles", "positioning", "style", "setProperty", "useEffect", "window", "addEventListener", "removeEventListener", "handleEntering", "handleExited", "useImperativeHandle", "updatePosition", "undefined", "handleResize", "clear", "externalForwardedProps", "transition", "TransitionSlot", "transitionSlotProps", "elementType", "getSlotProps", "handlers", "onEntering", "isAppearing", "_handlers$onEntering", "call", "onExited", "_handlers$onExited", "additionalProps", "appear", "in", "muiSupportAuto", "RootSlot", "_ref", "shouldForwardComponentProp", "backdrop", "invisible", "rootSlotsProp", "rootSlotPropsProp", "rootProps", "PaperSlot", "paperProps", "opacity", "timeout", "propTypes", "oneOfType", "func", "Error", "shape", "oneOf", "number", "isRequired", "BackdropComponent", "BackdropProps", "object", "node", "string", "bool", "onClose", "component", "sx", "arrayOf", "enter", "exit"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/Popover/Popover.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport refType from '@mui/utils/refType';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport isHostComponent from \"../utils/isHostComponent.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport debounce from \"../utils/debounce.js\";\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport ownerWindow from \"../utils/ownerWindow.js\";\nimport Grow from \"../Grow/index.js\";\nimport Modal from \"../Modal/index.js\";\nimport PaperBase from \"../Paper/index.js\";\nimport { getPopoverUtilityClass } from \"./popoverClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { mergeSlotProps } from \"../utils/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function getOffsetTop(rect, vertical) {\n  let offset = 0;\n  if (typeof vertical === 'number') {\n    offset = vertical;\n  } else if (vertical === 'center') {\n    offset = rect.height / 2;\n  } else if (vertical === 'bottom') {\n    offset = rect.height;\n  }\n  return offset;\n}\nexport function getOffsetLeft(rect, horizontal) {\n  let offset = 0;\n  if (typeof horizontal === 'number') {\n    offset = horizontal;\n  } else if (horizontal === 'center') {\n    offset = rect.width / 2;\n  } else if (horizontal === 'right') {\n    offset = rect.width;\n  }\n  return offset;\n}\nfunction getTransformOriginValue(transformOrigin) {\n  return [transformOrigin.horizontal, transformOrigin.vertical].map(n => typeof n === 'number' ? `${n}px` : n).join(' ');\n}\nfunction resolveAnchorEl(anchorEl) {\n  return typeof anchorEl === 'function' ? anchorEl() : anchorEl;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    paper: ['paper']\n  };\n  return composeClasses(slots, getPopoverUtilityClass, classes);\n};\nexport const PopoverRoot = styled(Modal, {\n  name: 'MuiPopover',\n  slot: 'Root'\n})({});\nexport const PopoverPaper = styled(PaperBase, {\n  name: 'MuiPopover',\n  slot: 'Paper'\n})({\n  position: 'absolute',\n  overflowY: 'auto',\n  overflowX: 'hidden',\n  // So we see the popover when it's empty.\n  // It's most likely on issue on userland.\n  minWidth: 16,\n  minHeight: 16,\n  maxWidth: 'calc(100% - 32px)',\n  maxHeight: 'calc(100% - 32px)',\n  // We disable the focus ring for mouse, touch and keyboard users.\n  outline: 0\n});\nconst Popover = /*#__PURE__*/React.forwardRef(function Popover(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPopover'\n  });\n  const {\n    action,\n    anchorEl,\n    anchorOrigin = {\n      vertical: 'top',\n      horizontal: 'left'\n    },\n    anchorPosition,\n    anchorReference = 'anchorEl',\n    children,\n    className,\n    container: containerProp,\n    elevation = 8,\n    marginThreshold = 16,\n    open,\n    PaperProps: PaperPropsProp = {},\n    // TODO: remove in v7\n    slots = {},\n    slotProps = {},\n    transformOrigin = {\n      vertical: 'top',\n      horizontal: 'left'\n    },\n    TransitionComponent,\n    // TODO: remove in v7\n    transitionDuration: transitionDurationProp = 'auto',\n    TransitionProps = {},\n    // TODO: remove in v7\n    disableScrollLock = false,\n    ...other\n  } = props;\n  const paperRef = React.useRef();\n  const ownerState = {\n    ...props,\n    anchorOrigin,\n    anchorReference,\n    elevation,\n    marginThreshold,\n    transformOrigin,\n    TransitionComponent,\n    transitionDuration: transitionDurationProp,\n    TransitionProps\n  };\n  const classes = useUtilityClasses(ownerState);\n\n  // Returns the top/left offset of the position\n  // to attach to on the anchor element (or body if none is provided)\n  const getAnchorOffset = React.useCallback(() => {\n    if (anchorReference === 'anchorPosition') {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!anchorPosition) {\n          console.error('MUI: You need to provide a `anchorPosition` prop when using ' + '<Popover anchorReference=\"anchorPosition\" />.');\n        }\n      }\n      return anchorPosition;\n    }\n    const resolvedAnchorEl = resolveAnchorEl(anchorEl);\n\n    // If an anchor element wasn't provided, just use the parent body element of this Popover\n    const anchorElement = resolvedAnchorEl && resolvedAnchorEl.nodeType === 1 ? resolvedAnchorEl : ownerDocument(paperRef.current).body;\n    const anchorRect = anchorElement.getBoundingClientRect();\n    if (process.env.NODE_ENV !== 'production') {\n      const box = anchorElement.getBoundingClientRect();\n      if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n        console.warn(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n      }\n    }\n    return {\n      top: anchorRect.top + getOffsetTop(anchorRect, anchorOrigin.vertical),\n      left: anchorRect.left + getOffsetLeft(anchorRect, anchorOrigin.horizontal)\n    };\n  }, [anchorEl, anchorOrigin.horizontal, anchorOrigin.vertical, anchorPosition, anchorReference]);\n\n  // Returns the base transform origin using the element\n  const getTransformOrigin = React.useCallback(elemRect => {\n    return {\n      vertical: getOffsetTop(elemRect, transformOrigin.vertical),\n      horizontal: getOffsetLeft(elemRect, transformOrigin.horizontal)\n    };\n  }, [transformOrigin.horizontal, transformOrigin.vertical]);\n  const getPositioningStyle = React.useCallback(element => {\n    const elemRect = {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    };\n\n    // Get the transform origin point on the element itself\n    const elemTransformOrigin = getTransformOrigin(elemRect);\n    if (anchorReference === 'none') {\n      return {\n        top: null,\n        left: null,\n        transformOrigin: getTransformOriginValue(elemTransformOrigin)\n      };\n    }\n\n    // Get the offset of the anchoring element\n    const anchorOffset = getAnchorOffset();\n\n    // Calculate element positioning\n    let top = anchorOffset.top - elemTransformOrigin.vertical;\n    let left = anchorOffset.left - elemTransformOrigin.horizontal;\n    const bottom = top + elemRect.height;\n    const right = left + elemRect.width;\n\n    // Use the parent window of the anchorEl if provided\n    const containerWindow = ownerWindow(resolveAnchorEl(anchorEl));\n\n    // Window thresholds taking required margin into account\n    const heightThreshold = containerWindow.innerHeight - marginThreshold;\n    const widthThreshold = containerWindow.innerWidth - marginThreshold;\n\n    // Check if the vertical axis needs shifting\n    if (marginThreshold !== null && top < marginThreshold) {\n      const diff = top - marginThreshold;\n      top -= diff;\n      elemTransformOrigin.vertical += diff;\n    } else if (marginThreshold !== null && bottom > heightThreshold) {\n      const diff = bottom - heightThreshold;\n      top -= diff;\n      elemTransformOrigin.vertical += diff;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (elemRect.height > heightThreshold && elemRect.height && heightThreshold) {\n        console.error(['MUI: The popover component is too tall.', `Some part of it can not be seen on the screen (${elemRect.height - heightThreshold}px).`, 'Please consider adding a `max-height` to improve the user-experience.'].join('\\n'));\n      }\n    }\n\n    // Check if the horizontal axis needs shifting\n    if (marginThreshold !== null && left < marginThreshold) {\n      const diff = left - marginThreshold;\n      left -= diff;\n      elemTransformOrigin.horizontal += diff;\n    } else if (right > widthThreshold) {\n      const diff = right - widthThreshold;\n      left -= diff;\n      elemTransformOrigin.horizontal += diff;\n    }\n    return {\n      top: `${Math.round(top)}px`,\n      left: `${Math.round(left)}px`,\n      transformOrigin: getTransformOriginValue(elemTransformOrigin)\n    };\n  }, [anchorEl, anchorReference, getAnchorOffset, getTransformOrigin, marginThreshold]);\n  const [isPositioned, setIsPositioned] = React.useState(open);\n  const setPositioningStyles = React.useCallback(() => {\n    const element = paperRef.current;\n    if (!element) {\n      return;\n    }\n    const positioning = getPositioningStyle(element);\n    if (positioning.top !== null) {\n      element.style.setProperty('top', positioning.top);\n    }\n    if (positioning.left !== null) {\n      element.style.left = positioning.left;\n    }\n    element.style.transformOrigin = positioning.transformOrigin;\n    setIsPositioned(true);\n  }, [getPositioningStyle]);\n  React.useEffect(() => {\n    if (disableScrollLock) {\n      window.addEventListener('scroll', setPositioningStyles);\n    }\n    return () => window.removeEventListener('scroll', setPositioningStyles);\n  }, [anchorEl, disableScrollLock, setPositioningStyles]);\n  const handleEntering = () => {\n    setPositioningStyles();\n  };\n  const handleExited = () => {\n    setIsPositioned(false);\n  };\n  React.useEffect(() => {\n    if (open) {\n      setPositioningStyles();\n    }\n  });\n  React.useImperativeHandle(action, () => open ? {\n    updatePosition: () => {\n      setPositioningStyles();\n    }\n  } : null, [open, setPositioningStyles]);\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n    const handleResize = debounce(() => {\n      setPositioningStyles();\n    });\n    const containerWindow = ownerWindow(resolveAnchorEl(anchorEl));\n    containerWindow.addEventListener('resize', handleResize);\n    return () => {\n      handleResize.clear();\n      containerWindow.removeEventListener('resize', handleResize);\n    };\n  }, [anchorEl, open, setPositioningStyles]);\n  let transitionDuration = transitionDurationProp;\n  const externalForwardedProps = {\n    slots: {\n      transition: TransitionComponent,\n      ...slots\n    },\n    slotProps: {\n      transition: TransitionProps,\n      paper: PaperPropsProp,\n      ...slotProps\n    }\n  };\n  const [TransitionSlot, transitionSlotProps] = useSlot('transition', {\n    elementType: Grow,\n    externalForwardedProps,\n    ownerState,\n    getSlotProps: handlers => ({\n      ...handlers,\n      onEntering: (element, isAppearing) => {\n        handlers.onEntering?.(element, isAppearing);\n        handleEntering();\n      },\n      onExited: element => {\n        handlers.onExited?.(element);\n        handleExited();\n      }\n    }),\n    additionalProps: {\n      appear: true,\n      in: open\n    }\n  });\n  if (transitionDurationProp === 'auto' && !TransitionSlot.muiSupportAuto) {\n    transitionDuration = undefined;\n  }\n\n  // If the container prop is provided, use that\n  // If the anchorEl prop is provided, use its parent body element as the container\n  // If neither are provided let the Modal take care of choosing the container\n  const container = containerProp || (anchorEl ? ownerDocument(resolveAnchorEl(anchorEl)).body : undefined);\n  const [RootSlot, {\n    slots: rootSlotsProp,\n    slotProps: rootSlotPropsProp,\n    ...rootProps\n  }] = useSlot('root', {\n    ref,\n    elementType: PopoverRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    shouldForwardComponentProp: true,\n    additionalProps: {\n      slots: {\n        backdrop: slots.backdrop\n      },\n      slotProps: {\n        backdrop: mergeSlotProps(typeof slotProps.backdrop === 'function' ? slotProps.backdrop(ownerState) : slotProps.backdrop, {\n          invisible: true\n        })\n      },\n      container,\n      open\n    },\n    ownerState,\n    className: clsx(classes.root, className)\n  });\n  const [PaperSlot, paperProps] = useSlot('paper', {\n    ref: paperRef,\n    className: classes.paper,\n    elementType: PopoverPaper,\n    externalForwardedProps,\n    shouldForwardComponentProp: true,\n    additionalProps: {\n      elevation,\n      style: isPositioned ? undefined : {\n        opacity: 0\n      }\n    },\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(RootSlot, {\n    ...rootProps,\n    ...(!isHostComponent(RootSlot) && {\n      slots: rootSlotsProp,\n      slotProps: rootSlotPropsProp,\n      disableScrollLock\n    }),\n    children: /*#__PURE__*/_jsx(TransitionSlot, {\n      ...transitionSlotProps,\n      timeout: transitionDuration,\n      children: /*#__PURE__*/_jsx(PaperSlot, {\n        ...paperProps,\n        children: children\n      })\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Popover.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A ref for imperative actions.\n   * It currently only supports updatePosition() action.\n   */\n  action: refType,\n  /**\n   * An HTML element, [PopoverVirtualElement](https://mui.com/material-ui/react-popover/#virtual-element),\n   * or a function that returns either.\n   * It's used to set the position of the popover.\n   */\n  anchorEl: chainPropTypes(PropTypes.oneOfType([HTMLElementType, PropTypes.func]), props => {\n    if (props.open && (!props.anchorReference || props.anchorReference === 'anchorEl')) {\n      const resolvedAnchorEl = resolveAnchorEl(props.anchorEl);\n      if (resolvedAnchorEl && resolvedAnchorEl.nodeType === 1) {\n        const box = resolvedAnchorEl.getBoundingClientRect();\n        if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n          return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n        }\n      } else {\n        return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', `It should be an Element or PopoverVirtualElement instance but it's \\`${resolvedAnchorEl}\\` instead.`].join('\\n'));\n      }\n    }\n    return null;\n  }),\n  /**\n   * This is the point on the anchor where the popover's\n   * `anchorEl` will attach to. This is not used when the\n   * anchorReference is 'anchorPosition'.\n   *\n   * Options:\n   * vertical: [top, center, bottom];\n   * horizontal: [left, center, right].\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'left',\n   * }\n   */\n  anchorOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOfType([PropTypes.oneOf(['center', 'left', 'right']), PropTypes.number]).isRequired,\n    vertical: PropTypes.oneOfType([PropTypes.oneOf(['bottom', 'center', 'top']), PropTypes.number]).isRequired\n  }),\n  /**\n   * This is the position that may be used to set the position of the popover.\n   * The coordinates are relative to the application's client area.\n   */\n  anchorPosition: PropTypes.shape({\n    left: PropTypes.number.isRequired,\n    top: PropTypes.number.isRequired\n  }),\n  /**\n   * This determines which anchor prop to refer to when setting\n   * the position of the popover.\n   * @default 'anchorEl'\n   */\n  anchorReference: PropTypes.oneOf(['anchorEl', 'anchorPosition', 'none']),\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   *   overridesResolver: (props, styles) => {\n   *     return styles.backdrop;\n   *   },\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Backdrop`](/material-ui/api/backdrop/) element.\n   * @deprecated Use `slotProps.backdrop` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * An HTML element, component instance, or function that returns either.\n   * The `container` will passed to the Modal component.\n   *\n   * By default, it uses the body of the anchorEl's top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * The elevation of the popover.\n   * @default 8\n   */\n  elevation: integerPropType,\n  /**\n   * Specifies how close to the edge of the window the popover can appear.\n   * If null, the popover will not be constrained by the window.\n   * @default 16\n   */\n  marginThreshold: PropTypes.number,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * Props applied to the [`Paper`](https://mui.com/material-ui/api/paper/) element.\n   *\n   * This prop is an alias for `slotProps.paper` and will be overriden by it if both are used.\n   * @deprecated Use `slotProps.paper` instead.\n   *\n   * @default {}\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef\n  }),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * This is the point on the popover which\n   * will attach to the anchor's origin.\n   *\n   * Options:\n   * vertical: [top, center, bottom, x(px)];\n   * horizontal: [left, center, right, x(px)].\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'left',\n   * }\n   */\n  transformOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOfType([PropTypes.oneOf(['center', 'left', 'right']), PropTypes.number]).isRequired,\n    vertical: PropTypes.oneOfType([PropTypes.oneOf(['bottom', 'center', 'top']), PropTypes.number]).isRequired\n  }),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @deprecated use the `slots.transition` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default Grow\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Set to 'auto' to automatically calculate transition time based on height.\n   * @default 'auto'\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @deprecated use the `slotProps.transition` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Popover;"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;EAAAC,UAAA;AAEb,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,uBAAuB,MAAM,oCAAoC;AACxE,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,eAAe,MAAM,6BAA6B;AACzD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAC3C,IAAIC,MAAM,GAAG,CAAC;EACd,IAAI,OAAOD,QAAQ,KAAK,QAAQ,EAAE;IAChCC,MAAM,GAAGD,QAAQ;EACnB,CAAC,MAAM,IAAIA,QAAQ,KAAK,QAAQ,EAAE;IAChCC,MAAM,GAAGF,IAAI,CAACG,MAAM,GAAG,CAAC;EAC1B,CAAC,MAAM,IAAIF,QAAQ,KAAK,QAAQ,EAAE;IAChCC,MAAM,GAAGF,IAAI,CAACG,MAAM;EACtB;EACA,OAAOD,MAAM;AACf;AACA,OAAO,SAASE,aAAaA,CAACJ,IAAI,EAAEK,UAAU,EAAE;EAC9C,IAAIH,MAAM,GAAG,CAAC;EACd,IAAI,OAAOG,UAAU,KAAK,QAAQ,EAAE;IAClCH,MAAM,GAAGG,UAAU;EACrB,CAAC,MAAM,IAAIA,UAAU,KAAK,QAAQ,EAAE;IAClCH,MAAM,GAAGF,IAAI,CAACM,KAAK,GAAG,CAAC;EACzB,CAAC,MAAM,IAAID,UAAU,KAAK,OAAO,EAAE;IACjCH,MAAM,GAAGF,IAAI,CAACM,KAAK;EACrB;EACA,OAAOJ,MAAM;AACf;AACA,SAASK,uBAAuBA,CAACC,eAAe,EAAE;EAChD,OAAO,CAACA,eAAe,CAACH,UAAU,EAAEG,eAAe,CAACP,QAAQ,CAAC,CAACQ,GAAG,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,MAAAC,MAAA,CAAMD,CAAC,UAAOA,CAAC,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;AACxH;AACA,SAASC,eAAeA,CAACC,QAAQ,EAAE;EACjC,OAAO,OAAOA,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC,CAAC,GAAGA,QAAQ;AAC/D;AACA,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,OAAOzC,cAAc,CAACuC,KAAK,EAAExB,sBAAsB,EAAEuB,OAAO,CAAC;AAC/D,CAAC;AACD,OAAO,MAAMI,WAAW,GAAGnC,MAAM,CAACM,KAAK,EAAE;EACvC8B,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,OAAO,MAAMC,YAAY,GAAGtC,MAAM,CAACO,SAAS,EAAE;EAC5C6B,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDE,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,MAAM;EACjBC,SAAS,EAAE,QAAQ;EACnB;EACA;EACAC,QAAQ,EAAE,EAAE;EACZC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE,mBAAmB;EAC7BC,SAAS,EAAE,mBAAmB;EAC9B;EACAC,OAAO,EAAE;AACX,CAAC,CAAC;AACF,MAAMC,OAAO,GAAG,aAAazD,KAAK,CAAC0D,UAAU,CAAC,SAASD,OAAOA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3E,MAAMC,KAAK,GAAGlD,eAAe,CAAC;IAC5BkD,KAAK,EAAEF,OAAO;IACdb,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACJgB,MAAM;MACNxB,QAAQ;MACRyB,YAAY,GAAG;QACbtC,QAAQ,EAAE,KAAK;QACfI,UAAU,EAAE;MACd,CAAC;MACDmC,cAAc;MACdC,eAAe,GAAG,UAAU;MAC5BC,QAAQ;MACRC,SAAS;MACTC,SAAS,EAAEC,aAAa;MACxBC,SAAS,GAAG,CAAC;MACbC,eAAe,GAAG,EAAE;MACpBC,IAAI;MACJC,UAAU,EAAEC,cAAc,GAAG,CAAC,CAAC;MAC/B;MACAhC,KAAK,GAAG,CAAC,CAAC;MACViC,SAAS,GAAG,CAAC,CAAC;MACd3C,eAAe,GAAG;QAChBP,QAAQ,EAAE,KAAK;QACfI,UAAU,EAAE;MACd,CAAC;MACD+C,mBAAmB;MACnB;MACAC,kBAAkB,EAAEC,sBAAsB,GAAG,MAAM;MACnDC,eAAe,GAAG,CAAC,CAAC;MACpB;MACAC,iBAAiB,GAAG;IAEtB,CAAC,GAAGnB,KAAK;IADJoB,KAAK,GAAApF,wBAAA,CACNgE,KAAK,EAAA/D,SAAA;EACT,MAAMoF,QAAQ,GAAGlF,KAAK,CAACmF,MAAM,CAAC,CAAC;EAC/B,MAAM3C,UAAU,GAAA5C,aAAA,CAAAA,aAAA,KACXiE,KAAK;IACRE,YAAY;IACZE,eAAe;IACfK,SAAS;IACTC,eAAe;IACfvC,eAAe;IACf4C,mBAAmB;IACnBC,kBAAkB,EAAEC,sBAAsB;IAC1CC;EAAe,EAChB;EACD,MAAMtC,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;;EAE7C;EACA;EACA,MAAM4C,eAAe,GAAGpF,KAAK,CAACqF,WAAW,CAAC,MAAM;IAC9C,IAAIpB,eAAe,KAAK,gBAAgB,EAAE;MACxC,IAAIqB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAI,CAACxB,cAAc,EAAE;UACnByB,OAAO,CAACC,KAAK,CAAC,8DAA8D,GAAG,+CAA+C,CAAC;QACjI;MACF;MACA,OAAO1B,cAAc;IACvB;IACA,MAAM2B,gBAAgB,GAAGtD,eAAe,CAACC,QAAQ,CAAC;;IAElD;IACA,MAAMsD,aAAa,GAAGD,gBAAgB,IAAIA,gBAAgB,CAACE,QAAQ,KAAK,CAAC,GAAGF,gBAAgB,GAAG9E,aAAa,CAACqE,QAAQ,CAACY,OAAO,CAAC,CAACC,IAAI;IACnI,MAAMC,UAAU,GAAGJ,aAAa,CAACK,qBAAqB,CAAC,CAAC;IACxD,IAAIX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,MAAMU,GAAG,GAAGN,aAAa,CAACK,qBAAqB,CAAC,CAAC;MACjD,IAAIX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIU,GAAG,CAACC,GAAG,KAAK,CAAC,IAAID,GAAG,CAACE,IAAI,KAAK,CAAC,IAAIF,GAAG,CAACG,KAAK,KAAK,CAAC,IAAIH,GAAG,CAACI,MAAM,KAAK,CAAC,EAAE;QAC7Gb,OAAO,CAACc,IAAI,CAAC,CAAC,gEAAgE,EAAE,2DAA2D,EAAE,iFAAiF,CAAC,CAACnE,IAAI,CAAC,IAAI,CAAC,CAAC;MAC7O;IACF;IACA,OAAO;MACL+D,GAAG,EAAEH,UAAU,CAACG,GAAG,GAAG5E,YAAY,CAACyE,UAAU,EAAEjC,YAAY,CAACtC,QAAQ,CAAC;MACrE2E,IAAI,EAAEJ,UAAU,CAACI,IAAI,GAAGxE,aAAa,CAACoE,UAAU,EAAEjC,YAAY,CAAClC,UAAU;IAC3E,CAAC;EACH,CAAC,EAAE,CAACS,QAAQ,EAAEyB,YAAY,CAAClC,UAAU,EAAEkC,YAAY,CAACtC,QAAQ,EAAEuC,cAAc,EAAEC,eAAe,CAAC,CAAC;;EAE/F;EACA,MAAMuC,kBAAkB,GAAGxG,KAAK,CAACqF,WAAW,CAACoB,QAAQ,IAAI;IACvD,OAAO;MACLhF,QAAQ,EAAEF,YAAY,CAACkF,QAAQ,EAAEzE,eAAe,CAACP,QAAQ,CAAC;MAC1DI,UAAU,EAAED,aAAa,CAAC6E,QAAQ,EAAEzE,eAAe,CAACH,UAAU;IAChE,CAAC;EACH,CAAC,EAAE,CAACG,eAAe,CAACH,UAAU,EAAEG,eAAe,CAACP,QAAQ,CAAC,CAAC;EAC1D,MAAMiF,mBAAmB,GAAG1G,KAAK,CAACqF,WAAW,CAACsB,OAAO,IAAI;IACvD,MAAMF,QAAQ,GAAG;MACf3E,KAAK,EAAE6E,OAAO,CAACC,WAAW;MAC1BjF,MAAM,EAAEgF,OAAO,CAACE;IAClB,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAGN,kBAAkB,CAACC,QAAQ,CAAC;IACxD,IAAIxC,eAAe,KAAK,MAAM,EAAE;MAC9B,OAAO;QACLkC,GAAG,EAAE,IAAI;QACTC,IAAI,EAAE,IAAI;QACVpE,eAAe,EAAED,uBAAuB,CAAC+E,mBAAmB;MAC9D,CAAC;IACH;;IAEA;IACA,MAAMC,YAAY,GAAG3B,eAAe,CAAC,CAAC;;IAEtC;IACA,IAAIe,GAAG,GAAGY,YAAY,CAACZ,GAAG,GAAGW,mBAAmB,CAACrF,QAAQ;IACzD,IAAI2E,IAAI,GAAGW,YAAY,CAACX,IAAI,GAAGU,mBAAmB,CAACjF,UAAU;IAC7D,MAAMyE,MAAM,GAAGH,GAAG,GAAGM,QAAQ,CAAC9E,MAAM;IACpC,MAAM0E,KAAK,GAAGD,IAAI,GAAGK,QAAQ,CAAC3E,KAAK;;IAEnC;IACA,MAAMkF,eAAe,GAAGlG,WAAW,CAACuB,eAAe,CAACC,QAAQ,CAAC,CAAC;;IAE9D;IACA,MAAM2E,eAAe,GAAGD,eAAe,CAACE,WAAW,GAAG3C,eAAe;IACrE,MAAM4C,cAAc,GAAGH,eAAe,CAACI,UAAU,GAAG7C,eAAe;;IAEnE;IACA,IAAIA,eAAe,KAAK,IAAI,IAAI4B,GAAG,GAAG5B,eAAe,EAAE;MACrD,MAAM8C,IAAI,GAAGlB,GAAG,GAAG5B,eAAe;MAClC4B,GAAG,IAAIkB,IAAI;MACXP,mBAAmB,CAACrF,QAAQ,IAAI4F,IAAI;IACtC,CAAC,MAAM,IAAI9C,eAAe,KAAK,IAAI,IAAI+B,MAAM,GAAGW,eAAe,EAAE;MAC/D,MAAMI,IAAI,GAAGf,MAAM,GAAGW,eAAe;MACrCd,GAAG,IAAIkB,IAAI;MACXP,mBAAmB,CAACrF,QAAQ,IAAI4F,IAAI;IACtC;IACA,IAAI/B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIiB,QAAQ,CAAC9E,MAAM,GAAGsF,eAAe,IAAIR,QAAQ,CAAC9E,MAAM,IAAIsF,eAAe,EAAE;QAC3ExB,OAAO,CAACC,KAAK,CAAC,CAAC,yCAAyC,oDAAAvD,MAAA,CAAoDsE,QAAQ,CAAC9E,MAAM,GAAGsF,eAAe,WAAQ,uEAAuE,CAAC,CAAC7E,IAAI,CAAC,IAAI,CAAC,CAAC;MAC3O;IACF;;IAEA;IACA,IAAImC,eAAe,KAAK,IAAI,IAAI6B,IAAI,GAAG7B,eAAe,EAAE;MACtD,MAAM8C,IAAI,GAAGjB,IAAI,GAAG7B,eAAe;MACnC6B,IAAI,IAAIiB,IAAI;MACZP,mBAAmB,CAACjF,UAAU,IAAIwF,IAAI;IACxC,CAAC,MAAM,IAAIhB,KAAK,GAAGc,cAAc,EAAE;MACjC,MAAME,IAAI,GAAGhB,KAAK,GAAGc,cAAc;MACnCf,IAAI,IAAIiB,IAAI;MACZP,mBAAmB,CAACjF,UAAU,IAAIwF,IAAI;IACxC;IACA,OAAO;MACLlB,GAAG,KAAAhE,MAAA,CAAKmF,IAAI,CAACC,KAAK,CAACpB,GAAG,CAAC,OAAI;MAC3BC,IAAI,KAAAjE,MAAA,CAAKmF,IAAI,CAACC,KAAK,CAACnB,IAAI,CAAC,OAAI;MAC7BpE,eAAe,EAAED,uBAAuB,CAAC+E,mBAAmB;IAC9D,CAAC;EACH,CAAC,EAAE,CAACxE,QAAQ,EAAE2B,eAAe,EAAEmB,eAAe,EAAEoB,kBAAkB,EAAEjC,eAAe,CAAC,CAAC;EACrF,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGzH,KAAK,CAAC0H,QAAQ,CAAClD,IAAI,CAAC;EAC5D,MAAMmD,oBAAoB,GAAG3H,KAAK,CAACqF,WAAW,CAAC,MAAM;IACnD,MAAMsB,OAAO,GAAGzB,QAAQ,CAACY,OAAO;IAChC,IAAI,CAACa,OAAO,EAAE;MACZ;IACF;IACA,MAAMiB,WAAW,GAAGlB,mBAAmB,CAACC,OAAO,CAAC;IAChD,IAAIiB,WAAW,CAACzB,GAAG,KAAK,IAAI,EAAE;MAC5BQ,OAAO,CAACkB,KAAK,CAACC,WAAW,CAAC,KAAK,EAAEF,WAAW,CAACzB,GAAG,CAAC;IACnD;IACA,IAAIyB,WAAW,CAACxB,IAAI,KAAK,IAAI,EAAE;MAC7BO,OAAO,CAACkB,KAAK,CAACzB,IAAI,GAAGwB,WAAW,CAACxB,IAAI;IACvC;IACAO,OAAO,CAACkB,KAAK,CAAC7F,eAAe,GAAG4F,WAAW,CAAC5F,eAAe;IAC3DyF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC,EAAE,CAACf,mBAAmB,CAAC,CAAC;EACzB1G,KAAK,CAAC+H,SAAS,CAAC,MAAM;IACpB,IAAI/C,iBAAiB,EAAE;MACrBgD,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEN,oBAAoB,CAAC;IACzD;IACA,OAAO,MAAMK,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEP,oBAAoB,CAAC;EACzE,CAAC,EAAE,CAACrF,QAAQ,EAAE0C,iBAAiB,EAAE2C,oBAAoB,CAAC,CAAC;EACvD,MAAMQ,cAAc,GAAGA,CAAA,KAAM;IAC3BR,oBAAoB,CAAC,CAAC;EACxB,CAAC;EACD,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzBX,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EACDzH,KAAK,CAAC+H,SAAS,CAAC,MAAM;IACpB,IAAIvD,IAAI,EAAE;MACRmD,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,CAAC;EACF3H,KAAK,CAACqI,mBAAmB,CAACvE,MAAM,EAAE,MAAMU,IAAI,GAAG;IAC7C8D,cAAc,EAAEA,CAAA,KAAM;MACpBX,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,GAAG,IAAI,EAAE,CAACnD,IAAI,EAAEmD,oBAAoB,CAAC,CAAC;EACvC3H,KAAK,CAAC+H,SAAS,CAAC,MAAM;IACpB,IAAI,CAACvD,IAAI,EAAE;MACT,OAAO+D,SAAS;IAClB;IACA,MAAMC,YAAY,GAAG5H,QAAQ,CAAC,MAAM;MAClC+G,oBAAoB,CAAC,CAAC;IACxB,CAAC,CAAC;IACF,MAAMX,eAAe,GAAGlG,WAAW,CAACuB,eAAe,CAACC,QAAQ,CAAC,CAAC;IAC9D0E,eAAe,CAACiB,gBAAgB,CAAC,QAAQ,EAAEO,YAAY,CAAC;IACxD,OAAO,MAAM;MACXA,YAAY,CAACC,KAAK,CAAC,CAAC;MACpBzB,eAAe,CAACkB,mBAAmB,CAAC,QAAQ,EAAEM,YAAY,CAAC;IAC7D,CAAC;EACH,CAAC,EAAE,CAAClG,QAAQ,EAAEkC,IAAI,EAAEmD,oBAAoB,CAAC,CAAC;EAC1C,IAAI9C,kBAAkB,GAAGC,sBAAsB;EAC/C,MAAM4D,sBAAsB,GAAG;IAC7BhG,KAAK,EAAA9C,aAAA;MACH+I,UAAU,EAAE/D;IAAmB,GAC5BlC,KAAK,CACT;IACDiC,SAAS,EAAA/E,aAAA;MACP+I,UAAU,EAAE5D,eAAe;MAC3BnC,KAAK,EAAE8B;IAAc,GAClBC,SAAS;EAEhB,CAAC;EACD,MAAM,CAACiE,cAAc,EAAEC,mBAAmB,CAAC,GAAG1H,OAAO,CAAC,YAAY,EAAE;IAClE2H,WAAW,EAAE/H,IAAI;IACjB2H,sBAAsB;IACtBlG,UAAU;IACVuG,YAAY,EAAEC,QAAQ,IAAApJ,aAAA,CAAAA,aAAA,KACjBoJ,QAAQ;MACXC,UAAU,EAAEA,CAACtC,OAAO,EAAEuC,WAAW,KAAK;QAAA,IAAAC,oBAAA;QACpC,CAAAA,oBAAA,GAAAH,QAAQ,CAACC,UAAU,cAAAE,oBAAA,eAAnBA,oBAAA,CAAAC,IAAA,CAAAJ,QAAQ,EAAcrC,OAAO,EAAEuC,WAAW,CAAC;QAC3Cf,cAAc,CAAC,CAAC;MAClB,CAAC;MACDkB,QAAQ,EAAE1C,OAAO,IAAI;QAAA,IAAA2C,kBAAA;QACnB,CAAAA,kBAAA,GAAAN,QAAQ,CAACK,QAAQ,cAAAC,kBAAA,eAAjBA,kBAAA,CAAAF,IAAA,CAAAJ,QAAQ,EAAYrC,OAAO,CAAC;QAC5ByB,YAAY,CAAC,CAAC;MAChB;IAAC,EACD;IACFmB,eAAe,EAAE;MACfC,MAAM,EAAE,IAAI;MACZC,EAAE,EAAEjF;IACN;EACF,CAAC,CAAC;EACF,IAAIM,sBAAsB,KAAK,MAAM,IAAI,CAAC8D,cAAc,CAACc,cAAc,EAAE;IACvE7E,kBAAkB,GAAG0D,SAAS;EAChC;;EAEA;EACA;EACA;EACA,MAAMnE,SAAS,GAAGC,aAAa,KAAK/B,QAAQ,GAAGzB,aAAa,CAACwB,eAAe,CAACC,QAAQ,CAAC,CAAC,CAACyD,IAAI,GAAGwC,SAAS,CAAC;EACzG,MAAM,CAACoB,QAAQ,EAAAC,IAAA,CAIb,GAAGzI,OAAO,CAAC,MAAM,EAAE;MACnByC,GAAG;MACHkF,WAAW,EAAEjG,WAAW;MACxB6F,sBAAsB,EAAA9I,aAAA,CAAAA,aAAA,KACjB8I,sBAAsB,GACtBzD,KAAK,CACT;MACD4E,0BAA0B,EAAE,IAAI;MAChCN,eAAe,EAAE;QACf7G,KAAK,EAAE;UACLoH,QAAQ,EAAEpH,KAAK,CAACoH;QAClB,CAAC;QACDnF,SAAS,EAAE;UACTmF,QAAQ,EAAE1I,cAAc,CAAC,OAAOuD,SAAS,CAACmF,QAAQ,KAAK,UAAU,GAAGnF,SAAS,CAACmF,QAAQ,CAACtH,UAAU,CAAC,GAAGmC,SAAS,CAACmF,QAAQ,EAAE;YACvHC,SAAS,EAAE;UACb,CAAC;QACH,CAAC;QACD3F,SAAS;QACTI;MACF,CAAC;MACDhC,UAAU;MACV2B,SAAS,EAAEjE,IAAI,CAACuC,OAAO,CAACE,IAAI,EAAEwB,SAAS;IACzC,CAAC,CAAC;IA1Be;MACfzB,KAAK,EAAEsH,aAAa;MACpBrF,SAAS,EAAEsF;IAEb,CAAC,GAAAL,IAAA;IADIM,SAAS,GAAArK,wBAAA,CAAA+J,IAAA,EAAA7J,UAAA;EAwBd,MAAM,CAACoK,SAAS,EAAEC,UAAU,CAAC,GAAGjJ,OAAO,CAAC,OAAO,EAAE;IAC/CyC,GAAG,EAAEsB,QAAQ;IACbf,SAAS,EAAE1B,OAAO,CAACG,KAAK;IACxBkG,WAAW,EAAE9F,YAAY;IACzB0F,sBAAsB;IACtBmB,0BAA0B,EAAE,IAAI;IAChCN,eAAe,EAAE;MACfjF,SAAS;MACTuD,KAAK,EAAEL,YAAY,GAAGe,SAAS,GAAG;QAChC8B,OAAO,EAAE;MACX;IACF,CAAC;IACD7H;EACF,CAAC,CAAC;EACF,OAAO,aAAalB,IAAI,CAACqI,QAAQ,EAAA/J,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAC5BsK,SAAS,GACR,CAACzJ,eAAe,CAACkJ,QAAQ,CAAC,IAAI;IAChCjH,KAAK,EAAEsH,aAAa;IACpBrF,SAAS,EAAEsF,iBAAiB;IAC5BjF;EACF,CAAC;IACDd,QAAQ,EAAE,aAAa5C,IAAI,CAACsH,cAAc,EAAAhJ,aAAA,CAAAA,aAAA,KACrCiJ,mBAAmB;MACtByB,OAAO,EAAEzF,kBAAkB;MAC3BX,QAAQ,EAAE,aAAa5C,IAAI,CAAC6I,SAAS,EAAAvK,aAAA,CAAAA,aAAA,KAChCwK,UAAU;QACblG,QAAQ,EAAEA;MAAQ,EACnB;IAAC,EACH;EAAC,EACH,CAAC;AACJ,CAAC,CAAC;AACFoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG/B,OAAO,CAAC8G,SAAS,CAAC,yBAAyB;EACjF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEzG,MAAM,EAAEzD,OAAO;EACf;AACF;AACA;AACA;AACA;EACEiC,QAAQ,EAAE9B,cAAc,CAACP,SAAS,CAACuK,SAAS,CAAC,CAACpK,eAAe,EAAEH,SAAS,CAACwK,IAAI,CAAC,CAAC,EAAE5G,KAAK,IAAI;IACxF,IAAIA,KAAK,CAACW,IAAI,KAAK,CAACX,KAAK,CAACI,eAAe,IAAIJ,KAAK,CAACI,eAAe,KAAK,UAAU,CAAC,EAAE;MAClF,MAAM0B,gBAAgB,GAAGtD,eAAe,CAACwB,KAAK,CAACvB,QAAQ,CAAC;MACxD,IAAIqD,gBAAgB,IAAIA,gBAAgB,CAACE,QAAQ,KAAK,CAAC,EAAE;QACvD,MAAMK,GAAG,GAAGP,gBAAgB,CAACM,qBAAqB,CAAC,CAAC;QACpD,IAAIX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIU,GAAG,CAACC,GAAG,KAAK,CAAC,IAAID,GAAG,CAACE,IAAI,KAAK,CAAC,IAAIF,GAAG,CAACG,KAAK,KAAK,CAAC,IAAIH,GAAG,CAACI,MAAM,KAAK,CAAC,EAAE;UAC7G,OAAO,IAAIoE,KAAK,CAAC,CAAC,gEAAgE,EAAE,2DAA2D,EAAE,iFAAiF,CAAC,CAACtI,IAAI,CAAC,IAAI,CAAC,CAAC;QACjP;MACF,CAAC,MAAM;QACL,OAAO,IAAIsI,KAAK,CAAC,CAAC,gEAAgE,yEAAAvI,MAAA,CAA0EwD,gBAAgB,gBAAc,CAACvD,IAAI,CAAC,IAAI,CAAC,CAAC;MACxM;IACF;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE2B,YAAY,EAAE9D,SAAS,CAAC0K,KAAK,CAAC;IAC5B9I,UAAU,EAAE5B,SAAS,CAACuK,SAAS,CAAC,CAACvK,SAAS,CAAC2K,KAAK,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE3K,SAAS,CAAC4K,MAAM,CAAC,CAAC,CAACC,UAAU;IAC5GrJ,QAAQ,EAAExB,SAAS,CAACuK,SAAS,CAAC,CAACvK,SAAS,CAAC2K,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAE3K,SAAS,CAAC4K,MAAM,CAAC,CAAC,CAACC;EAClG,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE9G,cAAc,EAAE/D,SAAS,CAAC0K,KAAK,CAAC;IAC9BvE,IAAI,EAAEnG,SAAS,CAAC4K,MAAM,CAACC,UAAU;IACjC3E,GAAG,EAAElG,SAAS,CAAC4K,MAAM,CAACC;EACxB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACE7G,eAAe,EAAEhE,SAAS,CAAC2K,KAAK,CAAC,CAAC,UAAU,EAAE,gBAAgB,EAAE,MAAM,CAAC,CAAC;EACxE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,iBAAiB,EAAE9K,SAAS,CAAC6I,WAAW;EACxC;AACF;AACA;AACA;EACEkC,aAAa,EAAE/K,SAAS,CAACgL,MAAM;EAC/B;AACF;AACA;EACE/G,QAAQ,EAAEjE,SAAS,CAACiL,IAAI;EACxB;AACF;AACA;EACEzI,OAAO,EAAExC,SAAS,CAACgL,MAAM;EACzB;AACF;AACA;EACE9G,SAAS,EAAElE,SAAS,CAACkL,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;EACE/G,SAAS,EAAEnE,SAAS,CAAC,sCAAsCuK,SAAS,CAAC,CAACpK,eAAe,EAAEH,SAAS,CAACwK,IAAI,CAAC,CAAC;EACvG;AACF;AACA;AACA;EACEzF,iBAAiB,EAAE/E,SAAS,CAACmL,IAAI;EACjC;AACF;AACA;AACA;EACE9G,SAAS,EAAE/D,eAAe;EAC1B;AACF;AACA;AACA;AACA;EACEgE,eAAe,EAAEtE,SAAS,CAAC4K,MAAM;EACjC;AACF;AACA;AACA;EACEQ,OAAO,EAAEpL,SAAS,CAACwK,IAAI;EACvB;AACF;AACA;EACEjG,IAAI,EAAEvE,SAAS,CAACmL,IAAI,CAACN,UAAU;EAC/B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACErG,UAAU,EAAExE,SAAS,CAAC,sCAAsC0K,KAAK,CAAC;IAChEW,SAAS,EAAEhL;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEqE,SAAS,EAAE1E,SAAS,CAAC0K,KAAK,CAAC;IACzBb,QAAQ,EAAE7J,SAAS,CAACuK,SAAS,CAAC,CAACvK,SAAS,CAACwK,IAAI,EAAExK,SAAS,CAACgL,MAAM,CAAC,CAAC;IACjErI,KAAK,EAAE3C,SAAS,CAACuK,SAAS,CAAC,CAACvK,SAAS,CAACwK,IAAI,EAAExK,SAAS,CAACgL,MAAM,CAAC,CAAC;IAC9DtI,IAAI,EAAE1C,SAAS,CAACuK,SAAS,CAAC,CAACvK,SAAS,CAACwK,IAAI,EAAExK,SAAS,CAACgL,MAAM,CAAC,CAAC;IAC7DtC,UAAU,EAAE1I,SAAS,CAACuK,SAAS,CAAC,CAACvK,SAAS,CAACwK,IAAI,EAAExK,SAAS,CAACgL,MAAM,CAAC;EACpE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEvI,KAAK,EAAEzC,SAAS,CAAC0K,KAAK,CAAC;IACrBb,QAAQ,EAAE7J,SAAS,CAAC6I,WAAW;IAC/BlG,KAAK,EAAE3C,SAAS,CAAC6I,WAAW;IAC5BnG,IAAI,EAAE1C,SAAS,CAAC6I,WAAW;IAC3BH,UAAU,EAAE1I,SAAS,CAAC6I;EACxB,CAAC,CAAC;EACF;AACF;AACA;EACEyC,EAAE,EAAEtL,SAAS,CAACuK,SAAS,CAAC,CAACvK,SAAS,CAACuL,OAAO,CAACvL,SAAS,CAACuK,SAAS,CAAC,CAACvK,SAAS,CAACwK,IAAI,EAAExK,SAAS,CAACgL,MAAM,EAAEhL,SAAS,CAACmL,IAAI,CAAC,CAAC,CAAC,EAAEnL,SAAS,CAACwK,IAAI,EAAExK,SAAS,CAACgL,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEjJ,eAAe,EAAE/B,SAAS,CAAC0K,KAAK,CAAC;IAC/B9I,UAAU,EAAE5B,SAAS,CAACuK,SAAS,CAAC,CAACvK,SAAS,CAAC2K,KAAK,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE3K,SAAS,CAAC4K,MAAM,CAAC,CAAC,CAACC,UAAU;IAC5GrJ,QAAQ,EAAExB,SAAS,CAACuK,SAAS,CAAC,CAACvK,SAAS,CAAC2K,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAE3K,SAAS,CAAC4K,MAAM,CAAC,CAAC,CAACC;EAClG,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;EACElG,mBAAmB,EAAE3E,SAAS,CAAC6I,WAAW;EAC1C;AACF;AACA;AACA;EACEjE,kBAAkB,EAAE5E,SAAS,CAACuK,SAAS,CAAC,CAACvK,SAAS,CAAC2K,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE3K,SAAS,CAAC4K,MAAM,EAAE5K,SAAS,CAAC0K,KAAK,CAAC;IACpGnB,MAAM,EAAEvJ,SAAS,CAAC4K,MAAM;IACxBY,KAAK,EAAExL,SAAS,CAAC4K,MAAM;IACvBa,IAAI,EAAEzL,SAAS,CAAC4K;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;AACA;AACA;AACA;EACE9F,eAAe,EAAE9E,SAAS,CAACgL;AAC7B,CAAC,GAAG,KAAK,CAAC;AACV,eAAexH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}