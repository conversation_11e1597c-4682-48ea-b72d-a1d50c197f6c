{"ast": null, "code": "import _objectSpread from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"breakpoints\", \"mixins\", \"spacing\", \"palette\", \"transitions\", \"typography\", \"shape\"];\nimport _formatErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nimport deepmerge from '@mui/utils/deepmerge';\nimport styleFunctionSx, { unstable_defaultSxConfig as defaultSxConfig } from '@mui/system/styleFunctionSx';\nimport systemCreateTheme from '@mui/system/createTheme';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport createMixins from \"./createMixins.js\";\nimport createPalette from \"./createPalette.js\";\nimport createTypography from \"./createTypography.js\";\nimport shadows from \"./shadows.js\";\nimport createTransitions from \"./createTransitions.js\";\nimport zIndex from \"./zIndex.js\";\nimport { stringifyTheme } from \"./stringifyTheme.js\";\nfunction createThemeNoVars() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n      breakpoints: breakpointsInput,\n      mixins: mixinsInput = {},\n      spacing: spacingInput,\n      palette: paletteInput = {},\n      transitions: transitionsInput = {},\n      typography: typographyInput = {},\n      shape: shapeInput\n    } = options,\n    other = _objectWithoutProperties(options, _excluded);\n  if (options.vars &&\n  // The error should throw only for the root theme creation because user is not allowed to use a custom node `vars`.\n  // `generateThemeVars` is the closest identifier for checking that the `options` is a result of `createTheme` with CSS variables so that user can create new theme for nested ThemeProvider.\n  options.generateThemeVars === undefined) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: `vars` is a private field used for CSS variables support.\\n' +\n    // #host-reference\n    'Please use another name or follow the [docs](https://mui.com/material-ui/customization/css-theme-variables/usage/) to enable the feature.' : _formatErrorMessage(20));\n  }\n  const palette = createPalette(paletteInput);\n  const systemTheme = systemCreateTheme(options);\n  let muiTheme = deepmerge(systemTheme, {\n    mixins: createMixins(systemTheme.breakpoints, mixinsInput),\n    palette,\n    // Don't use [...shadows] until you've verified its transpiled code is not invoking the iterator protocol.\n    shadows: shadows.slice(),\n    typography: createTypography(palette, typographyInput),\n    transitions: createTransitions(transitionsInput),\n    zIndex: _objectSpread({}, zIndex)\n  });\n  muiTheme = deepmerge(muiTheme, other);\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO v6: Refactor to use globalStateClassesMapping from @mui/utils once `readOnly` state class is used in Rating component.\n    const stateClasses = ['active', 'checked', 'completed', 'disabled', 'error', 'expanded', 'focused', 'focusVisible', 'required', 'selected'];\n    const traverse = (node, component) => {\n      let key;\n\n      // eslint-disable-next-line guard-for-in\n      for (key in node) {\n        const child = node[key];\n        if (stateClasses.includes(key) && Object.keys(child).length > 0) {\n          if (process.env.NODE_ENV !== 'production') {\n            const stateClass = generateUtilityClass('', key);\n            console.error([\"MUI: The `\".concat(component, \"` component increases \") + \"the CSS specificity of the `\".concat(key, \"` internal state.\"), 'You can not override it like this: ', JSON.stringify(node, null, 2), '', \"Instead, you need to use the '&.\".concat(stateClass, \"' syntax:\"), JSON.stringify({\n              root: {\n                [\"&.\".concat(stateClass)]: child\n              }\n            }, null, 2), '', 'https://mui.com/r/state-classes-guide'].join('\\n'));\n          }\n          // Remove the style to prevent global conflicts.\n          node[key] = {};\n        }\n      }\n    };\n    Object.keys(muiTheme.components).forEach(component => {\n      const styleOverrides = muiTheme.components[component].styleOverrides;\n      if (styleOverrides && component.startsWith('Mui')) {\n        traverse(styleOverrides, component);\n      }\n    });\n  }\n  muiTheme.unstable_sxConfig = _objectSpread(_objectSpread({}, defaultSxConfig), other === null || other === void 0 ? void 0 : other.unstable_sxConfig);\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  muiTheme.toRuntimeSource = stringifyTheme; // for Pigment CSS integration\n\n  return muiTheme;\n}\nexport default createThemeNoVars;", "map": {"version": 3, "names": ["_formatErrorMessage", "deepmerge", "styleFunctionSx", "unstable_defaultSxConfig", "defaultSxConfig", "systemCreateTheme", "generateUtilityClass", "createMixins", "createPalette", "createTypography", "shadows", "createTransitions", "zIndex", "stringifyTheme", "createThemeNoVars", "options", "arguments", "length", "undefined", "breakpoints", "breakpointsInput", "mixins", "mixinsInput", "spacing", "spacingInput", "palette", "paletteInput", "transitions", "transitionsInput", "typography", "typographyInput", "shape", "shapeInput", "other", "_objectWithoutProperties", "_excluded", "vars", "generateThemeVars", "Error", "process", "env", "NODE_ENV", "systemTheme", "muiTheme", "slice", "_objectSpread", "_len", "args", "Array", "_key", "reduce", "acc", "argument", "stateClasses", "traverse", "node", "component", "key", "child", "includes", "Object", "keys", "stateClass", "console", "error", "concat", "JSON", "stringify", "root", "join", "components", "for<PERSON>ach", "styleOverrides", "startsWith", "unstable_sxConfig", "unstable_sx", "sx", "props", "theme", "toRuntimeSource"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/styles/createThemeNoVars.js"], "sourcesContent": ["import _formatErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nimport deepmerge from '@mui/utils/deepmerge';\nimport styleFunctionSx, { unstable_defaultSxConfig as defaultSxConfig } from '@mui/system/styleFunctionSx';\nimport systemCreateTheme from '@mui/system/createTheme';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport createMixins from \"./createMixins.js\";\nimport createPalette from \"./createPalette.js\";\nimport createTypography from \"./createTypography.js\";\nimport shadows from \"./shadows.js\";\nimport createTransitions from \"./createTransitions.js\";\nimport zIndex from \"./zIndex.js\";\nimport { stringifyTheme } from \"./stringifyTheme.js\";\nfunction createThemeNoVars(options = {}, ...args) {\n  const {\n    breakpoints: breakpointsInput,\n    mixins: mixinsInput = {},\n    spacing: spacingInput,\n    palette: paletteInput = {},\n    transitions: transitionsInput = {},\n    typography: typographyInput = {},\n    shape: shapeInput,\n    ...other\n  } = options;\n  if (options.vars &&\n  // The error should throw only for the root theme creation because user is not allowed to use a custom node `vars`.\n  // `generateThemeVars` is the closest identifier for checking that the `options` is a result of `createTheme` with CSS variables so that user can create new theme for nested ThemeProvider.\n  options.generateThemeVars === undefined) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: `vars` is a private field used for CSS variables support.\\n' +\n    // #host-reference\n    'Please use another name or follow the [docs](https://mui.com/material-ui/customization/css-theme-variables/usage/) to enable the feature.' : _formatErrorMessage(20));\n  }\n  const palette = createPalette(paletteInput);\n  const systemTheme = systemCreateTheme(options);\n  let muiTheme = deepmerge(systemTheme, {\n    mixins: createMixins(systemTheme.breakpoints, mixinsInput),\n    palette,\n    // Don't use [...shadows] until you've verified its transpiled code is not invoking the iterator protocol.\n    shadows: shadows.slice(),\n    typography: createTypography(palette, typographyInput),\n    transitions: createTransitions(transitionsInput),\n    zIndex: {\n      ...zIndex\n    }\n  });\n  muiTheme = deepmerge(muiTheme, other);\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO v6: Refactor to use globalStateClassesMapping from @mui/utils once `readOnly` state class is used in Rating component.\n    const stateClasses = ['active', 'checked', 'completed', 'disabled', 'error', 'expanded', 'focused', 'focusVisible', 'required', 'selected'];\n    const traverse = (node, component) => {\n      let key;\n\n      // eslint-disable-next-line guard-for-in\n      for (key in node) {\n        const child = node[key];\n        if (stateClasses.includes(key) && Object.keys(child).length > 0) {\n          if (process.env.NODE_ENV !== 'production') {\n            const stateClass = generateUtilityClass('', key);\n            console.error([`MUI: The \\`${component}\\` component increases ` + `the CSS specificity of the \\`${key}\\` internal state.`, 'You can not override it like this: ', JSON.stringify(node, null, 2), '', `Instead, you need to use the '&.${stateClass}' syntax:`, JSON.stringify({\n              root: {\n                [`&.${stateClass}`]: child\n              }\n            }, null, 2), '', 'https://mui.com/r/state-classes-guide'].join('\\n'));\n          }\n          // Remove the style to prevent global conflicts.\n          node[key] = {};\n        }\n      }\n    };\n    Object.keys(muiTheme.components).forEach(component => {\n      const styleOverrides = muiTheme.components[component].styleOverrides;\n      if (styleOverrides && component.startsWith('Mui')) {\n        traverse(styleOverrides, component);\n      }\n    });\n  }\n  muiTheme.unstable_sxConfig = {\n    ...defaultSxConfig,\n    ...other?.unstable_sxConfig\n  };\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  muiTheme.toRuntimeSource = stringifyTheme; // for Pigment CSS integration\n\n  return muiTheme;\n}\nexport default createThemeNoVars;"], "mappings": ";;;AAAA,OAAOA,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,eAAe,IAAIC,wBAAwB,IAAIC,eAAe,QAAQ,6BAA6B;AAC1G,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,MAAM,MAAM,aAAa;AAChC,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,iBAAiBA,CAAA,EAAwB;EAAA,IAAvBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACrC,MAAM;MACJG,WAAW,EAAEC,gBAAgB;MAC7BC,MAAM,EAAEC,WAAW,GAAG,CAAC,CAAC;MACxBC,OAAO,EAAEC,YAAY;MACrBC,OAAO,EAAEC,YAAY,GAAG,CAAC,CAAC;MAC1BC,WAAW,EAAEC,gBAAgB,GAAG,CAAC,CAAC;MAClCC,UAAU,EAAEC,eAAe,GAAG,CAAC,CAAC;MAChCC,KAAK,EAAEC;IAET,CAAC,GAAGjB,OAAO;IADNkB,KAAK,GAAAC,wBAAA,CACNnB,OAAO,EAAAoB,SAAA;EACX,IAAIpB,OAAO,CAACqB,IAAI;EAChB;EACA;EACArB,OAAO,CAACsB,iBAAiB,KAAKnB,SAAS,EAAE;IACvC,MAAM,IAAIoB,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,kEAAkE;IAC1H;IACA,2IAA2I,GAAGzC,mBAAmB,CAAC,EAAE,CAAC,CAAC;EACxK;EACA,MAAMyB,OAAO,GAAGjB,aAAa,CAACkB,YAAY,CAAC;EAC3C,MAAMgB,WAAW,GAAGrC,iBAAiB,CAACU,OAAO,CAAC;EAC9C,IAAI4B,QAAQ,GAAG1C,SAAS,CAACyC,WAAW,EAAE;IACpCrB,MAAM,EAAEd,YAAY,CAACmC,WAAW,CAACvB,WAAW,EAAEG,WAAW,CAAC;IAC1DG,OAAO;IACP;IACAf,OAAO,EAAEA,OAAO,CAACkC,KAAK,CAAC,CAAC;IACxBf,UAAU,EAAEpB,gBAAgB,CAACgB,OAAO,EAAEK,eAAe,CAAC;IACtDH,WAAW,EAAEhB,iBAAiB,CAACiB,gBAAgB,CAAC;IAChDhB,MAAM,EAAAiC,aAAA,KACDjC,MAAM;EAEb,CAAC,CAAC;EACF+B,QAAQ,GAAG1C,SAAS,CAAC0C,QAAQ,EAAEV,KAAK,CAAC;EAAC,SAAAa,IAAA,GAAA9B,SAAA,CAAAC,MAAA,EAhCI8B,IAAI,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;IAAJF,IAAI,CAAAE,IAAA,QAAAjC,SAAA,CAAAiC,IAAA;EAAA;EAiC9CN,QAAQ,GAAGI,IAAI,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAKnD,SAAS,CAACkD,GAAG,EAAEC,QAAQ,CAAC,EAAET,QAAQ,CAAC;EAC7E,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA,MAAMY,YAAY,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,CAAC;IAC3I,MAAMC,QAAQ,GAAGA,CAACC,IAAI,EAAEC,SAAS,KAAK;MACpC,IAAIC,GAAG;;MAEP;MACA,KAAKA,GAAG,IAAIF,IAAI,EAAE;QAChB,MAAMG,KAAK,GAAGH,IAAI,CAACE,GAAG,CAAC;QACvB,IAAIJ,YAAY,CAACM,QAAQ,CAACF,GAAG,CAAC,IAAIG,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAACzC,MAAM,GAAG,CAAC,EAAE;UAC/D,IAAIsB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzC,MAAMqB,UAAU,GAAGxD,oBAAoB,CAAC,EAAE,EAAEmD,GAAG,CAAC;YAChDM,OAAO,CAACC,KAAK,CAAC,CAAC,aAAAC,MAAA,CAAcT,SAAS,6DAAAS,MAAA,CAA4DR,GAAG,sBAAoB,EAAE,qCAAqC,EAAES,IAAI,CAACC,SAAS,CAACZ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,qCAAAU,MAAA,CAAqCH,UAAU,gBAAaI,IAAI,CAACC,SAAS,CAAC;cAC5QC,IAAI,EAAE;gBACJ,MAAAH,MAAA,CAAMH,UAAU,IAAKJ;cACvB;YACF,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,uCAAuC,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC,CAAC;UACvE;UACA;UACAd,IAAI,CAACE,GAAG,CAAC,GAAG,CAAC,CAAC;QAChB;MACF;IACF,CAAC;IACDG,MAAM,CAACC,IAAI,CAAClB,QAAQ,CAAC2B,UAAU,CAAC,CAACC,OAAO,CAACf,SAAS,IAAI;MACpD,MAAMgB,cAAc,GAAG7B,QAAQ,CAAC2B,UAAU,CAACd,SAAS,CAAC,CAACgB,cAAc;MACpE,IAAIA,cAAc,IAAIhB,SAAS,CAACiB,UAAU,CAAC,KAAK,CAAC,EAAE;QACjDnB,QAAQ,CAACkB,cAAc,EAAEhB,SAAS,CAAC;MACrC;IACF,CAAC,CAAC;EACJ;EACAb,QAAQ,CAAC+B,iBAAiB,GAAA7B,aAAA,CAAAA,aAAA,KACrBzC,eAAe,GACf6B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyC,iBAAiB,CAC5B;EACD/B,QAAQ,CAACgC,WAAW,GAAG,SAASC,EAAEA,CAACC,KAAK,EAAE;IACxC,OAAO3E,eAAe,CAAC;MACrB0E,EAAE,EAAEC,KAAK;MACTC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EACDnC,QAAQ,CAACoC,eAAe,GAAGlE,cAAc,CAAC,CAAC;;EAE3C,OAAO8B,QAAQ;AACjB;AACA,eAAe7B,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}