{"ast": null, "code": "import _objectSpread from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _toPropertyKey from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/toPropertyKey.js\";\nimport _objectWithoutProperties from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"colorSchemes\", \"components\", \"defaultColorScheme\"];\nimport deepmerge from '@mui/utils/deepmerge';\nimport cssVarsParser from \"./cssVarsParser.js\";\nfunction prepareCssVars(theme) {\n  let parserConfig = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    getSelector = defaultGetSelector,\n    disableCssColorScheme,\n    colorSchemeSelector: selector\n  } = parserConfig;\n  // @ts-ignore - ignore components do not exist\n  const {\n      colorSchemes = {},\n      components,\n      defaultColorScheme = 'light'\n    } = theme,\n    otherTheme = _objectWithoutProperties(theme, _excluded);\n  const {\n    vars: rootVars,\n    css: rootCss,\n    varsWithDefaults: rootVarsWithDefaults\n  } = cssVarsParser(otherTheme, parserConfig);\n  let themeVars = rootVarsWithDefaults;\n  const colorSchemesMap = {};\n  const {\n      [defaultColorScheme]: defaultScheme\n    } = colorSchemes,\n    otherColorSchemes = _objectWithoutProperties(colorSchemes, [defaultColorScheme].map(_toPropertyKey));\n  Object.entries(otherColorSchemes || {}).forEach(_ref => {\n    let [key, scheme] = _ref;\n    const {\n      vars,\n      css,\n      varsWithDefaults\n    } = cssVarsParser(scheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[key] = {\n      css,\n      vars\n    };\n  });\n  if (defaultScheme) {\n    // default color scheme vars should be merged last to set as default\n    const {\n      css,\n      vars,\n      varsWithDefaults\n    } = cssVarsParser(defaultScheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[defaultColorScheme] = {\n      css,\n      vars\n    };\n  }\n  function defaultGetSelector(colorScheme, cssObject) {\n    let rule = selector;\n    if (selector === 'class') {\n      rule = '.%s';\n    }\n    if (selector === 'data') {\n      rule = '[data-%s]';\n    }\n    if (selector !== null && selector !== void 0 && selector.startsWith('data-') && !selector.includes('%s')) {\n      // 'data-joy-color-scheme' -> '[data-joy-color-scheme=\"%s\"]'\n      rule = \"[\".concat(selector, \"=\\\"%s\\\"]\");\n    }\n    if (colorScheme) {\n      if (rule === 'media') {\n        var _colorSchemes$colorSc;\n        if (theme.defaultColorScheme === colorScheme) {\n          return ':root';\n        }\n        const mode = ((_colorSchemes$colorSc = colorSchemes[colorScheme]) === null || _colorSchemes$colorSc === void 0 || (_colorSchemes$colorSc = _colorSchemes$colorSc.palette) === null || _colorSchemes$colorSc === void 0 ? void 0 : _colorSchemes$colorSc.mode) || colorScheme;\n        return {\n          [\"@media (prefers-color-scheme: \".concat(mode, \")\")]: {\n            ':root': cssObject\n          }\n        };\n      }\n      if (rule) {\n        if (theme.defaultColorScheme === colorScheme) {\n          return \":root, \".concat(rule.replace('%s', String(colorScheme)));\n        }\n        return rule.replace('%s', String(colorScheme));\n      }\n    }\n    return ':root';\n  }\n  const generateThemeVars = () => {\n    let vars = _objectSpread({}, rootVars);\n    Object.entries(colorSchemesMap).forEach(_ref2 => {\n      let [, {\n        vars: schemeVars\n      }] = _ref2;\n      vars = deepmerge(vars, schemeVars);\n    });\n    return vars;\n  };\n  const generateStyleSheets = () => {\n    const stylesheets = [];\n    const colorScheme = theme.defaultColorScheme || 'light';\n    function insertStyleSheet(key, css) {\n      if (Object.keys(css).length) {\n        stylesheets.push(typeof key === 'string' ? {\n          [key]: _objectSpread({}, css)\n        } : key);\n      }\n    }\n    insertStyleSheet(getSelector(undefined, _objectSpread({}, rootCss)), rootCss);\n    const {\n        [colorScheme]: defaultSchemeVal\n      } = colorSchemesMap,\n      other = _objectWithoutProperties(colorSchemesMap, [colorScheme].map(_toPropertyKey));\n    if (defaultSchemeVal) {\n      var _colorSchemes$colorSc2;\n      // default color scheme has to come before other color schemes\n      const {\n        css\n      } = defaultSchemeVal;\n      const cssColorSheme = (_colorSchemes$colorSc2 = colorSchemes[colorScheme]) === null || _colorSchemes$colorSc2 === void 0 || (_colorSchemes$colorSc2 = _colorSchemes$colorSc2.palette) === null || _colorSchemes$colorSc2 === void 0 ? void 0 : _colorSchemes$colorSc2.mode;\n      const finalCss = !disableCssColorScheme && cssColorSheme ? _objectSpread({\n        colorScheme: cssColorSheme\n      }, css) : _objectSpread({}, css);\n      insertStyleSheet(getSelector(colorScheme, _objectSpread({}, finalCss)), finalCss);\n    }\n    Object.entries(other).forEach(_ref3 => {\n      var _colorSchemes$key;\n      let [key, {\n        css\n      }] = _ref3;\n      const cssColorSheme = (_colorSchemes$key = colorSchemes[key]) === null || _colorSchemes$key === void 0 || (_colorSchemes$key = _colorSchemes$key.palette) === null || _colorSchemes$key === void 0 ? void 0 : _colorSchemes$key.mode;\n      const finalCss = !disableCssColorScheme && cssColorSheme ? _objectSpread({\n        colorScheme: cssColorSheme\n      }, css) : _objectSpread({}, css);\n      insertStyleSheet(getSelector(key, _objectSpread({}, finalCss)), finalCss);\n    });\n    return stylesheets;\n  };\n  return {\n    vars: themeVars,\n    generateThemeVars,\n    generateStyleSheets\n  };\n}\nexport default prepareCssVars;", "map": {"version": 3, "names": ["deepmerge", "cssVarsParser", "prepareCssVars", "theme", "parserConfig", "arguments", "length", "undefined", "getSelector", "defaultGetSelector", "disableCssColorScheme", "colorSchemeSelector", "selector", "colorSchemes", "components", "defaultColorScheme", "otherTheme", "_objectWithoutProperties", "_excluded", "vars", "rootVars", "css", "rootCss", "varsWithDefaults", "rootVarsWithDefaults", "themeVars", "colorSchemesMap", "defaultScheme", "otherColorSchemes", "map", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "entries", "for<PERSON>ach", "_ref", "key", "scheme", "colorScheme", "cssObject", "rule", "startsWith", "includes", "concat", "_colorSchemes$colorSc", "mode", "palette", "replace", "String", "generateThemeVars", "_objectSpread", "_ref2", "schemeVars", "generateStyleSheets", "stylesheets", "insertStyleSheet", "keys", "push", "defaultSchemeVal", "other", "_colorSchemes$colorSc2", "cssColorSheme", "finalCss", "_ref3", "_colorSchemes$key"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/system/esm/cssVars/prepareCssVars.js"], "sourcesContent": ["import deepmerge from '@mui/utils/deepmerge';\nimport cssVarsParser from \"./cssVarsParser.js\";\nfunction prepareCssVars(theme, parserConfig = {}) {\n  const {\n    getSelector = defaultGetSelector,\n    disableCssColorScheme,\n    colorSchemeSelector: selector\n  } = parserConfig;\n  // @ts-ignore - ignore components do not exist\n  const {\n    colorSchemes = {},\n    components,\n    defaultColorScheme = 'light',\n    ...otherTheme\n  } = theme;\n  const {\n    vars: rootVars,\n    css: rootCss,\n    varsWithDefaults: rootVarsWithDefaults\n  } = cssVarsParser(otherTheme, parserConfig);\n  let themeVars = rootVarsWithDefaults;\n  const colorSchemesMap = {};\n  const {\n    [defaultColorScheme]: defaultScheme,\n    ...otherColorSchemes\n  } = colorSchemes;\n  Object.entries(otherColorSchemes || {}).forEach(([key, scheme]) => {\n    const {\n      vars,\n      css,\n      varsWithDefaults\n    } = cssVarsParser(scheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[key] = {\n      css,\n      vars\n    };\n  });\n  if (defaultScheme) {\n    // default color scheme vars should be merged last to set as default\n    const {\n      css,\n      vars,\n      varsWithDefaults\n    } = cssVarsParser(defaultScheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[defaultColorScheme] = {\n      css,\n      vars\n    };\n  }\n  function defaultGetSelector(colorScheme, cssObject) {\n    let rule = selector;\n    if (selector === 'class') {\n      rule = '.%s';\n    }\n    if (selector === 'data') {\n      rule = '[data-%s]';\n    }\n    if (selector?.startsWith('data-') && !selector.includes('%s')) {\n      // 'data-joy-color-scheme' -> '[data-joy-color-scheme=\"%s\"]'\n      rule = `[${selector}=\"%s\"]`;\n    }\n    if (colorScheme) {\n      if (rule === 'media') {\n        if (theme.defaultColorScheme === colorScheme) {\n          return ':root';\n        }\n        const mode = colorSchemes[colorScheme]?.palette?.mode || colorScheme;\n        return {\n          [`@media (prefers-color-scheme: ${mode})`]: {\n            ':root': cssObject\n          }\n        };\n      }\n      if (rule) {\n        if (theme.defaultColorScheme === colorScheme) {\n          return `:root, ${rule.replace('%s', String(colorScheme))}`;\n        }\n        return rule.replace('%s', String(colorScheme));\n      }\n    }\n    return ':root';\n  }\n  const generateThemeVars = () => {\n    let vars = {\n      ...rootVars\n    };\n    Object.entries(colorSchemesMap).forEach(([, {\n      vars: schemeVars\n    }]) => {\n      vars = deepmerge(vars, schemeVars);\n    });\n    return vars;\n  };\n  const generateStyleSheets = () => {\n    const stylesheets = [];\n    const colorScheme = theme.defaultColorScheme || 'light';\n    function insertStyleSheet(key, css) {\n      if (Object.keys(css).length) {\n        stylesheets.push(typeof key === 'string' ? {\n          [key]: {\n            ...css\n          }\n        } : key);\n      }\n    }\n    insertStyleSheet(getSelector(undefined, {\n      ...rootCss\n    }), rootCss);\n    const {\n      [colorScheme]: defaultSchemeVal,\n      ...other\n    } = colorSchemesMap;\n    if (defaultSchemeVal) {\n      // default color scheme has to come before other color schemes\n      const {\n        css\n      } = defaultSchemeVal;\n      const cssColorSheme = colorSchemes[colorScheme]?.palette?.mode;\n      const finalCss = !disableCssColorScheme && cssColorSheme ? {\n        colorScheme: cssColorSheme,\n        ...css\n      } : {\n        ...css\n      };\n      insertStyleSheet(getSelector(colorScheme, {\n        ...finalCss\n      }), finalCss);\n    }\n    Object.entries(other).forEach(([key, {\n      css\n    }]) => {\n      const cssColorSheme = colorSchemes[key]?.palette?.mode;\n      const finalCss = !disableCssColorScheme && cssColorSheme ? {\n        colorScheme: cssColorSheme,\n        ...css\n      } : {\n        ...css\n      };\n      insertStyleSheet(getSelector(key, {\n        ...finalCss\n      }), finalCss);\n    });\n    return stylesheets;\n  };\n  return {\n    vars: themeVars,\n    generateThemeVars,\n    generateStyleSheets\n  };\n}\nexport default prepareCssVars;"], "mappings": ";;;;AAAA,OAAOA,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,SAASC,cAAcA,CAACC,KAAK,EAAqB;EAAA,IAAnBC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC9C,MAAM;IACJG,WAAW,GAAGC,kBAAkB;IAChCC,qBAAqB;IACrBC,mBAAmB,EAAEC;EACvB,CAAC,GAAGR,YAAY;EAChB;EACA,MAAM;MACJS,YAAY,GAAG,CAAC,CAAC;MACjBC,UAAU;MACVC,kBAAkB,GAAG;IAEvB,CAAC,GAAGZ,KAAK;IADJa,UAAU,GAAAC,wBAAA,CACXd,KAAK,EAAAe,SAAA;EACT,MAAM;IACJC,IAAI,EAAEC,QAAQ;IACdC,GAAG,EAAEC,OAAO;IACZC,gBAAgB,EAAEC;EACpB,CAAC,GAAGvB,aAAa,CAACe,UAAU,EAAEZ,YAAY,CAAC;EAC3C,IAAIqB,SAAS,GAAGD,oBAAoB;EACpC,MAAME,eAAe,GAAG,CAAC,CAAC;EAC1B,MAAM;MACJ,CAACX,kBAAkB,GAAGY;IAExB,CAAC,GAAGd,YAAY;IADXe,iBAAiB,GAAAX,wBAAA,CAClBJ,YAAY,GAFbE,kBAAkB,EAAAc,GAAA,CAAAC,cAAA;EAGrBC,MAAM,CAACC,OAAO,CAACJ,iBAAiB,IAAI,CAAC,CAAC,CAAC,CAACK,OAAO,CAACC,IAAA,IAAmB;IAAA,IAAlB,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAAF,IAAA;IAC5D,MAAM;MACJf,IAAI;MACJE,GAAG;MACHE;IACF,CAAC,GAAGtB,aAAa,CAACmC,MAAM,EAAEhC,YAAY,CAAC;IACvCqB,SAAS,GAAGzB,SAAS,CAACyB,SAAS,EAAEF,gBAAgB,CAAC;IAClDG,eAAe,CAACS,GAAG,CAAC,GAAG;MACrBd,GAAG;MACHF;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAIQ,aAAa,EAAE;IACjB;IACA,MAAM;MACJN,GAAG;MACHF,IAAI;MACJI;IACF,CAAC,GAAGtB,aAAa,CAAC0B,aAAa,EAAEvB,YAAY,CAAC;IAC9CqB,SAAS,GAAGzB,SAAS,CAACyB,SAAS,EAAEF,gBAAgB,CAAC;IAClDG,eAAe,CAACX,kBAAkB,CAAC,GAAG;MACpCM,GAAG;MACHF;IACF,CAAC;EACH;EACA,SAASV,kBAAkBA,CAAC4B,WAAW,EAAEC,SAAS,EAAE;IAClD,IAAIC,IAAI,GAAG3B,QAAQ;IACnB,IAAIA,QAAQ,KAAK,OAAO,EAAE;MACxB2B,IAAI,GAAG,KAAK;IACd;IACA,IAAI3B,QAAQ,KAAK,MAAM,EAAE;MACvB2B,IAAI,GAAG,WAAW;IACpB;IACA,IAAI3B,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAE4B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC5B,QAAQ,CAAC6B,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC7D;MACAF,IAAI,OAAAG,MAAA,CAAO9B,QAAQ,aAAQ;IAC7B;IACA,IAAIyB,WAAW,EAAE;MACf,IAAIE,IAAI,KAAK,OAAO,EAAE;QAAA,IAAAI,qBAAA;QACpB,IAAIxC,KAAK,CAACY,kBAAkB,KAAKsB,WAAW,EAAE;UAC5C,OAAO,OAAO;QAChB;QACA,MAAMO,IAAI,GAAG,EAAAD,qBAAA,GAAA9B,YAAY,CAACwB,WAAW,CAAC,cAAAM,qBAAA,gBAAAA,qBAAA,GAAzBA,qBAAA,CAA2BE,OAAO,cAAAF,qBAAA,uBAAlCA,qBAAA,CAAoCC,IAAI,KAAIP,WAAW;QACpE,OAAO;UACL,kCAAAK,MAAA,CAAkCE,IAAI,SAAM;YAC1C,OAAO,EAAEN;UACX;QACF,CAAC;MACH;MACA,IAAIC,IAAI,EAAE;QACR,IAAIpC,KAAK,CAACY,kBAAkB,KAAKsB,WAAW,EAAE;UAC5C,iBAAAK,MAAA,CAAiBH,IAAI,CAACO,OAAO,CAAC,IAAI,EAAEC,MAAM,CAACV,WAAW,CAAC,CAAC;QAC1D;QACA,OAAOE,IAAI,CAACO,OAAO,CAAC,IAAI,EAAEC,MAAM,CAACV,WAAW,CAAC,CAAC;MAChD;IACF;IACA,OAAO,OAAO;EAChB;EACA,MAAMW,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI7B,IAAI,GAAA8B,aAAA,KACH7B,QAAQ,CACZ;IACDW,MAAM,CAACC,OAAO,CAACN,eAAe,CAAC,CAACO,OAAO,CAACiB,KAAA,IAEjC;MAAA,IAFkC,GAAG;QAC1C/B,IAAI,EAAEgC;MACR,CAAC,CAAC,GAAAD,KAAA;MACA/B,IAAI,GAAGnB,SAAS,CAACmB,IAAI,EAAEgC,UAAU,CAAC;IACpC,CAAC,CAAC;IACF,OAAOhC,IAAI;EACb,CAAC;EACD,MAAMiC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,WAAW,GAAG,EAAE;IACtB,MAAMhB,WAAW,GAAGlC,KAAK,CAACY,kBAAkB,IAAI,OAAO;IACvD,SAASuC,gBAAgBA,CAACnB,GAAG,EAAEd,GAAG,EAAE;MAClC,IAAIU,MAAM,CAACwB,IAAI,CAAClC,GAAG,CAAC,CAACf,MAAM,EAAE;QAC3B+C,WAAW,CAACG,IAAI,CAAC,OAAOrB,GAAG,KAAK,QAAQ,GAAG;UACzC,CAACA,GAAG,GAAAc,aAAA,KACC5B,GAAG;QAEV,CAAC,GAAGc,GAAG,CAAC;MACV;IACF;IACAmB,gBAAgB,CAAC9C,WAAW,CAACD,SAAS,EAAA0C,aAAA,KACjC3B,OAAO,CACX,CAAC,EAAEA,OAAO,CAAC;IACZ,MAAM;QACJ,CAACe,WAAW,GAAGoB;MAEjB,CAAC,GAAG/B,eAAe;MADdgC,KAAK,GAAAzC,wBAAA,CACNS,eAAe,GAFhBW,WAAW,EAAAR,GAAA,CAAAC,cAAA;IAGd,IAAI2B,gBAAgB,EAAE;MAAA,IAAAE,sBAAA;MACpB;MACA,MAAM;QACJtC;MACF,CAAC,GAAGoC,gBAAgB;MACpB,MAAMG,aAAa,IAAAD,sBAAA,GAAG9C,YAAY,CAACwB,WAAW,CAAC,cAAAsB,sBAAA,gBAAAA,sBAAA,GAAzBA,sBAAA,CAA2Bd,OAAO,cAAAc,sBAAA,uBAAlCA,sBAAA,CAAoCf,IAAI;MAC9D,MAAMiB,QAAQ,GAAG,CAACnD,qBAAqB,IAAIkD,aAAa,GAAAX,aAAA;QACtDZ,WAAW,EAAEuB;MAAa,GACvBvC,GAAG,IAAA4B,aAAA,KAEH5B,GAAG,CACP;MACDiC,gBAAgB,CAAC9C,WAAW,CAAC6B,WAAW,EAAAY,aAAA,KACnCY,QAAQ,CACZ,CAAC,EAAEA,QAAQ,CAAC;IACf;IACA9B,MAAM,CAACC,OAAO,CAAC0B,KAAK,CAAC,CAACzB,OAAO,CAAC6B,KAAA,IAEvB;MAAA,IAAAC,iBAAA;MAAA,IAFwB,CAAC5B,GAAG,EAAE;QACnCd;MACF,CAAC,CAAC,GAAAyC,KAAA;MACA,MAAMF,aAAa,IAAAG,iBAAA,GAAGlD,YAAY,CAACsB,GAAG,CAAC,cAAA4B,iBAAA,gBAAAA,iBAAA,GAAjBA,iBAAA,CAAmBlB,OAAO,cAAAkB,iBAAA,uBAA1BA,iBAAA,CAA4BnB,IAAI;MACtD,MAAMiB,QAAQ,GAAG,CAACnD,qBAAqB,IAAIkD,aAAa,GAAAX,aAAA;QACtDZ,WAAW,EAAEuB;MAAa,GACvBvC,GAAG,IAAA4B,aAAA,KAEH5B,GAAG,CACP;MACDiC,gBAAgB,CAAC9C,WAAW,CAAC2B,GAAG,EAAAc,aAAA,KAC3BY,QAAQ,CACZ,CAAC,EAAEA,QAAQ,CAAC;IACf,CAAC,CAAC;IACF,OAAOR,WAAW;EACpB,CAAC;EACD,OAAO;IACLlC,IAAI,EAAEM,SAAS;IACfuB,iBAAiB;IACjBI;EACF,CAAC;AACH;AACA,eAAelD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}