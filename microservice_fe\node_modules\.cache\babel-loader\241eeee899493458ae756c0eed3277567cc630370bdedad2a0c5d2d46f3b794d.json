{"ast": null, "code": "import React from'react';import{Box,Paper,Typography,Grid,Divider}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StatisticsSummary=_ref=>{let{data,periodType,periodLabel}=_ref;// Calculate total revenue\nconst totalRevenue=data.reduce((sum,item)=>sum+item.totalRevenue,0);// Calculate total invoices\nconst totalInvoices=data.reduce((sum,item)=>sum+item.invoiceCount,0);// Format currency\nconst formatCurrency=value=>{return new Intl.NumberFormat('vi-VN',{style:'currency',currency:'VND',maximumFractionDigits:0}).format(value);};// Get period type label\nconst getPeriodTypeLabel=()=>{switch(periodType){case'daily':return'ngày';case'monthly':return'tháng';case'quarterly':return'quý';case'yearly':return'năm';default:return'khoảng thời gian';}};// Get summary title\nconst getSummaryTitle=()=>{if(periodLabel){return\"T\\u1ED5ng h\\u1EE3p doanh thu \".concat(getPeriodTypeLabel(),\" \").concat(periodLabel);}return\"T\\u1ED5ng h\\u1EE3p doanh thu theo \".concat(getPeriodTypeLabel());};return/*#__PURE__*/_jsxs(Paper,{elevation:2,sx:{p:2,mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:getSummaryTitle()}),/*#__PURE__*/_jsx(Divider,{sx:{mb:2}}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6},children:/*#__PURE__*/_jsxs(Box,{sx:{p:2,borderRadius:1,bgcolor:'primary.light',width:'100%'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"primary.contrastText\",children:\"T\\u1ED5ng doanh thu\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",color:\"primary.contrastText\",sx:{fontWeight:'bold'},children:formatCurrency(totalRevenue)})]})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6},children:/*#__PURE__*/_jsxs(Box,{sx:{p:2,borderRadius:1,bgcolor:'secondary.light',width:'100%'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"secondary.contrastText\",children:\"T\\u1ED5ng s\\u1ED1 h\\xF3a \\u0111\\u01A1n\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",color:\"secondary.contrastText\",sx:{fontWeight:'bold'},children:totalInvoices})]})})]})]});};export default StatisticsSummary;", "map": {"version": 3, "names": ["React", "Box", "Paper", "Typography", "Grid", "Divider", "jsx", "_jsx", "jsxs", "_jsxs", "StatisticsSummary", "_ref", "data", "periodType", "period<PERSON><PERSON><PERSON>", "totalRevenue", "reduce", "sum", "item", "totalInvoices", "invoiceCount", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "maximumFractionDigits", "format", "getPeriodTypeLabel", "getSummaryTitle", "concat", "elevation", "sx", "p", "mb", "children", "variant", "gutterBottom", "container", "spacing", "size", "xs", "sm", "borderRadius", "bgcolor", "width", "color", "fontWeight"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/statistics/StatisticsSummary.tsx"], "sourcesContent": ["import React from 'react';\nimport { Box, Paper, Typography, Grid, Divider } from '@mui/material';\nimport { TimeBasedRevenue } from '../../models';\n\ninterface StatisticsSummaryProps {\n  data: TimeBasedRevenue[];\n  periodType: string;\n  periodLabel?: string;\n}\n\nconst StatisticsSummary: React.FC<StatisticsSummaryProps> = ({\n  data,\n  periodType,\n  periodLabel\n}) => {\n  // Calculate total revenue\n  const totalRevenue = data.reduce((sum, item) => sum + item.totalRevenue, 0);\n\n  // Calculate total invoices\n  const totalInvoices = data.reduce((sum, item) => sum + item.invoiceCount, 0);\n\n  // Format currency\n  const formatCurrency = (value: number): string => {\n    return new Intl.NumberFormat('vi-VN', {\n      style: 'currency',\n      currency: 'VND',\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n\n  // Get period type label\n  const getPeriodTypeLabel = (): string => {\n    switch (periodType) {\n      case 'daily':\n        return 'ngày';\n      case 'monthly':\n        return 'tháng';\n      case 'quarterly':\n        return 'quý';\n      case 'yearly':\n        return 'năm';\n      default:\n        return 'khoảng thời gian';\n    }\n  };\n\n  // Get summary title\n  const getSummaryTitle = (): string => {\n    if (periodLabel) {\n      return `Tổng hợp doanh thu ${getPeriodTypeLabel()} ${periodLabel}`;\n    }\n    return `Tổng hợp doanh thu theo ${getPeriodTypeLabel()}`;\n  };\n\n  return (\n    <Paper elevation={2} sx={{ p: 2, mb: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        {getSummaryTitle()}\n      </Typography>\n      <Divider sx={{ mb: 2 }} />\n\n      <Grid container spacing={3}>\n        <Grid size={{ xs: 12, sm: 6 }}>\n          <Box sx={{ p: 2, borderRadius: 1, bgcolor: 'primary.light', width: '100%' }}>\n            <Typography variant=\"subtitle2\" color=\"primary.contrastText\">\n              Tổng doanh thu\n            </Typography>\n            <Typography variant=\"h4\" color=\"primary.contrastText\" sx={{ fontWeight: 'bold' }}>\n              {formatCurrency(totalRevenue)}\n            </Typography>\n          </Box>\n        </Grid>\n\n        <Grid size={{ xs: 12, sm: 6 }}>\n          <Box sx={{ p: 2, borderRadius: 1, bgcolor: 'secondary.light', width: '100%' }}>\n            <Typography variant=\"subtitle2\" color=\"secondary.contrastText\">\n              Tổng số hóa đơn\n            </Typography>\n            <Typography variant=\"h4\" color=\"secondary.contrastText\" sx={{ fontWeight: 'bold' }}>\n              {totalInvoices}\n            </Typography>\n          </Box>\n        </Grid>\n      </Grid>\n    </Paper>\n  );\n};\n\nexport default StatisticsSummary;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,GAAG,CAAEC,KAAK,CAAEC,UAAU,CAAEC,IAAI,CAAEC,OAAO,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAStE,KAAM,CAAAC,iBAAmD,CAAGC,IAAA,EAItD,IAJuD,CAC3DC,IAAI,CACJC,UAAU,CACVC,WACF,CAAC,CAAAH,IAAA,CACC;AACA,KAAM,CAAAI,YAAY,CAAGH,IAAI,CAACI,MAAM,CAAC,CAACC,GAAG,CAAEC,IAAI,GAAKD,GAAG,CAAGC,IAAI,CAACH,YAAY,CAAE,CAAC,CAAC,CAE3E;AACA,KAAM,CAAAI,aAAa,CAAGP,IAAI,CAACI,MAAM,CAAC,CAACC,GAAG,CAAEC,IAAI,GAAKD,GAAG,CAAGC,IAAI,CAACE,YAAY,CAAE,CAAC,CAAC,CAE5E;AACA,KAAM,CAAAC,cAAc,CAAIC,KAAa,EAAa,CAChD,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,KAAK,CACfC,qBAAqB,CAAE,CACzB,CAAC,CAAC,CAACC,MAAM,CAACN,KAAK,CAAC,CAClB,CAAC,CAED;AACA,KAAM,CAAAO,kBAAkB,CAAGA,CAAA,GAAc,CACvC,OAAQhB,UAAU,EAChB,IAAK,OAAO,CACV,MAAO,MAAM,CACf,IAAK,SAAS,CACZ,MAAO,OAAO,CAChB,IAAK,WAAW,CACd,MAAO,KAAK,CACd,IAAK,QAAQ,CACX,MAAO,KAAK,CACd,QACE,MAAO,kBAAkB,CAC7B,CACF,CAAC,CAED;AACA,KAAM,CAAAiB,eAAe,CAAGA,CAAA,GAAc,CACpC,GAAIhB,WAAW,CAAE,CACf,sCAAAiB,MAAA,CAA6BF,kBAAkB,CAAC,CAAC,MAAAE,MAAA,CAAIjB,WAAW,EAClE,CACA,2CAAAiB,MAAA,CAAkCF,kBAAkB,CAAC,CAAC,EACxD,CAAC,CAED,mBACEpB,KAAA,CAACP,KAAK,EAAC8B,SAAS,CAAE,CAAE,CAACC,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eACvC7B,IAAA,CAACJ,UAAU,EAACkC,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAClCN,eAAe,CAAC,CAAC,CACR,CAAC,cACbvB,IAAA,CAACF,OAAO,EAAC4B,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAE1B1B,KAAA,CAACL,IAAI,EAACmC,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAJ,QAAA,eACzB7B,IAAA,CAACH,IAAI,EAACqC,IAAI,CAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAP,QAAA,cAC5B3B,KAAA,CAACR,GAAG,EAACgC,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEU,YAAY,CAAE,CAAC,CAAEC,OAAO,CAAE,eAAe,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAV,QAAA,eAC1E7B,IAAA,CAACJ,UAAU,EAACkC,OAAO,CAAC,WAAW,CAACU,KAAK,CAAC,sBAAsB,CAAAX,QAAA,CAAC,qBAE7D,CAAY,CAAC,cACb7B,IAAA,CAACJ,UAAU,EAACkC,OAAO,CAAC,IAAI,CAACU,KAAK,CAAC,sBAAsB,CAACd,EAAE,CAAE,CAAEe,UAAU,CAAE,MAAO,CAAE,CAAAZ,QAAA,CAC9Ef,cAAc,CAACN,YAAY,CAAC,CACnB,CAAC,EACV,CAAC,CACF,CAAC,cAEPR,IAAA,CAACH,IAAI,EAACqC,IAAI,CAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAP,QAAA,cAC5B3B,KAAA,CAACR,GAAG,EAACgC,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEU,YAAY,CAAE,CAAC,CAAEC,OAAO,CAAE,iBAAiB,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAV,QAAA,eAC5E7B,IAAA,CAACJ,UAAU,EAACkC,OAAO,CAAC,WAAW,CAACU,KAAK,CAAC,wBAAwB,CAAAX,QAAA,CAAC,wCAE/D,CAAY,CAAC,cACb7B,IAAA,CAACJ,UAAU,EAACkC,OAAO,CAAC,IAAI,CAACU,KAAK,CAAC,wBAAwB,CAACd,EAAE,CAAE,CAAEe,UAAU,CAAE,MAAO,CAAE,CAAAZ,QAAA,CAChFjB,aAAa,CACJ,CAAC,EACV,CAAC,CACF,CAAC,EACH,CAAC,EACF,CAAC,CAEZ,CAAC,CAED,cAAe,CAAAT,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}