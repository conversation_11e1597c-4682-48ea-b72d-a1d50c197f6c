{"ast": null, "code": "export { default } from \"./flexbox.js\";\nexport * from \"./flexbox.js\";", "map": {"version": 3, "names": ["default"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/system/esm/flexbox/index.js"], "sourcesContent": ["export { default } from \"./flexbox.js\";\nexport * from \"./flexbox.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,cAAc,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}