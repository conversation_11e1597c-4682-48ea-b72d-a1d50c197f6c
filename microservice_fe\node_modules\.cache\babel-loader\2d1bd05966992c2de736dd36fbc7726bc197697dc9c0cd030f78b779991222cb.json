{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useControlled from '@mui/utils/useControlled';\nimport useTimeout from '@mui/utils/useTimeout';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { usePickerTranslations } from \"../../../hooks/usePickerTranslations.js\";\nimport { useUtils, useLocalizationContext } from \"../useUtils.js\";\nimport { mergeDateIntoReferenceDate, getSectionsBoundaries, validateSections, getDateFromDateSections, parseSelectedSections, getLocalizedDigits, getSectionOrder } from \"./useField.utils.js\";\nimport { buildSectionsFromFormat } from \"./buildSectionsFromFormat.js\";\nimport { useValidation } from \"../../../validation/index.js\";\nimport { useControlledValue } from \"../useControlledValue.js\";\nimport { getSectionTypeGranularity } from \"../../utils/getDefaultReferenceDate.js\";\nconst QUERY_LIFE_DURATION_MS = 5000;\nexport const useFieldState = parameters => {\n  var _state$sections$state;\n  const utils = useUtils();\n  const translations = usePickerTranslations();\n  const adapter = useLocalizationContext();\n  const isRtl = useRtl();\n  const {\n    manager: {\n      validator,\n      valueType,\n      internal_valueManager: valueManager,\n      internal_fieldValueManager: fieldValueManager\n    },\n    internalPropsWithDefaults,\n    internalPropsWithDefaults: {\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      onChange,\n      format,\n      formatDensity = 'dense',\n      selectedSections: selectedSectionsProp,\n      onSelectedSectionsChange,\n      shouldRespectLeadingZeros = false,\n      timezone: timezoneProp,\n      enableAccessibleFieldDOMStructure = true\n    },\n    forwardedProps: {\n      error: errorProp\n    }\n  } = parameters;\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = useControlledValue({\n    name: 'a field component',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager\n  });\n  const valueRef = React.useRef(value);\n  React.useEffect(() => {\n    valueRef.current = value;\n  }, [value]);\n  const {\n    hasValidationError\n  } = useValidation({\n    props: internalPropsWithDefaults,\n    validator,\n    timezone,\n    value,\n    onError: internalPropsWithDefaults.onError\n  });\n  const error = React.useMemo(() => {\n    // only override when `error` is undefined.\n    // in case of multi input fields, the `error` value is provided externally and will always be defined.\n    if (errorProp !== undefined) {\n      return errorProp;\n    }\n    return hasValidationError;\n  }, [hasValidationError, errorProp]);\n  const localizedDigits = React.useMemo(() => getLocalizedDigits(utils), [utils]);\n  const sectionsValueBoundaries = React.useMemo(() => getSectionsBoundaries(utils, localizedDigits, timezone), [utils, localizedDigits, timezone]);\n  const getSectionsFromValue = React.useCallback(valueToAnalyze => fieldValueManager.getSectionsFromValue(valueToAnalyze, date => buildSectionsFromFormat({\n    utils,\n    localeText: translations,\n    localizedDigits,\n    format,\n    date,\n    formatDensity,\n    shouldRespectLeadingZeros,\n    enableAccessibleFieldDOMStructure,\n    isRtl\n  })), [fieldValueManager, format, translations, localizedDigits, isRtl, shouldRespectLeadingZeros, utils, formatDensity, enableAccessibleFieldDOMStructure]);\n  const [state, setState] = React.useState(() => {\n    const sections = getSectionsFromValue(value);\n    validateSections(sections, valueType);\n    const stateWithoutReferenceDate = {\n      sections,\n      lastExternalValue: value,\n      lastSectionsDependencies: {\n        format,\n        isRtl,\n        locale: utils.locale\n      },\n      tempValueStrAndroid: null,\n      characterQuery: null\n    };\n    const granularity = getSectionTypeGranularity(sections);\n    const referenceValue = valueManager.getInitialReferenceValue({\n      referenceDate: referenceDateProp,\n      value,\n      utils,\n      props: internalPropsWithDefaults,\n      granularity,\n      timezone\n    });\n    return _extends({}, stateWithoutReferenceDate, {\n      referenceValue\n    });\n  });\n  const [selectedSections, innerSetSelectedSections] = useControlled({\n    controlled: selectedSectionsProp,\n    default: null,\n    name: 'useField',\n    state: 'selectedSections'\n  });\n  const setSelectedSections = newSelectedSections => {\n    innerSetSelectedSections(newSelectedSections);\n    onSelectedSectionsChange === null || onSelectedSectionsChange === void 0 || onSelectedSectionsChange(newSelectedSections);\n  };\n  const parsedSelectedSections = React.useMemo(() => parseSelectedSections(selectedSections, state.sections), [selectedSections, state.sections]);\n  const activeSectionIndex = parsedSelectedSections === 'all' ? 0 : parsedSelectedSections;\n  const sectionOrder = React.useMemo(() => getSectionOrder(state.sections, isRtl && !enableAccessibleFieldDOMStructure), [state.sections, isRtl, enableAccessibleFieldDOMStructure]);\n  const areAllSectionsEmpty = React.useMemo(() => state.sections.every(section => section.value === ''), [state.sections]);\n  const publishValue = newValue => {\n    const context = {\n      validationError: validator({\n        adapter,\n        value: newValue,\n        timezone,\n        props: internalPropsWithDefaults\n      })\n    };\n    handleValueChange(newValue, context);\n  };\n  const setSectionValue = (sectionIndex, newSectionValue) => {\n    const newSections = [...state.sections];\n    newSections[sectionIndex] = _extends({}, newSections[sectionIndex], {\n      value: newSectionValue,\n      modified: true\n    });\n    return newSections;\n  };\n  const sectionToUpdateOnNextInvalidDateRef = React.useRef(null);\n  const updateSectionValueOnNextInvalidDateTimeout = useTimeout();\n  const setSectionUpdateToApplyOnNextInvalidDate = newSectionValue => {\n    if (activeSectionIndex == null) {\n      return;\n    }\n    sectionToUpdateOnNextInvalidDateRef.current = {\n      sectionIndex: activeSectionIndex,\n      value: newSectionValue\n    };\n    updateSectionValueOnNextInvalidDateTimeout.start(0, () => {\n      sectionToUpdateOnNextInvalidDateRef.current = null;\n    });\n  };\n  const clearValue = () => {\n    if (valueManager.areValuesEqual(utils, value, valueManager.emptyValue)) {\n      setState(prevState => _extends({}, prevState, {\n        sections: prevState.sections.map(section => _extends({}, section, {\n          value: ''\n        })),\n        tempValueStrAndroid: null,\n        characterQuery: null\n      }));\n    } else {\n      setState(prevState => _extends({}, prevState, {\n        characterQuery: null\n      }));\n      publishValue(valueManager.emptyValue);\n    }\n  };\n  const clearActiveSection = () => {\n    if (activeSectionIndex == null) {\n      return;\n    }\n    const activeSection = state.sections[activeSectionIndex];\n    if (activeSection.value === '') {\n      return;\n    }\n    setSectionUpdateToApplyOnNextInvalidDate('');\n    if (fieldValueManager.getDateFromSection(value, activeSection) === null) {\n      setState(prevState => _extends({}, prevState, {\n        sections: setSectionValue(activeSectionIndex, ''),\n        tempValueStrAndroid: null,\n        characterQuery: null\n      }));\n    } else {\n      setState(prevState => _extends({}, prevState, {\n        characterQuery: null\n      }));\n      publishValue(fieldValueManager.updateDateInValue(value, activeSection, null));\n    }\n  };\n  const updateValueFromValueStr = valueStr => {\n    const parseDateStr = (dateStr, referenceDate) => {\n      const date = utils.parse(dateStr, format);\n      if (!utils.isValid(date)) {\n        return null;\n      }\n      const sections = buildSectionsFromFormat({\n        utils,\n        localeText: translations,\n        localizedDigits,\n        format,\n        date,\n        formatDensity,\n        shouldRespectLeadingZeros,\n        enableAccessibleFieldDOMStructure,\n        isRtl\n      });\n      return mergeDateIntoReferenceDate(utils, date, sections, referenceDate, false);\n    };\n    const newValue = fieldValueManager.parseValueStr(valueStr, state.referenceValue, parseDateStr);\n    publishValue(newValue);\n  };\n  const cleanActiveDateSectionsIfValueNullTimeout = useTimeout();\n  const updateSectionValue = _ref => {\n    let {\n      section,\n      newSectionValue,\n      shouldGoToNextSection\n    } = _ref;\n    updateSectionValueOnNextInvalidDateTimeout.clear();\n    cleanActiveDateSectionsIfValueNullTimeout.clear();\n    const activeDate = fieldValueManager.getDateFromSection(value, section);\n\n    /**\n     * Decide which section should be focused\n     */\n    if (shouldGoToNextSection && activeSectionIndex < state.sections.length - 1) {\n      setSelectedSections(activeSectionIndex + 1);\n    }\n\n    /**\n     * Try to build a valid date from the new section value\n     */\n    const newSections = setSectionValue(activeSectionIndex, newSectionValue);\n    const newActiveDateSections = fieldValueManager.getDateSectionsFromValue(newSections, section);\n    const newActiveDate = getDateFromDateSections(utils, newActiveDateSections, localizedDigits);\n\n    /**\n     * If the new date is valid,\n     * Then we merge the value of the modified sections into the reference date.\n     * This makes sure that we don't lose some information of the initial date (like the time on a date field).\n     */\n    if (utils.isValid(newActiveDate)) {\n      const mergedDate = mergeDateIntoReferenceDate(utils, newActiveDate, newActiveDateSections, fieldValueManager.getDateFromSection(state.referenceValue, section), true);\n      if (activeDate == null) {\n        cleanActiveDateSectionsIfValueNullTimeout.start(0, () => {\n          if (valueRef.current === value) {\n            setState(prevState => _extends({}, prevState, {\n              sections: fieldValueManager.clearDateSections(state.sections, section),\n              tempValueStrAndroid: null\n            }));\n          }\n        });\n      }\n      return publishValue(fieldValueManager.updateDateInValue(value, section, mergedDate));\n    }\n\n    /**\n     * If all the sections are filled but the date is invalid and the previous date is valid or null,\n     * Then we publish an invalid date.\n     */\n    if (newActiveDateSections.every(sectionBis => sectionBis.value !== '') && (activeDate == null || utils.isValid(activeDate))) {\n      setSectionUpdateToApplyOnNextInvalidDate(newSectionValue);\n      return publishValue(fieldValueManager.updateDateInValue(value, section, newActiveDate));\n    }\n\n    /**\n     * If the previous date is not null,\n     * Then we publish the date as `null`.\n     */\n    if (activeDate != null) {\n      setSectionUpdateToApplyOnNextInvalidDate(newSectionValue);\n      return publishValue(fieldValueManager.updateDateInValue(value, section, null));\n    }\n\n    /**\n     * If the previous date is already null,\n     * Then we don't publish the date and we update the sections.\n     */\n    return setState(prevState => _extends({}, prevState, {\n      sections: newSections,\n      tempValueStrAndroid: null\n    }));\n  };\n  const setTempAndroidValueStr = tempValueStrAndroid => setState(prevState => _extends({}, prevState, {\n    tempValueStrAndroid\n  }));\n  const setCharacterQuery = useEventCallback(newCharacterQuery => {\n    setState(prevState => _extends({}, prevState, {\n      characterQuery: newCharacterQuery\n    }));\n  });\n\n  // If `prop.value` changes, we update the state to reflect the new value\n  if (value !== state.lastExternalValue) {\n    let sections;\n    if (sectionToUpdateOnNextInvalidDateRef.current != null && !utils.isValid(fieldValueManager.getDateFromSection(value, state.sections[sectionToUpdateOnNextInvalidDateRef.current.sectionIndex]))) {\n      sections = setSectionValue(sectionToUpdateOnNextInvalidDateRef.current.sectionIndex, sectionToUpdateOnNextInvalidDateRef.current.value);\n    } else {\n      sections = getSectionsFromValue(value);\n    }\n    setState(prevState => _extends({}, prevState, {\n      lastExternalValue: value,\n      sections,\n      sectionsDependencies: {\n        format,\n        isRtl,\n        locale: utils.locale\n      },\n      referenceValue: fieldValueManager.updateReferenceValue(utils, value, prevState.referenceValue),\n      tempValueStrAndroid: null\n    }));\n  }\n  if (isRtl !== state.lastSectionsDependencies.isRtl || format !== state.lastSectionsDependencies.format || utils.locale !== state.lastSectionsDependencies.locale) {\n    const sections = getSectionsFromValue(value);\n    validateSections(sections, valueType);\n    setState(prevState => _extends({}, prevState, {\n      lastSectionsDependencies: {\n        format,\n        isRtl,\n        locale: utils.locale\n      },\n      sections,\n      tempValueStrAndroid: null,\n      characterQuery: null\n    }));\n  }\n  if (state.characterQuery != null && !error && activeSectionIndex == null) {\n    setCharacterQuery(null);\n  }\n  if (state.characterQuery != null && ((_state$sections$state = state.sections[state.characterQuery.sectionIndex]) === null || _state$sections$state === void 0 ? void 0 : _state$sections$state.type) !== state.characterQuery.sectionType) {\n    setCharacterQuery(null);\n  }\n  React.useEffect(() => {\n    if (sectionToUpdateOnNextInvalidDateRef.current != null) {\n      sectionToUpdateOnNextInvalidDateRef.current = null;\n    }\n  });\n  const cleanCharacterQueryTimeout = useTimeout();\n  React.useEffect(() => {\n    if (state.characterQuery != null) {\n      cleanCharacterQueryTimeout.start(QUERY_LIFE_DURATION_MS, () => setCharacterQuery(null));\n    }\n    return () => {};\n  }, [state.characterQuery, setCharacterQuery, cleanCharacterQueryTimeout]);\n\n  // If `tempValueStrAndroid` is still defined for some section when running `useEffect`,\n  // Then `onChange` has only been called once, which means the user pressed `Backspace` to reset the section.\n  // This causes a small flickering on Android,\n  // But we can't use `useEnhancedEffect` which is always called before the second `onChange` call and then would cause false positives.\n  React.useEffect(() => {\n    if (state.tempValueStrAndroid != null && activeSectionIndex != null) {\n      clearActiveSection();\n    }\n  }, [state.sections]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  return {\n    // States and derived states\n    activeSectionIndex,\n    areAllSectionsEmpty,\n    error,\n    localizedDigits,\n    parsedSelectedSections,\n    sectionOrder,\n    sectionsValueBoundaries,\n    state,\n    timezone,\n    value,\n    // Methods to update the states\n    clearValue,\n    clearActiveSection,\n    setCharacterQuery,\n    setSelectedSections,\n    setTempAndroidValueStr,\n    updateSectionValue,\n    updateValueFromValueStr,\n    // Utilities methods\n    getSectionsFromValue\n  };\n};", "map": {"version": 3, "names": ["_extends", "React", "useControlled", "useTimeout", "useEventCallback", "useRtl", "usePickerTranslations", "useUtils", "useLocalizationContext", "mergeDateIntoReferenceDate", "getSectionsBoundaries", "validateSections", "getDateFromDateSections", "parseSelectedSections", "getLocalizedDigits", "getSectionOrder", "buildSectionsFromFormat", "useValidation", "useControlledValue", "getSectionTypeGranularity", "QUERY_LIFE_DURATION_MS", "useFieldState", "parameters", "_state$sections$state", "utils", "translations", "adapter", "isRtl", "manager", "validator", "valueType", "internal_valueManager", "valueManager", "internal_fieldValueManager", "field<PERSON><PERSON>ueManager", "internalPropsWithDefaults", "value", "valueProp", "defaultValue", "referenceDate", "referenceDateProp", "onChange", "format", "formatDensity", "selectedSections", "selectedSectionsProp", "onSelectedSectionsChange", "shouldRespectLeadingZeros", "timezone", "timezoneProp", "enableAccessibleFieldDOMStructure", "forwardedProps", "error", "errorProp", "handleValueChange", "name", "valueRef", "useRef", "useEffect", "current", "hasValidationError", "props", "onError", "useMemo", "undefined", "localizedDigits", "sectionsValueBoundaries", "getSectionsFromValue", "useCallback", "valueToAnalyze", "date", "localeText", "state", "setState", "useState", "sections", "stateWithoutReferenceDate", "lastExternalValue", "lastSectionsDependencies", "locale", "tempValueStrAndroid", "<PERSON><PERSON><PERSON><PERSON>", "granularity", "referenceValue", "getInitialReferenceValue", "innerSetSelectedSections", "controlled", "default", "setSelectedSections", "newSelectedSections", "parsedSelectedSections", "activeSectionIndex", "sectionOrder", "areAllSectionsEmpty", "every", "section", "publishValue", "newValue", "context", "validationError", "setSectionValue", "sectionIndex", "newSectionValue", "newSections", "modified", "sectionToUpdateOnNextInvalidDateRef", "updateSectionValueOnNextInvalidDateTimeout", "setSectionUpdateToApplyOnNextInvalidDate", "start", "clearValue", "areValuesEqual", "emptyValue", "prevState", "map", "clearActiveSection", "activeSection", "getDateFromSection", "updateDateInValue", "updateValueFromValueStr", "valueStr", "parseDateStr", "dateStr", "parse", "<PERSON><PERSON><PERSON><PERSON>", "parseValueStr", "cleanActiveDateSectionsIfValueNullTimeout", "updateSectionValue", "_ref", "shouldGoToNextSection", "clear", "activeDate", "length", "newActiveDateSections", "getDateSectionsFromValue", "newActiveDate", "mergedDate", "clearDateSections", "sectionBis", "setTempAndroidValueStr", "setCharacterQuery", "newCharacterQuery", "sectionsDependencies", "updateReferenceValue", "type", "sectionType", "cleanCharacterQueryTimeout"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-date-pickers/esm/internals/hooks/useField/useFieldState.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useControlled from '@mui/utils/useControlled';\nimport useTimeout from '@mui/utils/useTimeout';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { usePickerTranslations } from \"../../../hooks/usePickerTranslations.js\";\nimport { useUtils, useLocalizationContext } from \"../useUtils.js\";\nimport { mergeDateIntoReferenceDate, getSectionsBoundaries, validateSections, getDateFromDateSections, parseSelectedSections, getLocalizedDigits, getSectionOrder } from \"./useField.utils.js\";\nimport { buildSectionsFromFormat } from \"./buildSectionsFromFormat.js\";\nimport { useValidation } from \"../../../validation/index.js\";\nimport { useControlledValue } from \"../useControlledValue.js\";\nimport { getSectionTypeGranularity } from \"../../utils/getDefaultReferenceDate.js\";\nconst QUERY_LIFE_DURATION_MS = 5000;\nexport const useFieldState = parameters => {\n  const utils = useUtils();\n  const translations = usePickerTranslations();\n  const adapter = useLocalizationContext();\n  const isRtl = useRtl();\n  const {\n    manager: {\n      validator,\n      valueType,\n      internal_valueManager: valueManager,\n      internal_fieldValueManager: fieldValueManager\n    },\n    internalPropsWithDefaults,\n    internalPropsWithDefaults: {\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      onChange,\n      format,\n      formatDensity = 'dense',\n      selectedSections: selectedSectionsProp,\n      onSelectedSectionsChange,\n      shouldRespectLeadingZeros = false,\n      timezone: timezoneProp,\n      enableAccessibleFieldDOMStructure = true\n    },\n    forwardedProps: {\n      error: errorProp\n    }\n  } = parameters;\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = useControlledValue({\n    name: 'a field component',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager\n  });\n  const valueRef = React.useRef(value);\n  React.useEffect(() => {\n    valueRef.current = value;\n  }, [value]);\n  const {\n    hasValidationError\n  } = useValidation({\n    props: internalPropsWithDefaults,\n    validator,\n    timezone,\n    value,\n    onError: internalPropsWithDefaults.onError\n  });\n  const error = React.useMemo(() => {\n    // only override when `error` is undefined.\n    // in case of multi input fields, the `error` value is provided externally and will always be defined.\n    if (errorProp !== undefined) {\n      return errorProp;\n    }\n    return hasValidationError;\n  }, [hasValidationError, errorProp]);\n  const localizedDigits = React.useMemo(() => getLocalizedDigits(utils), [utils]);\n  const sectionsValueBoundaries = React.useMemo(() => getSectionsBoundaries(utils, localizedDigits, timezone), [utils, localizedDigits, timezone]);\n  const getSectionsFromValue = React.useCallback(valueToAnalyze => fieldValueManager.getSectionsFromValue(valueToAnalyze, date => buildSectionsFromFormat({\n    utils,\n    localeText: translations,\n    localizedDigits,\n    format,\n    date,\n    formatDensity,\n    shouldRespectLeadingZeros,\n    enableAccessibleFieldDOMStructure,\n    isRtl\n  })), [fieldValueManager, format, translations, localizedDigits, isRtl, shouldRespectLeadingZeros, utils, formatDensity, enableAccessibleFieldDOMStructure]);\n  const [state, setState] = React.useState(() => {\n    const sections = getSectionsFromValue(value);\n    validateSections(sections, valueType);\n    const stateWithoutReferenceDate = {\n      sections,\n      lastExternalValue: value,\n      lastSectionsDependencies: {\n        format,\n        isRtl,\n        locale: utils.locale\n      },\n      tempValueStrAndroid: null,\n      characterQuery: null\n    };\n    const granularity = getSectionTypeGranularity(sections);\n    const referenceValue = valueManager.getInitialReferenceValue({\n      referenceDate: referenceDateProp,\n      value,\n      utils,\n      props: internalPropsWithDefaults,\n      granularity,\n      timezone\n    });\n    return _extends({}, stateWithoutReferenceDate, {\n      referenceValue\n    });\n  });\n  const [selectedSections, innerSetSelectedSections] = useControlled({\n    controlled: selectedSectionsProp,\n    default: null,\n    name: 'useField',\n    state: 'selectedSections'\n  });\n  const setSelectedSections = newSelectedSections => {\n    innerSetSelectedSections(newSelectedSections);\n    onSelectedSectionsChange?.(newSelectedSections);\n  };\n  const parsedSelectedSections = React.useMemo(() => parseSelectedSections(selectedSections, state.sections), [selectedSections, state.sections]);\n  const activeSectionIndex = parsedSelectedSections === 'all' ? 0 : parsedSelectedSections;\n  const sectionOrder = React.useMemo(() => getSectionOrder(state.sections, isRtl && !enableAccessibleFieldDOMStructure), [state.sections, isRtl, enableAccessibleFieldDOMStructure]);\n  const areAllSectionsEmpty = React.useMemo(() => state.sections.every(section => section.value === ''), [state.sections]);\n  const publishValue = newValue => {\n    const context = {\n      validationError: validator({\n        adapter,\n        value: newValue,\n        timezone,\n        props: internalPropsWithDefaults\n      })\n    };\n    handleValueChange(newValue, context);\n  };\n  const setSectionValue = (sectionIndex, newSectionValue) => {\n    const newSections = [...state.sections];\n    newSections[sectionIndex] = _extends({}, newSections[sectionIndex], {\n      value: newSectionValue,\n      modified: true\n    });\n    return newSections;\n  };\n  const sectionToUpdateOnNextInvalidDateRef = React.useRef(null);\n  const updateSectionValueOnNextInvalidDateTimeout = useTimeout();\n  const setSectionUpdateToApplyOnNextInvalidDate = newSectionValue => {\n    if (activeSectionIndex == null) {\n      return;\n    }\n    sectionToUpdateOnNextInvalidDateRef.current = {\n      sectionIndex: activeSectionIndex,\n      value: newSectionValue\n    };\n    updateSectionValueOnNextInvalidDateTimeout.start(0, () => {\n      sectionToUpdateOnNextInvalidDateRef.current = null;\n    });\n  };\n  const clearValue = () => {\n    if (valueManager.areValuesEqual(utils, value, valueManager.emptyValue)) {\n      setState(prevState => _extends({}, prevState, {\n        sections: prevState.sections.map(section => _extends({}, section, {\n          value: ''\n        })),\n        tempValueStrAndroid: null,\n        characterQuery: null\n      }));\n    } else {\n      setState(prevState => _extends({}, prevState, {\n        characterQuery: null\n      }));\n      publishValue(valueManager.emptyValue);\n    }\n  };\n  const clearActiveSection = () => {\n    if (activeSectionIndex == null) {\n      return;\n    }\n    const activeSection = state.sections[activeSectionIndex];\n    if (activeSection.value === '') {\n      return;\n    }\n    setSectionUpdateToApplyOnNextInvalidDate('');\n    if (fieldValueManager.getDateFromSection(value, activeSection) === null) {\n      setState(prevState => _extends({}, prevState, {\n        sections: setSectionValue(activeSectionIndex, ''),\n        tempValueStrAndroid: null,\n        characterQuery: null\n      }));\n    } else {\n      setState(prevState => _extends({}, prevState, {\n        characterQuery: null\n      }));\n      publishValue(fieldValueManager.updateDateInValue(value, activeSection, null));\n    }\n  };\n  const updateValueFromValueStr = valueStr => {\n    const parseDateStr = (dateStr, referenceDate) => {\n      const date = utils.parse(dateStr, format);\n      if (!utils.isValid(date)) {\n        return null;\n      }\n      const sections = buildSectionsFromFormat({\n        utils,\n        localeText: translations,\n        localizedDigits,\n        format,\n        date,\n        formatDensity,\n        shouldRespectLeadingZeros,\n        enableAccessibleFieldDOMStructure,\n        isRtl\n      });\n      return mergeDateIntoReferenceDate(utils, date, sections, referenceDate, false);\n    };\n    const newValue = fieldValueManager.parseValueStr(valueStr, state.referenceValue, parseDateStr);\n    publishValue(newValue);\n  };\n  const cleanActiveDateSectionsIfValueNullTimeout = useTimeout();\n  const updateSectionValue = ({\n    section,\n    newSectionValue,\n    shouldGoToNextSection\n  }) => {\n    updateSectionValueOnNextInvalidDateTimeout.clear();\n    cleanActiveDateSectionsIfValueNullTimeout.clear();\n    const activeDate = fieldValueManager.getDateFromSection(value, section);\n\n    /**\n     * Decide which section should be focused\n     */\n    if (shouldGoToNextSection && activeSectionIndex < state.sections.length - 1) {\n      setSelectedSections(activeSectionIndex + 1);\n    }\n\n    /**\n     * Try to build a valid date from the new section value\n     */\n    const newSections = setSectionValue(activeSectionIndex, newSectionValue);\n    const newActiveDateSections = fieldValueManager.getDateSectionsFromValue(newSections, section);\n    const newActiveDate = getDateFromDateSections(utils, newActiveDateSections, localizedDigits);\n\n    /**\n     * If the new date is valid,\n     * Then we merge the value of the modified sections into the reference date.\n     * This makes sure that we don't lose some information of the initial date (like the time on a date field).\n     */\n    if (utils.isValid(newActiveDate)) {\n      const mergedDate = mergeDateIntoReferenceDate(utils, newActiveDate, newActiveDateSections, fieldValueManager.getDateFromSection(state.referenceValue, section), true);\n      if (activeDate == null) {\n        cleanActiveDateSectionsIfValueNullTimeout.start(0, () => {\n          if (valueRef.current === value) {\n            setState(prevState => _extends({}, prevState, {\n              sections: fieldValueManager.clearDateSections(state.sections, section),\n              tempValueStrAndroid: null\n            }));\n          }\n        });\n      }\n      return publishValue(fieldValueManager.updateDateInValue(value, section, mergedDate));\n    }\n\n    /**\n     * If all the sections are filled but the date is invalid and the previous date is valid or null,\n     * Then we publish an invalid date.\n     */\n    if (newActiveDateSections.every(sectionBis => sectionBis.value !== '') && (activeDate == null || utils.isValid(activeDate))) {\n      setSectionUpdateToApplyOnNextInvalidDate(newSectionValue);\n      return publishValue(fieldValueManager.updateDateInValue(value, section, newActiveDate));\n    }\n\n    /**\n     * If the previous date is not null,\n     * Then we publish the date as `null`.\n     */\n    if (activeDate != null) {\n      setSectionUpdateToApplyOnNextInvalidDate(newSectionValue);\n      return publishValue(fieldValueManager.updateDateInValue(value, section, null));\n    }\n\n    /**\n     * If the previous date is already null,\n     * Then we don't publish the date and we update the sections.\n     */\n    return setState(prevState => _extends({}, prevState, {\n      sections: newSections,\n      tempValueStrAndroid: null\n    }));\n  };\n  const setTempAndroidValueStr = tempValueStrAndroid => setState(prevState => _extends({}, prevState, {\n    tempValueStrAndroid\n  }));\n  const setCharacterQuery = useEventCallback(newCharacterQuery => {\n    setState(prevState => _extends({}, prevState, {\n      characterQuery: newCharacterQuery\n    }));\n  });\n\n  // If `prop.value` changes, we update the state to reflect the new value\n  if (value !== state.lastExternalValue) {\n    let sections;\n    if (sectionToUpdateOnNextInvalidDateRef.current != null && !utils.isValid(fieldValueManager.getDateFromSection(value, state.sections[sectionToUpdateOnNextInvalidDateRef.current.sectionIndex]))) {\n      sections = setSectionValue(sectionToUpdateOnNextInvalidDateRef.current.sectionIndex, sectionToUpdateOnNextInvalidDateRef.current.value);\n    } else {\n      sections = getSectionsFromValue(value);\n    }\n    setState(prevState => _extends({}, prevState, {\n      lastExternalValue: value,\n      sections,\n      sectionsDependencies: {\n        format,\n        isRtl,\n        locale: utils.locale\n      },\n      referenceValue: fieldValueManager.updateReferenceValue(utils, value, prevState.referenceValue),\n      tempValueStrAndroid: null\n    }));\n  }\n  if (isRtl !== state.lastSectionsDependencies.isRtl || format !== state.lastSectionsDependencies.format || utils.locale !== state.lastSectionsDependencies.locale) {\n    const sections = getSectionsFromValue(value);\n    validateSections(sections, valueType);\n    setState(prevState => _extends({}, prevState, {\n      lastSectionsDependencies: {\n        format,\n        isRtl,\n        locale: utils.locale\n      },\n      sections,\n      tempValueStrAndroid: null,\n      characterQuery: null\n    }));\n  }\n  if (state.characterQuery != null && !error && activeSectionIndex == null) {\n    setCharacterQuery(null);\n  }\n  if (state.characterQuery != null && state.sections[state.characterQuery.sectionIndex]?.type !== state.characterQuery.sectionType) {\n    setCharacterQuery(null);\n  }\n  React.useEffect(() => {\n    if (sectionToUpdateOnNextInvalidDateRef.current != null) {\n      sectionToUpdateOnNextInvalidDateRef.current = null;\n    }\n  });\n  const cleanCharacterQueryTimeout = useTimeout();\n  React.useEffect(() => {\n    if (state.characterQuery != null) {\n      cleanCharacterQueryTimeout.start(QUERY_LIFE_DURATION_MS, () => setCharacterQuery(null));\n    }\n    return () => {};\n  }, [state.characterQuery, setCharacterQuery, cleanCharacterQueryTimeout]);\n\n  // If `tempValueStrAndroid` is still defined for some section when running `useEffect`,\n  // Then `onChange` has only been called once, which means the user pressed `Backspace` to reset the section.\n  // This causes a small flickering on Android,\n  // But we can't use `useEnhancedEffect` which is always called before the second `onChange` call and then would cause false positives.\n  React.useEffect(() => {\n    if (state.tempValueStrAndroid != null && activeSectionIndex != null) {\n      clearActiveSection();\n    }\n  }, [state.sections]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  return {\n    // States and derived states\n    activeSectionIndex,\n    areAllSectionsEmpty,\n    error,\n    localizedDigits,\n    parsedSelectedSections,\n    sectionOrder,\n    sectionsValueBoundaries,\n    state,\n    timezone,\n    value,\n    // Methods to update the states\n    clearValue,\n    clearActiveSection,\n    setCharacterQuery,\n    setSelectedSections,\n    setTempAndroidValueStr,\n    updateSectionValue,\n    updateValueFromValueStr,\n    // Utilities methods\n    getSectionsFromValue\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,QAAQ,EAAEC,sBAAsB,QAAQ,gBAAgB;AACjE,SAASC,0BAA0B,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,eAAe,QAAQ,qBAAqB;AAC9L,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,yBAAyB,QAAQ,wCAAwC;AAClF,MAAMC,sBAAsB,GAAG,IAAI;AACnC,OAAO,MAAMC,aAAa,GAAGC,UAAU,IAAI;EAAA,IAAAC,qBAAA;EACzC,MAAMC,KAAK,GAAGjB,QAAQ,CAAC,CAAC;EACxB,MAAMkB,YAAY,GAAGnB,qBAAqB,CAAC,CAAC;EAC5C,MAAMoB,OAAO,GAAGlB,sBAAsB,CAAC,CAAC;EACxC,MAAMmB,KAAK,GAAGtB,MAAM,CAAC,CAAC;EACtB,MAAM;IACJuB,OAAO,EAAE;MACPC,SAAS;MACTC,SAAS;MACTC,qBAAqB,EAAEC,YAAY;MACnCC,0BAA0B,EAAEC;IAC9B,CAAC;IACDC,yBAAyB;IACzBA,yBAAyB,EAAE;MACzBC,KAAK,EAAEC,SAAS;MAChBC,YAAY;MACZC,aAAa,EAAEC,iBAAiB;MAChCC,QAAQ;MACRC,MAAM;MACNC,aAAa,GAAG,OAAO;MACvBC,gBAAgB,EAAEC,oBAAoB;MACtCC,wBAAwB;MACxBC,yBAAyB,GAAG,KAAK;MACjCC,QAAQ,EAAEC,YAAY;MACtBC,iCAAiC,GAAG;IACtC,CAAC;IACDC,cAAc,EAAE;MACdC,KAAK,EAAEC;IACT;EACF,CAAC,GAAG/B,UAAU;EACd,MAAM;IACJc,KAAK;IACLkB,iBAAiB;IACjBN;EACF,CAAC,GAAG9B,kBAAkB,CAAC;IACrBqC,IAAI,EAAE,mBAAmB;IACzBP,QAAQ,EAAEC,YAAY;IACtBb,KAAK,EAAEC,SAAS;IAChBC,YAAY;IACZC,aAAa,EAAEC,iBAAiB;IAChCC,QAAQ;IACRT;EACF,CAAC,CAAC;EACF,MAAMwB,QAAQ,GAAGvD,KAAK,CAACwD,MAAM,CAACrB,KAAK,CAAC;EACpCnC,KAAK,CAACyD,SAAS,CAAC,MAAM;IACpBF,QAAQ,CAACG,OAAO,GAAGvB,KAAK;EAC1B,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,MAAM;IACJwB;EACF,CAAC,GAAG3C,aAAa,CAAC;IAChB4C,KAAK,EAAE1B,yBAAyB;IAChCN,SAAS;IACTmB,QAAQ;IACRZ,KAAK;IACL0B,OAAO,EAAE3B,yBAAyB,CAAC2B;EACrC,CAAC,CAAC;EACF,MAAMV,KAAK,GAAGnD,KAAK,CAAC8D,OAAO,CAAC,MAAM;IAChC;IACA;IACA,IAAIV,SAAS,KAAKW,SAAS,EAAE;MAC3B,OAAOX,SAAS;IAClB;IACA,OAAOO,kBAAkB;EAC3B,CAAC,EAAE,CAACA,kBAAkB,EAAEP,SAAS,CAAC,CAAC;EACnC,MAAMY,eAAe,GAAGhE,KAAK,CAAC8D,OAAO,CAAC,MAAMjD,kBAAkB,CAACU,KAAK,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAC/E,MAAM0C,uBAAuB,GAAGjE,KAAK,CAAC8D,OAAO,CAAC,MAAMrD,qBAAqB,CAACc,KAAK,EAAEyC,eAAe,EAAEjB,QAAQ,CAAC,EAAE,CAACxB,KAAK,EAAEyC,eAAe,EAAEjB,QAAQ,CAAC,CAAC;EAChJ,MAAMmB,oBAAoB,GAAGlE,KAAK,CAACmE,WAAW,CAACC,cAAc,IAAInC,iBAAiB,CAACiC,oBAAoB,CAACE,cAAc,EAAEC,IAAI,IAAItD,uBAAuB,CAAC;IACtJQ,KAAK;IACL+C,UAAU,EAAE9C,YAAY;IACxBwC,eAAe;IACfvB,MAAM;IACN4B,IAAI;IACJ3B,aAAa;IACbI,yBAAyB;IACzBG,iCAAiC;IACjCvB;EACF,CAAC,CAAC,CAAC,EAAE,CAACO,iBAAiB,EAAEQ,MAAM,EAAEjB,YAAY,EAAEwC,eAAe,EAAEtC,KAAK,EAAEoB,yBAAyB,EAAEvB,KAAK,EAAEmB,aAAa,EAAEO,iCAAiC,CAAC,CAAC;EAC3J,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGxE,KAAK,CAACyE,QAAQ,CAAC,MAAM;IAC7C,MAAMC,QAAQ,GAAGR,oBAAoB,CAAC/B,KAAK,CAAC;IAC5CzB,gBAAgB,CAACgE,QAAQ,EAAE7C,SAAS,CAAC;IACrC,MAAM8C,yBAAyB,GAAG;MAChCD,QAAQ;MACRE,iBAAiB,EAAEzC,KAAK;MACxB0C,wBAAwB,EAAE;QACxBpC,MAAM;QACNf,KAAK;QACLoD,MAAM,EAAEvD,KAAK,CAACuD;MAChB,CAAC;MACDC,mBAAmB,EAAE,IAAI;MACzBC,cAAc,EAAE;IAClB,CAAC;IACD,MAAMC,WAAW,GAAG/D,yBAAyB,CAACwD,QAAQ,CAAC;IACvD,MAAMQ,cAAc,GAAGnD,YAAY,CAACoD,wBAAwB,CAAC;MAC3D7C,aAAa,EAAEC,iBAAiB;MAChCJ,KAAK;MACLZ,KAAK;MACLqC,KAAK,EAAE1B,yBAAyB;MAChC+C,WAAW;MACXlC;IACF,CAAC,CAAC;IACF,OAAOhD,QAAQ,CAAC,CAAC,CAAC,EAAE4E,yBAAyB,EAAE;MAC7CO;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAM,CAACvC,gBAAgB,EAAEyC,wBAAwB,CAAC,GAAGnF,aAAa,CAAC;IACjEoF,UAAU,EAAEzC,oBAAoB;IAChC0C,OAAO,EAAE,IAAI;IACbhC,IAAI,EAAE,UAAU;IAChBiB,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMgB,mBAAmB,GAAGC,mBAAmB,IAAI;IACjDJ,wBAAwB,CAACI,mBAAmB,CAAC;IAC7C3C,wBAAwB,aAAxBA,wBAAwB,eAAxBA,wBAAwB,CAAG2C,mBAAmB,CAAC;EACjD,CAAC;EACD,MAAMC,sBAAsB,GAAGzF,KAAK,CAAC8D,OAAO,CAAC,MAAMlD,qBAAqB,CAAC+B,gBAAgB,EAAE4B,KAAK,CAACG,QAAQ,CAAC,EAAE,CAAC/B,gBAAgB,EAAE4B,KAAK,CAACG,QAAQ,CAAC,CAAC;EAC/I,MAAMgB,kBAAkB,GAAGD,sBAAsB,KAAK,KAAK,GAAG,CAAC,GAAGA,sBAAsB;EACxF,MAAME,YAAY,GAAG3F,KAAK,CAAC8D,OAAO,CAAC,MAAMhD,eAAe,CAACyD,KAAK,CAACG,QAAQ,EAAEhD,KAAK,IAAI,CAACuB,iCAAiC,CAAC,EAAE,CAACsB,KAAK,CAACG,QAAQ,EAAEhD,KAAK,EAAEuB,iCAAiC,CAAC,CAAC;EAClL,MAAM2C,mBAAmB,GAAG5F,KAAK,CAAC8D,OAAO,CAAC,MAAMS,KAAK,CAACG,QAAQ,CAACmB,KAAK,CAACC,OAAO,IAAIA,OAAO,CAAC3D,KAAK,KAAK,EAAE,CAAC,EAAE,CAACoC,KAAK,CAACG,QAAQ,CAAC,CAAC;EACxH,MAAMqB,YAAY,GAAGC,QAAQ,IAAI;IAC/B,MAAMC,OAAO,GAAG;MACdC,eAAe,EAAEtE,SAAS,CAAC;QACzBH,OAAO;QACPU,KAAK,EAAE6D,QAAQ;QACfjD,QAAQ;QACRa,KAAK,EAAE1B;MACT,CAAC;IACH,CAAC;IACDmB,iBAAiB,CAAC2C,QAAQ,EAAEC,OAAO,CAAC;EACtC,CAAC;EACD,MAAME,eAAe,GAAGA,CAACC,YAAY,EAAEC,eAAe,KAAK;IACzD,MAAMC,WAAW,GAAG,CAAC,GAAG/B,KAAK,CAACG,QAAQ,CAAC;IACvC4B,WAAW,CAACF,YAAY,CAAC,GAAGrG,QAAQ,CAAC,CAAC,CAAC,EAAEuG,WAAW,CAACF,YAAY,CAAC,EAAE;MAClEjE,KAAK,EAAEkE,eAAe;MACtBE,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF,OAAOD,WAAW;EACpB,CAAC;EACD,MAAME,mCAAmC,GAAGxG,KAAK,CAACwD,MAAM,CAAC,IAAI,CAAC;EAC9D,MAAMiD,0CAA0C,GAAGvG,UAAU,CAAC,CAAC;EAC/D,MAAMwG,wCAAwC,GAAGL,eAAe,IAAI;IAClE,IAAIX,kBAAkB,IAAI,IAAI,EAAE;MAC9B;IACF;IACAc,mCAAmC,CAAC9C,OAAO,GAAG;MAC5C0C,YAAY,EAAEV,kBAAkB;MAChCvD,KAAK,EAAEkE;IACT,CAAC;IACDI,0CAA0C,CAACE,KAAK,CAAC,CAAC,EAAE,MAAM;MACxDH,mCAAmC,CAAC9C,OAAO,GAAG,IAAI;IACpD,CAAC,CAAC;EACJ,CAAC;EACD,MAAMkD,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI7E,YAAY,CAAC8E,cAAc,CAACtF,KAAK,EAAEY,KAAK,EAAEJ,YAAY,CAAC+E,UAAU,CAAC,EAAE;MACtEtC,QAAQ,CAACuC,SAAS,IAAIhH,QAAQ,CAAC,CAAC,CAAC,EAAEgH,SAAS,EAAE;QAC5CrC,QAAQ,EAAEqC,SAAS,CAACrC,QAAQ,CAACsC,GAAG,CAAClB,OAAO,IAAI/F,QAAQ,CAAC,CAAC,CAAC,EAAE+F,OAAO,EAAE;UAChE3D,KAAK,EAAE;QACT,CAAC,CAAC,CAAC;QACH4C,mBAAmB,EAAE,IAAI;QACzBC,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLR,QAAQ,CAACuC,SAAS,IAAIhH,QAAQ,CAAC,CAAC,CAAC,EAAEgH,SAAS,EAAE;QAC5C/B,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;MACHe,YAAY,CAAChE,YAAY,CAAC+E,UAAU,CAAC;IACvC;EACF,CAAC;EACD,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIvB,kBAAkB,IAAI,IAAI,EAAE;MAC9B;IACF;IACA,MAAMwB,aAAa,GAAG3C,KAAK,CAACG,QAAQ,CAACgB,kBAAkB,CAAC;IACxD,IAAIwB,aAAa,CAAC/E,KAAK,KAAK,EAAE,EAAE;MAC9B;IACF;IACAuE,wCAAwC,CAAC,EAAE,CAAC;IAC5C,IAAIzE,iBAAiB,CAACkF,kBAAkB,CAAChF,KAAK,EAAE+E,aAAa,CAAC,KAAK,IAAI,EAAE;MACvE1C,QAAQ,CAACuC,SAAS,IAAIhH,QAAQ,CAAC,CAAC,CAAC,EAAEgH,SAAS,EAAE;QAC5CrC,QAAQ,EAAEyB,eAAe,CAACT,kBAAkB,EAAE,EAAE,CAAC;QACjDX,mBAAmB,EAAE,IAAI;QACzBC,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLR,QAAQ,CAACuC,SAAS,IAAIhH,QAAQ,CAAC,CAAC,CAAC,EAAEgH,SAAS,EAAE;QAC5C/B,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;MACHe,YAAY,CAAC9D,iBAAiB,CAACmF,iBAAiB,CAACjF,KAAK,EAAE+E,aAAa,EAAE,IAAI,CAAC,CAAC;IAC/E;EACF,CAAC;EACD,MAAMG,uBAAuB,GAAGC,QAAQ,IAAI;IAC1C,MAAMC,YAAY,GAAGA,CAACC,OAAO,EAAElF,aAAa,KAAK;MAC/C,MAAM+B,IAAI,GAAG9C,KAAK,CAACkG,KAAK,CAACD,OAAO,EAAE/E,MAAM,CAAC;MACzC,IAAI,CAAClB,KAAK,CAACmG,OAAO,CAACrD,IAAI,CAAC,EAAE;QACxB,OAAO,IAAI;MACb;MACA,MAAMK,QAAQ,GAAG3D,uBAAuB,CAAC;QACvCQ,KAAK;QACL+C,UAAU,EAAE9C,YAAY;QACxBwC,eAAe;QACfvB,MAAM;QACN4B,IAAI;QACJ3B,aAAa;QACbI,yBAAyB;QACzBG,iCAAiC;QACjCvB;MACF,CAAC,CAAC;MACF,OAAOlB,0BAA0B,CAACe,KAAK,EAAE8C,IAAI,EAAEK,QAAQ,EAAEpC,aAAa,EAAE,KAAK,CAAC;IAChF,CAAC;IACD,MAAM0D,QAAQ,GAAG/D,iBAAiB,CAAC0F,aAAa,CAACL,QAAQ,EAAE/C,KAAK,CAACW,cAAc,EAAEqC,YAAY,CAAC;IAC9FxB,YAAY,CAACC,QAAQ,CAAC;EACxB,CAAC;EACD,MAAM4B,yCAAyC,GAAG1H,UAAU,CAAC,CAAC;EAC9D,MAAM2H,kBAAkB,GAAGC,IAAA,IAIrB;IAAA,IAJsB;MAC1BhC,OAAO;MACPO,eAAe;MACf0B;IACF,CAAC,GAAAD,IAAA;IACCrB,0CAA0C,CAACuB,KAAK,CAAC,CAAC;IAClDJ,yCAAyC,CAACI,KAAK,CAAC,CAAC;IACjD,MAAMC,UAAU,GAAGhG,iBAAiB,CAACkF,kBAAkB,CAAChF,KAAK,EAAE2D,OAAO,CAAC;;IAEvE;AACJ;AACA;IACI,IAAIiC,qBAAqB,IAAIrC,kBAAkB,GAAGnB,KAAK,CAACG,QAAQ,CAACwD,MAAM,GAAG,CAAC,EAAE;MAC3E3C,mBAAmB,CAACG,kBAAkB,GAAG,CAAC,CAAC;IAC7C;;IAEA;AACJ;AACA;IACI,MAAMY,WAAW,GAAGH,eAAe,CAACT,kBAAkB,EAAEW,eAAe,CAAC;IACxE,MAAM8B,qBAAqB,GAAGlG,iBAAiB,CAACmG,wBAAwB,CAAC9B,WAAW,EAAER,OAAO,CAAC;IAC9F,MAAMuC,aAAa,GAAG1H,uBAAuB,CAACY,KAAK,EAAE4G,qBAAqB,EAAEnE,eAAe,CAAC;;IAE5F;AACJ;AACA;AACA;AACA;IACI,IAAIzC,KAAK,CAACmG,OAAO,CAACW,aAAa,CAAC,EAAE;MAChC,MAAMC,UAAU,GAAG9H,0BAA0B,CAACe,KAAK,EAAE8G,aAAa,EAAEF,qBAAqB,EAAElG,iBAAiB,CAACkF,kBAAkB,CAAC5C,KAAK,CAACW,cAAc,EAAEY,OAAO,CAAC,EAAE,IAAI,CAAC;MACrK,IAAImC,UAAU,IAAI,IAAI,EAAE;QACtBL,yCAAyC,CAACjB,KAAK,CAAC,CAAC,EAAE,MAAM;UACvD,IAAIpD,QAAQ,CAACG,OAAO,KAAKvB,KAAK,EAAE;YAC9BqC,QAAQ,CAACuC,SAAS,IAAIhH,QAAQ,CAAC,CAAC,CAAC,EAAEgH,SAAS,EAAE;cAC5CrC,QAAQ,EAAEzC,iBAAiB,CAACsG,iBAAiB,CAAChE,KAAK,CAACG,QAAQ,EAAEoB,OAAO,CAAC;cACtEf,mBAAmB,EAAE;YACvB,CAAC,CAAC,CAAC;UACL;QACF,CAAC,CAAC;MACJ;MACA,OAAOgB,YAAY,CAAC9D,iBAAiB,CAACmF,iBAAiB,CAACjF,KAAK,EAAE2D,OAAO,EAAEwC,UAAU,CAAC,CAAC;IACtF;;IAEA;AACJ;AACA;AACA;IACI,IAAIH,qBAAqB,CAACtC,KAAK,CAAC2C,UAAU,IAAIA,UAAU,CAACrG,KAAK,KAAK,EAAE,CAAC,KAAK8F,UAAU,IAAI,IAAI,IAAI1G,KAAK,CAACmG,OAAO,CAACO,UAAU,CAAC,CAAC,EAAE;MAC3HvB,wCAAwC,CAACL,eAAe,CAAC;MACzD,OAAON,YAAY,CAAC9D,iBAAiB,CAACmF,iBAAiB,CAACjF,KAAK,EAAE2D,OAAO,EAAEuC,aAAa,CAAC,CAAC;IACzF;;IAEA;AACJ;AACA;AACA;IACI,IAAIJ,UAAU,IAAI,IAAI,EAAE;MACtBvB,wCAAwC,CAACL,eAAe,CAAC;MACzD,OAAON,YAAY,CAAC9D,iBAAiB,CAACmF,iBAAiB,CAACjF,KAAK,EAAE2D,OAAO,EAAE,IAAI,CAAC,CAAC;IAChF;;IAEA;AACJ;AACA;AACA;IACI,OAAOtB,QAAQ,CAACuC,SAAS,IAAIhH,QAAQ,CAAC,CAAC,CAAC,EAAEgH,SAAS,EAAE;MACnDrC,QAAQ,EAAE4B,WAAW;MACrBvB,mBAAmB,EAAE;IACvB,CAAC,CAAC,CAAC;EACL,CAAC;EACD,MAAM0D,sBAAsB,GAAG1D,mBAAmB,IAAIP,QAAQ,CAACuC,SAAS,IAAIhH,QAAQ,CAAC,CAAC,CAAC,EAAEgH,SAAS,EAAE;IAClGhC;EACF,CAAC,CAAC,CAAC;EACH,MAAM2D,iBAAiB,GAAGvI,gBAAgB,CAACwI,iBAAiB,IAAI;IAC9DnE,QAAQ,CAACuC,SAAS,IAAIhH,QAAQ,CAAC,CAAC,CAAC,EAAEgH,SAAS,EAAE;MAC5C/B,cAAc,EAAE2D;IAClB,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;;EAEF;EACA,IAAIxG,KAAK,KAAKoC,KAAK,CAACK,iBAAiB,EAAE;IACrC,IAAIF,QAAQ;IACZ,IAAI8B,mCAAmC,CAAC9C,OAAO,IAAI,IAAI,IAAI,CAACnC,KAAK,CAACmG,OAAO,CAACzF,iBAAiB,CAACkF,kBAAkB,CAAChF,KAAK,EAAEoC,KAAK,CAACG,QAAQ,CAAC8B,mCAAmC,CAAC9C,OAAO,CAAC0C,YAAY,CAAC,CAAC,CAAC,EAAE;MAChM1B,QAAQ,GAAGyB,eAAe,CAACK,mCAAmC,CAAC9C,OAAO,CAAC0C,YAAY,EAAEI,mCAAmC,CAAC9C,OAAO,CAACvB,KAAK,CAAC;IACzI,CAAC,MAAM;MACLuC,QAAQ,GAAGR,oBAAoB,CAAC/B,KAAK,CAAC;IACxC;IACAqC,QAAQ,CAACuC,SAAS,IAAIhH,QAAQ,CAAC,CAAC,CAAC,EAAEgH,SAAS,EAAE;MAC5CnC,iBAAiB,EAAEzC,KAAK;MACxBuC,QAAQ;MACRkE,oBAAoB,EAAE;QACpBnG,MAAM;QACNf,KAAK;QACLoD,MAAM,EAAEvD,KAAK,CAACuD;MAChB,CAAC;MACDI,cAAc,EAAEjD,iBAAiB,CAAC4G,oBAAoB,CAACtH,KAAK,EAAEY,KAAK,EAAE4E,SAAS,CAAC7B,cAAc,CAAC;MAC9FH,mBAAmB,EAAE;IACvB,CAAC,CAAC,CAAC;EACL;EACA,IAAIrD,KAAK,KAAK6C,KAAK,CAACM,wBAAwB,CAACnD,KAAK,IAAIe,MAAM,KAAK8B,KAAK,CAACM,wBAAwB,CAACpC,MAAM,IAAIlB,KAAK,CAACuD,MAAM,KAAKP,KAAK,CAACM,wBAAwB,CAACC,MAAM,EAAE;IAChK,MAAMJ,QAAQ,GAAGR,oBAAoB,CAAC/B,KAAK,CAAC;IAC5CzB,gBAAgB,CAACgE,QAAQ,EAAE7C,SAAS,CAAC;IACrC2C,QAAQ,CAACuC,SAAS,IAAIhH,QAAQ,CAAC,CAAC,CAAC,EAAEgH,SAAS,EAAE;MAC5ClC,wBAAwB,EAAE;QACxBpC,MAAM;QACNf,KAAK;QACLoD,MAAM,EAAEvD,KAAK,CAACuD;MAChB,CAAC;MACDJ,QAAQ;MACRK,mBAAmB,EAAE,IAAI;MACzBC,cAAc,EAAE;IAClB,CAAC,CAAC,CAAC;EACL;EACA,IAAIT,KAAK,CAACS,cAAc,IAAI,IAAI,IAAI,CAAC7B,KAAK,IAAIuC,kBAAkB,IAAI,IAAI,EAAE;IACxEgD,iBAAiB,CAAC,IAAI,CAAC;EACzB;EACA,IAAInE,KAAK,CAACS,cAAc,IAAI,IAAI,IAAI,EAAA1D,qBAAA,GAAAiD,KAAK,CAACG,QAAQ,CAACH,KAAK,CAACS,cAAc,CAACoB,YAAY,CAAC,cAAA9E,qBAAA,uBAAjDA,qBAAA,CAAmDwH,IAAI,MAAKvE,KAAK,CAACS,cAAc,CAAC+D,WAAW,EAAE;IAChIL,iBAAiB,CAAC,IAAI,CAAC;EACzB;EACA1I,KAAK,CAACyD,SAAS,CAAC,MAAM;IACpB,IAAI+C,mCAAmC,CAAC9C,OAAO,IAAI,IAAI,EAAE;MACvD8C,mCAAmC,CAAC9C,OAAO,GAAG,IAAI;IACpD;EACF,CAAC,CAAC;EACF,MAAMsF,0BAA0B,GAAG9I,UAAU,CAAC,CAAC;EAC/CF,KAAK,CAACyD,SAAS,CAAC,MAAM;IACpB,IAAIc,KAAK,CAACS,cAAc,IAAI,IAAI,EAAE;MAChCgE,0BAA0B,CAACrC,KAAK,CAACxF,sBAAsB,EAAE,MAAMuH,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACzF;IACA,OAAO,MAAM,CAAC,CAAC;EACjB,CAAC,EAAE,CAACnE,KAAK,CAACS,cAAc,EAAE0D,iBAAiB,EAAEM,0BAA0B,CAAC,CAAC;;EAEzE;EACA;EACA;EACA;EACAhJ,KAAK,CAACyD,SAAS,CAAC,MAAM;IACpB,IAAIc,KAAK,CAACQ,mBAAmB,IAAI,IAAI,IAAIW,kBAAkB,IAAI,IAAI,EAAE;MACnEuB,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAAC1C,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEtB,OAAO;IACL;IACAgB,kBAAkB;IAClBE,mBAAmB;IACnBzC,KAAK;IACLa,eAAe;IACfyB,sBAAsB;IACtBE,YAAY;IACZ1B,uBAAuB;IACvBM,KAAK;IACLxB,QAAQ;IACRZ,KAAK;IACL;IACAyE,UAAU;IACVK,kBAAkB;IAClByB,iBAAiB;IACjBnD,mBAAmB;IACnBkD,sBAAsB;IACtBZ,kBAAkB;IAClBR,uBAAuB;IACvB;IACAnD;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}