{"ast": null, "code": "import * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { arrayIncludes } from \"../../../utils/utils.js\";\nfunction getOrientation() {\n  if (typeof window === 'undefined') {\n    return 'portrait';\n  }\n  if (window.screen && window.screen.orientation && window.screen.orientation.angle) {\n    return Math.abs(window.screen.orientation.angle) === 90 ? 'landscape' : 'portrait';\n  }\n\n  // Support IOS safari\n  if (window.orientation) {\n    return Math.abs(Number(window.orientation)) === 90 ? 'landscape' : 'portrait';\n  }\n  return 'portrait';\n}\nexport function useOrientation(views, customOrientation) {\n  const [orientation, setOrientation] = React.useState(getOrientation);\n  useEnhancedEffect(() => {\n    const eventHandler = () => {\n      setOrientation(getOrientation());\n    };\n    window.addEventListener('orientationchange', eventHandler);\n    return () => {\n      window.removeEventListener('orientationchange', eventHandler);\n    };\n  }, []);\n  if (arrayIncludes(views, ['hours', 'minutes', 'seconds'])) {\n    // could not display 13:34:44 in landscape mode\n    return 'portrait';\n  }\n  return customOrientation !== null && customOrientation !== void 0 ? customOrientation : orientation;\n}", "map": {"version": 3, "names": ["React", "useEnhancedEffect", "arrayIncludes", "getOrientation", "window", "screen", "orientation", "angle", "Math", "abs", "Number", "useOrientation", "views", "customOrientation", "setOrientation", "useState", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "removeEventListener"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-date-pickers/esm/internals/hooks/usePicker/hooks/useOrientation.js"], "sourcesContent": ["import * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { arrayIncludes } from \"../../../utils/utils.js\";\nfunction getOrientation() {\n  if (typeof window === 'undefined') {\n    return 'portrait';\n  }\n  if (window.screen && window.screen.orientation && window.screen.orientation.angle) {\n    return Math.abs(window.screen.orientation.angle) === 90 ? 'landscape' : 'portrait';\n  }\n\n  // Support IOS safari\n  if (window.orientation) {\n    return Math.abs(Number(window.orientation)) === 90 ? 'landscape' : 'portrait';\n  }\n  return 'portrait';\n}\nexport function useOrientation(views, customOrientation) {\n  const [orientation, setOrientation] = React.useState(getOrientation);\n  useEnhancedEffect(() => {\n    const eventHandler = () => {\n      setOrientation(getOrientation());\n    };\n    window.addEventListener('orientationchange', eventHandler);\n    return () => {\n      window.removeEventListener('orientationchange', eventHandler);\n    };\n  }, []);\n  if (arrayIncludes(views, ['hours', 'minutes', 'seconds'])) {\n    // could not display 13:34:44 in landscape mode\n    return 'portrait';\n  }\n  return customOrientation ?? orientation;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,cAAcA,CAAA,EAAG;EACxB,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjC,OAAO,UAAU;EACnB;EACA,IAAIA,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,WAAW,IAAIF,MAAM,CAACC,MAAM,CAACC,WAAW,CAACC,KAAK,EAAE;IACjF,OAAOC,IAAI,CAACC,GAAG,CAACL,MAAM,CAACC,MAAM,CAACC,WAAW,CAACC,KAAK,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG,UAAU;EACpF;;EAEA;EACA,IAAIH,MAAM,CAACE,WAAW,EAAE;IACtB,OAAOE,IAAI,CAACC,GAAG,CAACC,MAAM,CAACN,MAAM,CAACE,WAAW,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG,UAAU;EAC/E;EACA,OAAO,UAAU;AACnB;AACA,OAAO,SAASK,cAAcA,CAACC,KAAK,EAAEC,iBAAiB,EAAE;EACvD,MAAM,CAACP,WAAW,EAAEQ,cAAc,CAAC,GAAGd,KAAK,CAACe,QAAQ,CAACZ,cAAc,CAAC;EACpEF,iBAAiB,CAAC,MAAM;IACtB,MAAMe,YAAY,GAAGA,CAAA,KAAM;MACzBF,cAAc,CAACX,cAAc,CAAC,CAAC,CAAC;IAClC,CAAC;IACDC,MAAM,CAACa,gBAAgB,CAAC,mBAAmB,EAAED,YAAY,CAAC;IAC1D,OAAO,MAAM;MACXZ,MAAM,CAACc,mBAAmB,CAAC,mBAAmB,EAAEF,YAAY,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,IAAId,aAAa,CAACU,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE;IACzD;IACA,OAAO,UAAU;EACnB;EACA,OAAOC,iBAAiB,aAAjBA,iBAAiB,cAAjBA,iBAAiB,GAAIP,WAAW;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}