{"ast": null, "code": "import _objectSpread from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\n// This module is based on https://github.com/airbnb/prop-types-exact repository.\n// However, in order to reduce the number of dependencies and to remove some extra safe checks\n// the module was forked.\n\nconst specialProperty = 'exact-prop: \\u200b';\nexport default function exactProp(propTypes) {\n  if (process.env.NODE_ENV === 'production') {\n    return propTypes;\n  }\n  return _objectSpread(_objectSpread({}, propTypes), {}, {\n    [specialProperty]: props => {\n      const unsupportedProps = Object.keys(props).filter(prop => !propTypes.hasOwnProperty(prop));\n      if (unsupportedProps.length > 0) {\n        return new Error(\"The following props are not supported: \".concat(unsupportedProps.map(prop => \"`\".concat(prop, \"`\")).join(', '), \". Please remove them.\"));\n      }\n      return null;\n    }\n  });\n}", "map": {"version": 3, "names": ["specialProperty", "exactProp", "propTypes", "process", "env", "NODE_ENV", "_objectSpread", "props", "unsupportedProps", "Object", "keys", "filter", "prop", "hasOwnProperty", "length", "Error", "concat", "map", "join"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/utils/esm/exactProp/exactProp.js"], "sourcesContent": ["// This module is based on https://github.com/airbnb/prop-types-exact repository.\n// However, in order to reduce the number of dependencies and to remove some extra safe checks\n// the module was forked.\n\nconst specialProperty = 'exact-prop: \\u200b';\nexport default function exactProp(propTypes) {\n  if (process.env.NODE_ENV === 'production') {\n    return propTypes;\n  }\n  return {\n    ...propTypes,\n    [specialProperty]: props => {\n      const unsupportedProps = Object.keys(props).filter(prop => !propTypes.hasOwnProperty(prop));\n      if (unsupportedProps.length > 0) {\n        return new Error(`The following props are not supported: ${unsupportedProps.map(prop => `\\`${prop}\\``).join(', ')}. Please remove them.`);\n      }\n      return null;\n    }\n  };\n}"], "mappings": ";AAAA;AACA;AACA;;AAEA,MAAMA,eAAe,GAAG,oBAAoB;AAC5C,eAAe,SAASC,SAASA,CAACC,SAAS,EAAE;EAC3C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAOH,SAAS;EAClB;EACA,OAAAI,aAAA,CAAAA,aAAA,KACKJ,SAAS;IACZ,CAACF,eAAe,GAAGO,KAAK,IAAI;MAC1B,MAAMC,gBAAgB,GAAGC,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,MAAM,CAACC,IAAI,IAAI,CAACV,SAAS,CAACW,cAAc,CAACD,IAAI,CAAC,CAAC;MAC3F,IAAIJ,gBAAgB,CAACM,MAAM,GAAG,CAAC,EAAE;QAC/B,OAAO,IAAIC,KAAK,2CAAAC,MAAA,CAA2CR,gBAAgB,CAACS,GAAG,CAACL,IAAI,QAAAI,MAAA,CAASJ,IAAI,MAAI,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC,0BAAuB,CAAC;MAC3I;MACA,OAAO,IAAI;IACb;EAAC;AAEL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}