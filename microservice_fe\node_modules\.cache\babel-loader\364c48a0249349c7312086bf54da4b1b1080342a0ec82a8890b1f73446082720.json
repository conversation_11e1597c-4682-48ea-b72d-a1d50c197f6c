{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getGridLegacyUtilityClass(slot) {\n  return generateUtilityClass('MuiGridLegacy', slot);\n}\nconst SPACINGS = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\nconst DIRECTIONS = ['column-reverse', 'column', 'row-reverse', 'row'];\nconst WRAPS = ['nowrap', 'wrap-reverse', 'wrap'];\nconst GRID_SIZES = ['auto', true, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];\nconst gridClasses = generateUtilityClasses('MuiGridLegacy', ['root', 'container', 'item', 'zeroMinWidth',\n// spacings\n...SPACINGS.map(spacing => \"spacing-xs-\".concat(spacing)),\n// direction values\n...DIRECTIONS.map(direction => \"direction-xs-\".concat(direction)),\n// wrap values\n...WRAPS.map(wrap => \"wrap-xs-\".concat(wrap)),\n// grid sizes for all breakpoints\n...GRID_SIZES.map(size => \"grid-xs-\".concat(size)), ...GRID_SIZES.map(size => \"grid-sm-\".concat(size)), ...GRID_SIZES.map(size => \"grid-md-\".concat(size)), ...GRID_SIZES.map(size => \"grid-lg-\".concat(size)), ...GRID_SIZES.map(size => \"grid-xl-\".concat(size))]);\nexport default gridClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getGridLegacyUtilityClass", "slot", "SPACINGS", "DIRECTIONS", "WRAPS", "GRID_SIZES", "gridClasses", "map", "spacing", "concat", "direction", "wrap", "size"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/GridLegacy/gridLegacyClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getGridLegacyUtilityClass(slot) {\n  return generateUtilityClass('MuiGridLegacy', slot);\n}\nconst SPACINGS = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\nconst DIRECTIONS = ['column-reverse', 'column', 'row-reverse', 'row'];\nconst WRAPS = ['nowrap', 'wrap-reverse', 'wrap'];\nconst GRID_SIZES = ['auto', true, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];\nconst gridClasses = generateUtilityClasses('MuiGridLegacy', ['root', 'container', 'item', 'zeroMinWidth',\n// spacings\n...SPACINGS.map(spacing => `spacing-xs-${spacing}`),\n// direction values\n...DIRECTIONS.map(direction => `direction-xs-${direction}`),\n// wrap values\n...WRAPS.map(wrap => `wrap-xs-${wrap}`),\n// grid sizes for all breakpoints\n...GRID_SIZES.map(size => `grid-xs-${size}`), ...GRID_SIZES.map(size => `grid-sm-${size}`), ...GRID_SIZES.map(size => `grid-md-${size}`), ...GRID_SIZES.map(size => `grid-lg-${size}`), ...GRID_SIZES.map(size => `grid-xl-${size}`)]);\nexport default gridClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAOF,oBAAoB,CAAC,eAAe,EAAEE,IAAI,CAAC;AACpD;AACA,MAAMC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;AACnD,MAAMC,UAAU,GAAG,CAAC,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,CAAC;AACrE,MAAMC,KAAK,GAAG,CAAC,QAAQ,EAAE,cAAc,EAAE,MAAM,CAAC;AAChD,MAAMC,UAAU,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACxE,MAAMC,WAAW,GAAGR,sBAAsB,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc;AACxG;AACA,GAAGI,QAAQ,CAACK,GAAG,CAACC,OAAO,kBAAAC,MAAA,CAAkBD,OAAO,CAAE,CAAC;AACnD;AACA,GAAGL,UAAU,CAACI,GAAG,CAACG,SAAS,oBAAAD,MAAA,CAAoBC,SAAS,CAAE,CAAC;AAC3D;AACA,GAAGN,KAAK,CAACG,GAAG,CAACI,IAAI,eAAAF,MAAA,CAAeE,IAAI,CAAE,CAAC;AACvC;AACA,GAAGN,UAAU,CAACE,GAAG,CAACK,IAAI,eAAAH,MAAA,CAAeG,IAAI,CAAE,CAAC,EAAE,GAAGP,UAAU,CAACE,GAAG,CAACK,IAAI,eAAAH,MAAA,CAAeG,IAAI,CAAE,CAAC,EAAE,GAAGP,UAAU,CAACE,GAAG,CAACK,IAAI,eAAAH,MAAA,CAAeG,IAAI,CAAE,CAAC,EAAE,GAAGP,UAAU,CAACE,GAAG,CAACK,IAAI,eAAAH,MAAA,CAAeG,IAAI,CAAE,CAAC,EAAE,GAAGP,UAAU,CAACE,GAAG,CAACK,IAAI,eAAAH,MAAA,CAAeG,IAAI,CAAE,CAAC,CAAC,CAAC;AACtO,eAAeN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}