{"ast": null, "code": "import slotShouldForwardProp from \"./slotShouldForwardProp.js\";\nconst rootShouldForwardProp = prop => slotShouldForwardProp(prop) && prop !== 'classes';\nexport default rootShouldForwardProp;", "map": {"version": 3, "names": ["slotShouldForwardProp", "rootShouldForwardProp", "prop"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/styles/rootShouldForwardProp.js"], "sourcesContent": ["import slotShouldForwardProp from \"./slotShouldForwardProp.js\";\nconst rootShouldForwardProp = prop => slotShouldForwardProp(prop) && prop !== 'classes';\nexport default rootShouldForwardProp;"], "mappings": "AAAA,OAAOA,qBAAqB,MAAM,4BAA4B;AAC9D,MAAMC,qBAAqB,GAAGC,IAAI,IAAIF,qBAAqB,CAACE,IAAI,CAAC,IAAIA,IAAI,KAAK,SAAS;AACvF,eAAeD,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}