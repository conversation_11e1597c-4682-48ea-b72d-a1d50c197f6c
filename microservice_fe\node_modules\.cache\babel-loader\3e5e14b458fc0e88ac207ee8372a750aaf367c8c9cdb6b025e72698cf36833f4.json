{"ast": null, "code": "import{get,post,put,del}from'../api/apiClient';const BASE_URL='/api/customer';export const customerService={// Get all customers\ngetAllCustomers:async()=>{return get(BASE_URL);},// Get customer by ID\ngetCustomerById:async id=>{return get(\"\".concat(BASE_URL,\"/\").concat(id));},// Create a new customer\ncreateCustomer:async customer=>{return post(BASE_URL,customer);},// Update an existing customer\nupdateCustomer:async customer=>{return put(BASE_URL,customer);},// Delete a customer\ndeleteCustomer:async id=>{return del(\"\".concat(BASE_URL,\"/\").concat(id));},// Search customers by name and/or phone number\nsearchCustomers:async(fullName,phoneNumber)=>{let url=\"\".concat(BASE_URL,\"/search\");const params=[];if(fullName){params.push(\"fullName=\".concat(encodeURIComponent(fullName)));}if(phoneNumber){params.push(\"phoneNumber=\".concat(encodeURIComponent(phoneNumber)));}if(params.length>0){url+=\"?\".concat(params.join('&'));}return get(url);}};", "map": {"version": 3, "names": ["get", "post", "put", "del", "BASE_URL", "customerService", "getAllCustomers", "getCustomerById", "id", "concat", "createCustomer", "customer", "updateCustomer", "deleteCustomer", "searchCustomers", "fullName", "phoneNumber", "url", "params", "push", "encodeURIComponent", "length", "join"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/services/customer/customerService.ts"], "sourcesContent": ["import { Customer } from '../../models';\nimport { get, post, put, del } from '../api/apiClient';\n\nconst BASE_URL = '/api/customer';\n\nexport const customerService = {\n  // Get all customers\n  getAllCustomers: async (): Promise<Customer[]> => {\n    return get<Customer[]>(BASE_URL);\n  },\n\n  // Get customer by ID\n  getCustomerById: async (id: number): Promise<Customer> => {\n    return get<Customer>(`${BASE_URL}/${id}`);\n  },\n\n  // Create a new customer\n  createCustomer: async (customer: Customer): Promise<Customer> => {\n    return post<Customer>(BASE_URL, customer);\n  },\n\n  // Update an existing customer\n  updateCustomer: async (customer: Customer): Promise<Customer> => {\n    return put<Customer>(BASE_URL, customer);\n  },\n\n  // Delete a customer\n  deleteCustomer: async (id: number): Promise<void> => {\n    return del<void>(`${BASE_URL}/${id}`);\n  },\n\n  // Search customers by name and/or phone number\n  searchCustomers: async (fullName?: string, phoneNumber?: string): Promise<Customer[]> => {\n    let url = `${BASE_URL}/search`;\n    const params = [];\n\n    if (fullName) {\n      params.push(`fullName=${encodeURIComponent(fullName)}`);\n    }\n\n    if (phoneNumber) {\n      params.push(`phoneNumber=${encodeURIComponent(phoneNumber)}`);\n    }\n\n    if (params.length > 0) {\n      url += `?${params.join('&')}`;\n    }\n\n    return get<Customer[]>(url);\n  }\n};\n"], "mappings": "AACA,OAASA,GAAG,CAAEC,IAAI,CAAEC,GAAG,CAAEC,GAAG,KAAQ,kBAAkB,CAEtD,KAAM,CAAAC,QAAQ,CAAG,eAAe,CAEhC,MAAO,MAAM,CAAAC,eAAe,CAAG,CAC7B;AACAC,eAAe,CAAE,KAAAA,CAAA,GAAiC,CAChD,MAAO,CAAAN,GAAG,CAAaI,QAAQ,CAAC,CAClC,CAAC,CAED;AACAG,eAAe,CAAE,KAAO,CAAAC,EAAU,EAAwB,CACxD,MAAO,CAAAR,GAAG,IAAAS,MAAA,CAAcL,QAAQ,MAAAK,MAAA,CAAID,EAAE,CAAE,CAAC,CAC3C,CAAC,CAED;AACAE,cAAc,CAAE,KAAO,CAAAC,QAAkB,EAAwB,CAC/D,MAAO,CAAAV,IAAI,CAAWG,QAAQ,CAAEO,QAAQ,CAAC,CAC3C,CAAC,CAED;AACAC,cAAc,CAAE,KAAO,CAAAD,QAAkB,EAAwB,CAC/D,MAAO,CAAAT,GAAG,CAAWE,QAAQ,CAAEO,QAAQ,CAAC,CAC1C,CAAC,CAED;AACAE,cAAc,CAAE,KAAO,CAAAL,EAAU,EAAoB,CACnD,MAAO,CAAAL,GAAG,IAAAM,MAAA,CAAUL,QAAQ,MAAAK,MAAA,CAAID,EAAE,CAAE,CAAC,CACvC,CAAC,CAED;AACAM,eAAe,CAAE,KAAAA,CAAOC,QAAiB,CAAEC,WAAoB,GAA0B,CACvF,GAAI,CAAAC,GAAG,IAAAR,MAAA,CAAML,QAAQ,WAAS,CAC9B,KAAM,CAAAc,MAAM,CAAG,EAAE,CAEjB,GAAIH,QAAQ,CAAE,CACZG,MAAM,CAACC,IAAI,aAAAV,MAAA,CAAaW,kBAAkB,CAACL,QAAQ,CAAC,CAAE,CAAC,CACzD,CAEA,GAAIC,WAAW,CAAE,CACfE,MAAM,CAACC,IAAI,gBAAAV,MAAA,CAAgBW,kBAAkB,CAACJ,WAAW,CAAC,CAAE,CAAC,CAC/D,CAEA,GAAIE,MAAM,CAACG,MAAM,CAAG,CAAC,CAAE,CACrBJ,GAAG,MAAAR,MAAA,CAAQS,MAAM,CAACI,IAAI,CAAC,GAAG,CAAC,CAAE,CAC/B,CAEA,MAAO,CAAAtB,GAAG,CAAaiB,GAAG,CAAC,CAC7B,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}