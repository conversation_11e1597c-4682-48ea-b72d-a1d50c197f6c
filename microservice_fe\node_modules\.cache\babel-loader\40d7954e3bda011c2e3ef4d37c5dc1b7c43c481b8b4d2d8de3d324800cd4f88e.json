{"ast": null, "code": "import _objectSpread from\"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import{Dialog,DialogContent,Box,Typography,Button,IconButton,Slide}from'@mui/material';import CheckCircleOutlineIcon from'@mui/icons-material/CheckCircleOutline';import CloseIcon from'@mui/icons-material/Close';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Transition=/*#__PURE__*/React.forwardRef(function Transition(props,ref){return/*#__PURE__*/_jsx(Slide,_objectSpread({direction:\"up\",ref:ref},props));});const SuccessNotification=_ref=>{let{open,message,onClose}=_ref;return/*#__PURE__*/_jsxs(Dialog,{open:open,TransitionComponent:Transition,keepMounted:true,onClose:onClose,\"aria-describedby\":\"success-notification-dialog\",sx:{'& .MuiDialog-paper':{borderRadius:2,maxWidth:'400px',margin:'0 auto'}},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:8,right:8},children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"close\",onClick:onClose,sx:{color:'text.secondary'},children:/*#__PURE__*/_jsx(CloseIcon,{})})}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',textAlign:'center',py:3,px:1},children:[/*#__PURE__*/_jsx(CheckCircleOutlineIcon,{sx:{fontSize:80,color:'success.main',mb:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Th\\xE0nh c\\xF4ng\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",sx:{mb:3},children:message}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",onClick:onClose,sx:{minWidth:120},children:\"\\u0110\\xF3ng\"})]})})]});};export default SuccessNotification;", "map": {"version": 3, "names": ["React", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Box", "Typography", "<PERSON><PERSON>", "IconButton", "Slide", "CheckCircleOutlineIcon", "CloseIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Transition", "forwardRef", "props", "ref", "_objectSpread", "direction", "SuccessNotification", "_ref", "open", "message", "onClose", "TransitionComponent", "keepMounted", "sx", "borderRadius", "max<PERSON><PERSON><PERSON>", "margin", "children", "position", "top", "right", "onClick", "color", "display", "flexDirection", "alignItems", "textAlign", "py", "px", "fontSize", "mb", "variant", "gutterBottom", "min<PERSON><PERSON><PERSON>"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/payment/SuccessNotification.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Dialog,\n  DialogContent,\n  Box,\n  Typography,\n  Button,\n  IconButton,\n  Slide,\n} from '@mui/material';\nimport { TransitionProps } from '@mui/material/transitions';\nimport CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';\nimport CloseIcon from '@mui/icons-material/Close';\n\ninterface SuccessNotificationProps {\n  open: boolean;\n  message: string;\n  onClose: () => void;\n}\n\nconst Transition = React.forwardRef(function Transition(\n  props: TransitionProps & {\n    children: React.ReactElement;\n  },\n  ref: React.Ref<unknown>,\n) {\n  return <Slide direction=\"up\" ref={ref} {...props} />;\n});\n\nconst SuccessNotification: React.FC<SuccessNotificationProps> = ({\n  open,\n  message,\n  onClose,\n}) => {\n  return (\n    <Dialog\n      open={open}\n      TransitionComponent={Transition}\n      keepMounted\n      onClose={onClose}\n      aria-describedby=\"success-notification-dialog\"\n      sx={{\n        '& .MuiDialog-paper': {\n          borderRadius: 2,\n          maxWidth: '400px',\n          margin: '0 auto',\n        },\n      }}\n    >\n      <Box sx={{ position: 'absolute', top: 8, right: 8 }}>\n        <IconButton\n          aria-label=\"close\"\n          onClick={onClose}\n          sx={{ color: 'text.secondary' }}\n        >\n          <CloseIcon />\n        </IconButton>\n      </Box>\n\n      <DialogContent>\n        <Box\n          sx={{\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            textAlign: 'center',\n            py: 3,\n            px: 1,\n          }}\n        >\n          <CheckCircleOutlineIcon\n            sx={{\n              fontSize: 80,\n              color: 'success.main',\n              mb: 2,\n            }}\n          />\n\n          <Typography variant=\"h6\" gutterBottom>\n            Thành công\n          </Typography>\n\n          <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 3 }}>\n            {message}\n          </Typography>\n\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={onClose}\n            sx={{ minWidth: 120 }}\n          >\n            Đóng\n          </Button>\n        </Box>\n      </DialogContent>\n    </Dialog>\n  );\n};\n\nexport default SuccessNotification;\n"], "mappings": "gKAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,MAAM,CACNC,aAAa,CACbC,GAAG,CACHC,UAAU,CACVC,MAAM,CACNC,UAAU,CACVC,KAAK,KACA,eAAe,CAEtB,MAAO,CAAAC,sBAAsB,KAAM,wCAAwC,CAC3E,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQlD,KAAM,CAAAC,UAAU,cAAGd,KAAK,CAACe,UAAU,CAAC,QAAS,CAAAD,UAAUA,CACrDE,KAEC,CACDC,GAAuB,CACvB,CACA,mBAAON,IAAA,CAACJ,KAAK,CAAAW,aAAA,EAACC,SAAS,CAAC,IAAI,CAACF,GAAG,CAAEA,GAAI,EAAKD,KAAK,CAAG,CAAC,CACtD,CAAC,CAAC,CAEF,KAAM,CAAAI,mBAAuD,CAAGC,IAAA,EAI1D,IAJ2D,CAC/DC,IAAI,CACJC,OAAO,CACPC,OACF,CAAC,CAAAH,IAAA,CACC,mBACER,KAAA,CAACZ,MAAM,EACLqB,IAAI,CAAEA,IAAK,CACXG,mBAAmB,CAAEX,UAAW,CAChCY,WAAW,MACXF,OAAO,CAAEA,OAAQ,CACjB,mBAAiB,6BAA6B,CAC9CG,EAAE,CAAE,CACF,oBAAoB,CAAE,CACpBC,YAAY,CAAE,CAAC,CACfC,QAAQ,CAAE,OAAO,CACjBC,MAAM,CAAE,QACV,CACF,CAAE,CAAAC,QAAA,eAEFpB,IAAA,CAACR,GAAG,EAACwB,EAAE,CAAE,CAAEK,QAAQ,CAAE,UAAU,CAAEC,GAAG,CAAE,CAAC,CAAEC,KAAK,CAAE,CAAE,CAAE,CAAAH,QAAA,cAClDpB,IAAA,CAACL,UAAU,EACT,aAAW,OAAO,CAClB6B,OAAO,CAAEX,OAAQ,CACjBG,EAAE,CAAE,CAAES,KAAK,CAAE,gBAAiB,CAAE,CAAAL,QAAA,cAEhCpB,IAAA,CAACF,SAAS,GAAE,CAAC,CACH,CAAC,CACV,CAAC,cAENE,IAAA,CAACT,aAAa,EAAA6B,QAAA,cACZlB,KAAA,CAACV,GAAG,EACFwB,EAAE,CAAE,CACFU,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,QAAQ,CACnBC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CACN,CAAE,CAAAX,QAAA,eAEFpB,IAAA,CAACH,sBAAsB,EACrBmB,EAAE,CAAE,CACFgB,QAAQ,CAAE,EAAE,CACZP,KAAK,CAAE,cAAc,CACrBQ,EAAE,CAAE,CACN,CAAE,CACH,CAAC,cAEFjC,IAAA,CAACP,UAAU,EAACyC,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAf,QAAA,CAAC,kBAEtC,CAAY,CAAC,cAEbpB,IAAA,CAACP,UAAU,EAACyC,OAAO,CAAC,OAAO,CAACT,KAAK,CAAC,gBAAgB,CAACT,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,CAC9DR,OAAO,CACE,CAAC,cAEbZ,IAAA,CAACN,MAAM,EACLwC,OAAO,CAAC,WAAW,CACnBT,KAAK,CAAC,SAAS,CACfD,OAAO,CAAEX,OAAQ,CACjBG,EAAE,CAAE,CAAEoB,QAAQ,CAAE,GAAI,CAAE,CAAAhB,QAAA,CACvB,cAED,CAAQ,CAAC,EACN,CAAC,CACO,CAAC,EACV,CAAC,CAEb,CAAC,CAED,cAAe,CAAAX,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}