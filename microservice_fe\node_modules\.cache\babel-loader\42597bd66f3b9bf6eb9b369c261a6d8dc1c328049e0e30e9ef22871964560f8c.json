{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Dialog,DialogTitle,DialogContent,DialogActions,Button,TextField,Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Paper,Typography,InputAdornment,Avatar,useTheme}from'@mui/material';import SearchIcon from'@mui/icons-material/Search';import AddIcon from'@mui/icons-material/Add';import PhoneIcon from'@mui/icons-material/Phone';import EmailIcon from'@mui/icons-material/Email';import LocationOnIcon from'@mui/icons-material/LocationOn';import{customerService}from'../../services/customer/customerService';import{LoadingSpinner,ErrorAlert}from'../common';import CustomerForm from'./CustomerForm';import{parseDate}from'../../utils/dateUtils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CustomerDialog=_ref=>{let{open,onClose,onSelectCustomer}=_ref;const[customers,setCustomers]=useState([]);const[filteredCustomers,setFilteredCustomers]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[searchTerm,setSearchTerm]=useState('');const[showAddCustomerForm,setShowAddCustomerForm]=useState(false);const[selectedCustomer,setSelectedCustomer]=useState(null);const theme=useTheme();useEffect(()=>{const fetchCustomers=async()=>{try{const data=await customerService.getAllCustomers();// Sắp xếp theo thời gian tạo mới nhất\nconst sortedData=[...data].sort((a,b)=>{const dateA=parseDate(a.createdAt);const dateB=parseDate(b.createdAt);return dateB.getTime()-dateA.getTime();});setCustomers(sortedData);setFilteredCustomers(sortedData);}catch(err){setError(err.message||'Đã xảy ra lỗi khi tải danh sách khách hàng');}finally{setLoading(false);}};if(open){fetchCustomers();}},[open]);const handleSearch=async()=>{if(!searchTerm.trim()){setFilteredCustomers(customers);return;}try{setLoading(true);// Tìm kiếm theo cả tên và số điện thoại\nconst results=await customerService.searchCustomers(searchTerm,searchTerm);setFilteredCustomers(results);if(results.length===0){setError('Không tìm thấy khách hàng nào phù hợp');}else{setError(null);}}catch(err){console.error('Error searching customers:',err);setError(err.message||'Đã xảy ra lỗi khi tìm kiếm khách hàng');}finally{setLoading(false);}};const handleSearchChange=e=>{setSearchTerm(e.target.value);if(!e.target.value.trim()){setFilteredCustomers(customers);}};// Xử lý phím Enter đã được chuyển sang onKeyDown trực tiếp\nconst handleAddCustomer=()=>{setShowAddCustomerForm(true);};// Xử lý sau khi thêm khách hàng mới\nconst handleCustomerAdded=async _newCustomer=>{setShowAddCustomerForm(false);setLoading(true);try{const data=await customerService.getAllCustomers();// Sắp xếp theo thời gian tạo mới nhất\nconst sortedData=[...data].sort((a,b)=>{const dateA=parseDate(a.createdAt);const dateB=parseDate(b.createdAt);return dateB.getTime()-dateA.getTime();});setCustomers(sortedData);setFilteredCustomers(sortedData);}catch(err){setError(err.message||'Đã xảy ra lỗi khi tải lại danh sách khách hàng');}finally{setLoading(false);}};const handleSelectCustomer=customer=>{setSelectedCustomer(customer);onSelectCustomer(customer);onClose();};if(showAddCustomerForm){return/*#__PURE__*/_jsxs(Dialog,{open:open,onClose:onClose,maxWidth:\"md\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Th\\xEAm kh\\xE1ch h\\xE0ng m\\u1EDBi\"}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsx(CustomerForm,{onSave:handleCustomerAdded,onCancel:()=>setShowAddCustomerForm(false)})})]});}return/*#__PURE__*/_jsxs(Dialog,{open:open,onClose:onClose,maxWidth:\"md\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{sx:{borderBottom:\"1px solid \".concat(theme.palette.divider),pb:2},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:'bold',color:theme.palette.primary.main},children:\"Ch\\u1ECDn kh\\xE1ch h\\xE0ng\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",startIcon:/*#__PURE__*/_jsx(AddIcon,{}),onClick:handleAddCustomer,sx:{borderRadius:'20px',px:2},children:\"Th\\xEAm kh\\xE1ch h\\xE0ng\"})]})}),/*#__PURE__*/_jsxs(DialogContent,{sx:{pt:3},children:[error&&/*#__PURE__*/_jsx(ErrorAlert,{message:error}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3,display:'flex',gap:1},children:[/*#__PURE__*/_jsx(TextField,{fullWidth:true,placeholder:\"T\\xECm ki\\u1EBFm theo t\\xEAn ho\\u1EB7c s\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i\",value:searchTerm,onChange:handleSearchChange,onKeyDown:e=>e.key==='Enter'&&handleSearch(),InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(SearchIcon,{color:\"action\"})})},sx:{'& .MuiOutlinedInput-root':{borderRadius:'20px'}}}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",onClick:handleSearch,sx:{borderRadius:'20px',px:3},children:\"T\\xECm ki\\u1EBFm\"})]}),loading?/*#__PURE__*/_jsx(LoadingSpinner,{}):/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Box,{sx:{mb:2},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",children:[\"Hi\\u1EC3n th\\u1ECB \",filteredCustomers.length,\" kh\\xE1ch h\\xE0ng (s\\u1EAFp x\\u1EBFp theo th\\u1EDDi gian t\\u1EA1o m\\u1EDBi nh\\u1EA5t)\"]})}),/*#__PURE__*/_jsx(TableContainer,{component:Paper,sx:{borderRadius:'8px',boxShadow:'0 2px 8px rgba(0,0,0,0.05)','& .MuiTableCell-head':{backgroundColor:theme.palette.primary.light,color:theme.palette.primary.contrastText,fontWeight:'bold'}},children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"T\\xEAn kh\\xE1ch h\\xE0ng\"}),/*#__PURE__*/_jsx(TableCell,{children:\"C\\xF4ng ty\"}),/*#__PURE__*/_jsx(TableCell,{children:\"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Email\"}),/*#__PURE__*/_jsx(TableCell,{children:\"\\u0110\\u1ECBa ch\\u1EC9\"}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:\"Thao t\\xE1c\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:filteredCustomers.length===0?/*#__PURE__*/_jsx(TableRow,{children:/*#__PURE__*/_jsx(TableCell,{colSpan:6,align:\"center\",children:/*#__PURE__*/_jsxs(Box,{sx:{py:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:\"Kh\\xF4ng t\\xECm th\\u1EA5y kh\\xE1ch h\\xE0ng n\\xE0o\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mt:1},children:\"Th\\u1EED t\\xECm ki\\u1EBFm v\\u1EDBi t\\u1EEB kh\\xF3a kh\\xE1c ho\\u1EB7c th\\xEAm kh\\xE1ch h\\xE0ng m\\u1EDBi\"})]})})}):filteredCustomers.map(customer=>/*#__PURE__*/_jsxs(TableRow,{hover:true,sx:{cursor:'pointer','&:hover':{backgroundColor:theme.palette.action.hover}},onClick:()=>handleSelectCustomer(customer),children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Avatar,{sx:{bgcolor:theme.palette.primary.main,width:32,height:32,mr:1,fontSize:'0.9rem'},children:customer.fullName.charAt(0).toUpperCase()}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{fontWeight:'medium'},children:customer.fullName})]})}),/*#__PURE__*/_jsx(TableCell,{children:customer.companyName||'-'}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(PhoneIcon,{fontSize:\"small\",sx:{mr:0.5,color:theme.palette.text.secondary}}),customer.phoneNumber]})}),/*#__PURE__*/_jsx(TableCell,{children:customer.email?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(EmailIcon,{fontSize:\"small\",sx:{mr:0.5,color:theme.palette.text.secondary}}),customer.email]}):'-'}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(LocationOnIcon,{fontSize:\"small\",sx:{mr:0.5,color:theme.palette.text.secondary}}),customer.address]})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",size:\"small\",onClick:e=>{e.stopPropagation();handleSelectCustomer(customer);},sx:{borderRadius:'20px',px:2},children:\"Ch\\u1ECDn\"})})]},customer.id))})]})})]})]}),/*#__PURE__*/_jsx(DialogActions,{sx:{borderTop:\"1px solid \".concat(theme.palette.divider),p:2},children:/*#__PURE__*/_jsx(Button,{onClick:onClose,color:\"primary\",variant:\"outlined\",sx:{borderRadius:'20px',px:3},children:\"\\u0110\\xF3ng\"})})]});};export default CustomerDialog;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Box", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Typography", "InputAdornment", "Avatar", "useTheme", "SearchIcon", "AddIcon", "PhoneIcon", "EmailIcon", "LocationOnIcon", "customerService", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CustomerForm", "parseDate", "jsx", "_jsx", "jsxs", "_jsxs", "CustomerDialog", "_ref", "open", "onClose", "onSelectCustomer", "customers", "setCustomers", "filteredCustomers", "setFilteredCustomers", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "showAddCustomerForm", "setShowAddCustomerForm", "selectedCustomer", "setSelectedCustomer", "theme", "fetchCustomers", "data", "getAllCustomers", "sortedData", "sort", "a", "b", "dateA", "createdAt", "dateB", "getTime", "err", "message", "handleSearch", "trim", "results", "searchCustomers", "length", "console", "handleSearchChange", "e", "target", "value", "handleAddCustomer", "handleCustomerAdded", "_newCustomer", "handleSelectCustomer", "customer", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "onSave", "onCancel", "sx", "borderBottom", "concat", "palette", "divider", "pb", "display", "justifyContent", "alignItems", "variant", "fontWeight", "color", "primary", "main", "startIcon", "onClick", "borderRadius", "px", "pt", "mb", "gap", "placeholder", "onChange", "onKeyDown", "key", "InputProps", "startAdornment", "position", "component", "boxShadow", "backgroundColor", "light", "contrastText", "align", "colSpan", "py", "mt", "map", "hover", "cursor", "action", "bgcolor", "width", "height", "mr", "fontSize", "fullName", "char<PERSON>t", "toUpperCase", "companyName", "text", "secondary", "phoneNumber", "email", "address", "size", "stopPropagation", "id", "borderTop", "p"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/customer/CustomerDialog.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  TextField,\n  Box,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  IconButton,\n  Typography,\n  InputAdornment,\n  Avatar,\n  Chip,\n  Divider,\n  Card,\n  CardContent,\n  useTheme,\n} from '@mui/material';\nimport SearchIcon from '@mui/icons-material/Search';\nimport AddIcon from '@mui/icons-material/Add';\nimport PersonIcon from '@mui/icons-material/Person';\nimport BusinessIcon from '@mui/icons-material/Business';\nimport PhoneIcon from '@mui/icons-material/Phone';\nimport EmailIcon from '@mui/icons-material/Email';\nimport LocationOnIcon from '@mui/icons-material/LocationOn';\nimport { Customer } from '../../models';\nimport { customerService } from '../../services/customer/customerService';\nimport { LoadingSpinner, ErrorAlert } from '../common';\nimport CustomerForm from './CustomerForm';\nimport { parseDate, formatDateLocalized } from '../../utils/dateUtils';\n\ninterface CustomerDialogProps {\n  open: boolean;\n  onClose: () => void;\n  onSelectCustomer: (customer: Customer) => void;\n}\n\nconst CustomerDialog: React.FC<CustomerDialogProps> = ({ open, onClose, onSelectCustomer }) => {\n  const [customers, setCustomers] = useState<Customer[]>([]);\n  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showAddCustomerForm, setShowAddCustomerForm] = useState(false);\n  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);\n  const theme = useTheme();\n\n  useEffect(() => {\n    const fetchCustomers = async () => {\n      try {\n        const data = await customerService.getAllCustomers();\n        // Sắp xếp theo thời gian tạo mới nhất\n        const sortedData = [...data].sort((a, b) => {\n          const dateA = parseDate(a.createdAt);\n          const dateB = parseDate(b.createdAt);\n          return dateB.getTime() - dateA.getTime();\n        });\n        setCustomers(sortedData);\n        setFilteredCustomers(sortedData);\n      } catch (err: any) {\n        setError(err.message || 'Đã xảy ra lỗi khi tải danh sách khách hàng');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (open) {\n      fetchCustomers();\n    }\n  }, [open]);\n\n  const handleSearch = async () => {\n    if (!searchTerm.trim()) {\n      setFilteredCustomers(customers);\n      return;\n    }\n\n    try {\n      setLoading(true);\n      // Tìm kiếm theo cả tên và số điện thoại\n      const results = await customerService.searchCustomers(searchTerm, searchTerm);\n      setFilteredCustomers(results);\n\n      if (results.length === 0) {\n        setError('Không tìm thấy khách hàng nào phù hợp');\n      } else {\n        setError(null);\n      }\n    } catch (err: any) {\n      console.error('Error searching customers:', err);\n      setError(err.message || 'Đã xảy ra lỗi khi tìm kiếm khách hàng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setSearchTerm(e.target.value);\n    if (!e.target.value.trim()) {\n      setFilteredCustomers(customers);\n    }\n  };\n\n  // Xử lý phím Enter đã được chuyển sang onKeyDown trực tiếp\n\n  const handleAddCustomer = () => {\n    setShowAddCustomerForm(true);\n  };\n\n  // Xử lý sau khi thêm khách hàng mới\n  const handleCustomerAdded = async (_newCustomer: Customer) => {\n    setShowAddCustomerForm(false);\n    setLoading(true);\n    try {\n      const data = await customerService.getAllCustomers();\n      // Sắp xếp theo thời gian tạo mới nhất\n      const sortedData = [...data].sort((a, b) => {\n        const dateA = parseDate(a.createdAt);\n        const dateB = parseDate(b.createdAt);\n        return dateB.getTime() - dateA.getTime();\n      });\n      setCustomers(sortedData);\n      setFilteredCustomers(sortedData);\n    } catch (err: any) {\n      setError(err.message || 'Đã xảy ra lỗi khi tải lại danh sách khách hàng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSelectCustomer = (customer: Customer) => {\n    setSelectedCustomer(customer);\n    onSelectCustomer(customer);\n    onClose();\n  };\n\n  if (showAddCustomerForm) {\n    return (\n      <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\n        <DialogTitle>Thêm khách hàng mới</DialogTitle>\n        <DialogContent>\n          <CustomerForm\n            onSave={handleCustomerAdded}\n            onCancel={() => setShowAddCustomerForm(false)}\n          />\n        </DialogContent>\n      </Dialog>\n    );\n  }\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\n      <DialogTitle sx={{\n        borderBottom: `1px solid ${theme.palette.divider}`,\n        pb: 2\n      }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>\n            Chọn khách hàng\n          </Typography>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<AddIcon />}\n            onClick={handleAddCustomer}\n            sx={{\n              borderRadius: '20px',\n              px: 2\n            }}\n          >\n            Thêm khách hàng\n          </Button>\n        </Box>\n      </DialogTitle>\n      <DialogContent sx={{ pt: 3 }}>\n        {error && <ErrorAlert message={error} />}\n\n        <Box sx={{ mb: 3, display: 'flex', gap: 1 }}>\n          <TextField\n            fullWidth\n            placeholder=\"Tìm kiếm theo tên hoặc số điện thoại\"\n            value={searchTerm}\n            onChange={handleSearchChange}\n            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon color=\"action\" />\n                </InputAdornment>\n              ),\n            }}\n            sx={{\n              '& .MuiOutlinedInput-root': {\n                borderRadius: '20px',\n              }\n            }}\n          />\n          <Button\n            variant=\"outlined\"\n            onClick={handleSearch}\n            sx={{\n              borderRadius: '20px',\n              px: 3\n            }}\n          >\n            Tìm kiếm\n          </Button>\n        </Box>\n\n        {loading ? (\n          <LoadingSpinner />\n        ) : (\n          <Box>\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Hiển thị {filteredCustomers.length} khách hàng (sắp xếp theo thời gian tạo mới nhất)\n              </Typography>\n            </Box>\n\n            <TableContainer\n              component={Paper}\n              sx={{\n                borderRadius: '8px',\n                boxShadow: '0 2px 8px rgba(0,0,0,0.05)',\n                '& .MuiTableCell-head': {\n                  backgroundColor: theme.palette.primary.light,\n                  color: theme.palette.primary.contrastText,\n                  fontWeight: 'bold',\n                }\n              }}\n            >\n              <Table>\n                <TableHead>\n                  <TableRow>\n                    <TableCell>Tên khách hàng</TableCell>\n                    <TableCell>Công ty</TableCell>\n                    <TableCell>Số điện thoại</TableCell>\n                    <TableCell>Email</TableCell>\n                    <TableCell>Địa chỉ</TableCell>\n                    <TableCell align=\"center\">Thao tác</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {filteredCustomers.length === 0 ? (\n                    <TableRow>\n                      <TableCell colSpan={6} align=\"center\">\n                        <Box sx={{ py: 3 }}>\n                          <Typography variant=\"body1\">Không tìm thấy khách hàng nào</Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                            Thử tìm kiếm với từ khóa khác hoặc thêm khách hàng mới\n                          </Typography>\n                        </Box>\n                      </TableCell>\n                    </TableRow>\n                  ) : (\n                    filteredCustomers.map((customer) => (\n                      <TableRow\n                        key={customer.id}\n                        hover\n                        sx={{\n                          cursor: 'pointer',\n                          '&:hover': {\n                            backgroundColor: theme.palette.action.hover,\n                          }\n                        }}\n                        onClick={() => handleSelectCustomer(customer)}\n                      >\n                        <TableCell>\n                          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                            <Avatar\n                              sx={{\n                                bgcolor: theme.palette.primary.main,\n                                width: 32,\n                                height: 32,\n                                mr: 1,\n                                fontSize: '0.9rem'\n                              }}\n                            >\n                              {customer.fullName.charAt(0).toUpperCase()}\n                            </Avatar>\n                            <Typography variant=\"body1\" sx={{ fontWeight: 'medium' }}>\n                              {customer.fullName}\n                            </Typography>\n                          </Box>\n                        </TableCell>\n                        <TableCell>{customer.companyName || '-'}</TableCell>\n                        <TableCell>\n                          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                            <PhoneIcon fontSize=\"small\" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />\n                            {customer.phoneNumber}\n                          </Box>\n                        </TableCell>\n                        <TableCell>\n                          {customer.email ? (\n                            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                              <EmailIcon fontSize=\"small\" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />\n                              {customer.email}\n                            </Box>\n                          ) : '-'}\n                        </TableCell>\n                        <TableCell>\n                          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                            <LocationOnIcon fontSize=\"small\" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />\n                            {customer.address}\n                          </Box>\n                        </TableCell>\n                        <TableCell align=\"center\">\n                          <Button\n                            variant=\"contained\"\n                            size=\"small\"\n                            onClick={(e) => {\n                              e.stopPropagation();\n                              handleSelectCustomer(customer);\n                            }}\n                            sx={{\n                              borderRadius: '20px',\n                              px: 2\n                            }}\n                          >\n                            Chọn\n                          </Button>\n                        </TableCell>\n                      </TableRow>\n                    ))\n                  )}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </Box>\n        )}\n      </DialogContent>\n      <DialogActions sx={{ borderTop: `1px solid ${theme.palette.divider}`, p: 2 }}>\n        <Button\n          onClick={onClose}\n          color=\"primary\"\n          variant=\"outlined\"\n          sx={{\n            borderRadius: '20px',\n            px: 3\n          }}\n        >\n          Đóng\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default CustomerDialog;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,MAAM,CACNC,SAAS,CACTC,GAAG,CACHC,KAAK,CACLC,SAAS,CACTC,SAAS,CACTC,cAAc,CACdC,SAAS,CACTC,QAAQ,CACRC,KAAK,CAELC,UAAU,CACVC,cAAc,CACdC,MAAM,CAKNC,QAAQ,KACH,eAAe,CACtB,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAG7C,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAE3D,OAASC,eAAe,KAAQ,yCAAyC,CACzE,OAASC,cAAc,CAAEC,UAAU,KAAQ,WAAW,CACtD,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CACzC,OAASC,SAAS,KAA6B,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQvE,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAAyC,IAAxC,CAAEC,IAAI,CAAEC,OAAO,CAAEC,gBAAiB,CAAC,CAAAH,IAAA,CACxF,KAAM,CAACI,SAAS,CAAEC,YAAY,CAAC,CAAGxC,QAAQ,CAAa,EAAE,CAAC,CAC1D,KAAM,CAACyC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1C,QAAQ,CAAa,EAAE,CAAC,CAC1E,KAAM,CAAC2C,OAAO,CAAEC,UAAU,CAAC,CAAG5C,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC6C,KAAK,CAAEC,QAAQ,CAAC,CAAG9C,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAAC+C,UAAU,CAAEC,aAAa,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACiD,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CACrE,KAAM,CAACmD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpD,QAAQ,CAAkB,IAAI,CAAC,CAC/E,KAAM,CAAAqD,KAAK,CAAGlC,QAAQ,CAAC,CAAC,CAExBlB,SAAS,CAAC,IAAM,CACd,KAAM,CAAAqD,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACF,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAA9B,eAAe,CAAC+B,eAAe,CAAC,CAAC,CACpD;AACA,KAAM,CAAAC,UAAU,CAAG,CAAC,GAAGF,IAAI,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CAC1C,KAAM,CAAAC,KAAK,CAAGhC,SAAS,CAAC8B,CAAC,CAACG,SAAS,CAAC,CACpC,KAAM,CAAAC,KAAK,CAAGlC,SAAS,CAAC+B,CAAC,CAACE,SAAS,CAAC,CACpC,MAAO,CAAAC,KAAK,CAACC,OAAO,CAAC,CAAC,CAAGH,KAAK,CAACG,OAAO,CAAC,CAAC,CAC1C,CAAC,CAAC,CACFxB,YAAY,CAACiB,UAAU,CAAC,CACxBf,oBAAoB,CAACe,UAAU,CAAC,CAClC,CAAE,MAAOQ,GAAQ,CAAE,CACjBnB,QAAQ,CAACmB,GAAG,CAACC,OAAO,EAAI,4CAA4C,CAAC,CACvE,CAAC,OAAS,CACRtB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAIR,IAAI,CAAE,CACRkB,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAAClB,IAAI,CAAC,CAAC,CAEV,KAAM,CAAA+B,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CAACpB,UAAU,CAACqB,IAAI,CAAC,CAAC,CAAE,CACtB1B,oBAAoB,CAACH,SAAS,CAAC,CAC/B,OACF,CAEA,GAAI,CACFK,UAAU,CAAC,IAAI,CAAC,CAChB;AACA,KAAM,CAAAyB,OAAO,CAAG,KAAM,CAAA5C,eAAe,CAAC6C,eAAe,CAACvB,UAAU,CAAEA,UAAU,CAAC,CAC7EL,oBAAoB,CAAC2B,OAAO,CAAC,CAE7B,GAAIA,OAAO,CAACE,MAAM,GAAK,CAAC,CAAE,CACxBzB,QAAQ,CAAC,uCAAuC,CAAC,CACnD,CAAC,IAAM,CACLA,QAAQ,CAAC,IAAI,CAAC,CAChB,CACF,CAAE,MAAOmB,GAAQ,CAAE,CACjBO,OAAO,CAAC3B,KAAK,CAAC,4BAA4B,CAAEoB,GAAG,CAAC,CAChDnB,QAAQ,CAACmB,GAAG,CAACC,OAAO,EAAI,uCAAuC,CAAC,CAClE,CAAC,OAAS,CACRtB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA6B,kBAAkB,CAAIC,CAAsC,EAAK,CACrE1B,aAAa,CAAC0B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAC7B,GAAI,CAACF,CAAC,CAACC,MAAM,CAACC,KAAK,CAACR,IAAI,CAAC,CAAC,CAAE,CAC1B1B,oBAAoB,CAACH,SAAS,CAAC,CACjC,CACF,CAAC,CAED;AAEA,KAAM,CAAAsC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B3B,sBAAsB,CAAC,IAAI,CAAC,CAC9B,CAAC,CAED;AACA,KAAM,CAAA4B,mBAAmB,CAAG,KAAO,CAAAC,YAAsB,EAAK,CAC5D7B,sBAAsB,CAAC,KAAK,CAAC,CAC7BN,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAW,IAAI,CAAG,KAAM,CAAA9B,eAAe,CAAC+B,eAAe,CAAC,CAAC,CACpD;AACA,KAAM,CAAAC,UAAU,CAAG,CAAC,GAAGF,IAAI,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CAC1C,KAAM,CAAAC,KAAK,CAAGhC,SAAS,CAAC8B,CAAC,CAACG,SAAS,CAAC,CACpC,KAAM,CAAAC,KAAK,CAAGlC,SAAS,CAAC+B,CAAC,CAACE,SAAS,CAAC,CACpC,MAAO,CAAAC,KAAK,CAACC,OAAO,CAAC,CAAC,CAAGH,KAAK,CAACG,OAAO,CAAC,CAAC,CAC1C,CAAC,CAAC,CACFxB,YAAY,CAACiB,UAAU,CAAC,CACxBf,oBAAoB,CAACe,UAAU,CAAC,CAClC,CAAE,MAAOQ,GAAQ,CAAE,CACjBnB,QAAQ,CAACmB,GAAG,CAACC,OAAO,EAAI,gDAAgD,CAAC,CAC3E,CAAC,OAAS,CACRtB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAoC,oBAAoB,CAAIC,QAAkB,EAAK,CACnD7B,mBAAmB,CAAC6B,QAAQ,CAAC,CAC7B3C,gBAAgB,CAAC2C,QAAQ,CAAC,CAC1B5C,OAAO,CAAC,CAAC,CACX,CAAC,CAED,GAAIY,mBAAmB,CAAE,CACvB,mBACEhB,KAAA,CAAC/B,MAAM,EAACkC,IAAI,CAAEA,IAAK,CAACC,OAAO,CAAEA,OAAQ,CAAC6C,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAC,QAAA,eAC3DrD,IAAA,CAAC5B,WAAW,EAAAiF,QAAA,CAAC,mCAAmB,CAAa,CAAC,cAC9CrD,IAAA,CAAC3B,aAAa,EAAAgF,QAAA,cACZrD,IAAA,CAACH,YAAY,EACXyD,MAAM,CAAEP,mBAAoB,CAC5BQ,QAAQ,CAAEA,CAAA,GAAMpC,sBAAsB,CAAC,KAAK,CAAE,CAC/C,CAAC,CACW,CAAC,EACV,CAAC,CAEb,CAEA,mBACEjB,KAAA,CAAC/B,MAAM,EAACkC,IAAI,CAAEA,IAAK,CAACC,OAAO,CAAEA,OAAQ,CAAC6C,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAC,QAAA,eAC3DrD,IAAA,CAAC5B,WAAW,EAACoF,EAAE,CAAE,CACfC,YAAY,cAAAC,MAAA,CAAepC,KAAK,CAACqC,OAAO,CAACC,OAAO,CAAE,CAClDC,EAAE,CAAE,CACN,CAAE,CAAAR,QAAA,cACAnD,KAAA,CAACzB,GAAG,EAAC+E,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAX,QAAA,eAClFrD,IAAA,CAACf,UAAU,EAACgF,OAAO,CAAC,IAAI,CAACT,EAAE,CAAE,CAAEU,UAAU,CAAE,MAAM,CAAEC,KAAK,CAAE7C,KAAK,CAACqC,OAAO,CAACS,OAAO,CAACC,IAAK,CAAE,CAAAhB,QAAA,CAAC,4BAExF,CAAY,CAAC,cACbrD,IAAA,CAACzB,MAAM,EACL0F,OAAO,CAAC,WAAW,CACnBE,KAAK,CAAC,SAAS,CACfG,SAAS,cAAEtE,IAAA,CAACV,OAAO,GAAE,CAAE,CACvBiF,OAAO,CAAEzB,iBAAkB,CAC3BU,EAAE,CAAE,CACFgB,YAAY,CAAE,MAAM,CACpBC,EAAE,CAAE,CACN,CAAE,CAAApB,QAAA,CACH,0BAED,CAAQ,CAAC,EACN,CAAC,CACK,CAAC,cACdnD,KAAA,CAAC7B,aAAa,EAACmF,EAAE,CAAE,CAAEkB,EAAE,CAAE,CAAE,CAAE,CAAArB,QAAA,EAC1BvC,KAAK,eAAId,IAAA,CAACJ,UAAU,EAACuC,OAAO,CAAErB,KAAM,CAAE,CAAC,cAExCZ,KAAA,CAACzB,GAAG,EAAC+E,EAAE,CAAE,CAAEmB,EAAE,CAAE,CAAC,CAAEb,OAAO,CAAE,MAAM,CAAEc,GAAG,CAAE,CAAE,CAAE,CAAAvB,QAAA,eAC1CrD,IAAA,CAACxB,SAAS,EACR4E,SAAS,MACTyB,WAAW,CAAC,0EAAsC,CAClDhC,KAAK,CAAE7B,UAAW,CAClB8D,QAAQ,CAAEpC,kBAAmB,CAC7BqC,SAAS,CAAGpC,CAAC,EAAKA,CAAC,CAACqC,GAAG,GAAK,OAAO,EAAI5C,YAAY,CAAC,CAAE,CACtD6C,UAAU,CAAE,CACVC,cAAc,cACZlF,IAAA,CAACd,cAAc,EAACiG,QAAQ,CAAC,OAAO,CAAA9B,QAAA,cAC9BrD,IAAA,CAACX,UAAU,EAAC8E,KAAK,CAAC,QAAQ,CAAE,CAAC,CACf,CAEpB,CAAE,CACFX,EAAE,CAAE,CACF,0BAA0B,CAAE,CAC1BgB,YAAY,CAAE,MAChB,CACF,CAAE,CACH,CAAC,cACFxE,IAAA,CAACzB,MAAM,EACL0F,OAAO,CAAC,UAAU,CAClBM,OAAO,CAAEnC,YAAa,CACtBoB,EAAE,CAAE,CACFgB,YAAY,CAAE,MAAM,CACpBC,EAAE,CAAE,CACN,CAAE,CAAApB,QAAA,CACH,kBAED,CAAQ,CAAC,EACN,CAAC,CAELzC,OAAO,cACNZ,IAAA,CAACL,cAAc,GAAE,CAAC,cAElBO,KAAA,CAACzB,GAAG,EAAA4E,QAAA,eACFrD,IAAA,CAACvB,GAAG,EAAC+E,EAAE,CAAE,CAAEmB,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,cACjBnD,KAAA,CAACjB,UAAU,EAACgF,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAAd,QAAA,EAAC,qBACxC,CAAC3C,iBAAiB,CAAC8B,MAAM,CAAC,uFACrC,EAAY,CAAC,CACV,CAAC,cAENxC,IAAA,CAACnB,cAAc,EACbuG,SAAS,CAAEpG,KAAM,CACjBwE,EAAE,CAAE,CACFgB,YAAY,CAAE,KAAK,CACnBa,SAAS,CAAE,4BAA4B,CACvC,sBAAsB,CAAE,CACtBC,eAAe,CAAEhE,KAAK,CAACqC,OAAO,CAACS,OAAO,CAACmB,KAAK,CAC5CpB,KAAK,CAAE7C,KAAK,CAACqC,OAAO,CAACS,OAAO,CAACoB,YAAY,CACzCtB,UAAU,CAAE,MACd,CACF,CAAE,CAAAb,QAAA,cAEFnD,KAAA,CAACxB,KAAK,EAAA2E,QAAA,eACJrD,IAAA,CAAClB,SAAS,EAAAuE,QAAA,cACRnD,KAAA,CAACnB,QAAQ,EAAAsE,QAAA,eACPrD,IAAA,CAACpB,SAAS,EAAAyE,QAAA,CAAC,yBAAc,CAAW,CAAC,cACrCrD,IAAA,CAACpB,SAAS,EAAAyE,QAAA,CAAC,YAAO,CAAW,CAAC,cAC9BrD,IAAA,CAACpB,SAAS,EAAAyE,QAAA,CAAC,mCAAa,CAAW,CAAC,cACpCrD,IAAA,CAACpB,SAAS,EAAAyE,QAAA,CAAC,OAAK,CAAW,CAAC,cAC5BrD,IAAA,CAACpB,SAAS,EAAAyE,QAAA,CAAC,wBAAO,CAAW,CAAC,cAC9BrD,IAAA,CAACpB,SAAS,EAAC6G,KAAK,CAAC,QAAQ,CAAApC,QAAA,CAAC,aAAQ,CAAW,CAAC,EACtC,CAAC,CACF,CAAC,cACZrD,IAAA,CAACrB,SAAS,EAAA0E,QAAA,CACP3C,iBAAiB,CAAC8B,MAAM,GAAK,CAAC,cAC7BxC,IAAA,CAACjB,QAAQ,EAAAsE,QAAA,cACPrD,IAAA,CAACpB,SAAS,EAAC8G,OAAO,CAAE,CAAE,CAACD,KAAK,CAAC,QAAQ,CAAApC,QAAA,cACnCnD,KAAA,CAACzB,GAAG,EAAC+E,EAAE,CAAE,CAAEmC,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,eACjBrD,IAAA,CAACf,UAAU,EAACgF,OAAO,CAAC,OAAO,CAAAZ,QAAA,CAAC,mDAA6B,CAAY,CAAC,cACtErD,IAAA,CAACf,UAAU,EAACgF,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAACX,EAAE,CAAE,CAAEoC,EAAE,CAAE,CAAE,CAAE,CAAAvC,QAAA,CAAC,wGAElE,CAAY,CAAC,EACV,CAAC,CACG,CAAC,CACJ,CAAC,CAEX3C,iBAAiB,CAACmF,GAAG,CAAE3C,QAAQ,eAC7BhD,KAAA,CAACnB,QAAQ,EAEP+G,KAAK,MACLtC,EAAE,CAAE,CACFuC,MAAM,CAAE,SAAS,CACjB,SAAS,CAAE,CACTT,eAAe,CAAEhE,KAAK,CAACqC,OAAO,CAACqC,MAAM,CAACF,KACxC,CACF,CAAE,CACFvB,OAAO,CAAEA,CAAA,GAAMtB,oBAAoB,CAACC,QAAQ,CAAE,CAAAG,QAAA,eAE9CrD,IAAA,CAACpB,SAAS,EAAAyE,QAAA,cACRnD,KAAA,CAACzB,GAAG,EAAC+E,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAX,QAAA,eACjDrD,IAAA,CAACb,MAAM,EACLqE,EAAE,CAAE,CACFyC,OAAO,CAAE3E,KAAK,CAACqC,OAAO,CAACS,OAAO,CAACC,IAAI,CACnC6B,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,EAAE,CAAE,CAAC,CACLC,QAAQ,CAAE,QACZ,CAAE,CAAAhD,QAAA,CAEDH,QAAQ,CAACoD,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACpC,CAAC,cACTxG,IAAA,CAACf,UAAU,EAACgF,OAAO,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEU,UAAU,CAAE,QAAS,CAAE,CAAAb,QAAA,CACtDH,QAAQ,CAACoD,QAAQ,CACR,CAAC,EACV,CAAC,CACG,CAAC,cACZtG,IAAA,CAACpB,SAAS,EAAAyE,QAAA,CAAEH,QAAQ,CAACuD,WAAW,EAAI,GAAG,CAAY,CAAC,cACpDzG,IAAA,CAACpB,SAAS,EAAAyE,QAAA,cACRnD,KAAA,CAACzB,GAAG,EAAC+E,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAX,QAAA,eACjDrD,IAAA,CAACT,SAAS,EAAC8G,QAAQ,CAAC,OAAO,CAAC7C,EAAE,CAAE,CAAE4C,EAAE,CAAE,GAAG,CAAEjC,KAAK,CAAE7C,KAAK,CAACqC,OAAO,CAAC+C,IAAI,CAACC,SAAU,CAAE,CAAE,CAAC,CACnFzD,QAAQ,CAAC0D,WAAW,EAClB,CAAC,CACG,CAAC,cACZ5G,IAAA,CAACpB,SAAS,EAAAyE,QAAA,CACPH,QAAQ,CAAC2D,KAAK,cACb3G,KAAA,CAACzB,GAAG,EAAC+E,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAX,QAAA,eACjDrD,IAAA,CAACR,SAAS,EAAC6G,QAAQ,CAAC,OAAO,CAAC7C,EAAE,CAAE,CAAE4C,EAAE,CAAE,GAAG,CAAEjC,KAAK,CAAE7C,KAAK,CAACqC,OAAO,CAAC+C,IAAI,CAACC,SAAU,CAAE,CAAE,CAAC,CACnFzD,QAAQ,CAAC2D,KAAK,EACZ,CAAC,CACJ,GAAG,CACE,CAAC,cACZ7G,IAAA,CAACpB,SAAS,EAAAyE,QAAA,cACRnD,KAAA,CAACzB,GAAG,EAAC+E,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAX,QAAA,eACjDrD,IAAA,CAACP,cAAc,EAAC4G,QAAQ,CAAC,OAAO,CAAC7C,EAAE,CAAE,CAAE4C,EAAE,CAAE,GAAG,CAAEjC,KAAK,CAAE7C,KAAK,CAACqC,OAAO,CAAC+C,IAAI,CAACC,SAAU,CAAE,CAAE,CAAC,CACxFzD,QAAQ,CAAC4D,OAAO,EACd,CAAC,CACG,CAAC,cACZ9G,IAAA,CAACpB,SAAS,EAAC6G,KAAK,CAAC,QAAQ,CAAApC,QAAA,cACvBrD,IAAA,CAACzB,MAAM,EACL0F,OAAO,CAAC,WAAW,CACnB8C,IAAI,CAAC,OAAO,CACZxC,OAAO,CAAG5B,CAAC,EAAK,CACdA,CAAC,CAACqE,eAAe,CAAC,CAAC,CACnB/D,oBAAoB,CAACC,QAAQ,CAAC,CAChC,CAAE,CACFM,EAAE,CAAE,CACFgB,YAAY,CAAE,MAAM,CACpBC,EAAE,CAAE,CACN,CAAE,CAAApB,QAAA,CACH,WAED,CAAQ,CAAC,CACA,CAAC,GAhEPH,QAAQ,CAAC+D,EAiEN,CACX,CACF,CACQ,CAAC,EACP,CAAC,CACM,CAAC,EACd,CACN,EACY,CAAC,cAChBjH,IAAA,CAAC1B,aAAa,EAACkF,EAAE,CAAE,CAAE0D,SAAS,cAAAxD,MAAA,CAAepC,KAAK,CAACqC,OAAO,CAACC,OAAO,CAAE,CAAEuD,CAAC,CAAE,CAAE,CAAE,CAAA9D,QAAA,cAC3ErD,IAAA,CAACzB,MAAM,EACLgG,OAAO,CAAEjE,OAAQ,CACjB6D,KAAK,CAAC,SAAS,CACfF,OAAO,CAAC,UAAU,CAClBT,EAAE,CAAE,CACFgB,YAAY,CAAE,MAAM,CACpBC,EAAE,CAAE,CACN,CAAE,CAAApB,QAAA,CACH,cAED,CAAQ,CAAC,CACI,CAAC,EACV,CAAC,CAEb,CAAC,CAED,cAAe,CAAAlD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}