{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useForkRef from '@mui/utils/useForkRef';\nimport setRef from '@mui/utils/setRef';\nimport getReactElementRef from '@mui/utils/getReactElementRef';\nimport exactProp from '@mui/utils/exactProp';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nfunction getContainer(container) {\n  return typeof container === 'function' ? container() : container;\n}\n\n/**\n * Portals provide a first-class way to render children into a DOM node\n * that exists outside the DOM hierarchy of the parent component.\n *\n * Demos:\n *\n * - [Portal](https://mui.com/material-ui/react-portal/)\n *\n * API:\n *\n * - [Portal API](https://mui.com/material-ui/api/portal/)\n */\nconst Portal = /*#__PURE__*/React.forwardRef(function Portal(props, forwardedRef) {\n  const {\n    children,\n    container,\n    disablePortal = false\n  } = props;\n  const [mountNode, setMountNode] = React.useState(null);\n  const handleRef = useForkRef(/*#__PURE__*/React.isValidElement(children) ? getReactElementRef(children) : null, forwardedRef);\n  useEnhancedEffect(() => {\n    if (!disablePortal) {\n      setMountNode(getContainer(container) || document.body);\n    }\n  }, [container, disablePortal]);\n  useEnhancedEffect(() => {\n    if (mountNode && !disablePortal) {\n      setRef(forwardedRef, mountNode);\n      return () => {\n        setRef(forwardedRef, null);\n      };\n    }\n    return undefined;\n  }, [forwardedRef, mountNode, disablePortal]);\n  if (disablePortal) {\n    if (/*#__PURE__*/React.isValidElement(children)) {\n      const newProps = {\n        ref: handleRef\n      };\n      return /*#__PURE__*/React.cloneElement(children, newProps);\n    }\n    return children;\n  }\n  return mountNode ? /*#__PURE__*/ReactDOM.createPortal(children, mountNode) : mountNode;\n});\nprocess.env.NODE_ENV !== \"production\" ? Portal.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The children to render into the `container`.\n   */\n  children: PropTypes.node,\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * You can also provide a callback, which is called in a React layout effect.\n   * This lets you set the container from a ref, and also makes server-side rendering possible.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  Portal['propTypes' + ''] = exactProp(Portal.propTypes);\n}\nexport default Portal;", "map": {"version": 3, "names": ["React", "ReactDOM", "PropTypes", "useEnhancedEffect", "useForkRef", "setRef", "getReactElementRef", "exactProp", "HTMLElementType", "getContainer", "container", "Portal", "forwardRef", "props", "forwardedRef", "children", "disable<PERSON><PERSON><PERSON>", "mountNode", "setMountNode", "useState", "handleRef", "isValidElement", "document", "body", "undefined", "newProps", "ref", "cloneElement", "createPortal", "process", "env", "NODE_ENV", "propTypes", "node", "oneOfType", "func", "bool"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/Portal/Portal.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useForkRef from '@mui/utils/useForkRef';\nimport setRef from '@mui/utils/setRef';\nimport getReactElementRef from '@mui/utils/getReactElementRef';\nimport exactProp from '@mui/utils/exactProp';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nfunction getContainer(container) {\n  return typeof container === 'function' ? container() : container;\n}\n\n/**\n * Portals provide a first-class way to render children into a DOM node\n * that exists outside the DOM hierarchy of the parent component.\n *\n * Demos:\n *\n * - [Portal](https://mui.com/material-ui/react-portal/)\n *\n * API:\n *\n * - [Portal API](https://mui.com/material-ui/api/portal/)\n */\nconst Portal = /*#__PURE__*/React.forwardRef(function Portal(props, forwardedRef) {\n  const {\n    children,\n    container,\n    disablePortal = false\n  } = props;\n  const [mountNode, setMountNode] = React.useState(null);\n  const handleRef = useForkRef(/*#__PURE__*/React.isValidElement(children) ? getReactElementRef(children) : null, forwardedRef);\n  useEnhancedEffect(() => {\n    if (!disablePortal) {\n      setMountNode(getContainer(container) || document.body);\n    }\n  }, [container, disablePortal]);\n  useEnhancedEffect(() => {\n    if (mountNode && !disablePortal) {\n      setRef(forwardedRef, mountNode);\n      return () => {\n        setRef(forwardedRef, null);\n      };\n    }\n    return undefined;\n  }, [forwardedRef, mountNode, disablePortal]);\n  if (disablePortal) {\n    if (/*#__PURE__*/React.isValidElement(children)) {\n      const newProps = {\n        ref: handleRef\n      };\n      return /*#__PURE__*/React.cloneElement(children, newProps);\n    }\n    return children;\n  }\n  return mountNode ? /*#__PURE__*/ReactDOM.createPortal(children, mountNode) : mountNode;\n});\nprocess.env.NODE_ENV !== \"production\" ? Portal.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The children to render into the `container`.\n   */\n  children: PropTypes.node,\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * You can also provide a callback, which is called in a React layout effect.\n   * This lets you set the container from a ref, and also makes server-side rendering possible.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  Portal['propTypes' + ''] = exactProp(Portal.propTypes);\n}\nexport default Portal;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,+BAA+B;AAC9D,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,eAAe,MAAM,4BAA4B;AACxD,SAASC,YAAYA,CAACC,SAAS,EAAE;EAC/B,OAAO,OAAOA,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC,CAAC,GAAGA,SAAS;AAClE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG,aAAaX,KAAK,CAACY,UAAU,CAAC,SAASD,MAAMA,CAACE,KAAK,EAAEC,YAAY,EAAE;EAChF,MAAM;IACJC,QAAQ;IACRL,SAAS;IACTM,aAAa,GAAG;EAClB,CAAC,GAAGH,KAAK;EACT,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAGlB,KAAK,CAACmB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAMC,SAAS,GAAGhB,UAAU,CAAC,aAAaJ,KAAK,CAACqB,cAAc,CAACN,QAAQ,CAAC,GAAGT,kBAAkB,CAACS,QAAQ,CAAC,GAAG,IAAI,EAAED,YAAY,CAAC;EAC7HX,iBAAiB,CAAC,MAAM;IACtB,IAAI,CAACa,aAAa,EAAE;MAClBE,YAAY,CAACT,YAAY,CAACC,SAAS,CAAC,IAAIY,QAAQ,CAACC,IAAI,CAAC;IACxD;EACF,CAAC,EAAE,CAACb,SAAS,EAAEM,aAAa,CAAC,CAAC;EAC9Bb,iBAAiB,CAAC,MAAM;IACtB,IAAIc,SAAS,IAAI,CAACD,aAAa,EAAE;MAC/BX,MAAM,CAACS,YAAY,EAAEG,SAAS,CAAC;MAC/B,OAAO,MAAM;QACXZ,MAAM,CAACS,YAAY,EAAE,IAAI,CAAC;MAC5B,CAAC;IACH;IACA,OAAOU,SAAS;EAClB,CAAC,EAAE,CAACV,YAAY,EAAEG,SAAS,EAAED,aAAa,CAAC,CAAC;EAC5C,IAAIA,aAAa,EAAE;IACjB,IAAI,aAAahB,KAAK,CAACqB,cAAc,CAACN,QAAQ,CAAC,EAAE;MAC/C,MAAMU,QAAQ,GAAG;QACfC,GAAG,EAAEN;MACP,CAAC;MACD,OAAO,aAAapB,KAAK,CAAC2B,YAAY,CAACZ,QAAQ,EAAEU,QAAQ,CAAC;IAC5D;IACA,OAAOV,QAAQ;EACjB;EACA,OAAOE,SAAS,GAAG,aAAahB,QAAQ,CAAC2B,YAAY,CAACb,QAAQ,EAAEE,SAAS,CAAC,GAAGA,SAAS;AACxF,CAAC,CAAC;AACFY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGpB,MAAM,CAACqB,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;EACEjB,QAAQ,EAAEb,SAAS,CAAC+B,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEvB,SAAS,EAAER,SAAS,CAAC,sCAAsCgC,SAAS,CAAC,CAAC1B,eAAe,EAAEN,SAAS,CAACiC,IAAI,CAAC,CAAC;EACvG;AACF;AACA;AACA;EACEnB,aAAa,EAAEd,SAAS,CAACkC;AAC3B,CAAC,GAAG,KAAK,CAAC;AACV,IAAIP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC;EACApB,MAAM,CAAC,WAAW,GAAG,EAAE,CAAC,GAAGJ,SAAS,CAACI,MAAM,CAACqB,SAAS,CAAC;AACxD;AACA,eAAerB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}