{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useTimeout from '@mui/utils/useTimeout';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { useSplitFieldProps } from \"../../../hooks/index.js\";\nimport { getActiveElement } from \"../../utils/utils.js\";\nimport { getSectionVisibleValue, isAndroid } from \"./useField.utils.js\";\nimport { useFieldCharacterEditing } from \"./useFieldCharacterEditing.js\";\nimport { useFieldRootHandleKeyDown } from \"./useFieldRootHandleKeyDown.js\";\nimport { useFieldState } from \"./useFieldState.js\";\nimport { useFieldInternalPropsWithDefaults } from \"./useFieldInternalPropsWithDefaults.js\";\nconst cleanString = dirtyString => dirtyString.replace(/[\\u2066\\u2067\\u2068\\u2069]/g, '');\nexport const addPositionPropertiesToSections = (sections, localizedDigits, isRtl) => {\n  let position = 0;\n  let positionInInput = isRtl ? 1 : 0;\n  const newSections = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const renderedValue = getSectionVisibleValue(section, isRtl ? 'input-rtl' : 'input-ltr', localizedDigits);\n    const sectionStr = \"\".concat(section.startSeparator).concat(renderedValue).concat(section.endSeparator);\n    const sectionLength = cleanString(sectionStr).length;\n    const sectionLengthInInput = sectionStr.length;\n\n    // The ...InInput values consider the unicode characters but do include them in their indexes\n    const cleanedValue = cleanString(renderedValue);\n    const startInInput = positionInInput + (cleanedValue === '' ? 0 : renderedValue.indexOf(cleanedValue[0])) + section.startSeparator.length;\n    const endInInput = startInInput + cleanedValue.length;\n    newSections.push(_extends({}, section, {\n      start: position,\n      end: position + sectionLength,\n      startInInput,\n      endInInput\n    }));\n    position += sectionLength;\n    // Move position to the end of string associated to the current section\n    positionInInput += sectionLengthInInput;\n  }\n  return newSections;\n};\nexport const useFieldV6TextField = parameters => {\n  const isRtl = useRtl();\n  const focusTimeout = useTimeout();\n  const selectionSyncTimeout = useTimeout();\n  const {\n    props,\n    manager,\n    skipContextFieldRefAssignment,\n    manager: {\n      valueType,\n      internal_valueManager: valueManager,\n      internal_fieldValueManager: fieldValueManager,\n      internal_useOpenPickerButtonAriaLabel: useOpenPickerButtonAriaLabel\n    }\n  } = parameters;\n  const {\n    internalProps,\n    forwardedProps\n  } = useSplitFieldProps(props, valueType);\n  const internalPropsWithDefaults = useFieldInternalPropsWithDefaults({\n    manager,\n    internalProps,\n    skipContextFieldRefAssignment\n  });\n  const {\n    onFocus,\n    onClick,\n    onPaste,\n    onBlur,\n    onKeyDown,\n    onClear,\n    clearable,\n    inputRef: inputRefProp,\n    placeholder: inPlaceholder\n  } = forwardedProps;\n  const {\n    readOnly = false,\n    disabled = false,\n    autoFocus = false,\n    focused,\n    unstableFieldRef\n  } = internalPropsWithDefaults;\n  const inputRef = React.useRef(null);\n  const handleRef = useForkRef(inputRefProp, inputRef);\n  const stateResponse = useFieldState({\n    manager,\n    internalPropsWithDefaults,\n    forwardedProps\n  });\n  const {\n    // States and derived states\n    activeSectionIndex,\n    areAllSectionsEmpty,\n    error,\n    localizedDigits,\n    parsedSelectedSections,\n    sectionOrder,\n    state,\n    value,\n    // Methods to update the states\n    clearValue,\n    clearActiveSection,\n    setCharacterQuery,\n    setSelectedSections,\n    setTempAndroidValueStr,\n    updateSectionValue,\n    updateValueFromValueStr,\n    // Utilities methods\n    getSectionsFromValue\n  } = stateResponse;\n  const applyCharacterEditing = useFieldCharacterEditing({\n    stateResponse\n  });\n  const openPickerAriaLabel = useOpenPickerButtonAriaLabel(value);\n  const sections = React.useMemo(() => addPositionPropertiesToSections(state.sections, localizedDigits, isRtl), [state.sections, localizedDigits, isRtl]);\n  function syncSelectionFromDOM() {\n    var _inputRef$current$sel;\n    const browserStartIndex = (_inputRef$current$sel = inputRef.current.selectionStart) !== null && _inputRef$current$sel !== void 0 ? _inputRef$current$sel : 0;\n    let nextSectionIndex;\n    if (browserStartIndex <= sections[0].startInInput) {\n      // Special case if browser index is in invisible characters at the beginning\n      nextSectionIndex = 1;\n    } else if (browserStartIndex >= sections[sections.length - 1].endInInput) {\n      // If the click is after the last character of the input, then we want to select the 1st section.\n      nextSectionIndex = 1;\n    } else {\n      nextSectionIndex = sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n    }\n    const sectionIndex = nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    setSelectedSections(sectionIndex);\n  }\n  function focusField() {\n    var _inputRef$current;\n    let newSelectedSection = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    if (getActiveElement(document) === inputRef.current) {\n      return;\n    }\n    (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 || _inputRef$current.focus();\n    setSelectedSections(newSelectedSection);\n  }\n  const handleInputFocus = useEventCallback(event => {\n    onFocus === null || onFocus === void 0 || onFocus(event);\n    // The ref is guaranteed to be resolved at this point.\n    const input = inputRef.current;\n    focusTimeout.start(0, () => {\n      // The ref changed, the component got remounted, the focus event is no longer relevant.\n      if (!input || input !== inputRef.current) {\n        return;\n      }\n      if (activeSectionIndex != null) {\n        return;\n      }\n      if (\n      // avoid selecting all sections when focusing empty field without value\n      input.value.length && Number(input.selectionEnd) - Number(input.selectionStart) === input.value.length) {\n        setSelectedSections('all');\n      } else {\n        syncSelectionFromDOM();\n      }\n    });\n  });\n  const handleInputClick = useEventCallback(function (event) {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call of `handleInputClick` is actually intended, or a side effect.\n    if (event.isDefaultPrevented()) {\n      return;\n    }\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    onClick === null || onClick === void 0 || onClick(event, ...args);\n    syncSelectionFromDOM();\n  });\n  const handleInputPaste = useEventCallback(event => {\n    onPaste === null || onPaste === void 0 || onPaste(event);\n\n    // prevent default to avoid the input `onChange` handler being called\n    event.preventDefault();\n    if (readOnly || disabled) {\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    if (typeof parsedSelectedSections === 'number') {\n      const activeSection = state.sections[parsedSelectedSections];\n      const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n      const digitsOnly = /^[0-9]+$/.test(pastedValue);\n      const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n      const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n      if (isValidPastedValue) {\n        setCharacterQuery(null);\n        updateSectionValue({\n          section: activeSection,\n          newSectionValue: pastedValue,\n          shouldGoToNextSection: true\n        });\n        return;\n      }\n      if (lettersOnly || digitsOnly) {\n        // The pasted value corresponds to a single section, but not the expected type,\n        // skip the modification\n        return;\n      }\n    }\n    setCharacterQuery(null);\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleContainerBlur = useEventCallback(event => {\n    onBlur === null || onBlur === void 0 || onBlur(event);\n    setSelectedSections(null);\n  });\n  const handleInputChange = useEventCallback(event => {\n    if (readOnly) {\n      return;\n    }\n    const targetValue = event.target.value;\n    if (targetValue === '') {\n      clearValue();\n      return;\n    }\n    const eventData = event.nativeEvent.data;\n    // Calling `.fill(04/11/2022)` in playwright will trigger a change event with the requested content to insert in `event.nativeEvent.data`\n    // usual changes have only the currently typed character in the `event.nativeEvent.data`\n    const shouldUseEventData = eventData && eventData.length > 1;\n    const valueStr = shouldUseEventData ? eventData : targetValue;\n    const cleanValueStr = cleanString(valueStr);\n    if (parsedSelectedSections === 'all') {\n      setSelectedSections(activeSectionIndex);\n    }\n\n    // If no section is selected or eventData should be used, we just try to parse the new value\n    // This line is mostly triggered by imperative code / application tests.\n    if (activeSectionIndex == null || shouldUseEventData) {\n      updateValueFromValueStr(shouldUseEventData ? eventData : cleanValueStr);\n      return;\n    }\n    let keyPressed;\n    if (parsedSelectedSections === 'all' && cleanValueStr.length === 1) {\n      keyPressed = cleanValueStr;\n    } else {\n      const prevValueStr = cleanString(fieldValueManager.getV6InputValueFromSections(sections, localizedDigits, isRtl));\n      let startOfDiffIndex = -1;\n      let endOfDiffIndex = -1;\n      for (let i = 0; i < prevValueStr.length; i += 1) {\n        if (startOfDiffIndex === -1 && prevValueStr[i] !== cleanValueStr[i]) {\n          startOfDiffIndex = i;\n        }\n        if (endOfDiffIndex === -1 && prevValueStr[prevValueStr.length - i - 1] !== cleanValueStr[cleanValueStr.length - i - 1]) {\n          endOfDiffIndex = i;\n        }\n      }\n      const activeSection = sections[activeSectionIndex];\n      const hasDiffOutsideOfActiveSection = startOfDiffIndex < activeSection.start || prevValueStr.length - endOfDiffIndex - 1 > activeSection.end;\n      if (hasDiffOutsideOfActiveSection) {\n        // TODO: Support if the new date is valid\n        return;\n      }\n\n      // The active section being selected, the browser has replaced its value with the key pressed by the user.\n      const activeSectionEndRelativeToNewValue = cleanValueStr.length - prevValueStr.length + activeSection.end - cleanString(activeSection.endSeparator || '').length;\n      keyPressed = cleanValueStr.slice(activeSection.start + cleanString(activeSection.startSeparator || '').length, activeSectionEndRelativeToNewValue);\n    }\n    if (keyPressed.length === 0) {\n      if (isAndroid()) {\n        setTempAndroidValueStr(valueStr);\n      }\n      clearActiveSection();\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex: activeSectionIndex\n    });\n  });\n  const handleClear = useEventCallback(function (event) {\n    event.preventDefault();\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n    onClear === null || onClear === void 0 || onClear(event, ...args);\n    clearValue();\n    if (!isFieldFocused(inputRef)) {\n      // setSelectedSections is called internally\n      focusField(0);\n    } else {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const handleContainerKeyDown = useFieldRootHandleKeyDown({\n    manager,\n    internalPropsWithDefaults,\n    stateResponse\n  });\n  const wrappedHandleContainerKeyDown = useEventCallback(event => {\n    onKeyDown === null || onKeyDown === void 0 || onKeyDown(event);\n    handleContainerKeyDown(event);\n  });\n  const placeholder = React.useMemo(() => {\n    if (inPlaceholder !== undefined) {\n      return inPlaceholder;\n    }\n    return fieldValueManager.getV6InputValueFromSections(getSectionsFromValue(valueManager.emptyValue), localizedDigits, isRtl);\n  }, [inPlaceholder, fieldValueManager, getSectionsFromValue, valueManager.emptyValue, localizedDigits, isRtl]);\n  const valueStr = React.useMemo(() => {\n    var _state$tempValueStrAn;\n    return (_state$tempValueStrAn = state.tempValueStrAndroid) !== null && _state$tempValueStrAn !== void 0 ? _state$tempValueStrAn : fieldValueManager.getV6InputValueFromSections(state.sections, localizedDigits, isRtl);\n  }, [state.sections, fieldValueManager, state.tempValueStrAndroid, localizedDigits, isRtl]);\n  React.useEffect(() => {\n    // Select all the sections when focused on mount (`autoFocus = true` on the input)\n    if (inputRef.current && inputRef.current === getActiveElement(document)) {\n      setSelectedSections('all');\n    }\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEnhancedEffect(() => {\n    function syncSelectionToDOM() {\n      if (!inputRef.current) {\n        return;\n      }\n      if (parsedSelectedSections == null) {\n        if (inputRef.current.scrollLeft) {\n          // Ensure that input content is not marked as selected.\n          // setting selection range to 0 causes issues in Safari.\n          // https://bugs.webkit.org/show_bug.cgi?id=224425\n          inputRef.current.scrollLeft = 0;\n        }\n        return;\n      }\n\n      // On multi input range pickers we want to update selection range only for the active input\n      // This helps to avoid the focus jumping on Safari https://github.com/mui/mui-x/issues/9003\n      // because WebKit implements the `setSelectionRange` based on the spec: https://bugs.webkit.org/show_bug.cgi?id=224425\n      if (inputRef.current !== getActiveElement(document)) {\n        return;\n      }\n\n      // Fix scroll jumping on iOS browser: https://github.com/mui/mui-x/issues/8321\n      const currentScrollTop = inputRef.current.scrollTop;\n      if (parsedSelectedSections === 'all') {\n        inputRef.current.select();\n      } else {\n        const selectedSection = sections[parsedSelectedSections];\n        const selectionStart = selectedSection.type === 'empty' ? selectedSection.startInInput - selectedSection.startSeparator.length : selectedSection.startInInput;\n        const selectionEnd = selectedSection.type === 'empty' ? selectedSection.endInInput + selectedSection.endSeparator.length : selectedSection.endInInput;\n        if (selectionStart !== inputRef.current.selectionStart || selectionEnd !== inputRef.current.selectionEnd) {\n          if (inputRef.current === getActiveElement(document)) {\n            inputRef.current.setSelectionRange(selectionStart, selectionEnd);\n          }\n        }\n        selectionSyncTimeout.start(0, () => {\n          // handle case when the selection is not updated correctly\n          // could happen on Android\n          if (inputRef.current && inputRef.current === getActiveElement(document) &&\n          // The section might loose all selection, where `selectionStart === selectionEnd`\n          // https://github.com/mui/mui-x/pull/13652\n          inputRef.current.selectionStart === inputRef.current.selectionEnd && (inputRef.current.selectionStart !== selectionStart || inputRef.current.selectionEnd !== selectionEnd)) {\n            syncSelectionToDOM();\n          }\n        });\n      }\n\n      // Even reading this variable seems to do the trick, but also setting it just to make use of it\n      inputRef.current.scrollTop = currentScrollTop;\n    }\n    syncSelectionToDOM();\n  });\n  const inputMode = React.useMemo(() => {\n    if (activeSectionIndex == null) {\n      return 'text';\n    }\n    if (state.sections[activeSectionIndex].contentType === 'letter') {\n      return 'text';\n    }\n    return 'numeric';\n  }, [activeSectionIndex, state.sections]);\n  const inputHasFocus = inputRef.current && inputRef.current === getActiveElement(document);\n  const shouldShowPlaceholder = !inputHasFocus && areAllSectionsEmpty;\n  React.useImperativeHandle(unstableFieldRef, () => ({\n    getSections: () => state.sections,\n    getActiveSectionIndex: () => {\n      var _inputRef$current$sel2, _inputRef$current$sel3;\n      const browserStartIndex = (_inputRef$current$sel2 = inputRef.current.selectionStart) !== null && _inputRef$current$sel2 !== void 0 ? _inputRef$current$sel2 : 0;\n      const browserEndIndex = (_inputRef$current$sel3 = inputRef.current.selectionEnd) !== null && _inputRef$current$sel3 !== void 0 ? _inputRef$current$sel3 : 0;\n      if (browserStartIndex === 0 && browserEndIndex === 0) {\n        return null;\n      }\n      const nextSectionIndex = browserStartIndex <= sections[0].startInInput ? 1 // Special case if browser index is in invisible characters at the beginning.\n      : sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n      return nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    },\n    setSelectedSections: newSelectedSections => setSelectedSections(newSelectedSections),\n    focusField,\n    isFieldFocused: () => isFieldFocused(inputRef)\n  }));\n  return _extends({}, forwardedProps, {\n    error,\n    clearable: Boolean(clearable && !areAllSectionsEmpty && !readOnly && !disabled),\n    onBlur: handleContainerBlur,\n    onClick: handleInputClick,\n    onFocus: handleInputFocus,\n    onPaste: handleInputPaste,\n    onKeyDown: wrappedHandleContainerKeyDown,\n    onClear: handleClear,\n    inputRef: handleRef,\n    // Additional\n    enableAccessibleFieldDOMStructure: false,\n    placeholder,\n    inputMode,\n    autoComplete: 'off',\n    value: shouldShowPlaceholder ? '' : valueStr,\n    onChange: handleInputChange,\n    focused,\n    disabled,\n    readOnly,\n    autoFocus,\n    openPickerAriaLabel\n  });\n};\nfunction isFieldFocused(inputRef) {\n  return inputRef.current === getActiveElement(document);\n}", "map": {"version": 3, "names": ["_extends", "React", "useRtl", "useEnhancedEffect", "useEventCallback", "useTimeout", "useForkRef", "useSplitFieldProps", "getActiveElement", "getSectionVisibleValue", "isAndroid", "useFieldCharacterEditing", "useFieldRootHandleKeyDown", "useFieldState", "useFieldInternalPropsWithDefaults", "cleanString", "dirtyString", "replace", "addPositionPropertiesToSections", "sections", "localizedDigits", "isRtl", "position", "positionInInput", "newSections", "i", "length", "section", "renderedValue", "sectionStr", "concat", "startSeparator", "endSeparator", "sectionLength", "sectionLengthInInput", "cleanedValue", "startInInput", "indexOf", "endInInput", "push", "start", "end", "useFieldV6TextField", "parameters", "focusTimeout", "selectionSyncTimeout", "props", "manager", "skipContextFieldRefAssignment", "valueType", "internal_valueManager", "valueManager", "internal_fieldValueManager", "field<PERSON><PERSON>ueManager", "internal_useOpenPickerButtonAriaLabel", "useOpenPickerButtonAriaLabel", "internalProps", "forwardedProps", "internalPropsWithDefaults", "onFocus", "onClick", "onPaste", "onBlur", "onKeyDown", "onClear", "clearable", "inputRef", "inputRefProp", "placeholder", "inPlaceholder", "readOnly", "disabled", "autoFocus", "focused", "unstableFieldRef", "useRef", "handleRef", "stateResponse", "activeSectionIndex", "areAllSectionsEmpty", "error", "parsedSelectedSections", "sectionOrder", "state", "value", "clearValue", "clearActiveSection", "setCharacterQuery", "setSelectedSections", "setTempAndroidValueStr", "updateSectionValue", "updateValueFromValueStr", "getSectionsFromValue", "applyCharacterEditing", "openPickerAriaLabel", "useMemo", "syncSelectionFromDOM", "_inputRef$current$sel", "browserStartIndex", "current", "selectionStart", "nextSectionIndex", "findIndex", "sectionIndex", "focusField", "_inputRef$current", "newSelectedSection", "arguments", "undefined", "document", "focus", "handleInputFocus", "event", "input", "Number", "selectionEnd", "handleInputClick", "isDefaultPrevented", "_len", "args", "Array", "_key", "handleInputPaste", "preventDefault", "pastedValue", "clipboardData", "getData", "activeSection", "lettersOnly", "test", "digitsOnly", "digitsAndLetterOnly", "isValidPastedValue", "contentType", "newSectionValue", "shouldGoToNextSection", "handleContainerBlur", "handleInputChange", "targetValue", "target", "eventData", "nativeEvent", "data", "shouldUseEventData", "valueStr", "cleanValueStr", "keyPressed", "prevValueStr", "getV6InputValueFromSections", "startOfDiffIndex", "endOfDiffIndex", "hasDiffOutsideOfActiveSection", "activeSectionEndRelativeToNewValue", "slice", "handleClear", "_len2", "_key2", "isFieldFocused", "startIndex", "handleContainerKeyDown", "wrappedHandleContainerKeyDown", "emptyValue", "_state$tempValueStrAn", "tempValueStrAndroid", "useEffect", "syncSelectionToDOM", "scrollLeft", "currentScrollTop", "scrollTop", "select", "selectedSection", "type", "setSelectionRange", "inputMode", "inputHasFocus", "shouldShowPlaceholder", "useImperativeHandle", "getSections", "getActiveSectionIndex", "_inputRef$current$sel2", "_inputRef$current$sel3", "browserEndIndex", "newSelectedSections", "Boolean", "enableAccessibleFieldDOMStructure", "autoComplete", "onChange"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-date-pickers/esm/internals/hooks/useField/useFieldV6TextField.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useTimeout from '@mui/utils/useTimeout';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { useSplitFieldProps } from \"../../../hooks/index.js\";\nimport { getActiveElement } from \"../../utils/utils.js\";\nimport { getSectionVisibleValue, isAndroid } from \"./useField.utils.js\";\nimport { useFieldCharacterEditing } from \"./useFieldCharacterEditing.js\";\nimport { useFieldRootHandleKeyDown } from \"./useFieldRootHandleKeyDown.js\";\nimport { useFieldState } from \"./useFieldState.js\";\nimport { useFieldInternalPropsWithDefaults } from \"./useFieldInternalPropsWithDefaults.js\";\nconst cleanString = dirtyString => dirtyString.replace(/[\\u2066\\u2067\\u2068\\u2069]/g, '');\nexport const addPositionPropertiesToSections = (sections, localizedDigits, isRtl) => {\n  let position = 0;\n  let positionInInput = isRtl ? 1 : 0;\n  const newSections = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const renderedValue = getSectionVisibleValue(section, isRtl ? 'input-rtl' : 'input-ltr', localizedDigits);\n    const sectionStr = `${section.startSeparator}${renderedValue}${section.endSeparator}`;\n    const sectionLength = cleanString(sectionStr).length;\n    const sectionLengthInInput = sectionStr.length;\n\n    // The ...InInput values consider the unicode characters but do include them in their indexes\n    const cleanedValue = cleanString(renderedValue);\n    const startInInput = positionInInput + (cleanedValue === '' ? 0 : renderedValue.indexOf(cleanedValue[0])) + section.startSeparator.length;\n    const endInInput = startInInput + cleanedValue.length;\n    newSections.push(_extends({}, section, {\n      start: position,\n      end: position + sectionLength,\n      startInInput,\n      endInInput\n    }));\n    position += sectionLength;\n    // Move position to the end of string associated to the current section\n    positionInInput += sectionLengthInInput;\n  }\n  return newSections;\n};\nexport const useFieldV6TextField = parameters => {\n  const isRtl = useRtl();\n  const focusTimeout = useTimeout();\n  const selectionSyncTimeout = useTimeout();\n  const {\n    props,\n    manager,\n    skipContextFieldRefAssignment,\n    manager: {\n      valueType,\n      internal_valueManager: valueManager,\n      internal_fieldValueManager: fieldValueManager,\n      internal_useOpenPickerButtonAriaLabel: useOpenPickerButtonAriaLabel\n    }\n  } = parameters;\n  const {\n    internalProps,\n    forwardedProps\n  } = useSplitFieldProps(props, valueType);\n  const internalPropsWithDefaults = useFieldInternalPropsWithDefaults({\n    manager,\n    internalProps,\n    skipContextFieldRefAssignment\n  });\n  const {\n    onFocus,\n    onClick,\n    onPaste,\n    onBlur,\n    onKeyDown,\n    onClear,\n    clearable,\n    inputRef: inputRefProp,\n    placeholder: inPlaceholder\n  } = forwardedProps;\n  const {\n    readOnly = false,\n    disabled = false,\n    autoFocus = false,\n    focused,\n    unstableFieldRef\n  } = internalPropsWithDefaults;\n  const inputRef = React.useRef(null);\n  const handleRef = useForkRef(inputRefProp, inputRef);\n  const stateResponse = useFieldState({\n    manager,\n    internalPropsWithDefaults,\n    forwardedProps\n  });\n  const {\n    // States and derived states\n    activeSectionIndex,\n    areAllSectionsEmpty,\n    error,\n    localizedDigits,\n    parsedSelectedSections,\n    sectionOrder,\n    state,\n    value,\n    // Methods to update the states\n    clearValue,\n    clearActiveSection,\n    setCharacterQuery,\n    setSelectedSections,\n    setTempAndroidValueStr,\n    updateSectionValue,\n    updateValueFromValueStr,\n    // Utilities methods\n    getSectionsFromValue\n  } = stateResponse;\n  const applyCharacterEditing = useFieldCharacterEditing({\n    stateResponse\n  });\n  const openPickerAriaLabel = useOpenPickerButtonAriaLabel(value);\n  const sections = React.useMemo(() => addPositionPropertiesToSections(state.sections, localizedDigits, isRtl), [state.sections, localizedDigits, isRtl]);\n  function syncSelectionFromDOM() {\n    const browserStartIndex = inputRef.current.selectionStart ?? 0;\n    let nextSectionIndex;\n    if (browserStartIndex <= sections[0].startInInput) {\n      // Special case if browser index is in invisible characters at the beginning\n      nextSectionIndex = 1;\n    } else if (browserStartIndex >= sections[sections.length - 1].endInInput) {\n      // If the click is after the last character of the input, then we want to select the 1st section.\n      nextSectionIndex = 1;\n    } else {\n      nextSectionIndex = sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n    }\n    const sectionIndex = nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    setSelectedSections(sectionIndex);\n  }\n  function focusField(newSelectedSection = 0) {\n    if (getActiveElement(document) === inputRef.current) {\n      return;\n    }\n    inputRef.current?.focus();\n    setSelectedSections(newSelectedSection);\n  }\n  const handleInputFocus = useEventCallback(event => {\n    onFocus?.(event);\n    // The ref is guaranteed to be resolved at this point.\n    const input = inputRef.current;\n    focusTimeout.start(0, () => {\n      // The ref changed, the component got remounted, the focus event is no longer relevant.\n      if (!input || input !== inputRef.current) {\n        return;\n      }\n      if (activeSectionIndex != null) {\n        return;\n      }\n      if (\n      // avoid selecting all sections when focusing empty field without value\n      input.value.length && Number(input.selectionEnd) - Number(input.selectionStart) === input.value.length) {\n        setSelectedSections('all');\n      } else {\n        syncSelectionFromDOM();\n      }\n    });\n  });\n  const handleInputClick = useEventCallback((event, ...args) => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call of `handleInputClick` is actually intended, or a side effect.\n    if (event.isDefaultPrevented()) {\n      return;\n    }\n    onClick?.(event, ...args);\n    syncSelectionFromDOM();\n  });\n  const handleInputPaste = useEventCallback(event => {\n    onPaste?.(event);\n\n    // prevent default to avoid the input `onChange` handler being called\n    event.preventDefault();\n    if (readOnly || disabled) {\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    if (typeof parsedSelectedSections === 'number') {\n      const activeSection = state.sections[parsedSelectedSections];\n      const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n      const digitsOnly = /^[0-9]+$/.test(pastedValue);\n      const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n      const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n      if (isValidPastedValue) {\n        setCharacterQuery(null);\n        updateSectionValue({\n          section: activeSection,\n          newSectionValue: pastedValue,\n          shouldGoToNextSection: true\n        });\n        return;\n      }\n      if (lettersOnly || digitsOnly) {\n        // The pasted value corresponds to a single section, but not the expected type,\n        // skip the modification\n        return;\n      }\n    }\n    setCharacterQuery(null);\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleContainerBlur = useEventCallback(event => {\n    onBlur?.(event);\n    setSelectedSections(null);\n  });\n  const handleInputChange = useEventCallback(event => {\n    if (readOnly) {\n      return;\n    }\n    const targetValue = event.target.value;\n    if (targetValue === '') {\n      clearValue();\n      return;\n    }\n    const eventData = event.nativeEvent.data;\n    // Calling `.fill(04/11/2022)` in playwright will trigger a change event with the requested content to insert in `event.nativeEvent.data`\n    // usual changes have only the currently typed character in the `event.nativeEvent.data`\n    const shouldUseEventData = eventData && eventData.length > 1;\n    const valueStr = shouldUseEventData ? eventData : targetValue;\n    const cleanValueStr = cleanString(valueStr);\n    if (parsedSelectedSections === 'all') {\n      setSelectedSections(activeSectionIndex);\n    }\n\n    // If no section is selected or eventData should be used, we just try to parse the new value\n    // This line is mostly triggered by imperative code / application tests.\n    if (activeSectionIndex == null || shouldUseEventData) {\n      updateValueFromValueStr(shouldUseEventData ? eventData : cleanValueStr);\n      return;\n    }\n    let keyPressed;\n    if (parsedSelectedSections === 'all' && cleanValueStr.length === 1) {\n      keyPressed = cleanValueStr;\n    } else {\n      const prevValueStr = cleanString(fieldValueManager.getV6InputValueFromSections(sections, localizedDigits, isRtl));\n      let startOfDiffIndex = -1;\n      let endOfDiffIndex = -1;\n      for (let i = 0; i < prevValueStr.length; i += 1) {\n        if (startOfDiffIndex === -1 && prevValueStr[i] !== cleanValueStr[i]) {\n          startOfDiffIndex = i;\n        }\n        if (endOfDiffIndex === -1 && prevValueStr[prevValueStr.length - i - 1] !== cleanValueStr[cleanValueStr.length - i - 1]) {\n          endOfDiffIndex = i;\n        }\n      }\n      const activeSection = sections[activeSectionIndex];\n      const hasDiffOutsideOfActiveSection = startOfDiffIndex < activeSection.start || prevValueStr.length - endOfDiffIndex - 1 > activeSection.end;\n      if (hasDiffOutsideOfActiveSection) {\n        // TODO: Support if the new date is valid\n        return;\n      }\n\n      // The active section being selected, the browser has replaced its value with the key pressed by the user.\n      const activeSectionEndRelativeToNewValue = cleanValueStr.length - prevValueStr.length + activeSection.end - cleanString(activeSection.endSeparator || '').length;\n      keyPressed = cleanValueStr.slice(activeSection.start + cleanString(activeSection.startSeparator || '').length, activeSectionEndRelativeToNewValue);\n    }\n    if (keyPressed.length === 0) {\n      if (isAndroid()) {\n        setTempAndroidValueStr(valueStr);\n      }\n      clearActiveSection();\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex: activeSectionIndex\n    });\n  });\n  const handleClear = useEventCallback((event, ...args) => {\n    event.preventDefault();\n    onClear?.(event, ...args);\n    clearValue();\n    if (!isFieldFocused(inputRef)) {\n      // setSelectedSections is called internally\n      focusField(0);\n    } else {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const handleContainerKeyDown = useFieldRootHandleKeyDown({\n    manager,\n    internalPropsWithDefaults,\n    stateResponse\n  });\n  const wrappedHandleContainerKeyDown = useEventCallback(event => {\n    onKeyDown?.(event);\n    handleContainerKeyDown(event);\n  });\n  const placeholder = React.useMemo(() => {\n    if (inPlaceholder !== undefined) {\n      return inPlaceholder;\n    }\n    return fieldValueManager.getV6InputValueFromSections(getSectionsFromValue(valueManager.emptyValue), localizedDigits, isRtl);\n  }, [inPlaceholder, fieldValueManager, getSectionsFromValue, valueManager.emptyValue, localizedDigits, isRtl]);\n  const valueStr = React.useMemo(() => state.tempValueStrAndroid ?? fieldValueManager.getV6InputValueFromSections(state.sections, localizedDigits, isRtl), [state.sections, fieldValueManager, state.tempValueStrAndroid, localizedDigits, isRtl]);\n  React.useEffect(() => {\n    // Select all the sections when focused on mount (`autoFocus = true` on the input)\n    if (inputRef.current && inputRef.current === getActiveElement(document)) {\n      setSelectedSections('all');\n    }\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEnhancedEffect(() => {\n    function syncSelectionToDOM() {\n      if (!inputRef.current) {\n        return;\n      }\n      if (parsedSelectedSections == null) {\n        if (inputRef.current.scrollLeft) {\n          // Ensure that input content is not marked as selected.\n          // setting selection range to 0 causes issues in Safari.\n          // https://bugs.webkit.org/show_bug.cgi?id=224425\n          inputRef.current.scrollLeft = 0;\n        }\n        return;\n      }\n\n      // On multi input range pickers we want to update selection range only for the active input\n      // This helps to avoid the focus jumping on Safari https://github.com/mui/mui-x/issues/9003\n      // because WebKit implements the `setSelectionRange` based on the spec: https://bugs.webkit.org/show_bug.cgi?id=224425\n      if (inputRef.current !== getActiveElement(document)) {\n        return;\n      }\n\n      // Fix scroll jumping on iOS browser: https://github.com/mui/mui-x/issues/8321\n      const currentScrollTop = inputRef.current.scrollTop;\n      if (parsedSelectedSections === 'all') {\n        inputRef.current.select();\n      } else {\n        const selectedSection = sections[parsedSelectedSections];\n        const selectionStart = selectedSection.type === 'empty' ? selectedSection.startInInput - selectedSection.startSeparator.length : selectedSection.startInInput;\n        const selectionEnd = selectedSection.type === 'empty' ? selectedSection.endInInput + selectedSection.endSeparator.length : selectedSection.endInInput;\n        if (selectionStart !== inputRef.current.selectionStart || selectionEnd !== inputRef.current.selectionEnd) {\n          if (inputRef.current === getActiveElement(document)) {\n            inputRef.current.setSelectionRange(selectionStart, selectionEnd);\n          }\n        }\n        selectionSyncTimeout.start(0, () => {\n          // handle case when the selection is not updated correctly\n          // could happen on Android\n          if (inputRef.current && inputRef.current === getActiveElement(document) &&\n          // The section might loose all selection, where `selectionStart === selectionEnd`\n          // https://github.com/mui/mui-x/pull/13652\n          inputRef.current.selectionStart === inputRef.current.selectionEnd && (inputRef.current.selectionStart !== selectionStart || inputRef.current.selectionEnd !== selectionEnd)) {\n            syncSelectionToDOM();\n          }\n        });\n      }\n\n      // Even reading this variable seems to do the trick, but also setting it just to make use of it\n      inputRef.current.scrollTop = currentScrollTop;\n    }\n    syncSelectionToDOM();\n  });\n  const inputMode = React.useMemo(() => {\n    if (activeSectionIndex == null) {\n      return 'text';\n    }\n    if (state.sections[activeSectionIndex].contentType === 'letter') {\n      return 'text';\n    }\n    return 'numeric';\n  }, [activeSectionIndex, state.sections]);\n  const inputHasFocus = inputRef.current && inputRef.current === getActiveElement(document);\n  const shouldShowPlaceholder = !inputHasFocus && areAllSectionsEmpty;\n  React.useImperativeHandle(unstableFieldRef, () => ({\n    getSections: () => state.sections,\n    getActiveSectionIndex: () => {\n      const browserStartIndex = inputRef.current.selectionStart ?? 0;\n      const browserEndIndex = inputRef.current.selectionEnd ?? 0;\n      if (browserStartIndex === 0 && browserEndIndex === 0) {\n        return null;\n      }\n      const nextSectionIndex = browserStartIndex <= sections[0].startInInput ? 1 // Special case if browser index is in invisible characters at the beginning.\n      : sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n      return nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    },\n    setSelectedSections: newSelectedSections => setSelectedSections(newSelectedSections),\n    focusField,\n    isFieldFocused: () => isFieldFocused(inputRef)\n  }));\n  return _extends({}, forwardedProps, {\n    error,\n    clearable: Boolean(clearable && !areAllSectionsEmpty && !readOnly && !disabled),\n    onBlur: handleContainerBlur,\n    onClick: handleInputClick,\n    onFocus: handleInputFocus,\n    onPaste: handleInputPaste,\n    onKeyDown: wrappedHandleContainerKeyDown,\n    onClear: handleClear,\n    inputRef: handleRef,\n    // Additional\n    enableAccessibleFieldDOMStructure: false,\n    placeholder,\n    inputMode,\n    autoComplete: 'off',\n    value: shouldShowPlaceholder ? '' : valueStr,\n    onChange: handleInputChange,\n    focused,\n    disabled,\n    readOnly,\n    autoFocus,\n    openPickerAriaLabel\n  });\n};\nfunction isFieldFocused(inputRef) {\n  return inputRef.current === getActiveElement(document);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,sBAAsB,EAAEC,SAAS,QAAQ,qBAAqB;AACvE,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,iCAAiC,QAAQ,wCAAwC;AAC1F,MAAMC,WAAW,GAAGC,WAAW,IAAIA,WAAW,CAACC,OAAO,CAAC,6BAA6B,EAAE,EAAE,CAAC;AACzF,OAAO,MAAMC,+BAA+B,GAAGA,CAACC,QAAQ,EAAEC,eAAe,EAAEC,KAAK,KAAK;EACnF,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIC,eAAe,GAAGF,KAAK,GAAG,CAAC,GAAG,CAAC;EACnC,MAAMG,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,QAAQ,CAACO,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAC3C,MAAME,OAAO,GAAGR,QAAQ,CAACM,CAAC,CAAC;IAC3B,MAAMG,aAAa,GAAGnB,sBAAsB,CAACkB,OAAO,EAAEN,KAAK,GAAG,WAAW,GAAG,WAAW,EAAED,eAAe,CAAC;IACzG,MAAMS,UAAU,MAAAC,MAAA,CAAMH,OAAO,CAACI,cAAc,EAAAD,MAAA,CAAGF,aAAa,EAAAE,MAAA,CAAGH,OAAO,CAACK,YAAY,CAAE;IACrF,MAAMC,aAAa,GAAGlB,WAAW,CAACc,UAAU,CAAC,CAACH,MAAM;IACpD,MAAMQ,oBAAoB,GAAGL,UAAU,CAACH,MAAM;;IAE9C;IACA,MAAMS,YAAY,GAAGpB,WAAW,CAACa,aAAa,CAAC;IAC/C,MAAMQ,YAAY,GAAGb,eAAe,IAAIY,YAAY,KAAK,EAAE,GAAG,CAAC,GAAGP,aAAa,CAACS,OAAO,CAACF,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGR,OAAO,CAACI,cAAc,CAACL,MAAM;IACzI,MAAMY,UAAU,GAAGF,YAAY,GAAGD,YAAY,CAACT,MAAM;IACrDF,WAAW,CAACe,IAAI,CAACvC,QAAQ,CAAC,CAAC,CAAC,EAAE2B,OAAO,EAAE;MACrCa,KAAK,EAAElB,QAAQ;MACfmB,GAAG,EAAEnB,QAAQ,GAAGW,aAAa;MAC7BG,YAAY;MACZE;IACF,CAAC,CAAC,CAAC;IACHhB,QAAQ,IAAIW,aAAa;IACzB;IACAV,eAAe,IAAIW,oBAAoB;EACzC;EACA,OAAOV,WAAW;AACpB,CAAC;AACD,OAAO,MAAMkB,mBAAmB,GAAGC,UAAU,IAAI;EAC/C,MAAMtB,KAAK,GAAGnB,MAAM,CAAC,CAAC;EACtB,MAAM0C,YAAY,GAAGvC,UAAU,CAAC,CAAC;EACjC,MAAMwC,oBAAoB,GAAGxC,UAAU,CAAC,CAAC;EACzC,MAAM;IACJyC,KAAK;IACLC,OAAO;IACPC,6BAA6B;IAC7BD,OAAO,EAAE;MACPE,SAAS;MACTC,qBAAqB,EAAEC,YAAY;MACnCC,0BAA0B,EAAEC,iBAAiB;MAC7CC,qCAAqC,EAAEC;IACzC;EACF,CAAC,GAAGZ,UAAU;EACd,MAAM;IACJa,aAAa;IACbC;EACF,CAAC,GAAGlD,kBAAkB,CAACuC,KAAK,EAAEG,SAAS,CAAC;EACxC,MAAMS,yBAAyB,GAAG5C,iCAAiC,CAAC;IAClEiC,OAAO;IACPS,aAAa;IACbR;EACF,CAAC,CAAC;EACF,MAAM;IACJW,OAAO;IACPC,OAAO;IACPC,OAAO;IACPC,MAAM;IACNC,SAAS;IACTC,OAAO;IACPC,SAAS;IACTC,QAAQ,EAAEC,YAAY;IACtBC,WAAW,EAAEC;EACf,CAAC,GAAGZ,cAAc;EAClB,MAAM;IACJa,QAAQ,GAAG,KAAK;IAChBC,QAAQ,GAAG,KAAK;IAChBC,SAAS,GAAG,KAAK;IACjBC,OAAO;IACPC;EACF,CAAC,GAAGhB,yBAAyB;EAC7B,MAAMQ,QAAQ,GAAGjE,KAAK,CAAC0E,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMC,SAAS,GAAGtE,UAAU,CAAC6D,YAAY,EAAED,QAAQ,CAAC;EACpD,MAAMW,aAAa,GAAGhE,aAAa,CAAC;IAClCkC,OAAO;IACPW,yBAAyB;IACzBD;EACF,CAAC,CAAC;EACF,MAAM;IACJ;IACAqB,kBAAkB;IAClBC,mBAAmB;IACnBC,KAAK;IACL5D,eAAe;IACf6D,sBAAsB;IACtBC,YAAY;IACZC,KAAK;IACLC,KAAK;IACL;IACAC,UAAU;IACVC,kBAAkB;IAClBC,iBAAiB;IACjBC,mBAAmB;IACnBC,sBAAsB;IACtBC,kBAAkB;IAClBC,uBAAuB;IACvB;IACAC;EACF,CAAC,GAAGf,aAAa;EACjB,MAAMgB,qBAAqB,GAAGlF,wBAAwB,CAAC;IACrDkE;EACF,CAAC,CAAC;EACF,MAAMiB,mBAAmB,GAAGvC,4BAA4B,CAAC6B,KAAK,CAAC;EAC/D,MAAMjE,QAAQ,GAAGlB,KAAK,CAAC8F,OAAO,CAAC,MAAM7E,+BAA+B,CAACiE,KAAK,CAAChE,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC,EAAE,CAAC8D,KAAK,CAAChE,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC,CAAC;EACvJ,SAAS2E,oBAAoBA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IAC9B,MAAMC,iBAAiB,IAAAD,qBAAA,GAAG/B,QAAQ,CAACiC,OAAO,CAACC,cAAc,cAAAH,qBAAA,cAAAA,qBAAA,GAAI,CAAC;IAC9D,IAAII,gBAAgB;IACpB,IAAIH,iBAAiB,IAAI/E,QAAQ,CAAC,CAAC,CAAC,CAACiB,YAAY,EAAE;MACjD;MACAiE,gBAAgB,GAAG,CAAC;IACtB,CAAC,MAAM,IAAIH,iBAAiB,IAAI/E,QAAQ,CAACA,QAAQ,CAACO,MAAM,GAAG,CAAC,CAAC,CAACY,UAAU,EAAE;MACxE;MACA+D,gBAAgB,GAAG,CAAC;IACtB,CAAC,MAAM;MACLA,gBAAgB,GAAGlF,QAAQ,CAACmF,SAAS,CAAC3E,OAAO,IAAIA,OAAO,CAACS,YAAY,GAAGT,OAAO,CAACI,cAAc,CAACL,MAAM,GAAGwE,iBAAiB,CAAC;IAC5H;IACA,MAAMK,YAAY,GAAGF,gBAAgB,KAAK,CAAC,CAAC,GAAGlF,QAAQ,CAACO,MAAM,GAAG,CAAC,GAAG2E,gBAAgB,GAAG,CAAC;IACzFb,mBAAmB,CAACe,YAAY,CAAC;EACnC;EACA,SAASC,UAAUA,CAAA,EAAyB;IAAA,IAAAC,iBAAA;IAAA,IAAxBC,kBAAkB,GAAAC,SAAA,CAAAjF,MAAA,QAAAiF,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;IACxC,IAAInG,gBAAgB,CAACqG,QAAQ,CAAC,KAAK3C,QAAQ,CAACiC,OAAO,EAAE;MACnD;IACF;IACA,CAAAM,iBAAA,GAAAvC,QAAQ,CAACiC,OAAO,cAAAM,iBAAA,eAAhBA,iBAAA,CAAkBK,KAAK,CAAC,CAAC;IACzBtB,mBAAmB,CAACkB,kBAAkB,CAAC;EACzC;EACA,MAAMK,gBAAgB,GAAG3G,gBAAgB,CAAC4G,KAAK,IAAI;IACjDrD,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAGqD,KAAK,CAAC;IAChB;IACA,MAAMC,KAAK,GAAG/C,QAAQ,CAACiC,OAAO;IAC9BvD,YAAY,CAACJ,KAAK,CAAC,CAAC,EAAE,MAAM;MAC1B;MACA,IAAI,CAACyE,KAAK,IAAIA,KAAK,KAAK/C,QAAQ,CAACiC,OAAO,EAAE;QACxC;MACF;MACA,IAAIrB,kBAAkB,IAAI,IAAI,EAAE;QAC9B;MACF;MACA;MACA;MACAmC,KAAK,CAAC7B,KAAK,CAAC1D,MAAM,IAAIwF,MAAM,CAACD,KAAK,CAACE,YAAY,CAAC,GAAGD,MAAM,CAACD,KAAK,CAACb,cAAc,CAAC,KAAKa,KAAK,CAAC7B,KAAK,CAAC1D,MAAM,EAAE;QACtG8D,mBAAmB,CAAC,KAAK,CAAC;MAC5B,CAAC,MAAM;QACLQ,oBAAoB,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMoB,gBAAgB,GAAGhH,gBAAgB,CAAC,UAAC4G,KAAK,EAAc;IAC5D;IACA;IACA,IAAIA,KAAK,CAACK,kBAAkB,CAAC,CAAC,EAAE;MAC9B;IACF;IAAC,SAAAC,IAAA,GAAAX,SAAA,CAAAjF,MAAA,EALkD6F,IAAI,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAJF,IAAI,CAAAE,IAAA,QAAAd,SAAA,CAAAc,IAAA;IAAA;IAMvD7D,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAGoD,KAAK,EAAE,GAAGO,IAAI,CAAC;IACzBvB,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,MAAM0B,gBAAgB,GAAGtH,gBAAgB,CAAC4G,KAAK,IAAI;IACjDnD,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAGmD,KAAK,CAAC;;IAEhB;IACAA,KAAK,CAACW,cAAc,CAAC,CAAC;IACtB,IAAIrD,QAAQ,IAAIC,QAAQ,EAAE;MACxB;IACF;IACA,MAAMqD,WAAW,GAAGZ,KAAK,CAACa,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;IACvD,IAAI,OAAO7C,sBAAsB,KAAK,QAAQ,EAAE;MAC9C,MAAM8C,aAAa,GAAG5C,KAAK,CAAChE,QAAQ,CAAC8D,sBAAsB,CAAC;MAC5D,MAAM+C,WAAW,GAAG,aAAa,CAACC,IAAI,CAACL,WAAW,CAAC;MACnD,MAAMM,UAAU,GAAG,UAAU,CAACD,IAAI,CAACL,WAAW,CAAC;MAC/C,MAAMO,mBAAmB,GAAG,wCAAwC,CAACF,IAAI,CAACL,WAAW,CAAC;MACtF,MAAMQ,kBAAkB,GAAGL,aAAa,CAACM,WAAW,KAAK,QAAQ,IAAIL,WAAW,IAAID,aAAa,CAACM,WAAW,KAAK,OAAO,IAAIH,UAAU,IAAIH,aAAa,CAACM,WAAW,KAAK,mBAAmB,IAAIF,mBAAmB;MACnN,IAAIC,kBAAkB,EAAE;QACtB7C,iBAAiB,CAAC,IAAI,CAAC;QACvBG,kBAAkB,CAAC;UACjB/D,OAAO,EAAEoG,aAAa;UACtBO,eAAe,EAAEV,WAAW;UAC5BW,qBAAqB,EAAE;QACzB,CAAC,CAAC;QACF;MACF;MACA,IAAIP,WAAW,IAAIE,UAAU,EAAE;QAC7B;QACA;QACA;MACF;IACF;IACA3C,iBAAiB,CAAC,IAAI,CAAC;IACvBI,uBAAuB,CAACiC,WAAW,CAAC;EACtC,CAAC,CAAC;EACF,MAAMY,mBAAmB,GAAGpI,gBAAgB,CAAC4G,KAAK,IAAI;IACpDlD,MAAM,aAANA,MAAM,eAANA,MAAM,CAAGkD,KAAK,CAAC;IACfxB,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC,CAAC;EACF,MAAMiD,iBAAiB,GAAGrI,gBAAgB,CAAC4G,KAAK,IAAI;IAClD,IAAI1C,QAAQ,EAAE;MACZ;IACF;IACA,MAAMoE,WAAW,GAAG1B,KAAK,CAAC2B,MAAM,CAACvD,KAAK;IACtC,IAAIsD,WAAW,KAAK,EAAE,EAAE;MACtBrD,UAAU,CAAC,CAAC;MACZ;IACF;IACA,MAAMuD,SAAS,GAAG5B,KAAK,CAAC6B,WAAW,CAACC,IAAI;IACxC;IACA;IACA,MAAMC,kBAAkB,GAAGH,SAAS,IAAIA,SAAS,CAAClH,MAAM,GAAG,CAAC;IAC5D,MAAMsH,QAAQ,GAAGD,kBAAkB,GAAGH,SAAS,GAAGF,WAAW;IAC7D,MAAMO,aAAa,GAAGlI,WAAW,CAACiI,QAAQ,CAAC;IAC3C,IAAI/D,sBAAsB,KAAK,KAAK,EAAE;MACpCO,mBAAmB,CAACV,kBAAkB,CAAC;IACzC;;IAEA;IACA;IACA,IAAIA,kBAAkB,IAAI,IAAI,IAAIiE,kBAAkB,EAAE;MACpDpD,uBAAuB,CAACoD,kBAAkB,GAAGH,SAAS,GAAGK,aAAa,CAAC;MACvE;IACF;IACA,IAAIC,UAAU;IACd,IAAIjE,sBAAsB,KAAK,KAAK,IAAIgE,aAAa,CAACvH,MAAM,KAAK,CAAC,EAAE;MAClEwH,UAAU,GAAGD,aAAa;IAC5B,CAAC,MAAM;MACL,MAAME,YAAY,GAAGpI,WAAW,CAACsC,iBAAiB,CAAC+F,2BAA2B,CAACjI,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC,CAAC;MACjH,IAAIgI,gBAAgB,GAAG,CAAC,CAAC;MACzB,IAAIC,cAAc,GAAG,CAAC,CAAC;MACvB,KAAK,IAAI7H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0H,YAAY,CAACzH,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC/C,IAAI4H,gBAAgB,KAAK,CAAC,CAAC,IAAIF,YAAY,CAAC1H,CAAC,CAAC,KAAKwH,aAAa,CAACxH,CAAC,CAAC,EAAE;UACnE4H,gBAAgB,GAAG5H,CAAC;QACtB;QACA,IAAI6H,cAAc,KAAK,CAAC,CAAC,IAAIH,YAAY,CAACA,YAAY,CAACzH,MAAM,GAAGD,CAAC,GAAG,CAAC,CAAC,KAAKwH,aAAa,CAACA,aAAa,CAACvH,MAAM,GAAGD,CAAC,GAAG,CAAC,CAAC,EAAE;UACtH6H,cAAc,GAAG7H,CAAC;QACpB;MACF;MACA,MAAMsG,aAAa,GAAG5G,QAAQ,CAAC2D,kBAAkB,CAAC;MAClD,MAAMyE,6BAA6B,GAAGF,gBAAgB,GAAGtB,aAAa,CAACvF,KAAK,IAAI2G,YAAY,CAACzH,MAAM,GAAG4H,cAAc,GAAG,CAAC,GAAGvB,aAAa,CAACtF,GAAG;MAC5I,IAAI8G,6BAA6B,EAAE;QACjC;QACA;MACF;;MAEA;MACA,MAAMC,kCAAkC,GAAGP,aAAa,CAACvH,MAAM,GAAGyH,YAAY,CAACzH,MAAM,GAAGqG,aAAa,CAACtF,GAAG,GAAG1B,WAAW,CAACgH,aAAa,CAAC/F,YAAY,IAAI,EAAE,CAAC,CAACN,MAAM;MAChKwH,UAAU,GAAGD,aAAa,CAACQ,KAAK,CAAC1B,aAAa,CAACvF,KAAK,GAAGzB,WAAW,CAACgH,aAAa,CAAChG,cAAc,IAAI,EAAE,CAAC,CAACL,MAAM,EAAE8H,kCAAkC,CAAC;IACpJ;IACA,IAAIN,UAAU,CAACxH,MAAM,KAAK,CAAC,EAAE;MAC3B,IAAIhB,SAAS,CAAC,CAAC,EAAE;QACf+E,sBAAsB,CAACuD,QAAQ,CAAC;MAClC;MACA1D,kBAAkB,CAAC,CAAC;MACpB;IACF;IACAO,qBAAqB,CAAC;MACpBqD,UAAU;MACV3C,YAAY,EAAEzB;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAM4E,WAAW,GAAGtJ,gBAAgB,CAAC,UAAC4G,KAAK,EAAc;IACvDA,KAAK,CAACW,cAAc,CAAC,CAAC;IAAC,SAAAgC,KAAA,GAAAhD,SAAA,CAAAjF,MAAA,EADuB6F,IAAI,OAAAC,KAAA,CAAAmC,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJrC,IAAI,CAAAqC,KAAA,QAAAjD,SAAA,CAAAiD,KAAA;IAAA;IAElD5F,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAGgD,KAAK,EAAE,GAAGO,IAAI,CAAC;IACzBlC,UAAU,CAAC,CAAC;IACZ,IAAI,CAACwE,cAAc,CAAC3F,QAAQ,CAAC,EAAE;MAC7B;MACAsC,UAAU,CAAC,CAAC,CAAC;IACf,CAAC,MAAM;MACLhB,mBAAmB,CAACN,YAAY,CAAC4E,UAAU,CAAC;IAC9C;EACF,CAAC,CAAC;EACF,MAAMC,sBAAsB,GAAGnJ,yBAAyB,CAAC;IACvDmC,OAAO;IACPW,yBAAyB;IACzBmB;EACF,CAAC,CAAC;EACF,MAAMmF,6BAA6B,GAAG5J,gBAAgB,CAAC4G,KAAK,IAAI;IAC9DjD,SAAS,aAATA,SAAS,eAATA,SAAS,CAAGiD,KAAK,CAAC;IAClB+C,sBAAsB,CAAC/C,KAAK,CAAC;EAC/B,CAAC,CAAC;EACF,MAAM5C,WAAW,GAAGnE,KAAK,CAAC8F,OAAO,CAAC,MAAM;IACtC,IAAI1B,aAAa,KAAKuC,SAAS,EAAE;MAC/B,OAAOvC,aAAa;IACtB;IACA,OAAOhB,iBAAiB,CAAC+F,2BAA2B,CAACxD,oBAAoB,CAACzC,YAAY,CAAC8G,UAAU,CAAC,EAAE7I,eAAe,EAAEC,KAAK,CAAC;EAC7H,CAAC,EAAE,CAACgD,aAAa,EAAEhB,iBAAiB,EAAEuC,oBAAoB,EAAEzC,YAAY,CAAC8G,UAAU,EAAE7I,eAAe,EAAEC,KAAK,CAAC,CAAC;EAC7G,MAAM2H,QAAQ,GAAG/I,KAAK,CAAC8F,OAAO,CAAC;IAAA,IAAAmE,qBAAA;IAAA,QAAAA,qBAAA,GAAM/E,KAAK,CAACgF,mBAAmB,cAAAD,qBAAA,cAAAA,qBAAA,GAAI7G,iBAAiB,CAAC+F,2BAA2B,CAACjE,KAAK,CAAChE,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC;EAAA,GAAE,CAAC8D,KAAK,CAAChE,QAAQ,EAAEkC,iBAAiB,EAAE8B,KAAK,CAACgF,mBAAmB,EAAE/I,eAAe,EAAEC,KAAK,CAAC,CAAC;EAChPpB,KAAK,CAACmK,SAAS,CAAC,MAAM;IACpB;IACA,IAAIlG,QAAQ,CAACiC,OAAO,IAAIjC,QAAQ,CAACiC,OAAO,KAAK3F,gBAAgB,CAACqG,QAAQ,CAAC,EAAE;MACvErB,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAERrF,iBAAiB,CAAC,MAAM;IACtB,SAASkK,kBAAkBA,CAAA,EAAG;MAC5B,IAAI,CAACnG,QAAQ,CAACiC,OAAO,EAAE;QACrB;MACF;MACA,IAAIlB,sBAAsB,IAAI,IAAI,EAAE;QAClC,IAAIf,QAAQ,CAACiC,OAAO,CAACmE,UAAU,EAAE;UAC/B;UACA;UACA;UACApG,QAAQ,CAACiC,OAAO,CAACmE,UAAU,GAAG,CAAC;QACjC;QACA;MACF;;MAEA;MACA;MACA;MACA,IAAIpG,QAAQ,CAACiC,OAAO,KAAK3F,gBAAgB,CAACqG,QAAQ,CAAC,EAAE;QACnD;MACF;;MAEA;MACA,MAAM0D,gBAAgB,GAAGrG,QAAQ,CAACiC,OAAO,CAACqE,SAAS;MACnD,IAAIvF,sBAAsB,KAAK,KAAK,EAAE;QACpCf,QAAQ,CAACiC,OAAO,CAACsE,MAAM,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL,MAAMC,eAAe,GAAGvJ,QAAQ,CAAC8D,sBAAsB,CAAC;QACxD,MAAMmB,cAAc,GAAGsE,eAAe,CAACC,IAAI,KAAK,OAAO,GAAGD,eAAe,CAACtI,YAAY,GAAGsI,eAAe,CAAC3I,cAAc,CAACL,MAAM,GAAGgJ,eAAe,CAACtI,YAAY;QAC7J,MAAM+E,YAAY,GAAGuD,eAAe,CAACC,IAAI,KAAK,OAAO,GAAGD,eAAe,CAACpI,UAAU,GAAGoI,eAAe,CAAC1I,YAAY,CAACN,MAAM,GAAGgJ,eAAe,CAACpI,UAAU;QACrJ,IAAI8D,cAAc,KAAKlC,QAAQ,CAACiC,OAAO,CAACC,cAAc,IAAIe,YAAY,KAAKjD,QAAQ,CAACiC,OAAO,CAACgB,YAAY,EAAE;UACxG,IAAIjD,QAAQ,CAACiC,OAAO,KAAK3F,gBAAgB,CAACqG,QAAQ,CAAC,EAAE;YACnD3C,QAAQ,CAACiC,OAAO,CAACyE,iBAAiB,CAACxE,cAAc,EAAEe,YAAY,CAAC;UAClE;QACF;QACAtE,oBAAoB,CAACL,KAAK,CAAC,CAAC,EAAE,MAAM;UAClC;UACA;UACA,IAAI0B,QAAQ,CAACiC,OAAO,IAAIjC,QAAQ,CAACiC,OAAO,KAAK3F,gBAAgB,CAACqG,QAAQ,CAAC;UACvE;UACA;UACA3C,QAAQ,CAACiC,OAAO,CAACC,cAAc,KAAKlC,QAAQ,CAACiC,OAAO,CAACgB,YAAY,KAAKjD,QAAQ,CAACiC,OAAO,CAACC,cAAc,KAAKA,cAAc,IAAIlC,QAAQ,CAACiC,OAAO,CAACgB,YAAY,KAAKA,YAAY,CAAC,EAAE;YAC3KkD,kBAAkB,CAAC,CAAC;UACtB;QACF,CAAC,CAAC;MACJ;;MAEA;MACAnG,QAAQ,CAACiC,OAAO,CAACqE,SAAS,GAAGD,gBAAgB;IAC/C;IACAF,kBAAkB,CAAC,CAAC;EACtB,CAAC,CAAC;EACF,MAAMQ,SAAS,GAAG5K,KAAK,CAAC8F,OAAO,CAAC,MAAM;IACpC,IAAIjB,kBAAkB,IAAI,IAAI,EAAE;MAC9B,OAAO,MAAM;IACf;IACA,IAAIK,KAAK,CAAChE,QAAQ,CAAC2D,kBAAkB,CAAC,CAACuD,WAAW,KAAK,QAAQ,EAAE;MAC/D,OAAO,MAAM;IACf;IACA,OAAO,SAAS;EAClB,CAAC,EAAE,CAACvD,kBAAkB,EAAEK,KAAK,CAAChE,QAAQ,CAAC,CAAC;EACxC,MAAM2J,aAAa,GAAG5G,QAAQ,CAACiC,OAAO,IAAIjC,QAAQ,CAACiC,OAAO,KAAK3F,gBAAgB,CAACqG,QAAQ,CAAC;EACzF,MAAMkE,qBAAqB,GAAG,CAACD,aAAa,IAAI/F,mBAAmB;EACnE9E,KAAK,CAAC+K,mBAAmB,CAACtG,gBAAgB,EAAE,OAAO;IACjDuG,WAAW,EAAEA,CAAA,KAAM9F,KAAK,CAAChE,QAAQ;IACjC+J,qBAAqB,EAAEA,CAAA,KAAM;MAAA,IAAAC,sBAAA,EAAAC,sBAAA;MAC3B,MAAMlF,iBAAiB,IAAAiF,sBAAA,GAAGjH,QAAQ,CAACiC,OAAO,CAACC,cAAc,cAAA+E,sBAAA,cAAAA,sBAAA,GAAI,CAAC;MAC9D,MAAME,eAAe,IAAAD,sBAAA,GAAGlH,QAAQ,CAACiC,OAAO,CAACgB,YAAY,cAAAiE,sBAAA,cAAAA,sBAAA,GAAI,CAAC;MAC1D,IAAIlF,iBAAiB,KAAK,CAAC,IAAImF,eAAe,KAAK,CAAC,EAAE;QACpD,OAAO,IAAI;MACb;MACA,MAAMhF,gBAAgB,GAAGH,iBAAiB,IAAI/E,QAAQ,CAAC,CAAC,CAAC,CAACiB,YAAY,GAAG,CAAC,CAAC;MAAA,EACzEjB,QAAQ,CAACmF,SAAS,CAAC3E,OAAO,IAAIA,OAAO,CAACS,YAAY,GAAGT,OAAO,CAACI,cAAc,CAACL,MAAM,GAAGwE,iBAAiB,CAAC;MACzG,OAAOG,gBAAgB,KAAK,CAAC,CAAC,GAAGlF,QAAQ,CAACO,MAAM,GAAG,CAAC,GAAG2E,gBAAgB,GAAG,CAAC;IAC7E,CAAC;IACDb,mBAAmB,EAAE8F,mBAAmB,IAAI9F,mBAAmB,CAAC8F,mBAAmB,CAAC;IACpF9E,UAAU;IACVqD,cAAc,EAAEA,CAAA,KAAMA,cAAc,CAAC3F,QAAQ;EAC/C,CAAC,CAAC,CAAC;EACH,OAAOlE,QAAQ,CAAC,CAAC,CAAC,EAAEyD,cAAc,EAAE;IAClCuB,KAAK;IACLf,SAAS,EAAEsH,OAAO,CAACtH,SAAS,IAAI,CAACc,mBAAmB,IAAI,CAACT,QAAQ,IAAI,CAACC,QAAQ,CAAC;IAC/ET,MAAM,EAAE0E,mBAAmB;IAC3B5E,OAAO,EAAEwD,gBAAgB;IACzBzD,OAAO,EAAEoD,gBAAgB;IACzBlD,OAAO,EAAE6D,gBAAgB;IACzB3D,SAAS,EAAEiG,6BAA6B;IACxChG,OAAO,EAAE0F,WAAW;IACpBxF,QAAQ,EAAEU,SAAS;IACnB;IACA4G,iCAAiC,EAAE,KAAK;IACxCpH,WAAW;IACXyG,SAAS;IACTY,YAAY,EAAE,KAAK;IACnBrG,KAAK,EAAE2F,qBAAqB,GAAG,EAAE,GAAG/B,QAAQ;IAC5C0C,QAAQ,EAAEjD,iBAAiB;IAC3BhE,OAAO;IACPF,QAAQ;IACRD,QAAQ;IACRE,SAAS;IACTsB;EACF,CAAC,CAAC;AACJ,CAAC;AACD,SAAS+D,cAAcA,CAAC3F,QAAQ,EAAE;EAChC,OAAOA,QAAQ,CAACiC,OAAO,KAAK3F,gBAAgB,CAACqG,QAAQ,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}