{"ast": null, "code": "import style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nexport const position = style({\n  prop: 'position'\n});\nexport const zIndex = style({\n  prop: 'zIndex',\n  themeKey: 'zIndex'\n});\nexport const top = style({\n  prop: 'top'\n});\nexport const right = style({\n  prop: 'right'\n});\nexport const bottom = style({\n  prop: 'bottom'\n});\nexport const left = style({\n  prop: 'left'\n});\nexport default compose(position, zIndex, top, right, bottom, left);", "map": {"version": 3, "names": ["style", "compose", "position", "prop", "zIndex", "<PERSON><PERSON><PERSON>", "top", "right", "bottom", "left"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/system/esm/positions/positions.js"], "sourcesContent": ["import style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nexport const position = style({\n  prop: 'position'\n});\nexport const zIndex = style({\n  prop: 'zIndex',\n  themeKey: 'zIndex'\n});\nexport const top = style({\n  prop: 'top'\n});\nexport const right = style({\n  prop: 'right'\n});\nexport const bottom = style({\n  prop: 'bottom'\n});\nexport const left = style({\n  prop: 'left'\n});\nexport default compose(position, zIndex, top, right, bottom, left);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AACrC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAO,MAAMC,QAAQ,GAAGF,KAAK,CAAC;EAC5BG,IAAI,EAAE;AACR,CAAC,CAAC;AACF,OAAO,MAAMC,MAAM,GAAGJ,KAAK,CAAC;EAC1BG,IAAI,EAAE,QAAQ;EACdE,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,OAAO,MAAMC,GAAG,GAAGN,KAAK,CAAC;EACvBG,IAAI,EAAE;AACR,CAAC,CAAC;AACF,OAAO,MAAMI,KAAK,GAAGP,KAAK,CAAC;EACzBG,IAAI,EAAE;AACR,CAAC,CAAC;AACF,OAAO,MAAMK,MAAM,GAAGR,KAAK,CAAC;EAC1BG,IAAI,EAAE;AACR,CAAC,CAAC;AACF,OAAO,MAAMM,IAAI,GAAGT,KAAK,CAAC;EACxBG,IAAI,EAAE;AACR,CAAC,CAAC;AACF,eAAeF,OAAO,CAACC,QAAQ,EAAEE,MAAM,EAAEE,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}