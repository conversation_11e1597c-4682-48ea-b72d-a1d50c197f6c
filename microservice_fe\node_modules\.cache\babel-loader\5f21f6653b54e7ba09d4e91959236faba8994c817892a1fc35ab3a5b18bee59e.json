{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSvgIconUtilityClass(slot) {\n  return generateUtilityClass('MuiSvgIcon', slot);\n}\nconst svgIconClasses = generateUtilityClasses('MuiSvgIcon', ['root', 'colorPrimary', 'colorSecondary', 'colorAction', 'colorError', 'colorDisabled', 'fontSizeInherit', 'fontSizeSmall', 'fontSizeMedium', 'fontSizeLarge']);\nexport default svgIconClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getSvgIconUtilityClass", "slot", "svgIconClasses"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/SvgIcon/svgIconClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSvgIconUtilityClass(slot) {\n  return generateUtilityClass('MuiSvgIcon', slot);\n}\nconst svgIconClasses = generateUtilityClasses('MuiSvgIcon', ['root', 'colorPrimary', 'colorSecondary', 'colorAction', 'colorError', 'colorDisabled', 'fontSizeInherit', 'fontSizeSmall', 'fontSizeMedium', 'fontSizeLarge']);\nexport default svgIconClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,sBAAsBA,CAACC,IAAI,EAAE;EAC3C,OAAOF,oBAAoB,CAAC,YAAY,EAAEE,IAAI,CAAC;AACjD;AACA,MAAMC,cAAc,GAAGJ,sBAAsB,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,EAAE,iBAAiB,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;AAC5N,eAAeI,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}