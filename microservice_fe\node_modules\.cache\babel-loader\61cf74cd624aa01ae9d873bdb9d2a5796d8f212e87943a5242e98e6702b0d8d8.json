{"ast": null, "code": "/* eslint-disable */\n// Inspired by https://github.com/garycourt/murmurhash-js\n// Ported from https://github.com/aappleby/smhasher/blob/61a0530f28277f2e850bfc39600ce61d02b518de/src/MurmurHash2.cpp#L37-L86\nfunction murmur2(str) {\n  // 'm' and 'r' are mixing constants generated offline.\n  // They're not really 'magic', they just happen to work well.\n  // const m = 0x5bd1e995;\n  // const r = 24;\n  // Initialize the hash\n  var h = 0; // Mix 4 bytes at a time into the hash\n\n  var k,\n    i = 0,\n    len = str.length;\n  for (; len >= 4; ++i, len -= 4) {\n    k = str.charCodeAt(i) & 0xff | (str.charCodeAt(++i) & 0xff) << 8 | (str.charCodeAt(++i) & 0xff) << 16 | (str.charCodeAt(++i) & 0xff) << 24;\n    k = /* Math.imul(k, m): */\n    (k & 0xffff) * 0x5bd1e995 + ((k >>> 16) * 0xe995 << 16);\n    k ^= /* k >>> r: */\n    k >>> 24;\n    h = /* Math.imul(k, m): */\n    (k & 0xffff) * 0x5bd1e995 + ((k >>> 16) * 0xe995 << 16) ^ /* Math.imul(h, m): */\n    (h & 0xffff) * 0x5bd1e995 + ((h >>> 16) * 0xe995 << 16);\n  } // Handle the last few bytes of the input array\n\n  switch (len) {\n    case 3:\n      h ^= (str.charCodeAt(i + 2) & 0xff) << 16;\n    case 2:\n      h ^= (str.charCodeAt(i + 1) & 0xff) << 8;\n    case 1:\n      h ^= str.charCodeAt(i) & 0xff;\n      h = /* Math.imul(h, m): */\n      (h & 0xffff) * 0x5bd1e995 + ((h >>> 16) * 0xe995 << 16);\n  } // Do a few final mixes of the hash to ensure the last few\n  // bytes are well-incorporated.\n\n  h ^= h >>> 13;\n  h = /* Math.imul(h, m): */\n  (h & 0xffff) * 0x5bd1e995 + ((h >>> 16) * 0xe995 << 16);\n  return ((h ^ h >>> 15) >>> 0).toString(36);\n}\nexport { murmur2 as default };", "map": {"version": 3, "names": ["murmur2", "str", "h", "k", "i", "len", "length", "charCodeAt", "toString", "default"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@emotion/hash/dist/emotion-hash.esm.js"], "sourcesContent": ["/* eslint-disable */\n// Inspired by https://github.com/garycourt/murmurhash-js\n// Ported from https://github.com/aappleby/smhasher/blob/61a0530f28277f2e850bfc39600ce61d02b518de/src/MurmurHash2.cpp#L37-L86\nfunction murmur2(str) {\n  // 'm' and 'r' are mixing constants generated offline.\n  // They're not really 'magic', they just happen to work well.\n  // const m = 0x5bd1e995;\n  // const r = 24;\n  // Initialize the hash\n  var h = 0; // Mix 4 bytes at a time into the hash\n\n  var k,\n      i = 0,\n      len = str.length;\n\n  for (; len >= 4; ++i, len -= 4) {\n    k = str.charCodeAt(i) & 0xff | (str.charCodeAt(++i) & 0xff) << 8 | (str.charCodeAt(++i) & 0xff) << 16 | (str.charCodeAt(++i) & 0xff) << 24;\n    k =\n    /* Math.imul(k, m): */\n    (k & 0xffff) * 0x5bd1e995 + ((k >>> 16) * 0xe995 << 16);\n    k ^=\n    /* k >>> r: */\n    k >>> 24;\n    h =\n    /* Math.imul(k, m): */\n    (k & 0xffff) * 0x5bd1e995 + ((k >>> 16) * 0xe995 << 16) ^\n    /* Math.imul(h, m): */\n    (h & 0xffff) * 0x5bd1e995 + ((h >>> 16) * 0xe995 << 16);\n  } // Handle the last few bytes of the input array\n\n\n  switch (len) {\n    case 3:\n      h ^= (str.charCodeAt(i + 2) & 0xff) << 16;\n\n    case 2:\n      h ^= (str.charCodeAt(i + 1) & 0xff) << 8;\n\n    case 1:\n      h ^= str.charCodeAt(i) & 0xff;\n      h =\n      /* Math.imul(h, m): */\n      (h & 0xffff) * 0x5bd1e995 + ((h >>> 16) * 0xe995 << 16);\n  } // Do a few final mixes of the hash to ensure the last few\n  // bytes are well-incorporated.\n\n\n  h ^= h >>> 13;\n  h =\n  /* Math.imul(h, m): */\n  (h & 0xffff) * 0x5bd1e995 + ((h >>> 16) * 0xe995 << 16);\n  return ((h ^ h >>> 15) >>> 0).toString(36);\n}\n\nexport { murmur2 as default };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,OAAOA,CAACC,GAAG,EAAE;EACpB;EACA;EACA;EACA;EACA;EACA,IAAIC,CAAC,GAAG,CAAC,CAAC,CAAC;;EAEX,IAAIC,CAAC;IACDC,CAAC,GAAG,CAAC;IACLC,GAAG,GAAGJ,GAAG,CAACK,MAAM;EAEpB,OAAOD,GAAG,IAAI,CAAC,EAAE,EAAED,CAAC,EAAEC,GAAG,IAAI,CAAC,EAAE;IAC9BF,CAAC,GAAGF,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC,GAAG,IAAI,GAAG,CAACH,GAAG,CAACM,UAAU,CAAC,EAAEH,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAACH,GAAG,CAACM,UAAU,CAAC,EAAEH,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,GAAG,CAACH,GAAG,CAACM,UAAU,CAAC,EAAEH,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE;IAC1ID,CAAC,GACD;IACA,CAACA,CAAC,GAAG,MAAM,IAAI,UAAU,IAAI,CAACA,CAAC,KAAK,EAAE,IAAI,MAAM,IAAI,EAAE,CAAC;IACvDA,CAAC,IACD;IACAA,CAAC,KAAK,EAAE;IACRD,CAAC,GACD;IACA,CAACC,CAAC,GAAG,MAAM,IAAI,UAAU,IAAI,CAACA,CAAC,KAAK,EAAE,IAAI,MAAM,IAAI,EAAE,CAAC,GACvD;IACA,CAACD,CAAC,GAAG,MAAM,IAAI,UAAU,IAAI,CAACA,CAAC,KAAK,EAAE,IAAI,MAAM,IAAI,EAAE,CAAC;EACzD,CAAC,CAAC;;EAGF,QAAQG,GAAG;IACT,KAAK,CAAC;MACJH,CAAC,IAAI,CAACD,GAAG,CAACM,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE;IAE3C,KAAK,CAAC;MACJF,CAAC,IAAI,CAACD,GAAG,CAACM,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;IAE1C,KAAK,CAAC;MACJF,CAAC,IAAID,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC,GAAG,IAAI;MAC7BF,CAAC,GACD;MACA,CAACA,CAAC,GAAG,MAAM,IAAI,UAAU,IAAI,CAACA,CAAC,KAAK,EAAE,IAAI,MAAM,IAAI,EAAE,CAAC;EAC3D,CAAC,CAAC;EACF;;EAGAA,CAAC,IAAIA,CAAC,KAAK,EAAE;EACbA,CAAC,GACD;EACA,CAACA,CAAC,GAAG,MAAM,IAAI,UAAU,IAAI,CAACA,CAAC,KAAK,EAAE,IAAI,MAAM,IAAI,EAAE,CAAC;EACvD,OAAO,CAAC,CAACA,CAAC,GAAGA,CAAC,KAAK,EAAE,MAAM,CAAC,EAAEM,QAAQ,CAAC,EAAE,CAAC;AAC5C;AAEA,SAASR,OAAO,IAAIS,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}