{"ast": null, "code": "function noop() {}\nconst localStorageManager = _ref => {\n  let {\n    key,\n    storageWindow\n  } = _ref;\n  if (!storageWindow && typeof window !== 'undefined') {\n    storageWindow = window;\n  }\n  return {\n    get(defaultValue) {\n      if (typeof window === 'undefined') {\n        return undefined;\n      }\n      if (!storageWindow) {\n        return defaultValue;\n      }\n      let value;\n      try {\n        value = storageWindow.localStorage.getItem(key);\n      } catch (_unused) {\n        // Unsupported\n      }\n      return value || defaultValue;\n    },\n    set: value => {\n      if (storageWindow) {\n        try {\n          storageWindow.localStorage.setItem(key, value);\n        } catch (_unused2) {\n          // Unsupported\n        }\n      }\n    },\n    subscribe: handler => {\n      if (!storageWindow) {\n        return noop;\n      }\n      const listener = event => {\n        const value = event.newValue;\n        if (event.key === key) {\n          handler(value);\n        }\n      };\n      storageWindow.addEventListener('storage', listener);\n      return () => {\n        storageWindow.removeEventListener('storage', listener);\n      };\n    }\n  };\n};\nexport default localStorageManager;", "map": {"version": 3, "names": ["noop", "localStorageManager", "_ref", "key", "storageWindow", "window", "get", "defaultValue", "undefined", "value", "localStorage", "getItem", "_unused", "set", "setItem", "_unused2", "subscribe", "handler", "listener", "event", "newValue", "addEventListener", "removeEventListener"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/system/esm/cssVars/localStorageManager.js"], "sourcesContent": ["function noop() {}\nconst localStorageManager = ({\n  key,\n  storageWindow\n}) => {\n  if (!storageWindow && typeof window !== 'undefined') {\n    storageWindow = window;\n  }\n  return {\n    get(defaultValue) {\n      if (typeof window === 'undefined') {\n        return undefined;\n      }\n      if (!storageWindow) {\n        return defaultValue;\n      }\n      let value;\n      try {\n        value = storageWindow.localStorage.getItem(key);\n      } catch {\n        // Unsupported\n      }\n      return value || defaultValue;\n    },\n    set: value => {\n      if (storageWindow) {\n        try {\n          storageWindow.localStorage.setItem(key, value);\n        } catch {\n          // Unsupported\n        }\n      }\n    },\n    subscribe: handler => {\n      if (!storageWindow) {\n        return noop;\n      }\n      const listener = event => {\n        const value = event.newValue;\n        if (event.key === key) {\n          handler(value);\n        }\n      };\n      storageWindow.addEventListener('storage', listener);\n      return () => {\n        storageWindow.removeEventListener('storage', listener);\n      };\n    }\n  };\n};\nexport default localStorageManager;"], "mappings": "AAAA,SAASA,IAAIA,CAAA,EAAG,CAAC;AACjB,MAAMC,mBAAmB,GAAGC,IAAA,IAGtB;EAAA,IAHuB;IAC3BC,GAAG;IACHC;EACF,CAAC,GAAAF,IAAA;EACC,IAAI,CAACE,aAAa,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACnDD,aAAa,GAAGC,MAAM;EACxB;EACA,OAAO;IACLC,GAAGA,CAACC,YAAY,EAAE;MAChB,IAAI,OAAOF,MAAM,KAAK,WAAW,EAAE;QACjC,OAAOG,SAAS;MAClB;MACA,IAAI,CAACJ,aAAa,EAAE;QAClB,OAAOG,YAAY;MACrB;MACA,IAAIE,KAAK;MACT,IAAI;QACFA,KAAK,GAAGL,aAAa,CAACM,YAAY,CAACC,OAAO,CAACR,GAAG,CAAC;MACjD,CAAC,CAAC,OAAAS,OAAA,EAAM;QACN;MAAA;MAEF,OAAOH,KAAK,IAAIF,YAAY;IAC9B,CAAC;IACDM,GAAG,EAAEJ,KAAK,IAAI;MACZ,IAAIL,aAAa,EAAE;QACjB,IAAI;UACFA,aAAa,CAACM,YAAY,CAACI,OAAO,CAACX,GAAG,EAAEM,KAAK,CAAC;QAChD,CAAC,CAAC,OAAAM,QAAA,EAAM;UACN;QAAA;MAEJ;IACF,CAAC;IACDC,SAAS,EAAEC,OAAO,IAAI;MACpB,IAAI,CAACb,aAAa,EAAE;QAClB,OAAOJ,IAAI;MACb;MACA,MAAMkB,QAAQ,GAAGC,KAAK,IAAI;QACxB,MAAMV,KAAK,GAAGU,KAAK,CAACC,QAAQ;QAC5B,IAAID,KAAK,CAAChB,GAAG,KAAKA,GAAG,EAAE;UACrBc,OAAO,CAACR,KAAK,CAAC;QAChB;MACF,CAAC;MACDL,aAAa,CAACiB,gBAAgB,CAAC,SAAS,EAAEH,QAAQ,CAAC;MACnD,OAAO,MAAM;QACXd,aAAa,CAACkB,mBAAmB,CAAC,SAAS,EAAEJ,QAAQ,CAAC;MACxD,CAAC;IACH;EACF,CAAC;AACH,CAAC;AACD,eAAejB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}