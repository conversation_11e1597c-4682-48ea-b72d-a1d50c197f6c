{"ast": null, "code": "import isHostComponent from \"./isHostComponent.js\";\nconst shouldSpreadAdditionalProps = Slot => {\n  return !Slot || !isHostComponent(Slot);\n};\nexport default shouldSpreadAdditionalProps;", "map": {"version": 3, "names": ["isHostComponent", "shouldSpreadAdditionalProps", "Slot"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/utils/shouldSpreadAdditionalProps.js"], "sourcesContent": ["import isHostComponent from \"./isHostComponent.js\";\nconst shouldSpreadAdditionalProps = Slot => {\n  return !Slot || !isHostComponent(Slot);\n};\nexport default shouldSpreadAdditionalProps;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,sBAAsB;AAClD,MAAMC,2BAA2B,GAAGC,IAAI,IAAI;EAC1C,OAAO,CAACA,IAAI,IAAI,CAACF,eAAe,CAACE,IAAI,CAAC;AACxC,CAAC;AACD,eAAeD,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}