{"ast": null, "code": "export const PaymentMethodMap={0:'Tiền mặt',1:'Chuyển khoản',2:'Thẻ tín dụng',3:'<PERSON>h<PERSON><PERSON>'};", "map": {"version": 3, "names": ["PaymentMethodMap"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/models/CustomerPayment.ts"], "sourcesContent": ["export interface CustomerPayment {\n  id?: number;\n  paymentDate: string | Date;\n  paymentMethod: number;\n  paymentAmount: number;\n  note?: string;\n  customerContractId: number;\n  customerId: number;\n  isDeleted?: boolean;\n  createdAt?: string;\n  updatedAt?: string;\n}\n\nexport const PaymentMethodMap: Record<number, string> = {\n  0: 'Tiền mặt',\n  1: 'Chuyển khoản',\n  2: 'Thẻ tín dụng',\n  3: 'Khác'\n};\n"], "mappings": "AAaA,MAAO,MAAM,CAAAA,gBAAwC,CAAG,CACtD,CAAC,CAAE,UAAU,CACb,CAAC,CAAE,cAAc,CACjB,CAAC,CAAE,cAAc,CACjB,CAAC,CAAE,MACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}