{"ast": null, "code": "// Helper function to convert array date format [year, month, day, hour, minute, second, nano] to Date\nexport const arrayToDate=dateArray=>{if(!dateArray||!Array.isArray(dateArray)||dateArray.length<3){return undefined;}try{// Format: [year, month, day, hour, minute, second, nano]\nconst[year,month,day,hour=0,minute=0,second=0]=dateArray;// Note: JavaScript months are 0-based (0 = January, 11 = December)\nreturn new Date(year,month-1,day,hour,minute,second);}catch(error){console.error('Error converting array to date:',error,dateArray);return undefined;}};// Helper function to normalize CustomerRevenue data from API\nexport const normalizeCustomerRevenue=data=>{try{if(!data){console.error('Invalid customer revenue data: data is null or undefined');// Return a default object instead of throwing an error\nreturn{id:0,fullName:'Unknown',phoneNumber:'',address:'',totalRevenue:0};}// Ensure totalRevenue is a number\nlet totalRevenue=0;if(typeof data.totalRevenue==='number'){totalRevenue=data.totalRevenue;}else if(typeof data.totalRevenue==='string'){totalRevenue=parseFloat(data.totalRevenue)||0;}// Safely access properties with fallbacks\nconst id=typeof data.id==='number'?data.id:typeof data.id==='string'?parseInt(data.id,10)||0:0;// Create a normalized customer revenue object\nreturn{id:id,fullName:data.fullName||'',companyName:data.companyName||'',phoneNumber:data.phoneNumber||'',email:data.email||'',address:data.address||'',isDeleted:Boolean(data.isDeleted),totalRevenue:totalRevenue,// Handle different date formats safely\ncreatedAt:safelyParseDate(data.createdAt),updatedAt:safelyParseDate(data.updatedAt)};}catch(error){console.error('Error in normalizeCustomerRevenue:',error);// Return a default object in case of any error\nreturn{id:0,fullName:'Error',phoneNumber:'',address:'',totalRevenue:0};}};// Helper function to safely parse different date formats\nconst safelyParseDate=dateValue=>{try{if(!dateValue)return undefined;if(Array.isArray(dateValue)){return arrayToDate(dateValue);}else if(typeof dateValue==='string'){// Kiểm tra các định dạng chuỗi ngày tháng khác nhau\n// Kiểm tra định dạng ISO (YYYY-MM-DDTHH:mm:ss.sssZ)\nif(dateValue.includes('T')&&(dateValue.includes('Z')||dateValue.includes('+'))){const date=new Date(dateValue);if(!isNaN(date.getTime())){return date;}}// Kiểm tra định dạng YYYY-MM-DD\nif(dateValue.match(/^\\d{4}-\\d{2}-\\d{2}$/)){const[year,month,day]=dateValue.split('-').map(Number);return new Date(year,month-1,day,12,0,0);}// Kiểm tra định dạng DD/MM/YYYY\nif(dateValue.match(/^\\d{2}\\/\\d{2}\\/\\d{4}$/)){const[day,month,year]=dateValue.split('/').map(Number);return new Date(year,month-1,day,12,0,0);}// Thử parse với Date constructor\nconst date=new Date(dateValue);if(!isNaN(date.getTime())){return date;}console.warn('Unrecognized date format:',dateValue);return undefined;}else if(dateValue instanceof Date){return dateValue;}return undefined;}catch(error){console.error('Error parsing date:',error,dateValue);return undefined;}};", "map": {"version": 3, "names": ["arrayToDate", "dateArray", "Array", "isArray", "length", "undefined", "year", "month", "day", "hour", "minute", "second", "Date", "error", "console", "normalizeCustomerRevenue", "data", "id", "fullName", "phoneNumber", "address", "totalRevenue", "parseFloat", "parseInt", "companyName", "email", "isDeleted", "Boolean", "createdAt", "safelyParseDate", "updatedAt", "dateValue", "includes", "date", "isNaN", "getTime", "match", "split", "map", "Number", "warn"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/models/CustomerRevenue.ts"], "sourcesContent": ["import { Customer } from './Customer';\n\nexport interface CustomerRevenue extends Omit<Customer, 'createdAt' | 'updatedAt'> {\n  contractCount?: number; // Optional since we don't need it\n  totalRevenue: number;\n  createdAt?: string | number[] | Date;\n  updatedAt?: string | number[] | Date;\n}\n\n// Helper function to convert array date format [year, month, day, hour, minute, second, nano] to Date\nexport const arrayToDate = (dateArray: number[] | undefined): Date | undefined => {\n  if (!dateArray || !Array.isArray(dateArray) || dateArray.length < 3) {\n    return undefined;\n  }\n\n  try {\n    // Format: [year, month, day, hour, minute, second, nano]\n    const [year, month, day, hour = 0, minute = 0, second = 0] = dateArray;\n    // Note: JavaScript months are 0-based (0 = January, 11 = December)\n    return new Date(year, month - 1, day, hour, minute, second);\n  } catch (error) {\n    console.error('Error converting array to date:', error, dateArray);\n    return undefined;\n  }\n};\n\n// Helper function to normalize CustomerRevenue data from API\nexport const normalizeCustomerRevenue = (data: any): CustomerRevenue => {\n  try {\n    if (!data) {\n      console.error('Invalid customer revenue data: data is null or undefined');\n      // Return a default object instead of throwing an error\n      return {\n        id: 0,\n        fullName: 'Unknown',\n        phoneNumber: '',\n        address: '',\n        totalRevenue: 0\n      } as CustomerRevenue;\n    }\n\n    // Ensure totalRevenue is a number\n    let totalRevenue = 0;\n    if (typeof data.totalRevenue === 'number') {\n      totalRevenue = data.totalRevenue;\n    } else if (typeof data.totalRevenue === 'string') {\n      totalRevenue = parseFloat(data.totalRevenue) || 0;\n    }\n\n    // Safely access properties with fallbacks\n    const id = typeof data.id === 'number' ? data.id :\n               typeof data.id === 'string' ? parseInt(data.id, 10) || 0 : 0;\n\n    // Create a normalized customer revenue object\n    return {\n      id: id,\n      fullName: data.fullName || '',\n      companyName: data.companyName || '',\n      phoneNumber: data.phoneNumber || '',\n      email: data.email || '',\n      address: data.address || '',\n      isDeleted: Boolean(data.isDeleted),\n      totalRevenue: totalRevenue,\n      // Handle different date formats safely\n      createdAt: safelyParseDate(data.createdAt),\n      updatedAt: safelyParseDate(data.updatedAt)\n    };\n  } catch (error) {\n    console.error('Error in normalizeCustomerRevenue:', error);\n    // Return a default object in case of any error\n    return {\n      id: 0,\n      fullName: 'Error',\n      phoneNumber: '',\n      address: '',\n      totalRevenue: 0\n    } as CustomerRevenue;\n  }\n};\n\n// Helper function to safely parse different date formats\nconst safelyParseDate = (dateValue: any): Date | undefined => {\n  try {\n    if (!dateValue) return undefined;\n\n    if (Array.isArray(dateValue)) {\n      return arrayToDate(dateValue);\n    } else if (typeof dateValue === 'string') {\n      // Kiểm tra các định dạng chuỗi ngày tháng khác nhau\n\n      // Kiểm tra định dạng ISO (YYYY-MM-DDTHH:mm:ss.sssZ)\n      if (dateValue.includes('T') && (dateValue.includes('Z') || dateValue.includes('+'))) {\n        const date = new Date(dateValue);\n        if (!isNaN(date.getTime())) {\n          return date;\n        }\n      }\n\n      // Kiểm tra định dạng YYYY-MM-DD\n      if (dateValue.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n        const [year, month, day] = dateValue.split('-').map(Number);\n        return new Date(year, month - 1, day, 12, 0, 0);\n      }\n\n      // Kiểm tra định dạng DD/MM/YYYY\n      if (dateValue.match(/^\\d{2}\\/\\d{2}\\/\\d{4}$/)) {\n        const [day, month, year] = dateValue.split('/').map(Number);\n        return new Date(year, month - 1, day, 12, 0, 0);\n      }\n\n      // Thử parse với Date constructor\n      const date = new Date(dateValue);\n      if (!isNaN(date.getTime())) {\n        return date;\n      }\n\n      console.warn('Unrecognized date format:', dateValue);\n      return undefined;\n    } else if (dateValue instanceof Date) {\n      return dateValue;\n    }\n    return undefined;\n  } catch (error) {\n    console.error('Error parsing date:', error, dateValue);\n    return undefined;\n  }\n};\n"], "mappings": "AASA;AACA,MAAO,MAAM,CAAAA,WAAW,CAAIC,SAA+B,EAAuB,CAChF,GAAI,CAACA,SAAS,EAAI,CAACC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAIA,SAAS,CAACG,MAAM,CAAG,CAAC,CAAE,CACnE,MAAO,CAAAC,SAAS,CAClB,CAEA,GAAI,CACF;AACA,KAAM,CAACC,IAAI,CAAEC,KAAK,CAAEC,GAAG,CAAEC,IAAI,CAAG,CAAC,CAAEC,MAAM,CAAG,CAAC,CAAEC,MAAM,CAAG,CAAC,CAAC,CAAGV,SAAS,CACtE;AACA,MAAO,IAAI,CAAAW,IAAI,CAACN,IAAI,CAAEC,KAAK,CAAG,CAAC,CAAEC,GAAG,CAAEC,IAAI,CAAEC,MAAM,CAAEC,MAAM,CAAC,CAC7D,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAEZ,SAAS,CAAC,CAClE,MAAO,CAAAI,SAAS,CAClB,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAU,wBAAwB,CAAIC,IAAS,EAAsB,CACtE,GAAI,CACF,GAAI,CAACA,IAAI,CAAE,CACTF,OAAO,CAACD,KAAK,CAAC,0DAA0D,CAAC,CACzE;AACA,MAAO,CACLI,EAAE,CAAE,CAAC,CACLC,QAAQ,CAAE,SAAS,CACnBC,WAAW,CAAE,EAAE,CACfC,OAAO,CAAE,EAAE,CACXC,YAAY,CAAE,CAChB,CAAC,CACH,CAEA;AACA,GAAI,CAAAA,YAAY,CAAG,CAAC,CACpB,GAAI,MAAO,CAAAL,IAAI,CAACK,YAAY,GAAK,QAAQ,CAAE,CACzCA,YAAY,CAAGL,IAAI,CAACK,YAAY,CAClC,CAAC,IAAM,IAAI,MAAO,CAAAL,IAAI,CAACK,YAAY,GAAK,QAAQ,CAAE,CAChDA,YAAY,CAAGC,UAAU,CAACN,IAAI,CAACK,YAAY,CAAC,EAAI,CAAC,CACnD,CAEA;AACA,KAAM,CAAAJ,EAAE,CAAG,MAAO,CAAAD,IAAI,CAACC,EAAE,GAAK,QAAQ,CAAGD,IAAI,CAACC,EAAE,CACrC,MAAO,CAAAD,IAAI,CAACC,EAAE,GAAK,QAAQ,CAAGM,QAAQ,CAACP,IAAI,CAACC,EAAE,CAAE,EAAE,CAAC,EAAI,CAAC,CAAG,CAAC,CAEvE;AACA,MAAO,CACLA,EAAE,CAAEA,EAAE,CACNC,QAAQ,CAAEF,IAAI,CAACE,QAAQ,EAAI,EAAE,CAC7BM,WAAW,CAAER,IAAI,CAACQ,WAAW,EAAI,EAAE,CACnCL,WAAW,CAAEH,IAAI,CAACG,WAAW,EAAI,EAAE,CACnCM,KAAK,CAAET,IAAI,CAACS,KAAK,EAAI,EAAE,CACvBL,OAAO,CAAEJ,IAAI,CAACI,OAAO,EAAI,EAAE,CAC3BM,SAAS,CAAEC,OAAO,CAACX,IAAI,CAACU,SAAS,CAAC,CAClCL,YAAY,CAAEA,YAAY,CAC1B;AACAO,SAAS,CAAEC,eAAe,CAACb,IAAI,CAACY,SAAS,CAAC,CAC1CE,SAAS,CAAED,eAAe,CAACb,IAAI,CAACc,SAAS,CAC3C,CAAC,CACH,CAAE,MAAOjB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1D;AACA,MAAO,CACLI,EAAE,CAAE,CAAC,CACLC,QAAQ,CAAE,OAAO,CACjBC,WAAW,CAAE,EAAE,CACfC,OAAO,CAAE,EAAE,CACXC,YAAY,CAAE,CAChB,CAAC,CACH,CACF,CAAC,CAED;AACA,KAAM,CAAAQ,eAAe,CAAIE,SAAc,EAAuB,CAC5D,GAAI,CACF,GAAI,CAACA,SAAS,CAAE,MAAO,CAAA1B,SAAS,CAEhC,GAAIH,KAAK,CAACC,OAAO,CAAC4B,SAAS,CAAC,CAAE,CAC5B,MAAO,CAAA/B,WAAW,CAAC+B,SAAS,CAAC,CAC/B,CAAC,IAAM,IAAI,MAAO,CAAAA,SAAS,GAAK,QAAQ,CAAE,CACxC;AAEA;AACA,GAAIA,SAAS,CAACC,QAAQ,CAAC,GAAG,CAAC,GAAKD,SAAS,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAID,SAAS,CAACC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAE,CACnF,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAArB,IAAI,CAACmB,SAAS,CAAC,CAChC,GAAI,CAACG,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAAE,CAC1B,MAAO,CAAAF,IAAI,CACb,CACF,CAEA;AACA,GAAIF,SAAS,CAACK,KAAK,CAAC,qBAAqB,CAAC,CAAE,CAC1C,KAAM,CAAC9B,IAAI,CAAEC,KAAK,CAAEC,GAAG,CAAC,CAAGuB,SAAS,CAACM,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC,CAC3D,MAAO,IAAI,CAAA3B,IAAI,CAACN,IAAI,CAAEC,KAAK,CAAG,CAAC,CAAEC,GAAG,CAAE,EAAE,CAAE,CAAC,CAAE,CAAC,CAAC,CACjD,CAEA;AACA,GAAIuB,SAAS,CAACK,KAAK,CAAC,uBAAuB,CAAC,CAAE,CAC5C,KAAM,CAAC5B,GAAG,CAAED,KAAK,CAAED,IAAI,CAAC,CAAGyB,SAAS,CAACM,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC,CAC3D,MAAO,IAAI,CAAA3B,IAAI,CAACN,IAAI,CAAEC,KAAK,CAAG,CAAC,CAAEC,GAAG,CAAE,EAAE,CAAE,CAAC,CAAE,CAAC,CAAC,CACjD,CAEA;AACA,KAAM,CAAAyB,IAAI,CAAG,GAAI,CAAArB,IAAI,CAACmB,SAAS,CAAC,CAChC,GAAI,CAACG,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAAE,CAC1B,MAAO,CAAAF,IAAI,CACb,CAEAnB,OAAO,CAAC0B,IAAI,CAAC,2BAA2B,CAAET,SAAS,CAAC,CACpD,MAAO,CAAA1B,SAAS,CAClB,CAAC,IAAM,IAAI0B,SAAS,WAAY,CAAAnB,IAAI,CAAE,CACpC,MAAO,CAAAmB,SAAS,CAClB,CACA,MAAO,CAAA1B,SAAS,CAClB,CAAE,MAAOQ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAEkB,SAAS,CAAC,CACtD,MAAO,CAAA1B,SAAS,CAClB,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}