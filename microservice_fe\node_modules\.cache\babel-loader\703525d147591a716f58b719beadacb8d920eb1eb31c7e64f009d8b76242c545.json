{"ast": null, "code": "import _defineProperty from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport { constructFrom } from \"../../../constructFrom.js\";\nimport { Parser } from \"../Parser.js\";\nimport { parseAnyDigitsSigned } from \"../utils.js\";\nexport class TimestampSecondsParser extends Parser {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"priority\", 40);\n    _defineProperty(this, \"incompatibleTokens\", \"*\");\n  }\n  parse(dateString) {\n    return parseAnyDigitsSigned(dateString);\n  }\n  set(date, _flags, value) {\n    return [constructFrom(date, value * 1000), {\n      timestampIsSet: true\n    }];\n  }\n}", "map": {"version": 3, "names": ["constructFrom", "<PERSON><PERSON><PERSON>", "parseAnyDigitsSigned", "TimestampSecondsParser", "constructor", "arguments", "_defineProperty", "parse", "dateString", "set", "date", "_flags", "value", "timestampIsSet"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/date-fns/parse/_lib/parsers/TimestampSecondsParser.js"], "sourcesContent": ["import { constructFrom } from \"../../../constructFrom.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseAnyDigitsSigned } from \"../utils.js\";\n\nexport class TimestampSecondsParser extends Parser {\n  priority = 40;\n\n  parse(dateString) {\n    return parseAnyDigitsSigned(dateString);\n  }\n\n  set(date, _flags, value) {\n    return [constructFrom(date, value * 1000), { timestampIsSet: true }];\n  }\n\n  incompatibleTokens = \"*\";\n}\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,MAAM,QAAQ,cAAc;AAErC,SAASC,oBAAoB,QAAQ,aAAa;AAElD,OAAO,MAAMC,sBAAsB,SAASF,MAAM,CAAC;EAAAG,YAAA;IAAA,SAAAC,SAAA;IAAAC,eAAA,mBACtC,EAAE;IAAAA,eAAA,6BAUQ,GAAG;EAAA;EARxBC,KAAKA,CAACC,UAAU,EAAE;IAChB,OAAON,oBAAoB,CAACM,UAAU,CAAC;EACzC;EAEAC,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE;IACvB,OAAO,CAACZ,aAAa,CAACU,IAAI,EAAEE,KAAK,GAAG,IAAI,CAAC,EAAE;MAAEC,cAAc,EAAE;IAAK,CAAC,CAAC;EACtE;AAGF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}