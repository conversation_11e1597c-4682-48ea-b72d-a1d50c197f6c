{"ast": null, "code": "'use client';\n\n/* eslint-disable no-constant-condition */\nimport _objectSpread from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport * as React from 'react';\nimport setRef from '@mui/utils/setRef';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useControlled from '@mui/utils/useControlled';\nimport useId from '@mui/utils/useId';\nimport usePreviousProps from '@mui/utils/usePreviousProps';\n\n// https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\nfunction stripDiacritics(string) {\n  return string.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');\n}\nexport function createFilterOptions() {\n  let config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    ignoreAccents = true,\n    ignoreCase = true,\n    limit,\n    matchFrom = 'any',\n    stringify,\n    trim = false\n  } = config;\n  return (options, _ref) => {\n    let {\n      inputValue,\n      getOptionLabel\n    } = _ref;\n    let input = trim ? inputValue.trim() : inputValue;\n    if (ignoreCase) {\n      input = input.toLowerCase();\n    }\n    if (ignoreAccents) {\n      input = stripDiacritics(input);\n    }\n    const filteredOptions = !input ? options : options.filter(option => {\n      let candidate = (stringify || getOptionLabel)(option);\n      if (ignoreCase) {\n        candidate = candidate.toLowerCase();\n      }\n      if (ignoreAccents) {\n        candidate = stripDiacritics(candidate);\n      }\n      return matchFrom === 'start' ? candidate.startsWith(input) : candidate.includes(input);\n    });\n    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;\n  };\n}\nconst defaultFilterOptions = createFilterOptions();\n\n// Number of options to jump in list box when `Page Up` and `Page Down` keys are used.\nconst pageSize = 5;\nconst defaultIsActiveElementInListbox = listboxRef => {\n  var _listboxRef$current$p;\n  return listboxRef.current !== null && ((_listboxRef$current$p = listboxRef.current.parentElement) === null || _listboxRef$current$p === void 0 ? void 0 : _listboxRef$current$p.contains(document.activeElement));\n};\nconst MULTIPLE_DEFAULT_VALUE = [];\nfunction getInputValue(value, multiple, getOptionLabel, renderValue) {\n  if (multiple || value == null || renderValue) {\n    return '';\n  }\n  const optionLabel = getOptionLabel(value);\n  return typeof optionLabel === 'string' ? optionLabel : '';\n}\nfunction useAutocomplete(props) {\n  const {\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_isActiveElementInListbox = defaultIsActiveElementInListbox,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_classNamePrefix = 'Mui',\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    componentName = 'useAutocomplete',\n    defaultValue = props.multiple ? MULTIPLE_DEFAULT_VALUE : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled: disabledProp,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    filterOptions = defaultFilterOptions,\n    filterSelectedOptions = false,\n    freeSolo = false,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp = option => {\n      var _option$label;\n      return (_option$label = option.label) !== null && _option$label !== void 0 ? _option$label : option;\n    },\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    isOptionEqualToValue = (option, value) => option === value,\n    multiple = false,\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open: openProp,\n    openOnFocus = false,\n    options,\n    readOnly = false,\n    renderValue,\n    selectOnFocus = !props.freeSolo,\n    value: valueProp\n  } = props;\n  const id = useId(idProp);\n  let getOptionLabel = getOptionLabelProp;\n  getOptionLabel = option => {\n    const optionLabel = getOptionLabelProp(option);\n    if (typeof optionLabel !== 'string') {\n      if (process.env.NODE_ENV !== 'production') {\n        const erroneousReturn = optionLabel === undefined ? 'undefined' : \"\".concat(typeof optionLabel, \" (\").concat(optionLabel, \")\");\n        console.error(\"MUI: The `getOptionLabel` method of \".concat(componentName, \" returned \").concat(erroneousReturn, \" instead of a string for \").concat(JSON.stringify(option), \".\"));\n      }\n      return String(optionLabel);\n    }\n    return optionLabel;\n  };\n  const ignoreFocus = React.useRef(false);\n  const firstFocus = React.useRef(true);\n  const inputRef = React.useRef(null);\n  const listboxRef = React.useRef(null);\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const [focusedItem, setFocusedItem] = React.useState(-1);\n  const defaultHighlighted = autoHighlight ? 0 : -1;\n  const highlightedIndexRef = React.useRef(defaultHighlighted);\n\n  // Calculate the initial inputValue on mount only.\n  // useRef ensures it doesn't update dynamically with defaultValue or value props.\n  const initialInputValue = React.useRef(getInputValue(defaultValue !== null && defaultValue !== void 0 ? defaultValue : valueProp, multiple, getOptionLabel)).current;\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: componentName\n  });\n  const [inputValue, setInputValueState] = useControlled({\n    controlled: inputValueProp,\n    default: initialInputValue,\n    name: componentName,\n    state: 'inputValue'\n  });\n  const [focused, setFocused] = React.useState(false);\n  const resetInputValue = React.useCallback((event, newValue, reason) => {\n    // retain current `inputValue` if new option isn't selected and `clearOnBlur` is false\n    // When `multiple` is enabled, `newValue` is an array of all selected items including the newly selected item\n    const isOptionSelected = multiple ? value.length < newValue.length : newValue !== null;\n    if (!isOptionSelected && !clearOnBlur) {\n      return;\n    }\n    const newInputValue = getInputValue(newValue, multiple, getOptionLabel, renderValue);\n    if (inputValue === newInputValue) {\n      return;\n    }\n    setInputValueState(newInputValue);\n    if (onInputChange) {\n      onInputChange(event, newInputValue, reason);\n    }\n  }, [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value, renderValue]);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: componentName,\n    state: 'open'\n  });\n  const [inputPristine, setInputPristine] = React.useState(true);\n  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);\n  const popupOpen = open && !readOnly;\n  const filteredOptions = popupOpen ? filterOptions(options.filter(option => {\n    if (filterSelectedOptions && (multiple ? value : [value]).some(value2 => value2 !== null && isOptionEqualToValue(option, value2))) {\n      return false;\n    }\n    return true;\n  }),\n  // we use the empty string to manipulate `filterOptions` to not filter any options\n  // i.e. the filter predicate always returns true\n  {\n    inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,\n    getOptionLabel\n  }) : [];\n  const previousProps = usePreviousProps({\n    filteredOptions,\n    value,\n    inputValue\n  });\n  React.useEffect(() => {\n    const valueChange = value !== previousProps.value;\n    if (focused && !valueChange) {\n      return;\n    }\n\n    // Only reset the input's value when freeSolo if the component's value changes.\n    if (freeSolo && !valueChange) {\n      return;\n    }\n    resetInputValue(null, value, 'reset');\n  }, [value, resetInputValue, focused, previousProps.value, freeSolo]);\n  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;\n  const focusItem = useEventCallback(itemToFocus => {\n    if (itemToFocus === -1) {\n      inputRef.current.focus();\n    } else {\n      // Using `data-tag-index` for deprecated `renderTags`. Remove when `renderTags` is gone.\n      const indexType = renderValue ? 'data-item-index' : 'data-tag-index';\n      anchorEl.querySelector(\"[\".concat(indexType, \"=\\\"\").concat(itemToFocus, \"\\\"]\")).focus();\n    }\n  });\n\n  // Ensure the focusedItem is never inconsistent\n  React.useEffect(() => {\n    if (multiple && focusedItem > value.length - 1) {\n      setFocusedItem(-1);\n      focusItem(-1);\n    }\n  }, [value, multiple, focusedItem, focusItem]);\n  function validOptionIndex(index, direction) {\n    if (!listboxRef.current || index < 0 || index >= filteredOptions.length) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      const option = listboxRef.current.querySelector(\"[data-option-index=\\\"\".concat(nextFocus, \"\\\"]\"));\n\n      // Same logic as MenuList.js\n      const nextFocusDisabled = disabledItemsFocusable ? false : !option || option.disabled || option.getAttribute('aria-disabled') === 'true';\n      if (option && option.hasAttribute('tabindex') && !nextFocusDisabled) {\n        // The next option is available\n        return nextFocus;\n      }\n\n      // The next option is disabled, move to the next element.\n      // with looped index\n      if (direction === 'next') {\n        nextFocus = (nextFocus + 1) % filteredOptions.length;\n      } else {\n        nextFocus = (nextFocus - 1 + filteredOptions.length) % filteredOptions.length;\n      }\n\n      // We end up with initial index, that means we don't have available options.\n      // All of them are disabled\n      if (nextFocus === index) {\n        return -1;\n      }\n    }\n  }\n  const setHighlightedIndex = useEventCallback(_ref2 => {\n    let {\n      event,\n      index,\n      reason\n    } = _ref2;\n    highlightedIndexRef.current = index;\n\n    // does the index exist?\n    if (index === -1) {\n      inputRef.current.removeAttribute('aria-activedescendant');\n    } else {\n      inputRef.current.setAttribute('aria-activedescendant', \"\".concat(id, \"-option-\").concat(index));\n    }\n    if (onHighlightChange && ['mouse', 'keyboard', 'touch'].includes(reason)) {\n      onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n    const prev = listboxRef.current.querySelector(\"[role=\\\"option\\\"].\".concat(unstable_classNamePrefix, \"-focused\"));\n    if (prev) {\n      prev.classList.remove(\"\".concat(unstable_classNamePrefix, \"-focused\"));\n      prev.classList.remove(\"\".concat(unstable_classNamePrefix, \"-focusVisible\"));\n    }\n    let listboxNode = listboxRef.current;\n    if (listboxRef.current.getAttribute('role') !== 'listbox') {\n      listboxNode = listboxRef.current.parentElement.querySelector('[role=\"listbox\"]');\n    }\n\n    // \"No results\"\n    if (!listboxNode) {\n      return;\n    }\n    if (index === -1) {\n      listboxNode.scrollTop = 0;\n      return;\n    }\n    const option = listboxRef.current.querySelector(\"[data-option-index=\\\"\".concat(index, \"\\\"]\"));\n    if (!option) {\n      return;\n    }\n    option.classList.add(\"\".concat(unstable_classNamePrefix, \"-focused\"));\n    if (reason === 'keyboard') {\n      option.classList.add(\"\".concat(unstable_classNamePrefix, \"-focusVisible\"));\n    }\n\n    // Scroll active descendant into view.\n    // Logic copied from https://www.w3.org/WAI/content-assets/wai-aria-practices/patterns/combobox/examples/js/select-only.js\n    // In case of mouse clicks and touch (in mobile devices) we avoid scrolling the element and keep both behaviors same.\n    // Consider this API instead once it has a better browser support:\n    // .scrollIntoView({ scrollMode: 'if-needed', block: 'nearest' });\n    if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse' && reason !== 'touch') {\n      const element = option;\n      const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;\n      const elementBottom = element.offsetTop + element.offsetHeight;\n      if (elementBottom > scrollBottom) {\n        listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;\n      } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {\n        listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);\n      }\n    }\n  });\n  const changeHighlightedIndex = useEventCallback(_ref3 => {\n    let {\n      event,\n      diff,\n      direction = 'next',\n      reason\n    } = _ref3;\n    if (!popupOpen) {\n      return;\n    }\n    const getNextIndex = () => {\n      const maxIndex = filteredOptions.length - 1;\n      if (diff === 'reset') {\n        return defaultHighlighted;\n      }\n      if (diff === 'start') {\n        return 0;\n      }\n      if (diff === 'end') {\n        return maxIndex;\n      }\n      const newIndex = highlightedIndexRef.current + diff;\n      if (newIndex < 0) {\n        if (newIndex === -1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap && highlightedIndexRef.current !== -1 || Math.abs(diff) > 1) {\n          return 0;\n        }\n        return maxIndex;\n      }\n      if (newIndex > maxIndex) {\n        if (newIndex === maxIndex + 1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap || Math.abs(diff) > 1) {\n          return maxIndex;\n        }\n        return 0;\n      }\n      return newIndex;\n    };\n    const nextIndex = validOptionIndex(getNextIndex(), direction);\n    setHighlightedIndex({\n      index: nextIndex,\n      reason,\n      event\n    });\n\n    // Sync the content of the input with the highlighted option.\n    if (autoComplete && diff !== 'reset') {\n      if (nextIndex === -1) {\n        inputRef.current.value = inputValue;\n      } else {\n        const option = getOptionLabel(filteredOptions[nextIndex]);\n        inputRef.current.value = option;\n\n        // The portion of the selected suggestion that has not been typed by the user,\n        // a completion string, appears inline after the input cursor in the textbox.\n        const index = option.toLowerCase().indexOf(inputValue.toLowerCase());\n        if (index === 0 && inputValue.length > 0) {\n          inputRef.current.setSelectionRange(inputValue.length, option.length);\n        }\n      }\n    }\n  });\n  const getPreviousHighlightedOptionIndex = () => {\n    const isSameValue = (value1, value2) => {\n      const label1 = value1 ? getOptionLabel(value1) : '';\n      const label2 = value2 ? getOptionLabel(value2) : '';\n      return label1 === label2;\n    };\n    if (highlightedIndexRef.current !== -1 && previousProps.filteredOptions && previousProps.filteredOptions.length !== filteredOptions.length && previousProps.inputValue === inputValue && (multiple ? value.length === previousProps.value.length && previousProps.value.every((val, i) => getOptionLabel(value[i]) === getOptionLabel(val)) : isSameValue(previousProps.value, value))) {\n      const previousHighlightedOption = previousProps.filteredOptions[highlightedIndexRef.current];\n      if (previousHighlightedOption) {\n        return filteredOptions.findIndex(option => {\n          return getOptionLabel(option) === getOptionLabel(previousHighlightedOption);\n        });\n      }\n    }\n    return -1;\n  };\n  const syncHighlightedIndex = React.useCallback(() => {\n    if (!popupOpen) {\n      return;\n    }\n\n    // Check if the previously highlighted option still exists in the updated filtered options list and if the value and inputValue haven't changed\n    // If it exists and the value and the inputValue haven't changed, just update its index, otherwise continue execution\n    const previousHighlightedOptionIndex = getPreviousHighlightedOptionIndex();\n    if (previousHighlightedOptionIndex !== -1) {\n      highlightedIndexRef.current = previousHighlightedOptionIndex;\n      return;\n    }\n    const valueItem = multiple ? value[0] : value;\n\n    // The popup is empty, reset\n    if (filteredOptions.length === 0 || valueItem == null) {\n      changeHighlightedIndex({\n        diff: 'reset'\n      });\n      return;\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n\n    // Synchronize the value with the highlighted index\n    if (valueItem != null) {\n      const currentOption = filteredOptions[highlightedIndexRef.current];\n\n      // Keep the current highlighted index if possible\n      if (multiple && currentOption && value.findIndex(val => isOptionEqualToValue(currentOption, val)) !== -1) {\n        return;\n      }\n      const itemIndex = filteredOptions.findIndex(optionItem => isOptionEqualToValue(optionItem, valueItem));\n      if (itemIndex === -1) {\n        changeHighlightedIndex({\n          diff: 'reset'\n        });\n      } else {\n        setHighlightedIndex({\n          index: itemIndex\n        });\n      }\n      return;\n    }\n\n    // Prevent the highlighted index to leak outside the boundaries.\n    if (highlightedIndexRef.current >= filteredOptions.length - 1) {\n      setHighlightedIndex({\n        index: filteredOptions.length - 1\n      });\n      return;\n    }\n\n    // Restore the focus to the previous index.\n    setHighlightedIndex({\n      index: highlightedIndexRef.current\n    });\n    // Ignore filteredOptions (and options, isOptionEqualToValue, getOptionLabel) not to break the scroll position\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n  // Only sync the highlighted index when the option switch between empty and not\n  filteredOptions.length,\n  // Don't sync the highlighted index with the value when multiple\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  multiple ? false : value, filterSelectedOptions, changeHighlightedIndex, setHighlightedIndex, popupOpen, inputValue, multiple]);\n  const handleListboxRef = useEventCallback(node => {\n    setRef(listboxRef, node);\n    if (!node) {\n      return;\n    }\n    syncHighlightedIndex();\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!inputRef.current || inputRef.current.nodeName !== 'INPUT') {\n        if (inputRef.current && inputRef.current.nodeName === 'TEXTAREA') {\n          console.warn([\"A textarea element was provided to \".concat(componentName, \" where input was expected.\"), \"This is not a supported scenario but it may work under certain conditions.\", \"A textarea keyboard navigation may conflict with Autocomplete controls (for example enter and arrow keys).\", \"Make sure to test keyboard navigation and add custom event handlers if necessary.\"].join('\\n'));\n        } else {\n          console.error([\"MUI: Unable to find the input element. It was resolved to \".concat(inputRef.current, \" while an HTMLInputElement was expected.\"), \"Instead, \".concat(componentName, \" expects an input element.\"), '', componentName === 'useAutocomplete' ? 'Make sure you have bound getInputProps correctly and that the normal ref/effect resolutions order is guaranteed.' : 'Make sure you have customized the input component correctly.'].join('\\n'));\n        }\n      }\n    }, [componentName]);\n  }\n  React.useEffect(() => {\n    syncHighlightedIndex();\n  }, [syncHighlightedIndex]);\n  const handleOpen = event => {\n    if (open) {\n      return;\n    }\n    setOpenState(true);\n    setInputPristine(true);\n    if (onOpen) {\n      onOpen(event);\n    }\n  };\n  const handleClose = (event, reason) => {\n    if (!open) {\n      return;\n    }\n    setOpenState(false);\n    if (onClose) {\n      onClose(event, reason);\n    }\n  };\n  const handleValue = (event, newValue, reason, details) => {\n    if (multiple) {\n      if (value.length === newValue.length && value.every((val, i) => val === newValue[i])) {\n        return;\n      }\n    } else if (value === newValue) {\n      return;\n    }\n    if (onChange) {\n      onChange(event, newValue, reason, details);\n    }\n    setValueState(newValue);\n  };\n  const isTouch = React.useRef(false);\n  const selectNewValue = function (event, option) {\n    let reasonProp = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'selectOption';\n    let origin = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'options';\n    let reason = reasonProp;\n    let newValue = option;\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      if (process.env.NODE_ENV !== 'production') {\n        const matches = newValue.filter(val => isOptionEqualToValue(option, val));\n        if (matches.length > 1) {\n          console.error([\"MUI: The `isOptionEqualToValue` method of \".concat(componentName, \" does not handle the arguments correctly.\"), \"The component expects a single value to match a given option but found \".concat(matches.length, \" matches.\")].join('\\n'));\n        }\n      }\n      const itemIndex = newValue.findIndex(valueItem => isOptionEqualToValue(option, valueItem));\n      if (itemIndex === -1) {\n        newValue.push(option);\n      } else if (origin !== 'freeSolo') {\n        newValue.splice(itemIndex, 1);\n        reason = 'removeOption';\n      }\n    }\n    resetInputValue(event, newValue, reason);\n    handleValue(event, newValue, reason, {\n      option\n    });\n    if (!disableCloseOnSelect && (!event || !event.ctrlKey && !event.metaKey)) {\n      handleClose(event, reason);\n    }\n    if (blurOnSelect === true || blurOnSelect === 'touch' && isTouch.current || blurOnSelect === 'mouse' && !isTouch.current) {\n      inputRef.current.blur();\n    }\n  };\n  function validItemIndex(index, direction) {\n    if (index === -1) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === value.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n\n      // Using `data-tag-index` for deprecated `renderTags`. Remove when `renderTags` is removed.\n      const indexType = renderValue ? 'data-item-index' : 'data-tag-index';\n      const option = anchorEl.querySelector(\"[\".concat(indexType, \"=\\\"\").concat(nextFocus, \"\\\"]\"));\n\n      // Same logic as MenuList.js\n      if (!option || !option.hasAttribute('tabindex') || option.disabled || option.getAttribute('aria-disabled') === 'true') {\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n  const handleFocusItem = (event, direction) => {\n    if (!multiple) {\n      return;\n    }\n    if (inputValue === '') {\n      handleClose(event, 'toggleInput');\n    }\n    let nextItem = focusedItem;\n    if (focusedItem === -1) {\n      if (inputValue === '' && direction === 'previous') {\n        nextItem = value.length - 1;\n      }\n    } else {\n      nextItem += direction === 'next' ? 1 : -1;\n      if (nextItem < 0) {\n        nextItem = 0;\n      }\n      if (nextItem === value.length) {\n        nextItem = -1;\n      }\n    }\n    nextItem = validItemIndex(nextItem, direction);\n    setFocusedItem(nextItem);\n    focusItem(nextItem);\n  };\n  const handleClear = event => {\n    ignoreFocus.current = true;\n    setInputValueState('');\n    if (onInputChange) {\n      onInputChange(event, '', 'clear');\n    }\n    handleValue(event, multiple ? [] : null, 'clear');\n  };\n  const handleKeyDown = other => event => {\n    if (other.onKeyDown) {\n      other.onKeyDown(event);\n    }\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (focusedItem !== -1 && !['ArrowLeft', 'ArrowRight'].includes(event.key)) {\n      setFocusedItem(-1);\n      focusItem(-1);\n    }\n\n    // Wait until IME is settled.\n    if (event.which !== 229) {\n      switch (event.key) {\n        case 'Home':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'start',\n              direction: 'next',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'End':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'end',\n              direction: 'previous',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'PageUp':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -pageSize,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'PageDown':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: pageSize,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowDown':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: 1,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowUp':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -1,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowLeft':\n          if (!multiple && renderValue) {\n            focusItem(0);\n          } else {\n            handleFocusItem(event, 'previous');\n          }\n          break;\n        case 'ArrowRight':\n          if (!multiple && renderValue) {\n            focusItem(-1);\n          } else {\n            handleFocusItem(event, 'next');\n          }\n          break;\n        case 'Enter':\n          if (highlightedIndexRef.current !== -1 && popupOpen) {\n            const option = filteredOptions[highlightedIndexRef.current];\n            const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n\n            // Avoid early form validation, let the end-users continue filling the form.\n            event.preventDefault();\n            if (disabled) {\n              return;\n            }\n            selectNewValue(event, option, 'selectOption');\n\n            // Move the selection to the end.\n            if (autoComplete) {\n              inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);\n            }\n          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {\n            if (multiple) {\n              // Allow people to add new values before they submit the form.\n              event.preventDefault();\n            }\n            selectNewValue(event, inputValue, 'createOption', 'freeSolo');\n          }\n          break;\n        case 'Escape':\n          if (popupOpen) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClose(event, 'escape');\n          } else if (clearOnEscape && (inputValue !== '' || multiple && value.length > 0 || renderValue)) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClear(event);\n          }\n          break;\n        case 'Backspace':\n          // Remove the value on the left of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0) {\n            const index = focusedItem === -1 ? value.length - 1 : focusedItem;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          if (!multiple && renderValue && !readOnly) {\n            setValueState(null);\n            focusItem(-1);\n          }\n          break;\n        case 'Delete':\n          // Remove the value on the right of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0 && focusedItem !== -1) {\n            const index = focusedItem;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          if (!multiple && renderValue && !readOnly) {\n            setValueState(null);\n            focusItem(-1);\n          }\n          break;\n        default:\n      }\n    }\n  };\n  const handleFocus = event => {\n    setFocused(true);\n    if (openOnFocus && !ignoreFocus.current) {\n      handleOpen(event);\n    }\n  };\n  const handleBlur = event => {\n    // Ignore the event when using the scrollbar with IE11\n    if (unstable_isActiveElementInListbox(listboxRef)) {\n      inputRef.current.focus();\n      return;\n    }\n    setFocused(false);\n    firstFocus.current = true;\n    ignoreFocus.current = false;\n    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {\n      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');\n    } else if (autoSelect && freeSolo && inputValue !== '') {\n      selectNewValue(event, inputValue, 'blur', 'freeSolo');\n    } else if (clearOnBlur) {\n      resetInputValue(event, value, 'blur');\n    }\n    handleClose(event, 'blur');\n  };\n  const handleInputChange = event => {\n    const newValue = event.target.value;\n    if (inputValue !== newValue) {\n      setInputValueState(newValue);\n      setInputPristine(false);\n      if (onInputChange) {\n        onInputChange(event, newValue, 'input');\n      }\n    }\n    if (newValue === '') {\n      if (!disableClearable && !multiple) {\n        handleValue(event, null, 'clear');\n      }\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleOptionMouseMove = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    if (highlightedIndexRef.current !== index) {\n      setHighlightedIndex({\n        event,\n        index,\n        reason: 'mouse'\n      });\n    }\n  };\n  const handleOptionTouchStart = event => {\n    setHighlightedIndex({\n      event,\n      index: Number(event.currentTarget.getAttribute('data-option-index')),\n      reason: 'touch'\n    });\n    isTouch.current = true;\n  };\n  const handleOptionClick = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    selectNewValue(event, filteredOptions[index], 'selectOption');\n    isTouch.current = false;\n  };\n  const handleItemDelete = index => event => {\n    const newValue = value.slice();\n    newValue.splice(index, 1);\n    handleValue(event, newValue, 'removeOption', {\n      option: value[index]\n    });\n  };\n  const handleSingleItemDelete = event => {\n    handleValue(event, null, 'removeOption', {\n      option: value\n    });\n  };\n  const handlePopupIndicator = event => {\n    if (open) {\n      handleClose(event, 'toggleInput');\n    } else {\n      handleOpen(event);\n    }\n  };\n\n  // Prevent input blur when interacting with the combobox\n  const handleMouseDown = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    if (event.target.getAttribute('id') !== id) {\n      event.preventDefault();\n    }\n  };\n\n  // Focus the input when interacting with the combobox\n  const handleClick = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    inputRef.current.focus();\n    if (selectOnFocus && firstFocus.current && inputRef.current.selectionEnd - inputRef.current.selectionStart === 0) {\n      inputRef.current.select();\n    }\n    firstFocus.current = false;\n  };\n  const handleInputMouseDown = event => {\n    if (!disabledProp && (inputValue === '' || !open)) {\n      handlePopupIndicator(event);\n    }\n  };\n  let dirty = freeSolo && inputValue.length > 0;\n  dirty = dirty || (multiple ? value.length > 0 : value !== null);\n  let groupedOptions = filteredOptions;\n  if (groupBy) {\n    // used to keep track of key and indexes in the result array\n    const indexBy = new Map();\n    let warn = false;\n    groupedOptions = filteredOptions.reduce((acc, option, index) => {\n      const group = groupBy(option);\n      if (acc.length > 0 && acc[acc.length - 1].group === group) {\n        acc[acc.length - 1].options.push(option);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          if (indexBy.get(group) && !warn) {\n            console.warn(\"MUI: The options provided combined with the `groupBy` method of \".concat(componentName, \" returns duplicated headers.\"), 'You can solve the issue by sorting the options with the output of `groupBy`.');\n            warn = true;\n          }\n          indexBy.set(group, true);\n        }\n        acc.push({\n          key: index,\n          index,\n          group,\n          options: [option]\n        });\n      }\n      return acc;\n    }, []);\n  }\n  if (disabledProp && focused) {\n    handleBlur();\n  }\n  return {\n    getRootProps: function () {\n      let other = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      return _objectSpread(_objectSpread({}, other), {}, {\n        onKeyDown: handleKeyDown(other),\n        onMouseDown: handleMouseDown,\n        onClick: handleClick\n      });\n    },\n    getInputLabelProps: () => ({\n      id: \"\".concat(id, \"-label\"),\n      htmlFor: id\n    }),\n    getInputProps: () => ({\n      id,\n      value: inputValue,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: handleInputChange,\n      onMouseDown: handleInputMouseDown,\n      // if open then this is handled imperatively so don't let react override\n      // only have an opinion about this when closed\n      'aria-activedescendant': popupOpen ? '' : null,\n      'aria-autocomplete': autoComplete ? 'both' : 'list',\n      'aria-controls': listboxAvailable ? \"\".concat(id, \"-listbox\") : undefined,\n      'aria-expanded': listboxAvailable,\n      // Disable browser's suggestion that might overlap with the popup.\n      // Handle autocomplete but not autofill.\n      autoComplete: 'off',\n      ref: inputRef,\n      autoCapitalize: 'none',\n      spellCheck: 'false',\n      role: 'combobox',\n      disabled: disabledProp\n    }),\n    getClearProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handleClear\n    }),\n    getItemProps: function () {\n      let {\n        index = 0\n      } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      return _objectSpread(_objectSpread(_objectSpread({}, multiple && {\n        key: index\n      }), renderValue ? {\n        'data-item-index': index\n      } : {\n        'data-tag-index': index\n      }), {}, {\n        tabIndex: -1\n      }, !readOnly && {\n        onDelete: multiple ? handleItemDelete(index) : handleSingleItemDelete\n      });\n    },\n    getPopupIndicatorProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handlePopupIndicator\n    }),\n    // deprecated\n    getTagProps: _ref4 => {\n      let {\n        index\n      } = _ref4;\n      return _objectSpread({\n        key: index,\n        'data-tag-index': index,\n        tabIndex: -1\n      }, !readOnly && {\n        onDelete: handleItemDelete(index)\n      });\n    },\n    getListboxProps: () => ({\n      role: 'listbox',\n      id: \"\".concat(id, \"-listbox\"),\n      'aria-labelledby': \"\".concat(id, \"-label\"),\n      ref: handleListboxRef,\n      onMouseDown: event => {\n        // Prevent blur\n        event.preventDefault();\n      }\n    }),\n    getOptionProps: _ref5 => {\n      var _getOptionKey;\n      let {\n        index,\n        option\n      } = _ref5;\n      const selected = (multiple ? value : [value]).some(value2 => value2 != null && isOptionEqualToValue(option, value2));\n      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n      return {\n        key: (_getOptionKey = getOptionKey === null || getOptionKey === void 0 ? void 0 : getOptionKey(option)) !== null && _getOptionKey !== void 0 ? _getOptionKey : getOptionLabel(option),\n        tabIndex: -1,\n        role: 'option',\n        id: \"\".concat(id, \"-option-\").concat(index),\n        onMouseMove: handleOptionMouseMove,\n        onClick: handleOptionClick,\n        onTouchStart: handleOptionTouchStart,\n        'data-option-index': index,\n        'aria-disabled': disabled,\n        'aria-selected': selected\n      };\n    },\n    id,\n    inputValue,\n    value,\n    dirty,\n    expanded: popupOpen && anchorEl,\n    popupOpen,\n    focused: focused || focusedItem !== -1,\n    anchorEl,\n    setAnchorEl,\n    focusedItem,\n    // deprecated\n    focusedTag: focusedItem,\n    groupedOptions\n  };\n}\nexport default useAutocomplete;", "map": {"version": 3, "names": ["_objectSpread", "React", "setRef", "useEventCallback", "useControlled", "useId", "usePreviousProps", "stripDiacritics", "string", "normalize", "replace", "createFilterOptions", "config", "arguments", "length", "undefined", "ignoreAccents", "ignoreCase", "limit", "matchFrom", "stringify", "trim", "options", "_ref", "inputValue", "getOptionLabel", "input", "toLowerCase", "filteredOptions", "filter", "option", "candidate", "startsWith", "includes", "slice", "defaultFilterOptions", "pageSize", "defaultIsActiveElementInListbox", "listboxRef", "_listboxRef$current$p", "current", "parentElement", "contains", "document", "activeElement", "MULTIPLE_DEFAULT_VALUE", "getInputValue", "value", "multiple", "renderValue", "optionLabel", "useAutocomplete", "props", "unstable_isActiveElementInListbox", "unstable_classNamePrefix", "autoComplete", "autoHighlight", "autoSelect", "blurOnSelect", "clearOnBlur", "freeSolo", "clearOnEscape", "componentName", "defaultValue", "disableClearable", "disableCloseOnSelect", "disabled", "disabledProp", "disabledItemsFocusable", "disableListWrap", "filterOptions", "filterSelectedOptions", "getOptionDisabled", "getOption<PERSON>ey", "getOptionLabelProp", "_option$label", "label", "groupBy", "handleHomeEndKeys", "id", "idProp", "includeInputInList", "inputValueProp", "isOptionEqualToValue", "onChange", "onClose", "onHighlightChange", "onInputChange", "onOpen", "open", "openProp", "openOnFocus", "readOnly", "selectOnFocus", "valueProp", "process", "env", "NODE_ENV", "erroneousReturn", "concat", "console", "error", "JSON", "String", "ignoreFocus", "useRef", "firstFocus", "inputRef", "anchorEl", "setAnchorEl", "useState", "focusedItem", "setFocusedItem", "defaultHighlighted", "highlightedIndexRef", "initialInputValue", "setValueState", "controlled", "default", "name", "setInputValueState", "state", "focused", "setFocused", "resetInputValue", "useCallback", "event", "newValue", "reason", "isOptionSelected", "newInputValue", "setOpenState", "inputPristine", "setInputPristine", "inputValueIsSelectedValue", "popupOpen", "some", "value2", "previousProps", "useEffect", "valueChange", "listboxAvailable", "focusItem", "itemToFocus", "focus", "indexType", "querySelector", "validOptionIndex", "index", "direction", "nextFocus", "nextFocusDisabled", "getAttribute", "hasAttribute", "setHighlightedIndex", "_ref2", "removeAttribute", "setAttribute", "prev", "classList", "remove", "listboxNode", "scrollTop", "add", "scrollHeight", "clientHeight", "element", "scrollBottom", "elementBottom", "offsetTop", "offsetHeight", "changeHighlightedIndex", "_ref3", "diff", "getNextIndex", "maxIndex", "newIndex", "Math", "abs", "nextIndex", "indexOf", "setSelectionRange", "getPreviousHighlightedOptionIndex", "isSameValue", "value1", "label1", "label2", "every", "val", "i", "previousHighlightedOption", "findIndex", "syncHighlightedIndex", "previousHighlightedOptionIndex", "valueItem", "currentOption", "itemIndex", "optionItem", "handleListboxRef", "node", "nodeName", "warn", "join", "handleOpen", "handleClose", "handleValue", "details", "is<PERSON><PERSON>ch", "selectNewValue", "reasonProp", "origin", "Array", "isArray", "matches", "push", "splice", "ctrl<PERSON>ey", "metaKey", "blur", "validItemIndex", "handleFocusItem", "nextItem", "handleClear", "handleKeyDown", "other", "onKeyDown", "defaultMuiPrevented", "key", "which", "preventDefault", "stopPropagation", "handleFocus", "handleBlur", "handleInputChange", "target", "handleOptionMouseMove", "Number", "currentTarget", "handleOptionTouchStart", "handleOptionClick", "handleItemDelete", "handleSingleItemDelete", "handlePopupIndicator", "handleMouseDown", "handleClick", "selectionEnd", "selectionStart", "select", "handleInputMouseDown", "dirty", "groupedOptions", "indexBy", "Map", "reduce", "acc", "group", "get", "set", "getRootProps", "onMouseDown", "onClick", "getInputLabelProps", "htmlFor", "getInputProps", "onBlur", "onFocus", "ref", "autoCapitalize", "spell<PERSON>heck", "role", "getClearProps", "tabIndex", "type", "getItemProps", "onDelete", "getPopupIndicatorProps", "getTagProps", "_ref4", "getListboxProps", "getOptionProps", "_ref5", "_getO<PERSON><PERSON>ey", "selected", "onMouseMove", "onTouchStart", "expanded", "focusedTag"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/useAutocomplete/useAutocomplete.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable no-constant-condition */\nimport * as React from 'react';\nimport setRef from '@mui/utils/setRef';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useControlled from '@mui/utils/useControlled';\nimport useId from '@mui/utils/useId';\nimport usePreviousProps from '@mui/utils/usePreviousProps';\n\n// https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\nfunction stripDiacritics(string) {\n  return string.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');\n}\nexport function createFilterOptions(config = {}) {\n  const {\n    ignoreAccents = true,\n    ignoreCase = true,\n    limit,\n    matchFrom = 'any',\n    stringify,\n    trim = false\n  } = config;\n  return (options, {\n    inputValue,\n    getOptionLabel\n  }) => {\n    let input = trim ? inputValue.trim() : inputValue;\n    if (ignoreCase) {\n      input = input.toLowerCase();\n    }\n    if (ignoreAccents) {\n      input = stripDiacritics(input);\n    }\n    const filteredOptions = !input ? options : options.filter(option => {\n      let candidate = (stringify || getOptionLabel)(option);\n      if (ignoreCase) {\n        candidate = candidate.toLowerCase();\n      }\n      if (ignoreAccents) {\n        candidate = stripDiacritics(candidate);\n      }\n      return matchFrom === 'start' ? candidate.startsWith(input) : candidate.includes(input);\n    });\n    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;\n  };\n}\nconst defaultFilterOptions = createFilterOptions();\n\n// Number of options to jump in list box when `Page Up` and `Page Down` keys are used.\nconst pageSize = 5;\nconst defaultIsActiveElementInListbox = listboxRef => listboxRef.current !== null && listboxRef.current.parentElement?.contains(document.activeElement);\nconst MULTIPLE_DEFAULT_VALUE = [];\nfunction getInputValue(value, multiple, getOptionLabel, renderValue) {\n  if (multiple || value == null || renderValue) {\n    return '';\n  }\n  const optionLabel = getOptionLabel(value);\n  return typeof optionLabel === 'string' ? optionLabel : '';\n}\nfunction useAutocomplete(props) {\n  const {\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_isActiveElementInListbox = defaultIsActiveElementInListbox,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_classNamePrefix = 'Mui',\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    componentName = 'useAutocomplete',\n    defaultValue = props.multiple ? MULTIPLE_DEFAULT_VALUE : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled: disabledProp,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    filterOptions = defaultFilterOptions,\n    filterSelectedOptions = false,\n    freeSolo = false,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp = option => option.label ?? option,\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    isOptionEqualToValue = (option, value) => option === value,\n    multiple = false,\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open: openProp,\n    openOnFocus = false,\n    options,\n    readOnly = false,\n    renderValue,\n    selectOnFocus = !props.freeSolo,\n    value: valueProp\n  } = props;\n  const id = useId(idProp);\n  let getOptionLabel = getOptionLabelProp;\n  getOptionLabel = option => {\n    const optionLabel = getOptionLabelProp(option);\n    if (typeof optionLabel !== 'string') {\n      if (process.env.NODE_ENV !== 'production') {\n        const erroneousReturn = optionLabel === undefined ? 'undefined' : `${typeof optionLabel} (${optionLabel})`;\n        console.error(`MUI: The \\`getOptionLabel\\` method of ${componentName} returned ${erroneousReturn} instead of a string for ${JSON.stringify(option)}.`);\n      }\n      return String(optionLabel);\n    }\n    return optionLabel;\n  };\n  const ignoreFocus = React.useRef(false);\n  const firstFocus = React.useRef(true);\n  const inputRef = React.useRef(null);\n  const listboxRef = React.useRef(null);\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const [focusedItem, setFocusedItem] = React.useState(-1);\n  const defaultHighlighted = autoHighlight ? 0 : -1;\n  const highlightedIndexRef = React.useRef(defaultHighlighted);\n\n  // Calculate the initial inputValue on mount only.\n  // useRef ensures it doesn't update dynamically with defaultValue or value props.\n  const initialInputValue = React.useRef(getInputValue(defaultValue ?? valueProp, multiple, getOptionLabel)).current;\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: componentName\n  });\n  const [inputValue, setInputValueState] = useControlled({\n    controlled: inputValueProp,\n    default: initialInputValue,\n    name: componentName,\n    state: 'inputValue'\n  });\n  const [focused, setFocused] = React.useState(false);\n  const resetInputValue = React.useCallback((event, newValue, reason) => {\n    // retain current `inputValue` if new option isn't selected and `clearOnBlur` is false\n    // When `multiple` is enabled, `newValue` is an array of all selected items including the newly selected item\n    const isOptionSelected = multiple ? value.length < newValue.length : newValue !== null;\n    if (!isOptionSelected && !clearOnBlur) {\n      return;\n    }\n    const newInputValue = getInputValue(newValue, multiple, getOptionLabel, renderValue);\n    if (inputValue === newInputValue) {\n      return;\n    }\n    setInputValueState(newInputValue);\n    if (onInputChange) {\n      onInputChange(event, newInputValue, reason);\n    }\n  }, [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value, renderValue]);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: componentName,\n    state: 'open'\n  });\n  const [inputPristine, setInputPristine] = React.useState(true);\n  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);\n  const popupOpen = open && !readOnly;\n  const filteredOptions = popupOpen ? filterOptions(options.filter(option => {\n    if (filterSelectedOptions && (multiple ? value : [value]).some(value2 => value2 !== null && isOptionEqualToValue(option, value2))) {\n      return false;\n    }\n    return true;\n  }),\n  // we use the empty string to manipulate `filterOptions` to not filter any options\n  // i.e. the filter predicate always returns true\n  {\n    inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,\n    getOptionLabel\n  }) : [];\n  const previousProps = usePreviousProps({\n    filteredOptions,\n    value,\n    inputValue\n  });\n  React.useEffect(() => {\n    const valueChange = value !== previousProps.value;\n    if (focused && !valueChange) {\n      return;\n    }\n\n    // Only reset the input's value when freeSolo if the component's value changes.\n    if (freeSolo && !valueChange) {\n      return;\n    }\n    resetInputValue(null, value, 'reset');\n  }, [value, resetInputValue, focused, previousProps.value, freeSolo]);\n  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;\n  const focusItem = useEventCallback(itemToFocus => {\n    if (itemToFocus === -1) {\n      inputRef.current.focus();\n    } else {\n      // Using `data-tag-index` for deprecated `renderTags`. Remove when `renderTags` is gone.\n      const indexType = renderValue ? 'data-item-index' : 'data-tag-index';\n      anchorEl.querySelector(`[${indexType}=\"${itemToFocus}\"]`).focus();\n    }\n  });\n\n  // Ensure the focusedItem is never inconsistent\n  React.useEffect(() => {\n    if (multiple && focusedItem > value.length - 1) {\n      setFocusedItem(-1);\n      focusItem(-1);\n    }\n  }, [value, multiple, focusedItem, focusItem]);\n  function validOptionIndex(index, direction) {\n    if (!listboxRef.current || index < 0 || index >= filteredOptions.length) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      const option = listboxRef.current.querySelector(`[data-option-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      const nextFocusDisabled = disabledItemsFocusable ? false : !option || option.disabled || option.getAttribute('aria-disabled') === 'true';\n      if (option && option.hasAttribute('tabindex') && !nextFocusDisabled) {\n        // The next option is available\n        return nextFocus;\n      }\n\n      // The next option is disabled, move to the next element.\n      // with looped index\n      if (direction === 'next') {\n        nextFocus = (nextFocus + 1) % filteredOptions.length;\n      } else {\n        nextFocus = (nextFocus - 1 + filteredOptions.length) % filteredOptions.length;\n      }\n\n      // We end up with initial index, that means we don't have available options.\n      // All of them are disabled\n      if (nextFocus === index) {\n        return -1;\n      }\n    }\n  }\n  const setHighlightedIndex = useEventCallback(({\n    event,\n    index,\n    reason\n  }) => {\n    highlightedIndexRef.current = index;\n\n    // does the index exist?\n    if (index === -1) {\n      inputRef.current.removeAttribute('aria-activedescendant');\n    } else {\n      inputRef.current.setAttribute('aria-activedescendant', `${id}-option-${index}`);\n    }\n    if (onHighlightChange && ['mouse', 'keyboard', 'touch'].includes(reason)) {\n      onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n    const prev = listboxRef.current.querySelector(`[role=\"option\"].${unstable_classNamePrefix}-focused`);\n    if (prev) {\n      prev.classList.remove(`${unstable_classNamePrefix}-focused`);\n      prev.classList.remove(`${unstable_classNamePrefix}-focusVisible`);\n    }\n    let listboxNode = listboxRef.current;\n    if (listboxRef.current.getAttribute('role') !== 'listbox') {\n      listboxNode = listboxRef.current.parentElement.querySelector('[role=\"listbox\"]');\n    }\n\n    // \"No results\"\n    if (!listboxNode) {\n      return;\n    }\n    if (index === -1) {\n      listboxNode.scrollTop = 0;\n      return;\n    }\n    const option = listboxRef.current.querySelector(`[data-option-index=\"${index}\"]`);\n    if (!option) {\n      return;\n    }\n    option.classList.add(`${unstable_classNamePrefix}-focused`);\n    if (reason === 'keyboard') {\n      option.classList.add(`${unstable_classNamePrefix}-focusVisible`);\n    }\n\n    // Scroll active descendant into view.\n    // Logic copied from https://www.w3.org/WAI/content-assets/wai-aria-practices/patterns/combobox/examples/js/select-only.js\n    // In case of mouse clicks and touch (in mobile devices) we avoid scrolling the element and keep both behaviors same.\n    // Consider this API instead once it has a better browser support:\n    // .scrollIntoView({ scrollMode: 'if-needed', block: 'nearest' });\n    if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse' && reason !== 'touch') {\n      const element = option;\n      const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;\n      const elementBottom = element.offsetTop + element.offsetHeight;\n      if (elementBottom > scrollBottom) {\n        listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;\n      } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {\n        listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);\n      }\n    }\n  });\n  const changeHighlightedIndex = useEventCallback(({\n    event,\n    diff,\n    direction = 'next',\n    reason\n  }) => {\n    if (!popupOpen) {\n      return;\n    }\n    const getNextIndex = () => {\n      const maxIndex = filteredOptions.length - 1;\n      if (diff === 'reset') {\n        return defaultHighlighted;\n      }\n      if (diff === 'start') {\n        return 0;\n      }\n      if (diff === 'end') {\n        return maxIndex;\n      }\n      const newIndex = highlightedIndexRef.current + diff;\n      if (newIndex < 0) {\n        if (newIndex === -1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap && highlightedIndexRef.current !== -1 || Math.abs(diff) > 1) {\n          return 0;\n        }\n        return maxIndex;\n      }\n      if (newIndex > maxIndex) {\n        if (newIndex === maxIndex + 1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap || Math.abs(diff) > 1) {\n          return maxIndex;\n        }\n        return 0;\n      }\n      return newIndex;\n    };\n    const nextIndex = validOptionIndex(getNextIndex(), direction);\n    setHighlightedIndex({\n      index: nextIndex,\n      reason,\n      event\n    });\n\n    // Sync the content of the input with the highlighted option.\n    if (autoComplete && diff !== 'reset') {\n      if (nextIndex === -1) {\n        inputRef.current.value = inputValue;\n      } else {\n        const option = getOptionLabel(filteredOptions[nextIndex]);\n        inputRef.current.value = option;\n\n        // The portion of the selected suggestion that has not been typed by the user,\n        // a completion string, appears inline after the input cursor in the textbox.\n        const index = option.toLowerCase().indexOf(inputValue.toLowerCase());\n        if (index === 0 && inputValue.length > 0) {\n          inputRef.current.setSelectionRange(inputValue.length, option.length);\n        }\n      }\n    }\n  });\n  const getPreviousHighlightedOptionIndex = () => {\n    const isSameValue = (value1, value2) => {\n      const label1 = value1 ? getOptionLabel(value1) : '';\n      const label2 = value2 ? getOptionLabel(value2) : '';\n      return label1 === label2;\n    };\n    if (highlightedIndexRef.current !== -1 && previousProps.filteredOptions && previousProps.filteredOptions.length !== filteredOptions.length && previousProps.inputValue === inputValue && (multiple ? value.length === previousProps.value.length && previousProps.value.every((val, i) => getOptionLabel(value[i]) === getOptionLabel(val)) : isSameValue(previousProps.value, value))) {\n      const previousHighlightedOption = previousProps.filteredOptions[highlightedIndexRef.current];\n      if (previousHighlightedOption) {\n        return filteredOptions.findIndex(option => {\n          return getOptionLabel(option) === getOptionLabel(previousHighlightedOption);\n        });\n      }\n    }\n    return -1;\n  };\n  const syncHighlightedIndex = React.useCallback(() => {\n    if (!popupOpen) {\n      return;\n    }\n\n    // Check if the previously highlighted option still exists in the updated filtered options list and if the value and inputValue haven't changed\n    // If it exists and the value and the inputValue haven't changed, just update its index, otherwise continue execution\n    const previousHighlightedOptionIndex = getPreviousHighlightedOptionIndex();\n    if (previousHighlightedOptionIndex !== -1) {\n      highlightedIndexRef.current = previousHighlightedOptionIndex;\n      return;\n    }\n    const valueItem = multiple ? value[0] : value;\n\n    // The popup is empty, reset\n    if (filteredOptions.length === 0 || valueItem == null) {\n      changeHighlightedIndex({\n        diff: 'reset'\n      });\n      return;\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n\n    // Synchronize the value with the highlighted index\n    if (valueItem != null) {\n      const currentOption = filteredOptions[highlightedIndexRef.current];\n\n      // Keep the current highlighted index if possible\n      if (multiple && currentOption && value.findIndex(val => isOptionEqualToValue(currentOption, val)) !== -1) {\n        return;\n      }\n      const itemIndex = filteredOptions.findIndex(optionItem => isOptionEqualToValue(optionItem, valueItem));\n      if (itemIndex === -1) {\n        changeHighlightedIndex({\n          diff: 'reset'\n        });\n      } else {\n        setHighlightedIndex({\n          index: itemIndex\n        });\n      }\n      return;\n    }\n\n    // Prevent the highlighted index to leak outside the boundaries.\n    if (highlightedIndexRef.current >= filteredOptions.length - 1) {\n      setHighlightedIndex({\n        index: filteredOptions.length - 1\n      });\n      return;\n    }\n\n    // Restore the focus to the previous index.\n    setHighlightedIndex({\n      index: highlightedIndexRef.current\n    });\n    // Ignore filteredOptions (and options, isOptionEqualToValue, getOptionLabel) not to break the scroll position\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n  // Only sync the highlighted index when the option switch between empty and not\n  filteredOptions.length,\n  // Don't sync the highlighted index with the value when multiple\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  multiple ? false : value, filterSelectedOptions, changeHighlightedIndex, setHighlightedIndex, popupOpen, inputValue, multiple]);\n  const handleListboxRef = useEventCallback(node => {\n    setRef(listboxRef, node);\n    if (!node) {\n      return;\n    }\n    syncHighlightedIndex();\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!inputRef.current || inputRef.current.nodeName !== 'INPUT') {\n        if (inputRef.current && inputRef.current.nodeName === 'TEXTAREA') {\n          console.warn([`A textarea element was provided to ${componentName} where input was expected.`, `This is not a supported scenario but it may work under certain conditions.`, `A textarea keyboard navigation may conflict with Autocomplete controls (for example enter and arrow keys).`, `Make sure to test keyboard navigation and add custom event handlers if necessary.`].join('\\n'));\n        } else {\n          console.error([`MUI: Unable to find the input element. It was resolved to ${inputRef.current} while an HTMLInputElement was expected.`, `Instead, ${componentName} expects an input element.`, '', componentName === 'useAutocomplete' ? 'Make sure you have bound getInputProps correctly and that the normal ref/effect resolutions order is guaranteed.' : 'Make sure you have customized the input component correctly.'].join('\\n'));\n        }\n      }\n    }, [componentName]);\n  }\n  React.useEffect(() => {\n    syncHighlightedIndex();\n  }, [syncHighlightedIndex]);\n  const handleOpen = event => {\n    if (open) {\n      return;\n    }\n    setOpenState(true);\n    setInputPristine(true);\n    if (onOpen) {\n      onOpen(event);\n    }\n  };\n  const handleClose = (event, reason) => {\n    if (!open) {\n      return;\n    }\n    setOpenState(false);\n    if (onClose) {\n      onClose(event, reason);\n    }\n  };\n  const handleValue = (event, newValue, reason, details) => {\n    if (multiple) {\n      if (value.length === newValue.length && value.every((val, i) => val === newValue[i])) {\n        return;\n      }\n    } else if (value === newValue) {\n      return;\n    }\n    if (onChange) {\n      onChange(event, newValue, reason, details);\n    }\n    setValueState(newValue);\n  };\n  const isTouch = React.useRef(false);\n  const selectNewValue = (event, option, reasonProp = 'selectOption', origin = 'options') => {\n    let reason = reasonProp;\n    let newValue = option;\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      if (process.env.NODE_ENV !== 'production') {\n        const matches = newValue.filter(val => isOptionEqualToValue(option, val));\n        if (matches.length > 1) {\n          console.error([`MUI: The \\`isOptionEqualToValue\\` method of ${componentName} does not handle the arguments correctly.`, `The component expects a single value to match a given option but found ${matches.length} matches.`].join('\\n'));\n        }\n      }\n      const itemIndex = newValue.findIndex(valueItem => isOptionEqualToValue(option, valueItem));\n      if (itemIndex === -1) {\n        newValue.push(option);\n      } else if (origin !== 'freeSolo') {\n        newValue.splice(itemIndex, 1);\n        reason = 'removeOption';\n      }\n    }\n    resetInputValue(event, newValue, reason);\n    handleValue(event, newValue, reason, {\n      option\n    });\n    if (!disableCloseOnSelect && (!event || !event.ctrlKey && !event.metaKey)) {\n      handleClose(event, reason);\n    }\n    if (blurOnSelect === true || blurOnSelect === 'touch' && isTouch.current || blurOnSelect === 'mouse' && !isTouch.current) {\n      inputRef.current.blur();\n    }\n  };\n  function validItemIndex(index, direction) {\n    if (index === -1) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === value.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n\n      // Using `data-tag-index` for deprecated `renderTags`. Remove when `renderTags` is removed.\n      const indexType = renderValue ? 'data-item-index' : 'data-tag-index';\n      const option = anchorEl.querySelector(`[${indexType}=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      if (!option || !option.hasAttribute('tabindex') || option.disabled || option.getAttribute('aria-disabled') === 'true') {\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n  const handleFocusItem = (event, direction) => {\n    if (!multiple) {\n      return;\n    }\n    if (inputValue === '') {\n      handleClose(event, 'toggleInput');\n    }\n    let nextItem = focusedItem;\n    if (focusedItem === -1) {\n      if (inputValue === '' && direction === 'previous') {\n        nextItem = value.length - 1;\n      }\n    } else {\n      nextItem += direction === 'next' ? 1 : -1;\n      if (nextItem < 0) {\n        nextItem = 0;\n      }\n      if (nextItem === value.length) {\n        nextItem = -1;\n      }\n    }\n    nextItem = validItemIndex(nextItem, direction);\n    setFocusedItem(nextItem);\n    focusItem(nextItem);\n  };\n  const handleClear = event => {\n    ignoreFocus.current = true;\n    setInputValueState('');\n    if (onInputChange) {\n      onInputChange(event, '', 'clear');\n    }\n    handleValue(event, multiple ? [] : null, 'clear');\n  };\n  const handleKeyDown = other => event => {\n    if (other.onKeyDown) {\n      other.onKeyDown(event);\n    }\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (focusedItem !== -1 && !['ArrowLeft', 'ArrowRight'].includes(event.key)) {\n      setFocusedItem(-1);\n      focusItem(-1);\n    }\n\n    // Wait until IME is settled.\n    if (event.which !== 229) {\n      switch (event.key) {\n        case 'Home':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'start',\n              direction: 'next',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'End':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'end',\n              direction: 'previous',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'PageUp':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -pageSize,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'PageDown':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: pageSize,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowDown':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: 1,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowUp':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -1,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowLeft':\n          if (!multiple && renderValue) {\n            focusItem(0);\n          } else {\n            handleFocusItem(event, 'previous');\n          }\n          break;\n        case 'ArrowRight':\n          if (!multiple && renderValue) {\n            focusItem(-1);\n          } else {\n            handleFocusItem(event, 'next');\n          }\n          break;\n        case 'Enter':\n          if (highlightedIndexRef.current !== -1 && popupOpen) {\n            const option = filteredOptions[highlightedIndexRef.current];\n            const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n\n            // Avoid early form validation, let the end-users continue filling the form.\n            event.preventDefault();\n            if (disabled) {\n              return;\n            }\n            selectNewValue(event, option, 'selectOption');\n\n            // Move the selection to the end.\n            if (autoComplete) {\n              inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);\n            }\n          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {\n            if (multiple) {\n              // Allow people to add new values before they submit the form.\n              event.preventDefault();\n            }\n            selectNewValue(event, inputValue, 'createOption', 'freeSolo');\n          }\n          break;\n        case 'Escape':\n          if (popupOpen) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClose(event, 'escape');\n          } else if (clearOnEscape && (inputValue !== '' || multiple && value.length > 0 || renderValue)) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClear(event);\n          }\n          break;\n        case 'Backspace':\n          // Remove the value on the left of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0) {\n            const index = focusedItem === -1 ? value.length - 1 : focusedItem;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          if (!multiple && renderValue && !readOnly) {\n            setValueState(null);\n            focusItem(-1);\n          }\n          break;\n        case 'Delete':\n          // Remove the value on the right of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0 && focusedItem !== -1) {\n            const index = focusedItem;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          if (!multiple && renderValue && !readOnly) {\n            setValueState(null);\n            focusItem(-1);\n          }\n          break;\n        default:\n      }\n    }\n  };\n  const handleFocus = event => {\n    setFocused(true);\n    if (openOnFocus && !ignoreFocus.current) {\n      handleOpen(event);\n    }\n  };\n  const handleBlur = event => {\n    // Ignore the event when using the scrollbar with IE11\n    if (unstable_isActiveElementInListbox(listboxRef)) {\n      inputRef.current.focus();\n      return;\n    }\n    setFocused(false);\n    firstFocus.current = true;\n    ignoreFocus.current = false;\n    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {\n      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');\n    } else if (autoSelect && freeSolo && inputValue !== '') {\n      selectNewValue(event, inputValue, 'blur', 'freeSolo');\n    } else if (clearOnBlur) {\n      resetInputValue(event, value, 'blur');\n    }\n    handleClose(event, 'blur');\n  };\n  const handleInputChange = event => {\n    const newValue = event.target.value;\n    if (inputValue !== newValue) {\n      setInputValueState(newValue);\n      setInputPristine(false);\n      if (onInputChange) {\n        onInputChange(event, newValue, 'input');\n      }\n    }\n    if (newValue === '') {\n      if (!disableClearable && !multiple) {\n        handleValue(event, null, 'clear');\n      }\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleOptionMouseMove = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    if (highlightedIndexRef.current !== index) {\n      setHighlightedIndex({\n        event,\n        index,\n        reason: 'mouse'\n      });\n    }\n  };\n  const handleOptionTouchStart = event => {\n    setHighlightedIndex({\n      event,\n      index: Number(event.currentTarget.getAttribute('data-option-index')),\n      reason: 'touch'\n    });\n    isTouch.current = true;\n  };\n  const handleOptionClick = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    selectNewValue(event, filteredOptions[index], 'selectOption');\n    isTouch.current = false;\n  };\n  const handleItemDelete = index => event => {\n    const newValue = value.slice();\n    newValue.splice(index, 1);\n    handleValue(event, newValue, 'removeOption', {\n      option: value[index]\n    });\n  };\n  const handleSingleItemDelete = event => {\n    handleValue(event, null, 'removeOption', {\n      option: value\n    });\n  };\n  const handlePopupIndicator = event => {\n    if (open) {\n      handleClose(event, 'toggleInput');\n    } else {\n      handleOpen(event);\n    }\n  };\n\n  // Prevent input blur when interacting with the combobox\n  const handleMouseDown = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    if (event.target.getAttribute('id') !== id) {\n      event.preventDefault();\n    }\n  };\n\n  // Focus the input when interacting with the combobox\n  const handleClick = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    inputRef.current.focus();\n    if (selectOnFocus && firstFocus.current && inputRef.current.selectionEnd - inputRef.current.selectionStart === 0) {\n      inputRef.current.select();\n    }\n    firstFocus.current = false;\n  };\n  const handleInputMouseDown = event => {\n    if (!disabledProp && (inputValue === '' || !open)) {\n      handlePopupIndicator(event);\n    }\n  };\n  let dirty = freeSolo && inputValue.length > 0;\n  dirty = dirty || (multiple ? value.length > 0 : value !== null);\n  let groupedOptions = filteredOptions;\n  if (groupBy) {\n    // used to keep track of key and indexes in the result array\n    const indexBy = new Map();\n    let warn = false;\n    groupedOptions = filteredOptions.reduce((acc, option, index) => {\n      const group = groupBy(option);\n      if (acc.length > 0 && acc[acc.length - 1].group === group) {\n        acc[acc.length - 1].options.push(option);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          if (indexBy.get(group) && !warn) {\n            console.warn(`MUI: The options provided combined with the \\`groupBy\\` method of ${componentName} returns duplicated headers.`, 'You can solve the issue by sorting the options with the output of `groupBy`.');\n            warn = true;\n          }\n          indexBy.set(group, true);\n        }\n        acc.push({\n          key: index,\n          index,\n          group,\n          options: [option]\n        });\n      }\n      return acc;\n    }, []);\n  }\n  if (disabledProp && focused) {\n    handleBlur();\n  }\n  return {\n    getRootProps: (other = {}) => ({\n      ...other,\n      onKeyDown: handleKeyDown(other),\n      onMouseDown: handleMouseDown,\n      onClick: handleClick\n    }),\n    getInputLabelProps: () => ({\n      id: `${id}-label`,\n      htmlFor: id\n    }),\n    getInputProps: () => ({\n      id,\n      value: inputValue,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: handleInputChange,\n      onMouseDown: handleInputMouseDown,\n      // if open then this is handled imperatively so don't let react override\n      // only have an opinion about this when closed\n      'aria-activedescendant': popupOpen ? '' : null,\n      'aria-autocomplete': autoComplete ? 'both' : 'list',\n      'aria-controls': listboxAvailable ? `${id}-listbox` : undefined,\n      'aria-expanded': listboxAvailable,\n      // Disable browser's suggestion that might overlap with the popup.\n      // Handle autocomplete but not autofill.\n      autoComplete: 'off',\n      ref: inputRef,\n      autoCapitalize: 'none',\n      spellCheck: 'false',\n      role: 'combobox',\n      disabled: disabledProp\n    }),\n    getClearProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handleClear\n    }),\n    getItemProps: ({\n      index = 0\n    } = {}) => ({\n      ...(multiple && {\n        key: index\n      }),\n      ...(renderValue ? {\n        'data-item-index': index\n      } : {\n        'data-tag-index': index\n      }),\n      tabIndex: -1,\n      ...(!readOnly && {\n        onDelete: multiple ? handleItemDelete(index) : handleSingleItemDelete\n      })\n    }),\n    getPopupIndicatorProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handlePopupIndicator\n    }),\n    // deprecated\n    getTagProps: ({\n      index\n    }) => ({\n      key: index,\n      'data-tag-index': index,\n      tabIndex: -1,\n      ...(!readOnly && {\n        onDelete: handleItemDelete(index)\n      })\n    }),\n    getListboxProps: () => ({\n      role: 'listbox',\n      id: `${id}-listbox`,\n      'aria-labelledby': `${id}-label`,\n      ref: handleListboxRef,\n      onMouseDown: event => {\n        // Prevent blur\n        event.preventDefault();\n      }\n    }),\n    getOptionProps: ({\n      index,\n      option\n    }) => {\n      const selected = (multiple ? value : [value]).some(value2 => value2 != null && isOptionEqualToValue(option, value2));\n      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n      return {\n        key: getOptionKey?.(option) ?? getOptionLabel(option),\n        tabIndex: -1,\n        role: 'option',\n        id: `${id}-option-${index}`,\n        onMouseMove: handleOptionMouseMove,\n        onClick: handleOptionClick,\n        onTouchStart: handleOptionTouchStart,\n        'data-option-index': index,\n        'aria-disabled': disabled,\n        'aria-selected': selected\n      };\n    },\n    id,\n    inputValue,\n    value,\n    dirty,\n    expanded: popupOpen && anchorEl,\n    popupOpen,\n    focused: focused || focusedItem !== -1,\n    anchorEl,\n    setAnchorEl,\n    focusedItem,\n    // deprecated\n    focusedTag: focusedItem,\n    groupedOptions\n  };\n}\nexport default useAutocomplete;"], "mappings": "AAAA,YAAY;;AAEZ;AAAA,OAAAA,aAAA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,gBAAgB,MAAM,6BAA6B;;AAE1D;AACA,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC/B,OAAOA,MAAM,CAACC,SAAS,CAAC,KAAK,CAAC,CAACC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;AAChE;AACA,OAAO,SAASC,mBAAmBA,CAAA,EAAc;EAAA,IAAbC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC7C,MAAM;IACJG,aAAa,GAAG,IAAI;IACpBC,UAAU,GAAG,IAAI;IACjBC,KAAK;IACLC,SAAS,GAAG,KAAK;IACjBC,SAAS;IACTC,IAAI,GAAG;EACT,CAAC,GAAGT,MAAM;EACV,OAAO,CAACU,OAAO,EAAAC,IAAA,KAGT;IAAA,IAHW;MACfC,UAAU;MACVC;IACF,CAAC,GAAAF,IAAA;IACC,IAAIG,KAAK,GAAGL,IAAI,GAAGG,UAAU,CAACH,IAAI,CAAC,CAAC,GAAGG,UAAU;IACjD,IAAIP,UAAU,EAAE;MACdS,KAAK,GAAGA,KAAK,CAACC,WAAW,CAAC,CAAC;IAC7B;IACA,IAAIX,aAAa,EAAE;MACjBU,KAAK,GAAGnB,eAAe,CAACmB,KAAK,CAAC;IAChC;IACA,MAAME,eAAe,GAAG,CAACF,KAAK,GAAGJ,OAAO,GAAGA,OAAO,CAACO,MAAM,CAACC,MAAM,IAAI;MAClE,IAAIC,SAAS,GAAG,CAACX,SAAS,IAAIK,cAAc,EAAEK,MAAM,CAAC;MACrD,IAAIb,UAAU,EAAE;QACdc,SAAS,GAAGA,SAAS,CAACJ,WAAW,CAAC,CAAC;MACrC;MACA,IAAIX,aAAa,EAAE;QACjBe,SAAS,GAAGxB,eAAe,CAACwB,SAAS,CAAC;MACxC;MACA,OAAOZ,SAAS,KAAK,OAAO,GAAGY,SAAS,CAACC,UAAU,CAACN,KAAK,CAAC,GAAGK,SAAS,CAACE,QAAQ,CAACP,KAAK,CAAC;IACxF,CAAC,CAAC;IACF,OAAO,OAAOR,KAAK,KAAK,QAAQ,GAAGU,eAAe,CAACM,KAAK,CAAC,CAAC,EAAEhB,KAAK,CAAC,GAAGU,eAAe;EACtF,CAAC;AACH;AACA,MAAMO,oBAAoB,GAAGxB,mBAAmB,CAAC,CAAC;;AAElD;AACA,MAAMyB,QAAQ,GAAG,CAAC;AAClB,MAAMC,+BAA+B,GAAGC,UAAU;EAAA,IAAAC,qBAAA;EAAA,OAAID,UAAU,CAACE,OAAO,KAAK,IAAI,MAAAD,qBAAA,GAAID,UAAU,CAACE,OAAO,CAACC,aAAa,cAAAF,qBAAA,uBAAhCA,qBAAA,CAAkCG,QAAQ,CAACC,QAAQ,CAACC,aAAa,CAAC;AAAA;AACvJ,MAAMC,sBAAsB,GAAG,EAAE;AACjC,SAASC,aAAaA,CAACC,KAAK,EAAEC,QAAQ,EAAEvB,cAAc,EAAEwB,WAAW,EAAE;EACnE,IAAID,QAAQ,IAAID,KAAK,IAAI,IAAI,IAAIE,WAAW,EAAE;IAC5C,OAAO,EAAE;EACX;EACA,MAAMC,WAAW,GAAGzB,cAAc,CAACsB,KAAK,CAAC;EACzC,OAAO,OAAOG,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAG,EAAE;AAC3D;AACA,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,MAAM;IACJ;IACAC,iCAAiC,GAAGhB,+BAA+B;IACnE;IACAiB,wBAAwB,GAAG,KAAK;IAChCC,YAAY,GAAG,KAAK;IACpBC,aAAa,GAAG,KAAK;IACrBC,UAAU,GAAG,KAAK;IAClBC,YAAY,GAAG,KAAK;IACpBC,WAAW,GAAG,CAACP,KAAK,CAACQ,QAAQ;IAC7BC,aAAa,GAAG,KAAK;IACrBC,aAAa,GAAG,iBAAiB;IACjCC,YAAY,GAAGX,KAAK,CAACJ,QAAQ,GAAGH,sBAAsB,GAAG,IAAI;IAC7DmB,gBAAgB,GAAG,KAAK;IACxBC,oBAAoB,GAAG,KAAK;IAC5BC,QAAQ,EAAEC,YAAY;IACtBC,sBAAsB,GAAG,KAAK;IAC9BC,eAAe,GAAG,KAAK;IACvBC,aAAa,GAAGnC,oBAAoB;IACpCoC,qBAAqB,GAAG,KAAK;IAC7BX,QAAQ,GAAG,KAAK;IAChBY,iBAAiB;IACjBC,YAAY;IACZhD,cAAc,EAAEiD,kBAAkB,GAAG5C,MAAM;MAAA,IAAA6C,aAAA;MAAA,QAAAA,aAAA,GAAI7C,MAAM,CAAC8C,KAAK,cAAAD,aAAA,cAAAA,aAAA,GAAI7C,MAAM;IAAA;IACrE+C,OAAO;IACPC,iBAAiB,GAAG,CAAC1B,KAAK,CAACQ,QAAQ;IACnCmB,EAAE,EAAEC,MAAM;IACVC,kBAAkB,GAAG,KAAK;IAC1BzD,UAAU,EAAE0D,cAAc;IAC1BC,oBAAoB,GAAGA,CAACrD,MAAM,EAAEiB,KAAK,KAAKjB,MAAM,KAAKiB,KAAK;IAC1DC,QAAQ,GAAG,KAAK;IAChBoC,QAAQ;IACRC,OAAO;IACPC,iBAAiB;IACjBC,aAAa;IACbC,MAAM;IACNC,IAAI,EAAEC,QAAQ;IACdC,WAAW,GAAG,KAAK;IACnBrE,OAAO;IACPsE,QAAQ,GAAG,KAAK;IAChB3C,WAAW;IACX4C,aAAa,GAAG,CAACzC,KAAK,CAACQ,QAAQ;IAC/Bb,KAAK,EAAE+C;EACT,CAAC,GAAG1C,KAAK;EACT,MAAM2B,EAAE,GAAG1E,KAAK,CAAC2E,MAAM,CAAC;EACxB,IAAIvD,cAAc,GAAGiD,kBAAkB;EACvCjD,cAAc,GAAGK,MAAM,IAAI;IACzB,MAAMoB,WAAW,GAAGwB,kBAAkB,CAAC5C,MAAM,CAAC;IAC9C,IAAI,OAAOoB,WAAW,KAAK,QAAQ,EAAE;MACnC,IAAI6C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,MAAMC,eAAe,GAAGhD,WAAW,KAAKnC,SAAS,GAAG,WAAW,MAAAoF,MAAA,CAAM,OAAOjD,WAAW,QAAAiD,MAAA,CAAKjD,WAAW,MAAG;QAC1GkD,OAAO,CAACC,KAAK,wCAAAF,MAAA,CAA0CrC,aAAa,gBAAAqC,MAAA,CAAaD,eAAe,+BAAAC,MAAA,CAA4BG,IAAI,CAAClF,SAAS,CAACU,MAAM,CAAC,MAAG,CAAC;MACxJ;MACA,OAAOyE,MAAM,CAACrD,WAAW,CAAC;IAC5B;IACA,OAAOA,WAAW;EACpB,CAAC;EACD,MAAMsD,WAAW,GAAGvG,KAAK,CAACwG,MAAM,CAAC,KAAK,CAAC;EACvC,MAAMC,UAAU,GAAGzG,KAAK,CAACwG,MAAM,CAAC,IAAI,CAAC;EACrC,MAAME,QAAQ,GAAG1G,KAAK,CAACwG,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMnE,UAAU,GAAGrC,KAAK,CAACwG,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAG5G,KAAK,CAAC6G,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/G,KAAK,CAAC6G,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAMG,kBAAkB,GAAGzD,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;EACjD,MAAM0D,mBAAmB,GAAGjH,KAAK,CAACwG,MAAM,CAACQ,kBAAkB,CAAC;;EAE5D;EACA;EACA,MAAME,iBAAiB,GAAGlH,KAAK,CAACwG,MAAM,CAAC3D,aAAa,CAACiB,YAAY,aAAZA,YAAY,cAAZA,YAAY,GAAI+B,SAAS,EAAE9C,QAAQ,EAAEvB,cAAc,CAAC,CAAC,CAACe,OAAO;EAClH,MAAM,CAACO,KAAK,EAAEqE,aAAa,CAAC,GAAGhH,aAAa,CAAC;IAC3CiH,UAAU,EAAEvB,SAAS;IACrBwB,OAAO,EAAEvD,YAAY;IACrBwD,IAAI,EAAEzD;EACR,CAAC,CAAC;EACF,MAAM,CAACtC,UAAU,EAAEgG,kBAAkB,CAAC,GAAGpH,aAAa,CAAC;IACrDiH,UAAU,EAAEnC,cAAc;IAC1BoC,OAAO,EAAEH,iBAAiB;IAC1BI,IAAI,EAAEzD,aAAa;IACnB2D,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1H,KAAK,CAAC6G,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMc,eAAe,GAAG3H,KAAK,CAAC4H,WAAW,CAAC,CAACC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,KAAK;IACrE;IACA;IACA,MAAMC,gBAAgB,GAAGjF,QAAQ,GAAGD,KAAK,CAACjC,MAAM,GAAGiH,QAAQ,CAACjH,MAAM,GAAGiH,QAAQ,KAAK,IAAI;IACtF,IAAI,CAACE,gBAAgB,IAAI,CAACtE,WAAW,EAAE;MACrC;IACF;IACA,MAAMuE,aAAa,GAAGpF,aAAa,CAACiF,QAAQ,EAAE/E,QAAQ,EAAEvB,cAAc,EAAEwB,WAAW,CAAC;IACpF,IAAIzB,UAAU,KAAK0G,aAAa,EAAE;MAChC;IACF;IACAV,kBAAkB,CAACU,aAAa,CAAC;IACjC,IAAI3C,aAAa,EAAE;MACjBA,aAAa,CAACuC,KAAK,EAAEI,aAAa,EAAEF,MAAM,CAAC;IAC7C;EACF,CAAC,EAAE,CAACvG,cAAc,EAAED,UAAU,EAAEwB,QAAQ,EAAEuC,aAAa,EAAEiC,kBAAkB,EAAE7D,WAAW,EAAEZ,KAAK,EAAEE,WAAW,CAAC,CAAC;EAC9G,MAAM,CAACwC,IAAI,EAAE0C,YAAY,CAAC,GAAG/H,aAAa,CAAC;IACzCiH,UAAU,EAAE3B,QAAQ;IACpB4B,OAAO,EAAE,KAAK;IACdC,IAAI,EAAEzD,aAAa;IACnB2D,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAGpI,KAAK,CAAC6G,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAMwB,yBAAyB,GAAG,CAACtF,QAAQ,IAAID,KAAK,IAAI,IAAI,IAAIvB,UAAU,KAAKC,cAAc,CAACsB,KAAK,CAAC;EACpG,MAAMwF,SAAS,GAAG9C,IAAI,IAAI,CAACG,QAAQ;EACnC,MAAMhE,eAAe,GAAG2G,SAAS,GAAGjE,aAAa,CAAChD,OAAO,CAACO,MAAM,CAACC,MAAM,IAAI;IACzE,IAAIyC,qBAAqB,IAAI,CAACvB,QAAQ,GAAGD,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEyF,IAAI,CAACC,MAAM,IAAIA,MAAM,KAAK,IAAI,IAAItD,oBAAoB,CAACrD,MAAM,EAAE2G,MAAM,CAAC,CAAC,EAAE;MACjI,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;EACA;EACA;IACEjH,UAAU,EAAE8G,yBAAyB,IAAIF,aAAa,GAAG,EAAE,GAAG5G,UAAU;IACxEC;EACF,CAAC,CAAC,GAAG,EAAE;EACP,MAAMiH,aAAa,GAAGpI,gBAAgB,CAAC;IACrCsB,eAAe;IACfmB,KAAK;IACLvB;EACF,CAAC,CAAC;EACFvB,KAAK,CAAC0I,SAAS,CAAC,MAAM;IACpB,MAAMC,WAAW,GAAG7F,KAAK,KAAK2F,aAAa,CAAC3F,KAAK;IACjD,IAAI2E,OAAO,IAAI,CAACkB,WAAW,EAAE;MAC3B;IACF;;IAEA;IACA,IAAIhF,QAAQ,IAAI,CAACgF,WAAW,EAAE;MAC5B;IACF;IACAhB,eAAe,CAAC,IAAI,EAAE7E,KAAK,EAAE,OAAO,CAAC;EACvC,CAAC,EAAE,CAACA,KAAK,EAAE6E,eAAe,EAAEF,OAAO,EAAEgB,aAAa,CAAC3F,KAAK,EAAEa,QAAQ,CAAC,CAAC;EACpE,MAAMiF,gBAAgB,GAAGpD,IAAI,IAAI7D,eAAe,CAACd,MAAM,GAAG,CAAC,IAAI,CAAC8E,QAAQ;EACxE,MAAMkD,SAAS,GAAG3I,gBAAgB,CAAC4I,WAAW,IAAI;IAChD,IAAIA,WAAW,KAAK,CAAC,CAAC,EAAE;MACtBpC,QAAQ,CAACnE,OAAO,CAACwG,KAAK,CAAC,CAAC;IAC1B,CAAC,MAAM;MACL;MACA,MAAMC,SAAS,GAAGhG,WAAW,GAAG,iBAAiB,GAAG,gBAAgB;MACpE2D,QAAQ,CAACsC,aAAa,KAAA/C,MAAA,CAAK8C,SAAS,SAAA9C,MAAA,CAAK4C,WAAW,QAAI,CAAC,CAACC,KAAK,CAAC,CAAC;IACnE;EACF,CAAC,CAAC;;EAEF;EACA/I,KAAK,CAAC0I,SAAS,CAAC,MAAM;IACpB,IAAI3F,QAAQ,IAAI+D,WAAW,GAAGhE,KAAK,CAACjC,MAAM,GAAG,CAAC,EAAE;MAC9CkG,cAAc,CAAC,CAAC,CAAC,CAAC;MAClB8B,SAAS,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAAC/F,KAAK,EAAEC,QAAQ,EAAE+D,WAAW,EAAE+B,SAAS,CAAC,CAAC;EAC7C,SAASK,gBAAgBA,CAACC,KAAK,EAAEC,SAAS,EAAE;IAC1C,IAAI,CAAC/G,UAAU,CAACE,OAAO,IAAI4G,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAIxH,eAAe,CAACd,MAAM,EAAE;MACvE,OAAO,CAAC,CAAC;IACX;IACA,IAAIwI,SAAS,GAAGF,KAAK;IACrB,OAAO,IAAI,EAAE;MACX,MAAMtH,MAAM,GAAGQ,UAAU,CAACE,OAAO,CAAC0G,aAAa,yBAAA/C,MAAA,CAAwBmD,SAAS,QAAI,CAAC;;MAErF;MACA,MAAMC,iBAAiB,GAAGnF,sBAAsB,GAAG,KAAK,GAAG,CAACtC,MAAM,IAAIA,MAAM,CAACoC,QAAQ,IAAIpC,MAAM,CAAC0H,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;MACxI,IAAI1H,MAAM,IAAIA,MAAM,CAAC2H,YAAY,CAAC,UAAU,CAAC,IAAI,CAACF,iBAAiB,EAAE;QACnE;QACA,OAAOD,SAAS;MAClB;;MAEA;MACA;MACA,IAAID,SAAS,KAAK,MAAM,EAAE;QACxBC,SAAS,GAAG,CAACA,SAAS,GAAG,CAAC,IAAI1H,eAAe,CAACd,MAAM;MACtD,CAAC,MAAM;QACLwI,SAAS,GAAG,CAACA,SAAS,GAAG,CAAC,GAAG1H,eAAe,CAACd,MAAM,IAAIc,eAAe,CAACd,MAAM;MAC/E;;MAEA;MACA;MACA,IAAIwI,SAAS,KAAKF,KAAK,EAAE;QACvB,OAAO,CAAC,CAAC;MACX;IACF;EACF;EACA,MAAMM,mBAAmB,GAAGvJ,gBAAgB,CAACwJ,KAAA,IAIvC;IAAA,IAJwC;MAC5C7B,KAAK;MACLsB,KAAK;MACLpB;IACF,CAAC,GAAA2B,KAAA;IACCzC,mBAAmB,CAAC1E,OAAO,GAAG4G,KAAK;;IAEnC;IACA,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBzC,QAAQ,CAACnE,OAAO,CAACoH,eAAe,CAAC,uBAAuB,CAAC;IAC3D,CAAC,MAAM;MACLjD,QAAQ,CAACnE,OAAO,CAACqH,YAAY,CAAC,uBAAuB,KAAA1D,MAAA,CAAKpB,EAAE,cAAAoB,MAAA,CAAWiD,KAAK,CAAE,CAAC;IACjF;IACA,IAAI9D,iBAAiB,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAACrD,QAAQ,CAAC+F,MAAM,CAAC,EAAE;MACxE1C,iBAAiB,CAACwC,KAAK,EAAEsB,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,GAAGxH,eAAe,CAACwH,KAAK,CAAC,EAAEpB,MAAM,CAAC;IAChF;IACA,IAAI,CAAC1F,UAAU,CAACE,OAAO,EAAE;MACvB;IACF;IACA,MAAMsH,IAAI,GAAGxH,UAAU,CAACE,OAAO,CAAC0G,aAAa,sBAAA/C,MAAA,CAAoB7C,wBAAwB,aAAU,CAAC;IACpG,IAAIwG,IAAI,EAAE;MACRA,IAAI,CAACC,SAAS,CAACC,MAAM,IAAA7D,MAAA,CAAI7C,wBAAwB,aAAU,CAAC;MAC5DwG,IAAI,CAACC,SAAS,CAACC,MAAM,IAAA7D,MAAA,CAAI7C,wBAAwB,kBAAe,CAAC;IACnE;IACA,IAAI2G,WAAW,GAAG3H,UAAU,CAACE,OAAO;IACpC,IAAIF,UAAU,CAACE,OAAO,CAACgH,YAAY,CAAC,MAAM,CAAC,KAAK,SAAS,EAAE;MACzDS,WAAW,GAAG3H,UAAU,CAACE,OAAO,CAACC,aAAa,CAACyG,aAAa,CAAC,kBAAkB,CAAC;IAClF;;IAEA;IACA,IAAI,CAACe,WAAW,EAAE;MAChB;IACF;IACA,IAAIb,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBa,WAAW,CAACC,SAAS,GAAG,CAAC;MACzB;IACF;IACA,MAAMpI,MAAM,GAAGQ,UAAU,CAACE,OAAO,CAAC0G,aAAa,yBAAA/C,MAAA,CAAwBiD,KAAK,QAAI,CAAC;IACjF,IAAI,CAACtH,MAAM,EAAE;MACX;IACF;IACAA,MAAM,CAACiI,SAAS,CAACI,GAAG,IAAAhE,MAAA,CAAI7C,wBAAwB,aAAU,CAAC;IAC3D,IAAI0E,MAAM,KAAK,UAAU,EAAE;MACzBlG,MAAM,CAACiI,SAAS,CAACI,GAAG,IAAAhE,MAAA,CAAI7C,wBAAwB,kBAAe,CAAC;IAClE;;IAEA;IACA;IACA;IACA;IACA;IACA,IAAI2G,WAAW,CAACG,YAAY,GAAGH,WAAW,CAACI,YAAY,IAAIrC,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,OAAO,EAAE;MACnG,MAAMsC,OAAO,GAAGxI,MAAM;MACtB,MAAMyI,YAAY,GAAGN,WAAW,CAACI,YAAY,GAAGJ,WAAW,CAACC,SAAS;MACrE,MAAMM,aAAa,GAAGF,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY;MAC9D,IAAIF,aAAa,GAAGD,YAAY,EAAE;QAChCN,WAAW,CAACC,SAAS,GAAGM,aAAa,GAAGP,WAAW,CAACI,YAAY;MAClE,CAAC,MAAM,IAAIC,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY,IAAI7F,OAAO,GAAG,GAAG,GAAG,CAAC,CAAC,GAAGoF,WAAW,CAACC,SAAS,EAAE;QACjGD,WAAW,CAACC,SAAS,GAAGI,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY,IAAI7F,OAAO,GAAG,GAAG,GAAG,CAAC,CAAC;MACxF;IACF;EACF,CAAC,CAAC;EACF,MAAM8F,sBAAsB,GAAGxK,gBAAgB,CAACyK,KAAA,IAK1C;IAAA,IAL2C;MAC/C9C,KAAK;MACL+C,IAAI;MACJxB,SAAS,GAAG,MAAM;MAClBrB;IACF,CAAC,GAAA4C,KAAA;IACC,IAAI,CAACrC,SAAS,EAAE;MACd;IACF;IACA,MAAMuC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,QAAQ,GAAGnJ,eAAe,CAACd,MAAM,GAAG,CAAC;MAC3C,IAAI+J,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO5D,kBAAkB;MAC3B;MACA,IAAI4D,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO,CAAC;MACV;MACA,IAAIA,IAAI,KAAK,KAAK,EAAE;QAClB,OAAOE,QAAQ;MACjB;MACA,MAAMC,QAAQ,GAAG9D,mBAAmB,CAAC1E,OAAO,GAAGqI,IAAI;MACnD,IAAIG,QAAQ,GAAG,CAAC,EAAE;QAChB,IAAIA,QAAQ,KAAK,CAAC,CAAC,IAAI/F,kBAAkB,EAAE;UACzC,OAAO,CAAC,CAAC;QACX;QACA,IAAIZ,eAAe,IAAI6C,mBAAmB,CAAC1E,OAAO,KAAK,CAAC,CAAC,IAAIyI,IAAI,CAACC,GAAG,CAACL,IAAI,CAAC,GAAG,CAAC,EAAE;UAC/E,OAAO,CAAC;QACV;QACA,OAAOE,QAAQ;MACjB;MACA,IAAIC,QAAQ,GAAGD,QAAQ,EAAE;QACvB,IAAIC,QAAQ,KAAKD,QAAQ,GAAG,CAAC,IAAI9F,kBAAkB,EAAE;UACnD,OAAO,CAAC,CAAC;QACX;QACA,IAAIZ,eAAe,IAAI4G,IAAI,CAACC,GAAG,CAACL,IAAI,CAAC,GAAG,CAAC,EAAE;UACzC,OAAOE,QAAQ;QACjB;QACA,OAAO,CAAC;MACV;MACA,OAAOC,QAAQ;IACjB,CAAC;IACD,MAAMG,SAAS,GAAGhC,gBAAgB,CAAC2B,YAAY,CAAC,CAAC,EAAEzB,SAAS,CAAC;IAC7DK,mBAAmB,CAAC;MAClBN,KAAK,EAAE+B,SAAS;MAChBnD,MAAM;MACNF;IACF,CAAC,CAAC;;IAEF;IACA,IAAIvE,YAAY,IAAIsH,IAAI,KAAK,OAAO,EAAE;MACpC,IAAIM,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBxE,QAAQ,CAACnE,OAAO,CAACO,KAAK,GAAGvB,UAAU;MACrC,CAAC,MAAM;QACL,MAAMM,MAAM,GAAGL,cAAc,CAACG,eAAe,CAACuJ,SAAS,CAAC,CAAC;QACzDxE,QAAQ,CAACnE,OAAO,CAACO,KAAK,GAAGjB,MAAM;;QAE/B;QACA;QACA,MAAMsH,KAAK,GAAGtH,MAAM,CAACH,WAAW,CAAC,CAAC,CAACyJ,OAAO,CAAC5J,UAAU,CAACG,WAAW,CAAC,CAAC,CAAC;QACpE,IAAIyH,KAAK,KAAK,CAAC,IAAI5H,UAAU,CAACV,MAAM,GAAG,CAAC,EAAE;UACxC6F,QAAQ,CAACnE,OAAO,CAAC6I,iBAAiB,CAAC7J,UAAU,CAACV,MAAM,EAAEgB,MAAM,CAAChB,MAAM,CAAC;QACtE;MACF;IACF;EACF,CAAC,CAAC;EACF,MAAMwK,iCAAiC,GAAGA,CAAA,KAAM;IAC9C,MAAMC,WAAW,GAAGA,CAACC,MAAM,EAAE/C,MAAM,KAAK;MACtC,MAAMgD,MAAM,GAAGD,MAAM,GAAG/J,cAAc,CAAC+J,MAAM,CAAC,GAAG,EAAE;MACnD,MAAME,MAAM,GAAGjD,MAAM,GAAGhH,cAAc,CAACgH,MAAM,CAAC,GAAG,EAAE;MACnD,OAAOgD,MAAM,KAAKC,MAAM;IAC1B,CAAC;IACD,IAAIxE,mBAAmB,CAAC1E,OAAO,KAAK,CAAC,CAAC,IAAIkG,aAAa,CAAC9G,eAAe,IAAI8G,aAAa,CAAC9G,eAAe,CAACd,MAAM,KAAKc,eAAe,CAACd,MAAM,IAAI4H,aAAa,CAAClH,UAAU,KAAKA,UAAU,KAAKwB,QAAQ,GAAGD,KAAK,CAACjC,MAAM,KAAK4H,aAAa,CAAC3F,KAAK,CAACjC,MAAM,IAAI4H,aAAa,CAAC3F,KAAK,CAAC4I,KAAK,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKpK,cAAc,CAACsB,KAAK,CAAC8I,CAAC,CAAC,CAAC,KAAKpK,cAAc,CAACmK,GAAG,CAAC,CAAC,GAAGL,WAAW,CAAC7C,aAAa,CAAC3F,KAAK,EAAEA,KAAK,CAAC,CAAC,EAAE;MACtX,MAAM+I,yBAAyB,GAAGpD,aAAa,CAAC9G,eAAe,CAACsF,mBAAmB,CAAC1E,OAAO,CAAC;MAC5F,IAAIsJ,yBAAyB,EAAE;QAC7B,OAAOlK,eAAe,CAACmK,SAAS,CAACjK,MAAM,IAAI;UACzC,OAAOL,cAAc,CAACK,MAAM,CAAC,KAAKL,cAAc,CAACqK,yBAAyB,CAAC;QAC7E,CAAC,CAAC;MACJ;IACF;IACA,OAAO,CAAC,CAAC;EACX,CAAC;EACD,MAAME,oBAAoB,GAAG/L,KAAK,CAAC4H,WAAW,CAAC,MAAM;IACnD,IAAI,CAACU,SAAS,EAAE;MACd;IACF;;IAEA;IACA;IACA,MAAM0D,8BAA8B,GAAGX,iCAAiC,CAAC,CAAC;IAC1E,IAAIW,8BAA8B,KAAK,CAAC,CAAC,EAAE;MACzC/E,mBAAmB,CAAC1E,OAAO,GAAGyJ,8BAA8B;MAC5D;IACF;IACA,MAAMC,SAAS,GAAGlJ,QAAQ,GAAGD,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK;;IAE7C;IACA,IAAInB,eAAe,CAACd,MAAM,KAAK,CAAC,IAAIoL,SAAS,IAAI,IAAI,EAAE;MACrDvB,sBAAsB,CAAC;QACrBE,IAAI,EAAE;MACR,CAAC,CAAC;MACF;IACF;IACA,IAAI,CAACvI,UAAU,CAACE,OAAO,EAAE;MACvB;IACF;;IAEA;IACA,IAAI0J,SAAS,IAAI,IAAI,EAAE;MACrB,MAAMC,aAAa,GAAGvK,eAAe,CAACsF,mBAAmB,CAAC1E,OAAO,CAAC;;MAElE;MACA,IAAIQ,QAAQ,IAAImJ,aAAa,IAAIpJ,KAAK,CAACgJ,SAAS,CAACH,GAAG,IAAIzG,oBAAoB,CAACgH,aAAa,EAAEP,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACxG;MACF;MACA,MAAMQ,SAAS,GAAGxK,eAAe,CAACmK,SAAS,CAACM,UAAU,IAAIlH,oBAAoB,CAACkH,UAAU,EAAEH,SAAS,CAAC,CAAC;MACtG,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBzB,sBAAsB,CAAC;UACrBE,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACLnB,mBAAmB,CAAC;UAClBN,KAAK,EAAEgD;QACT,CAAC,CAAC;MACJ;MACA;IACF;;IAEA;IACA,IAAIlF,mBAAmB,CAAC1E,OAAO,IAAIZ,eAAe,CAACd,MAAM,GAAG,CAAC,EAAE;MAC7D4I,mBAAmB,CAAC;QAClBN,KAAK,EAAExH,eAAe,CAACd,MAAM,GAAG;MAClC,CAAC,CAAC;MACF;IACF;;IAEA;IACA4I,mBAAmB,CAAC;MAClBN,KAAK,EAAElC,mBAAmB,CAAC1E;IAC7B,CAAC,CAAC;IACF;IACA;EACF,CAAC,EAAE;EACH;EACAZ,eAAe,CAACd,MAAM;EACtB;EACA;EACAkC,QAAQ,GAAG,KAAK,GAAGD,KAAK,EAAEwB,qBAAqB,EAAEoG,sBAAsB,EAAEjB,mBAAmB,EAAEnB,SAAS,EAAE/G,UAAU,EAAEwB,QAAQ,CAAC,CAAC;EAC/H,MAAMsJ,gBAAgB,GAAGnM,gBAAgB,CAACoM,IAAI,IAAI;IAChDrM,MAAM,CAACoC,UAAU,EAAEiK,IAAI,CAAC;IACxB,IAAI,CAACA,IAAI,EAAE;MACT;IACF;IACAP,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,IAAIjG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACAhG,KAAK,CAAC0I,SAAS,CAAC,MAAM;MACpB,IAAI,CAAChC,QAAQ,CAACnE,OAAO,IAAImE,QAAQ,CAACnE,OAAO,CAACgK,QAAQ,KAAK,OAAO,EAAE;QAC9D,IAAI7F,QAAQ,CAACnE,OAAO,IAAImE,QAAQ,CAACnE,OAAO,CAACgK,QAAQ,KAAK,UAAU,EAAE;UAChEpG,OAAO,CAACqG,IAAI,CAAC,uCAAAtG,MAAA,CAAuCrC,aAAa,iTAA8S,CAAC4I,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7X,CAAC,MAAM;UACLtG,OAAO,CAACC,KAAK,CAAC,8DAAAF,MAAA,CAA8DQ,QAAQ,CAACnE,OAAO,2DAAA2D,MAAA,CAAwDrC,aAAa,iCAA8B,EAAE,EAAEA,aAAa,KAAK,iBAAiB,GAAG,kHAAkH,GAAG,8DAA8D,CAAC,CAAC4I,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3a;MACF;IACF,CAAC,EAAE,CAAC5I,aAAa,CAAC,CAAC;EACrB;EACA7D,KAAK,CAAC0I,SAAS,CAAC,MAAM;IACpBqD,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;EAC1B,MAAMW,UAAU,GAAG7E,KAAK,IAAI;IAC1B,IAAIrC,IAAI,EAAE;MACR;IACF;IACA0C,YAAY,CAAC,IAAI,CAAC;IAClBE,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI7C,MAAM,EAAE;MACVA,MAAM,CAACsC,KAAK,CAAC;IACf;EACF,CAAC;EACD,MAAM8E,WAAW,GAAGA,CAAC9E,KAAK,EAAEE,MAAM,KAAK;IACrC,IAAI,CAACvC,IAAI,EAAE;MACT;IACF;IACA0C,YAAY,CAAC,KAAK,CAAC;IACnB,IAAI9C,OAAO,EAAE;MACXA,OAAO,CAACyC,KAAK,EAAEE,MAAM,CAAC;IACxB;EACF,CAAC;EACD,MAAM6E,WAAW,GAAGA,CAAC/E,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAE8E,OAAO,KAAK;IACxD,IAAI9J,QAAQ,EAAE;MACZ,IAAID,KAAK,CAACjC,MAAM,KAAKiH,QAAQ,CAACjH,MAAM,IAAIiC,KAAK,CAAC4I,KAAK,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,KAAK7D,QAAQ,CAAC8D,CAAC,CAAC,CAAC,EAAE;QACpF;MACF;IACF,CAAC,MAAM,IAAI9I,KAAK,KAAKgF,QAAQ,EAAE;MAC7B;IACF;IACA,IAAI3C,QAAQ,EAAE;MACZA,QAAQ,CAAC0C,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAE8E,OAAO,CAAC;IAC5C;IACA1F,aAAa,CAACW,QAAQ,CAAC;EACzB,CAAC;EACD,MAAMgF,OAAO,GAAG9M,KAAK,CAACwG,MAAM,CAAC,KAAK,CAAC;EACnC,MAAMuG,cAAc,GAAG,SAAAA,CAAClF,KAAK,EAAEhG,MAAM,EAAsD;IAAA,IAApDmL,UAAU,GAAApM,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,cAAc;IAAA,IAAEqM,MAAM,GAAArM,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,SAAS;IACpF,IAAImH,MAAM,GAAGiF,UAAU;IACvB,IAAIlF,QAAQ,GAAGjG,MAAM;IACrB,IAAIkB,QAAQ,EAAE;MACZ+E,QAAQ,GAAGoF,KAAK,CAACC,OAAO,CAACrK,KAAK,CAAC,GAAGA,KAAK,CAACb,KAAK,CAAC,CAAC,GAAG,EAAE;MACpD,IAAI6D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,MAAMoH,OAAO,GAAGtF,QAAQ,CAAClG,MAAM,CAAC+J,GAAG,IAAIzG,oBAAoB,CAACrD,MAAM,EAAE8J,GAAG,CAAC,CAAC;QACzE,IAAIyB,OAAO,CAACvM,MAAM,GAAG,CAAC,EAAE;UACtBsF,OAAO,CAACC,KAAK,CAAC,8CAAAF,MAAA,CAAgDrC,aAAa,0HAAAqC,MAAA,CAAuHkH,OAAO,CAACvM,MAAM,eAAY,CAAC4L,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1O;MACF;MACA,MAAMN,SAAS,GAAGrE,QAAQ,CAACgE,SAAS,CAACG,SAAS,IAAI/G,oBAAoB,CAACrD,MAAM,EAAEoK,SAAS,CAAC,CAAC;MAC1F,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBrE,QAAQ,CAACuF,IAAI,CAACxL,MAAM,CAAC;MACvB,CAAC,MAAM,IAAIoL,MAAM,KAAK,UAAU,EAAE;QAChCnF,QAAQ,CAACwF,MAAM,CAACnB,SAAS,EAAE,CAAC,CAAC;QAC7BpE,MAAM,GAAG,cAAc;MACzB;IACF;IACAJ,eAAe,CAACE,KAAK,EAAEC,QAAQ,EAAEC,MAAM,CAAC;IACxC6E,WAAW,CAAC/E,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAE;MACnClG;IACF,CAAC,CAAC;IACF,IAAI,CAACmC,oBAAoB,KAAK,CAAC6D,KAAK,IAAI,CAACA,KAAK,CAAC0F,OAAO,IAAI,CAAC1F,KAAK,CAAC2F,OAAO,CAAC,EAAE;MACzEb,WAAW,CAAC9E,KAAK,EAAEE,MAAM,CAAC;IAC5B;IACA,IAAItE,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,OAAO,IAAIqJ,OAAO,CAACvK,OAAO,IAAIkB,YAAY,KAAK,OAAO,IAAI,CAACqJ,OAAO,CAACvK,OAAO,EAAE;MACxHmE,QAAQ,CAACnE,OAAO,CAACkL,IAAI,CAAC,CAAC;IACzB;EACF,CAAC;EACD,SAASC,cAAcA,CAACvE,KAAK,EAAEC,SAAS,EAAE;IACxC,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAO,CAAC,CAAC;IACX;IACA,IAAIE,SAAS,GAAGF,KAAK;IACrB,OAAO,IAAI,EAAE;MACX;MACA,IAAIC,SAAS,KAAK,MAAM,IAAIC,SAAS,KAAKvG,KAAK,CAACjC,MAAM,IAAIuI,SAAS,KAAK,UAAU,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;QACtG,OAAO,CAAC,CAAC;MACX;;MAEA;MACA,MAAML,SAAS,GAAGhG,WAAW,GAAG,iBAAiB,GAAG,gBAAgB;MACpE,MAAMnB,MAAM,GAAG8E,QAAQ,CAACsC,aAAa,KAAA/C,MAAA,CAAK8C,SAAS,SAAA9C,MAAA,CAAKmD,SAAS,QAAI,CAAC;;MAEtE;MACA,IAAI,CAACxH,MAAM,IAAI,CAACA,MAAM,CAAC2H,YAAY,CAAC,UAAU,CAAC,IAAI3H,MAAM,CAACoC,QAAQ,IAAIpC,MAAM,CAAC0H,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM,EAAE;QACrHF,SAAS,IAAID,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MAC5C,CAAC,MAAM;QACL,OAAOC,SAAS;MAClB;IACF;EACF;EACA,MAAMsE,eAAe,GAAGA,CAAC9F,KAAK,EAAEuB,SAAS,KAAK;IAC5C,IAAI,CAACrG,QAAQ,EAAE;MACb;IACF;IACA,IAAIxB,UAAU,KAAK,EAAE,EAAE;MACrBoL,WAAW,CAAC9E,KAAK,EAAE,aAAa,CAAC;IACnC;IACA,IAAI+F,QAAQ,GAAG9G,WAAW;IAC1B,IAAIA,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,IAAIvF,UAAU,KAAK,EAAE,IAAI6H,SAAS,KAAK,UAAU,EAAE;QACjDwE,QAAQ,GAAG9K,KAAK,CAACjC,MAAM,GAAG,CAAC;MAC7B;IACF,CAAC,MAAM;MACL+M,QAAQ,IAAIxE,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACzC,IAAIwE,QAAQ,GAAG,CAAC,EAAE;QAChBA,QAAQ,GAAG,CAAC;MACd;MACA,IAAIA,QAAQ,KAAK9K,KAAK,CAACjC,MAAM,EAAE;QAC7B+M,QAAQ,GAAG,CAAC,CAAC;MACf;IACF;IACAA,QAAQ,GAAGF,cAAc,CAACE,QAAQ,EAAExE,SAAS,CAAC;IAC9CrC,cAAc,CAAC6G,QAAQ,CAAC;IACxB/E,SAAS,CAAC+E,QAAQ,CAAC;EACrB,CAAC;EACD,MAAMC,WAAW,GAAGhG,KAAK,IAAI;IAC3BtB,WAAW,CAAChE,OAAO,GAAG,IAAI;IAC1BgF,kBAAkB,CAAC,EAAE,CAAC;IACtB,IAAIjC,aAAa,EAAE;MACjBA,aAAa,CAACuC,KAAK,EAAE,EAAE,EAAE,OAAO,CAAC;IACnC;IACA+E,WAAW,CAAC/E,KAAK,EAAE9E,QAAQ,GAAG,EAAE,GAAG,IAAI,EAAE,OAAO,CAAC;EACnD,CAAC;EACD,MAAM+K,aAAa,GAAGC,KAAK,IAAIlG,KAAK,IAAI;IACtC,IAAIkG,KAAK,CAACC,SAAS,EAAE;MACnBD,KAAK,CAACC,SAAS,CAACnG,KAAK,CAAC;IACxB;IACA,IAAIA,KAAK,CAACoG,mBAAmB,EAAE;MAC7B;IACF;IACA,IAAInH,WAAW,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC9E,QAAQ,CAAC6F,KAAK,CAACqG,GAAG,CAAC,EAAE;MAC1EnH,cAAc,CAAC,CAAC,CAAC,CAAC;MAClB8B,SAAS,CAAC,CAAC,CAAC,CAAC;IACf;;IAEA;IACA,IAAIhB,KAAK,CAACsG,KAAK,KAAK,GAAG,EAAE;MACvB,QAAQtG,KAAK,CAACqG,GAAG;QACf,KAAK,MAAM;UACT,IAAI5F,SAAS,IAAIzD,iBAAiB,EAAE;YAClC;YACAgD,KAAK,CAACuG,cAAc,CAAC,CAAC;YACtB1D,sBAAsB,CAAC;cACrBE,IAAI,EAAE,OAAO;cACbxB,SAAS,EAAE,MAAM;cACjBrB,MAAM,EAAE,UAAU;cAClBF;YACF,CAAC,CAAC;UACJ;UACA;QACF,KAAK,KAAK;UACR,IAAIS,SAAS,IAAIzD,iBAAiB,EAAE;YAClC;YACAgD,KAAK,CAACuG,cAAc,CAAC,CAAC;YACtB1D,sBAAsB,CAAC;cACrBE,IAAI,EAAE,KAAK;cACXxB,SAAS,EAAE,UAAU;cACrBrB,MAAM,EAAE,UAAU;cAClBF;YACF,CAAC,CAAC;UACJ;UACA;QACF,KAAK,QAAQ;UACX;UACAA,KAAK,CAACuG,cAAc,CAAC,CAAC;UACtB1D,sBAAsB,CAAC;YACrBE,IAAI,EAAE,CAACzI,QAAQ;YACfiH,SAAS,EAAE,UAAU;YACrBrB,MAAM,EAAE,UAAU;YAClBF;UACF,CAAC,CAAC;UACF6E,UAAU,CAAC7E,KAAK,CAAC;UACjB;QACF,KAAK,UAAU;UACb;UACAA,KAAK,CAACuG,cAAc,CAAC,CAAC;UACtB1D,sBAAsB,CAAC;YACrBE,IAAI,EAAEzI,QAAQ;YACdiH,SAAS,EAAE,MAAM;YACjBrB,MAAM,EAAE,UAAU;YAClBF;UACF,CAAC,CAAC;UACF6E,UAAU,CAAC7E,KAAK,CAAC;UACjB;QACF,KAAK,WAAW;UACd;UACAA,KAAK,CAACuG,cAAc,CAAC,CAAC;UACtB1D,sBAAsB,CAAC;YACrBE,IAAI,EAAE,CAAC;YACPxB,SAAS,EAAE,MAAM;YACjBrB,MAAM,EAAE,UAAU;YAClBF;UACF,CAAC,CAAC;UACF6E,UAAU,CAAC7E,KAAK,CAAC;UACjB;QACF,KAAK,SAAS;UACZ;UACAA,KAAK,CAACuG,cAAc,CAAC,CAAC;UACtB1D,sBAAsB,CAAC;YACrBE,IAAI,EAAE,CAAC,CAAC;YACRxB,SAAS,EAAE,UAAU;YACrBrB,MAAM,EAAE,UAAU;YAClBF;UACF,CAAC,CAAC;UACF6E,UAAU,CAAC7E,KAAK,CAAC;UACjB;QACF,KAAK,WAAW;UACd,IAAI,CAAC9E,QAAQ,IAAIC,WAAW,EAAE;YAC5B6F,SAAS,CAAC,CAAC,CAAC;UACd,CAAC,MAAM;YACL8E,eAAe,CAAC9F,KAAK,EAAE,UAAU,CAAC;UACpC;UACA;QACF,KAAK,YAAY;UACf,IAAI,CAAC9E,QAAQ,IAAIC,WAAW,EAAE;YAC5B6F,SAAS,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,MAAM;YACL8E,eAAe,CAAC9F,KAAK,EAAE,MAAM,CAAC;UAChC;UACA;QACF,KAAK,OAAO;UACV,IAAIZ,mBAAmB,CAAC1E,OAAO,KAAK,CAAC,CAAC,IAAI+F,SAAS,EAAE;YACnD,MAAMzG,MAAM,GAAGF,eAAe,CAACsF,mBAAmB,CAAC1E,OAAO,CAAC;YAC3D,MAAM0B,QAAQ,GAAGM,iBAAiB,GAAGA,iBAAiB,CAAC1C,MAAM,CAAC,GAAG,KAAK;;YAEtE;YACAgG,KAAK,CAACuG,cAAc,CAAC,CAAC;YACtB,IAAInK,QAAQ,EAAE;cACZ;YACF;YACA8I,cAAc,CAAClF,KAAK,EAAEhG,MAAM,EAAE,cAAc,CAAC;;YAE7C;YACA,IAAIyB,YAAY,EAAE;cAChBoD,QAAQ,CAACnE,OAAO,CAAC6I,iBAAiB,CAAC1E,QAAQ,CAACnE,OAAO,CAACO,KAAK,CAACjC,MAAM,EAAE6F,QAAQ,CAACnE,OAAO,CAACO,KAAK,CAACjC,MAAM,CAAC;YAClG;UACF,CAAC,MAAM,IAAI8C,QAAQ,IAAIpC,UAAU,KAAK,EAAE,IAAI8G,yBAAyB,KAAK,KAAK,EAAE;YAC/E,IAAItF,QAAQ,EAAE;cACZ;cACA8E,KAAK,CAACuG,cAAc,CAAC,CAAC;YACxB;YACArB,cAAc,CAAClF,KAAK,EAAEtG,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC;UAC/D;UACA;QACF,KAAK,QAAQ;UACX,IAAI+G,SAAS,EAAE;YACb;YACAT,KAAK,CAACuG,cAAc,CAAC,CAAC;YACtB;YACAvG,KAAK,CAACwG,eAAe,CAAC,CAAC;YACvB1B,WAAW,CAAC9E,KAAK,EAAE,QAAQ,CAAC;UAC9B,CAAC,MAAM,IAAIjE,aAAa,KAAKrC,UAAU,KAAK,EAAE,IAAIwB,QAAQ,IAAID,KAAK,CAACjC,MAAM,GAAG,CAAC,IAAImC,WAAW,CAAC,EAAE;YAC9F;YACA6E,KAAK,CAACuG,cAAc,CAAC,CAAC;YACtB;YACAvG,KAAK,CAACwG,eAAe,CAAC,CAAC;YACvBR,WAAW,CAAChG,KAAK,CAAC;UACpB;UACA;QACF,KAAK,WAAW;UACd;UACA,IAAI9E,QAAQ,IAAI,CAAC4C,QAAQ,IAAIpE,UAAU,KAAK,EAAE,IAAIuB,KAAK,CAACjC,MAAM,GAAG,CAAC,EAAE;YAClE,MAAMsI,KAAK,GAAGrC,WAAW,KAAK,CAAC,CAAC,GAAGhE,KAAK,CAACjC,MAAM,GAAG,CAAC,GAAGiG,WAAW;YACjE,MAAMgB,QAAQ,GAAGhF,KAAK,CAACb,KAAK,CAAC,CAAC;YAC9B6F,QAAQ,CAACwF,MAAM,CAACnE,KAAK,EAAE,CAAC,CAAC;YACzByD,WAAW,CAAC/E,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;cAC3CjG,MAAM,EAAEiB,KAAK,CAACqG,KAAK;YACrB,CAAC,CAAC;UACJ;UACA,IAAI,CAACpG,QAAQ,IAAIC,WAAW,IAAI,CAAC2C,QAAQ,EAAE;YACzCwB,aAAa,CAAC,IAAI,CAAC;YACnB0B,SAAS,CAAC,CAAC,CAAC,CAAC;UACf;UACA;QACF,KAAK,QAAQ;UACX;UACA,IAAI9F,QAAQ,IAAI,CAAC4C,QAAQ,IAAIpE,UAAU,KAAK,EAAE,IAAIuB,KAAK,CAACjC,MAAM,GAAG,CAAC,IAAIiG,WAAW,KAAK,CAAC,CAAC,EAAE;YACxF,MAAMqC,KAAK,GAAGrC,WAAW;YACzB,MAAMgB,QAAQ,GAAGhF,KAAK,CAACb,KAAK,CAAC,CAAC;YAC9B6F,QAAQ,CAACwF,MAAM,CAACnE,KAAK,EAAE,CAAC,CAAC;YACzByD,WAAW,CAAC/E,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;cAC3CjG,MAAM,EAAEiB,KAAK,CAACqG,KAAK;YACrB,CAAC,CAAC;UACJ;UACA,IAAI,CAACpG,QAAQ,IAAIC,WAAW,IAAI,CAAC2C,QAAQ,EAAE;YACzCwB,aAAa,CAAC,IAAI,CAAC;YACnB0B,SAAS,CAAC,CAAC,CAAC,CAAC;UACf;UACA;QACF;MACF;IACF;EACF,CAAC;EACD,MAAMyF,WAAW,GAAGzG,KAAK,IAAI;IAC3BH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAIhC,WAAW,IAAI,CAACa,WAAW,CAAChE,OAAO,EAAE;MACvCmK,UAAU,CAAC7E,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAM0G,UAAU,GAAG1G,KAAK,IAAI;IAC1B;IACA,IAAIzE,iCAAiC,CAACf,UAAU,CAAC,EAAE;MACjDqE,QAAQ,CAACnE,OAAO,CAACwG,KAAK,CAAC,CAAC;MACxB;IACF;IACArB,UAAU,CAAC,KAAK,CAAC;IACjBjB,UAAU,CAAClE,OAAO,GAAG,IAAI;IACzBgE,WAAW,CAAChE,OAAO,GAAG,KAAK;IAC3B,IAAIiB,UAAU,IAAIyD,mBAAmB,CAAC1E,OAAO,KAAK,CAAC,CAAC,IAAI+F,SAAS,EAAE;MACjEyE,cAAc,CAAClF,KAAK,EAAElG,eAAe,CAACsF,mBAAmB,CAAC1E,OAAO,CAAC,EAAE,MAAM,CAAC;IAC7E,CAAC,MAAM,IAAIiB,UAAU,IAAIG,QAAQ,IAAIpC,UAAU,KAAK,EAAE,EAAE;MACtDwL,cAAc,CAAClF,KAAK,EAAEtG,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC;IACvD,CAAC,MAAM,IAAImC,WAAW,EAAE;MACtBiE,eAAe,CAACE,KAAK,EAAE/E,KAAK,EAAE,MAAM,CAAC;IACvC;IACA6J,WAAW,CAAC9E,KAAK,EAAE,MAAM,CAAC;EAC5B,CAAC;EACD,MAAM2G,iBAAiB,GAAG3G,KAAK,IAAI;IACjC,MAAMC,QAAQ,GAAGD,KAAK,CAAC4G,MAAM,CAAC3L,KAAK;IACnC,IAAIvB,UAAU,KAAKuG,QAAQ,EAAE;MAC3BP,kBAAkB,CAACO,QAAQ,CAAC;MAC5BM,gBAAgB,CAAC,KAAK,CAAC;MACvB,IAAI9C,aAAa,EAAE;QACjBA,aAAa,CAACuC,KAAK,EAAEC,QAAQ,EAAE,OAAO,CAAC;MACzC;IACF;IACA,IAAIA,QAAQ,KAAK,EAAE,EAAE;MACnB,IAAI,CAAC/D,gBAAgB,IAAI,CAAChB,QAAQ,EAAE;QAClC6J,WAAW,CAAC/E,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC;MACnC;IACF,CAAC,MAAM;MACL6E,UAAU,CAAC7E,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAM6G,qBAAqB,GAAG7G,KAAK,IAAI;IACrC,MAAMsB,KAAK,GAAGwF,MAAM,CAAC9G,KAAK,CAAC+G,aAAa,CAACrF,YAAY,CAAC,mBAAmB,CAAC,CAAC;IAC3E,IAAItC,mBAAmB,CAAC1E,OAAO,KAAK4G,KAAK,EAAE;MACzCM,mBAAmB,CAAC;QAClB5B,KAAK;QACLsB,KAAK;QACLpB,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAM8G,sBAAsB,GAAGhH,KAAK,IAAI;IACtC4B,mBAAmB,CAAC;MAClB5B,KAAK;MACLsB,KAAK,EAAEwF,MAAM,CAAC9G,KAAK,CAAC+G,aAAa,CAACrF,YAAY,CAAC,mBAAmB,CAAC,CAAC;MACpExB,MAAM,EAAE;IACV,CAAC,CAAC;IACF+E,OAAO,CAACvK,OAAO,GAAG,IAAI;EACxB,CAAC;EACD,MAAMuM,iBAAiB,GAAGjH,KAAK,IAAI;IACjC,MAAMsB,KAAK,GAAGwF,MAAM,CAAC9G,KAAK,CAAC+G,aAAa,CAACrF,YAAY,CAAC,mBAAmB,CAAC,CAAC;IAC3EwD,cAAc,CAAClF,KAAK,EAAElG,eAAe,CAACwH,KAAK,CAAC,EAAE,cAAc,CAAC;IAC7D2D,OAAO,CAACvK,OAAO,GAAG,KAAK;EACzB,CAAC;EACD,MAAMwM,gBAAgB,GAAG5F,KAAK,IAAItB,KAAK,IAAI;IACzC,MAAMC,QAAQ,GAAGhF,KAAK,CAACb,KAAK,CAAC,CAAC;IAC9B6F,QAAQ,CAACwF,MAAM,CAACnE,KAAK,EAAE,CAAC,CAAC;IACzByD,WAAW,CAAC/E,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;MAC3CjG,MAAM,EAAEiB,KAAK,CAACqG,KAAK;IACrB,CAAC,CAAC;EACJ,CAAC;EACD,MAAM6F,sBAAsB,GAAGnH,KAAK,IAAI;IACtC+E,WAAW,CAAC/E,KAAK,EAAE,IAAI,EAAE,cAAc,EAAE;MACvChG,MAAM,EAAEiB;IACV,CAAC,CAAC;EACJ,CAAC;EACD,MAAMmM,oBAAoB,GAAGpH,KAAK,IAAI;IACpC,IAAIrC,IAAI,EAAE;MACRmH,WAAW,CAAC9E,KAAK,EAAE,aAAa,CAAC;IACnC,CAAC,MAAM;MACL6E,UAAU,CAAC7E,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqH,eAAe,GAAGrH,KAAK,IAAI;IAC/B;IACA,IAAI,CAACA,KAAK,CAAC+G,aAAa,CAACnM,QAAQ,CAACoF,KAAK,CAAC4G,MAAM,CAAC,EAAE;MAC/C;IACF;IACA,IAAI5G,KAAK,CAAC4G,MAAM,CAAClF,YAAY,CAAC,IAAI,CAAC,KAAKzE,EAAE,EAAE;MAC1C+C,KAAK,CAACuG,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMe,WAAW,GAAGtH,KAAK,IAAI;IAC3B;IACA,IAAI,CAACA,KAAK,CAAC+G,aAAa,CAACnM,QAAQ,CAACoF,KAAK,CAAC4G,MAAM,CAAC,EAAE;MAC/C;IACF;IACA/H,QAAQ,CAACnE,OAAO,CAACwG,KAAK,CAAC,CAAC;IACxB,IAAInD,aAAa,IAAIa,UAAU,CAAClE,OAAO,IAAImE,QAAQ,CAACnE,OAAO,CAAC6M,YAAY,GAAG1I,QAAQ,CAACnE,OAAO,CAAC8M,cAAc,KAAK,CAAC,EAAE;MAChH3I,QAAQ,CAACnE,OAAO,CAAC+M,MAAM,CAAC,CAAC;IAC3B;IACA7I,UAAU,CAAClE,OAAO,GAAG,KAAK;EAC5B,CAAC;EACD,MAAMgN,oBAAoB,GAAG1H,KAAK,IAAI;IACpC,IAAI,CAAC3D,YAAY,KAAK3C,UAAU,KAAK,EAAE,IAAI,CAACiE,IAAI,CAAC,EAAE;MACjDyJ,oBAAoB,CAACpH,KAAK,CAAC;IAC7B;EACF,CAAC;EACD,IAAI2H,KAAK,GAAG7L,QAAQ,IAAIpC,UAAU,CAACV,MAAM,GAAG,CAAC;EAC7C2O,KAAK,GAAGA,KAAK,KAAKzM,QAAQ,GAAGD,KAAK,CAACjC,MAAM,GAAG,CAAC,GAAGiC,KAAK,KAAK,IAAI,CAAC;EAC/D,IAAI2M,cAAc,GAAG9N,eAAe;EACpC,IAAIiD,OAAO,EAAE;IACX;IACA,MAAM8K,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB,IAAInD,IAAI,GAAG,KAAK;IAChBiD,cAAc,GAAG9N,eAAe,CAACiO,MAAM,CAAC,CAACC,GAAG,EAAEhO,MAAM,EAAEsH,KAAK,KAAK;MAC9D,MAAM2G,KAAK,GAAGlL,OAAO,CAAC/C,MAAM,CAAC;MAC7B,IAAIgO,GAAG,CAAChP,MAAM,GAAG,CAAC,IAAIgP,GAAG,CAACA,GAAG,CAAChP,MAAM,GAAG,CAAC,CAAC,CAACiP,KAAK,KAAKA,KAAK,EAAE;QACzDD,GAAG,CAACA,GAAG,CAAChP,MAAM,GAAG,CAAC,CAAC,CAACQ,OAAO,CAACgM,IAAI,CAACxL,MAAM,CAAC;MAC1C,CAAC,MAAM;QACL,IAAIiE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,IAAI0J,OAAO,CAACK,GAAG,CAACD,KAAK,CAAC,IAAI,CAACtD,IAAI,EAAE;YAC/BrG,OAAO,CAACqG,IAAI,oEAAAtG,MAAA,CAAsErC,aAAa,mCAAgC,8EAA8E,CAAC;YAC9M2I,IAAI,GAAG,IAAI;UACb;UACAkD,OAAO,CAACM,GAAG,CAACF,KAAK,EAAE,IAAI,CAAC;QAC1B;QACAD,GAAG,CAACxC,IAAI,CAAC;UACPa,GAAG,EAAE/E,KAAK;UACVA,KAAK;UACL2G,KAAK;UACLzO,OAAO,EAAE,CAACQ,MAAM;QAClB,CAAC,CAAC;MACJ;MACA,OAAOgO,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;EACR;EACA,IAAI3L,YAAY,IAAIuD,OAAO,EAAE;IAC3B8G,UAAU,CAAC,CAAC;EACd;EACA,OAAO;IACL0B,YAAY,EAAE,SAAAA,CAAA;MAAA,IAAClC,KAAK,GAAAnN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,OAAAb,aAAA,CAAAA,aAAA,KACpBgO,KAAK;QACRC,SAAS,EAAEF,aAAa,CAACC,KAAK,CAAC;QAC/BmC,WAAW,EAAEhB,eAAe;QAC5BiB,OAAO,EAAEhB;MAAW;IAAA,CACpB;IACFiB,kBAAkB,EAAEA,CAAA,MAAO;MACzBtL,EAAE,KAAAoB,MAAA,CAAKpB,EAAE,WAAQ;MACjBuL,OAAO,EAAEvL;IACX,CAAC,CAAC;IACFwL,aAAa,EAAEA,CAAA,MAAO;MACpBxL,EAAE;MACFhC,KAAK,EAAEvB,UAAU;MACjBgP,MAAM,EAAEhC,UAAU;MAClBiC,OAAO,EAAElC,WAAW;MACpBnJ,QAAQ,EAAEqJ,iBAAiB;MAC3B0B,WAAW,EAAEX,oBAAoB;MACjC;MACA;MACA,uBAAuB,EAAEjH,SAAS,GAAG,EAAE,GAAG,IAAI;MAC9C,mBAAmB,EAAEhF,YAAY,GAAG,MAAM,GAAG,MAAM;MACnD,eAAe,EAAEsF,gBAAgB,MAAA1C,MAAA,CAAMpB,EAAE,gBAAahE,SAAS;MAC/D,eAAe,EAAE8H,gBAAgB;MACjC;MACA;MACAtF,YAAY,EAAE,KAAK;MACnBmN,GAAG,EAAE/J,QAAQ;MACbgK,cAAc,EAAE,MAAM;MACtBC,UAAU,EAAE,OAAO;MACnBC,IAAI,EAAE,UAAU;MAChB3M,QAAQ,EAAEC;IACZ,CAAC,CAAC;IACF2M,aAAa,EAAEA,CAAA,MAAO;MACpBC,QAAQ,EAAE,CAAC,CAAC;MACZC,IAAI,EAAE,QAAQ;MACdZ,OAAO,EAAEtC;IACX,CAAC,CAAC;IACFmD,YAAY,EAAE,SAAAA,CAAA;MAAA,IAAC;QACb7H,KAAK,GAAG;MACV,CAAC,GAAAvI,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,OAAAb,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACAgD,QAAQ,IAAI;QACdmL,GAAG,EAAE/E;MACP,CAAC,GACGnG,WAAW,GAAG;QAChB,iBAAiB,EAAEmG;MACrB,CAAC,GAAG;QACF,gBAAgB,EAAEA;MACpB,CAAC;QACD2H,QAAQ,EAAE,CAAC;MAAC,GACR,CAACnL,QAAQ,IAAI;QACfsL,QAAQ,EAAElO,QAAQ,GAAGgM,gBAAgB,CAAC5F,KAAK,CAAC,GAAG6F;MACjD,CAAC;IAAA,CACD;IACFkC,sBAAsB,EAAEA,CAAA,MAAO;MAC7BJ,QAAQ,EAAE,CAAC,CAAC;MACZC,IAAI,EAAE,QAAQ;MACdZ,OAAO,EAAElB;IACX,CAAC,CAAC;IACF;IACAkC,WAAW,EAAEC,KAAA;MAAA,IAAC;QACZjI;MACF,CAAC,GAAAiI,KAAA;MAAA,OAAArR,aAAA;QACCmO,GAAG,EAAE/E,KAAK;QACV,gBAAgB,EAAEA,KAAK;QACvB2H,QAAQ,EAAE,CAAC;MAAC,GACR,CAACnL,QAAQ,IAAI;QACfsL,QAAQ,EAAElC,gBAAgB,CAAC5F,KAAK;MAClC,CAAC;IAAA,CACD;IACFkI,eAAe,EAAEA,CAAA,MAAO;MACtBT,IAAI,EAAE,SAAS;MACf9L,EAAE,KAAAoB,MAAA,CAAKpB,EAAE,aAAU;MACnB,iBAAiB,KAAAoB,MAAA,CAAKpB,EAAE,WAAQ;MAChC2L,GAAG,EAAEpE,gBAAgB;MACrB6D,WAAW,EAAErI,KAAK,IAAI;QACpB;QACAA,KAAK,CAACuG,cAAc,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;IACFkD,cAAc,EAAEC,KAAA,IAGV;MAAA,IAAAC,aAAA;MAAA,IAHW;QACfrI,KAAK;QACLtH;MACF,CAAC,GAAA0P,KAAA;MACC,MAAME,QAAQ,GAAG,CAAC1O,QAAQ,GAAGD,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEyF,IAAI,CAACC,MAAM,IAAIA,MAAM,IAAI,IAAI,IAAItD,oBAAoB,CAACrD,MAAM,EAAE2G,MAAM,CAAC,CAAC;MACpH,MAAMvE,QAAQ,GAAGM,iBAAiB,GAAGA,iBAAiB,CAAC1C,MAAM,CAAC,GAAG,KAAK;MACtE,OAAO;QACLqM,GAAG,GAAAsD,aAAA,GAAEhN,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAG3C,MAAM,CAAC,cAAA2P,aAAA,cAAAA,aAAA,GAAIhQ,cAAc,CAACK,MAAM,CAAC;QACrDiP,QAAQ,EAAE,CAAC,CAAC;QACZF,IAAI,EAAE,QAAQ;QACd9L,EAAE,KAAAoB,MAAA,CAAKpB,EAAE,cAAAoB,MAAA,CAAWiD,KAAK,CAAE;QAC3BuI,WAAW,EAAEhD,qBAAqB;QAClCyB,OAAO,EAAErB,iBAAiB;QAC1B6C,YAAY,EAAE9C,sBAAsB;QACpC,mBAAmB,EAAE1F,KAAK;QAC1B,eAAe,EAAElF,QAAQ;QACzB,eAAe,EAAEwN;MACnB,CAAC;IACH,CAAC;IACD3M,EAAE;IACFvD,UAAU;IACVuB,KAAK;IACL0M,KAAK;IACLoC,QAAQ,EAAEtJ,SAAS,IAAI3B,QAAQ;IAC/B2B,SAAS;IACTb,OAAO,EAAEA,OAAO,IAAIX,WAAW,KAAK,CAAC,CAAC;IACtCH,QAAQ;IACRC,WAAW;IACXE,WAAW;IACX;IACA+K,UAAU,EAAE/K,WAAW;IACvB2I;EACF,CAAC;AACH;AACA,eAAevM,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}