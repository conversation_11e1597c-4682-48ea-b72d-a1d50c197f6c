{"ast": null, "code": "'use client';\n\nimport getThemeProps from \"./getThemeProps.js\";\nimport useTheme from \"../useTheme/index.js\";\nexport default function useThemeProps(_ref) {\n  let {\n    props,\n    name,\n    defaultTheme,\n    themeId\n  } = _ref;\n  let theme = useTheme(defaultTheme);\n  if (themeId) {\n    theme = theme[themeId] || theme;\n  }\n  return getThemeProps({\n    theme,\n    name,\n    props\n  });\n}", "map": {"version": 3, "names": ["getThemeProps", "useTheme", "useThemeProps", "_ref", "props", "name", "defaultTheme", "themeId", "theme"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/system/esm/useThemeProps/useThemeProps.js"], "sourcesContent": ["'use client';\n\nimport getThemeProps from \"./getThemeProps.js\";\nimport useTheme from \"../useTheme/index.js\";\nexport default function useThemeProps({\n  props,\n  name,\n  defaultTheme,\n  themeId\n}) {\n  let theme = useTheme(defaultTheme);\n  if (themeId) {\n    theme = theme[themeId] || theme;\n  }\n  return getThemeProps({\n    theme,\n    name,\n    props\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,eAAe,SAASC,aAAaA,CAAAC,IAAA,EAKlC;EAAA,IALmC;IACpCC,KAAK;IACLC,IAAI;IACJC,YAAY;IACZC;EACF,CAAC,GAAAJ,IAAA;EACC,IAAIK,KAAK,GAAGP,QAAQ,CAACK,YAAY,CAAC;EAClC,IAAIC,OAAO,EAAE;IACXC,KAAK,GAAGA,KAAK,CAACD,OAAO,CAAC,IAAIC,KAAK;EACjC;EACA,OAAOR,aAAa,CAAC;IACnBQ,KAAK;IACLH,IAAI;IACJD;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}