{"ast": null, "code": "'use client';\n\nimport _objectSpread from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"theme\"];\nimport * as React from 'react';\nimport { ThemeProvider as SystemThemeProvider } from '@mui/system';\nimport THEME_ID from \"./identifier.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function ThemeProviderNoVars(_ref) {\n  let {\n      theme: themeInput\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  const scopedTheme = THEME_ID in themeInput ? themeInput[THEME_ID] : undefined;\n  return /*#__PURE__*/_jsx(SystemThemeProvider, _objectSpread(_objectSpread({}, props), {}, {\n    themeId: scopedTheme ? THEME_ID : undefined,\n    theme: scopedTheme || themeInput\n  }));\n}", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "React", "ThemeProvider", "SystemThemeProvider", "THEME_ID", "jsx", "_jsx", "ThemeProviderNoVars", "_ref", "theme", "themeInput", "props", "scopedTheme", "undefined", "themeId"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/styles/ThemeProviderNoVars.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { ThemeProvider as SystemThemeProvider } from '@mui/system';\nimport THEME_ID from \"./identifier.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function ThemeProviderNoVars({\n  theme: themeInput,\n  ...props\n}) {\n  const scopedTheme = THEME_ID in themeInput ? themeInput[THEME_ID] : undefined;\n  return /*#__PURE__*/_jsx(SystemThemeProvider, {\n    ...props,\n    themeId: scopedTheme ? THEME_ID : undefined,\n    theme: scopedTheme || themeInput\n  });\n}"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;AAEb,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,IAAIC,mBAAmB,QAAQ,aAAa;AAClE,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAe,SAASC,mBAAmBA,CAAAC,IAAA,EAGxC;EAAA,IAHyC;MAC1CC,KAAK,EAAEC;IAET,CAAC,GAAAF,IAAA;IADIG,KAAK,GAAAZ,wBAAA,CAAAS,IAAA,EAAAR,SAAA;EAER,MAAMY,WAAW,GAAGR,QAAQ,IAAIM,UAAU,GAAGA,UAAU,CAACN,QAAQ,CAAC,GAAGS,SAAS;EAC7E,OAAO,aAAaP,IAAI,CAACH,mBAAmB,EAAAL,aAAA,CAAAA,aAAA,KACvCa,KAAK;IACRG,OAAO,EAAEF,WAAW,GAAGR,QAAQ,GAAGS,SAAS;IAC3CJ,KAAK,EAAEG,WAAW,IAAIF;EAAU,EACjC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}