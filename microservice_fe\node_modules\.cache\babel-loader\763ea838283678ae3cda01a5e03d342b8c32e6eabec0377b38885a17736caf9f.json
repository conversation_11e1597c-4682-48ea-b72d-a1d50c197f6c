{"ast": null, "code": "import _objectSpread from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport clsx from 'clsx';\nimport extractEventHandlers from \"../extractEventHandlers/index.js\";\nimport omitEventHandlers from \"../omitEventHandlers/index.js\";\n/**\n * Merges the slot component internal props (usually coming from a hook)\n * with the externally provided ones.\n *\n * The merge order is (the latter overrides the former):\n * 1. The internal props (specified as a getter function to work with get*Props hook result)\n * 2. Additional props (specified internally on a Base UI component)\n * 3. External props specified on the owner component. These should only be used on a root slot.\n * 4. External props specified in the `slotProps.*` prop.\n * 5. The `className` prop - combined from all the above.\n * @param parameters\n * @returns\n */\nfunction mergeSlotProps(parameters) {\n  const {\n    getSlotProps,\n    additionalProps,\n    externalSlotProps,\n    externalForwardedProps,\n    className\n  } = parameters;\n  if (!getSlotProps) {\n    // The simpler case - getSlotProps is not defined, so no internal event handlers are defined,\n    // so we can simply merge all the props without having to worry about extracting event handlers.\n    const joinedClasses = clsx(additionalProps === null || additionalProps === void 0 ? void 0 : additionalProps.className, className, externalForwardedProps === null || externalForwardedProps === void 0 ? void 0 : externalForwardedProps.className, externalSlotProps === null || externalSlotProps === void 0 ? void 0 : externalSlotProps.className);\n    const mergedStyle = _objectSpread(_objectSpread(_objectSpread({}, additionalProps === null || additionalProps === void 0 ? void 0 : additionalProps.style), externalForwardedProps === null || externalForwardedProps === void 0 ? void 0 : externalForwardedProps.style), externalSlotProps === null || externalSlotProps === void 0 ? void 0 : externalSlotProps.style);\n    const props = _objectSpread(_objectSpread(_objectSpread({}, additionalProps), externalForwardedProps), externalSlotProps);\n    if (joinedClasses.length > 0) {\n      props.className = joinedClasses;\n    }\n    if (Object.keys(mergedStyle).length > 0) {\n      props.style = mergedStyle;\n    }\n    return {\n      props,\n      internalRef: undefined\n    };\n  }\n\n  // In this case, getSlotProps is responsible for calling the external event handlers.\n  // We don't need to include them in the merged props because of this.\n\n  const eventHandlers = extractEventHandlers(_objectSpread(_objectSpread({}, externalForwardedProps), externalSlotProps));\n  const componentsPropsWithoutEventHandlers = omitEventHandlers(externalSlotProps);\n  const otherPropsWithoutEventHandlers = omitEventHandlers(externalForwardedProps);\n  const internalSlotProps = getSlotProps(eventHandlers);\n\n  // The order of classes is important here.\n  // Emotion (that we use in libraries consuming Base UI) depends on this order\n  // to properly override style. It requires the most important classes to be last\n  // (see https://github.com/mui/material-ui/pull/33205) for the related discussion.\n  const joinedClasses = clsx(internalSlotProps === null || internalSlotProps === void 0 ? void 0 : internalSlotProps.className, additionalProps === null || additionalProps === void 0 ? void 0 : additionalProps.className, className, externalForwardedProps === null || externalForwardedProps === void 0 ? void 0 : externalForwardedProps.className, externalSlotProps === null || externalSlotProps === void 0 ? void 0 : externalSlotProps.className);\n  const mergedStyle = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, internalSlotProps === null || internalSlotProps === void 0 ? void 0 : internalSlotProps.style), additionalProps === null || additionalProps === void 0 ? void 0 : additionalProps.style), externalForwardedProps === null || externalForwardedProps === void 0 ? void 0 : externalForwardedProps.style), externalSlotProps === null || externalSlotProps === void 0 ? void 0 : externalSlotProps.style);\n  const props = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, internalSlotProps), additionalProps), otherPropsWithoutEventHandlers), componentsPropsWithoutEventHandlers);\n  if (joinedClasses.length > 0) {\n    props.className = joinedClasses;\n  }\n  if (Object.keys(mergedStyle).length > 0) {\n    props.style = mergedStyle;\n  }\n  return {\n    props,\n    internalRef: internalSlotProps.ref\n  };\n}\nexport default mergeSlotProps;", "map": {"version": 3, "names": ["clsx", "extractEventHandlers", "omitEventHandlers", "mergeSlotProps", "parameters", "getSlotProps", "additionalProps", "externalSlotProps", "externalForwardedProps", "className", "joinedClasses", "mergedStyle", "_objectSpread", "style", "props", "length", "Object", "keys", "internalRef", "undefined", "eventHandlers", "componentsPropsWithoutEventHandlers", "otherPropsWithoutEventHandlers", "internalSlotProps", "ref"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/utils/esm/mergeSlotProps/mergeSlotProps.js"], "sourcesContent": ["import clsx from 'clsx';\nimport extractEventHandlers from \"../extractEventHandlers/index.js\";\nimport omitEventHandlers from \"../omitEventHandlers/index.js\";\n/**\n * Merges the slot component internal props (usually coming from a hook)\n * with the externally provided ones.\n *\n * The merge order is (the latter overrides the former):\n * 1. The internal props (specified as a getter function to work with get*Props hook result)\n * 2. Additional props (specified internally on a Base UI component)\n * 3. External props specified on the owner component. These should only be used on a root slot.\n * 4. External props specified in the `slotProps.*` prop.\n * 5. The `className` prop - combined from all the above.\n * @param parameters\n * @returns\n */\nfunction mergeSlotProps(parameters) {\n  const {\n    getSlotProps,\n    additionalProps,\n    externalSlotProps,\n    externalForwardedProps,\n    className\n  } = parameters;\n  if (!getSlotProps) {\n    // The simpler case - getSlotProps is not defined, so no internal event handlers are defined,\n    // so we can simply merge all the props without having to worry about extracting event handlers.\n    const joinedClasses = clsx(additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n    const mergedStyle = {\n      ...additionalProps?.style,\n      ...externalForwardedProps?.style,\n      ...externalSlotProps?.style\n    };\n    const props = {\n      ...additionalProps,\n      ...externalForwardedProps,\n      ...externalSlotProps\n    };\n    if (joinedClasses.length > 0) {\n      props.className = joinedClasses;\n    }\n    if (Object.keys(mergedStyle).length > 0) {\n      props.style = mergedStyle;\n    }\n    return {\n      props,\n      internalRef: undefined\n    };\n  }\n\n  // In this case, getSlotProps is responsible for calling the external event handlers.\n  // We don't need to include them in the merged props because of this.\n\n  const eventHandlers = extractEventHandlers({\n    ...externalForwardedProps,\n    ...externalSlotProps\n  });\n  const componentsPropsWithoutEventHandlers = omitEventHandlers(externalSlotProps);\n  const otherPropsWithoutEventHandlers = omitEventHandlers(externalForwardedProps);\n  const internalSlotProps = getSlotProps(eventHandlers);\n\n  // The order of classes is important here.\n  // Emotion (that we use in libraries consuming Base UI) depends on this order\n  // to properly override style. It requires the most important classes to be last\n  // (see https://github.com/mui/material-ui/pull/33205) for the related discussion.\n  const joinedClasses = clsx(internalSlotProps?.className, additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n  const mergedStyle = {\n    ...internalSlotProps?.style,\n    ...additionalProps?.style,\n    ...externalForwardedProps?.style,\n    ...externalSlotProps?.style\n  };\n  const props = {\n    ...internalSlotProps,\n    ...additionalProps,\n    ...otherPropsWithoutEventHandlers,\n    ...componentsPropsWithoutEventHandlers\n  };\n  if (joinedClasses.length > 0) {\n    props.className = joinedClasses;\n  }\n  if (Object.keys(mergedStyle).length > 0) {\n    props.style = mergedStyle;\n  }\n  return {\n    props,\n    internalRef: internalSlotProps.ref\n  };\n}\nexport default mergeSlotProps;"], "mappings": ";AAAA,OAAOA,IAAI,MAAM,MAAM;AACvB,OAAOC,oBAAoB,MAAM,kCAAkC;AACnE,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,UAAU,EAAE;EAClC,MAAM;IACJC,YAAY;IACZC,eAAe;IACfC,iBAAiB;IACjBC,sBAAsB;IACtBC;EACF,CAAC,GAAGL,UAAU;EACd,IAAI,CAACC,YAAY,EAAE;IACjB;IACA;IACA,MAAMK,aAAa,GAAGV,IAAI,CAACM,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,SAAS,EAAEA,SAAS,EAAED,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAEC,SAAS,EAAEF,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEE,SAAS,CAAC;IAClI,MAAME,WAAW,GAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACZN,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEO,KAAK,GACtBL,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAEK,KAAK,GAC7BN,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEM,KAAK,CAC5B;IACD,MAAMC,KAAK,GAAAF,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACNN,eAAe,GACfE,sBAAsB,GACtBD,iBAAiB,CACrB;IACD,IAAIG,aAAa,CAACK,MAAM,GAAG,CAAC,EAAE;MAC5BD,KAAK,CAACL,SAAS,GAAGC,aAAa;IACjC;IACA,IAAIM,MAAM,CAACC,IAAI,CAACN,WAAW,CAAC,CAACI,MAAM,GAAG,CAAC,EAAE;MACvCD,KAAK,CAACD,KAAK,GAAGF,WAAW;IAC3B;IACA,OAAO;MACLG,KAAK;MACLI,WAAW,EAAEC;IACf,CAAC;EACH;;EAEA;EACA;;EAEA,MAAMC,aAAa,GAAGnB,oBAAoB,CAAAW,aAAA,CAAAA,aAAA,KACrCJ,sBAAsB,GACtBD,iBAAiB,CACrB,CAAC;EACF,MAAMc,mCAAmC,GAAGnB,iBAAiB,CAACK,iBAAiB,CAAC;EAChF,MAAMe,8BAA8B,GAAGpB,iBAAiB,CAACM,sBAAsB,CAAC;EAChF,MAAMe,iBAAiB,GAAGlB,YAAY,CAACe,aAAa,CAAC;;EAErD;EACA;EACA;EACA;EACA,MAAMV,aAAa,GAAGV,IAAI,CAACuB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEd,SAAS,EAAEH,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,SAAS,EAAEA,SAAS,EAAED,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAEC,SAAS,EAAEF,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEE,SAAS,CAAC;EAChK,MAAME,WAAW,GAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACZW,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEV,KAAK,GACxBP,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEO,KAAK,GACtBL,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAEK,KAAK,GAC7BN,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEM,KAAK,CAC5B;EACD,MAAMC,KAAK,GAAAF,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACNW,iBAAiB,GACjBjB,eAAe,GACfgB,8BAA8B,GAC9BD,mCAAmC,CACvC;EACD,IAAIX,aAAa,CAACK,MAAM,GAAG,CAAC,EAAE;IAC5BD,KAAK,CAACL,SAAS,GAAGC,aAAa;EACjC;EACA,IAAIM,MAAM,CAACC,IAAI,CAACN,WAAW,CAAC,CAACI,MAAM,GAAG,CAAC,EAAE;IACvCD,KAAK,CAACD,KAAK,GAAGF,WAAW;EAC3B;EACA,OAAO;IACLG,KAAK;IACLI,WAAW,EAAEK,iBAAiB,CAACC;EACjC,CAAC;AACH;AACA,eAAerB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}