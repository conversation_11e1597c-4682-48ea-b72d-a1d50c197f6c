{"ast": null, "code": "const warnedOnceCache = new Set();\n\n/**\n * Logs a message to the console on development mode. The warning will only be logged once.\n *\n * The message is the log's cache key. Two identical messages will only be logged once.\n *\n * This function is a no-op in production.\n *\n * @param message the message to log\n * @param gravity the gravity of the warning. Defaults to `'warning'`.\n * @returns\n */\nexport function warnOnce(message) {\n  let gravity = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'warning';\n  if (process.env.NODE_ENV === 'production') {\n    return;\n  }\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  if (!warnedOnceCache.has(cleanMessage)) {\n    warnedOnceCache.add(cleanMessage);\n    if (gravity === 'error') {\n      console.error(cleanMessage);\n    } else {\n      console.warn(cleanMessage);\n    }\n  }\n}\nexport function clearWarningsCache() {\n  warnedOnceCache.clear();\n}", "map": {"version": 3, "names": ["warnedOnceCache", "Set", "warnOnce", "message", "gravity", "arguments", "length", "undefined", "process", "env", "NODE_ENV", "cleanMessage", "Array", "isArray", "join", "has", "add", "console", "error", "warn", "clearWarningsCache", "clear"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-internals/esm/warning/warning.js"], "sourcesContent": ["const warnedOnceCache = new Set();\n\n/**\n * Logs a message to the console on development mode. The warning will only be logged once.\n *\n * The message is the log's cache key. Two identical messages will only be logged once.\n *\n * This function is a no-op in production.\n *\n * @param message the message to log\n * @param gravity the gravity of the warning. Defaults to `'warning'`.\n * @returns\n */\nexport function warnOnce(message, gravity = 'warning') {\n  if (process.env.NODE_ENV === 'production') {\n    return;\n  }\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  if (!warnedOnceCache.has(cleanMessage)) {\n    warnedOnceCache.add(cleanMessage);\n    if (gravity === 'error') {\n      console.error(cleanMessage);\n    } else {\n      console.warn(cleanMessage);\n    }\n  }\n}\nexport function clearWarningsCache() {\n  warnedOnceCache.clear();\n}"], "mappings": "AAAA,MAAMA,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,OAAO,EAAuB;EAAA,IAArBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,SAAS;EACnD,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;EACF;EACA,MAAMC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACV,OAAO,CAAC,GAAGA,OAAO,CAACW,IAAI,CAAC,IAAI,CAAC,GAAGX,OAAO;EAC1E,IAAI,CAACH,eAAe,CAACe,GAAG,CAACJ,YAAY,CAAC,EAAE;IACtCX,eAAe,CAACgB,GAAG,CAACL,YAAY,CAAC;IACjC,IAAIP,OAAO,KAAK,OAAO,EAAE;MACvBa,OAAO,CAACC,KAAK,CAACP,YAAY,CAAC;IAC7B,CAAC,MAAM;MACLM,OAAO,CAACE,IAAI,CAACR,YAAY,CAAC;IAC5B;EACF;AACF;AACA,OAAO,SAASS,kBAAkBA,CAAA,EAAG;EACnCpB,eAAe,CAACqB,KAAK,CAAC,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}