{"ast": null, "code": "import _objectSpread from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport excludeVariablesFromRoot from \"./excludeVariablesFromRoot.js\";\nexport default theme => (colorScheme, css) => {\n  const root = theme.rootSelector || ':root';\n  const selector = theme.colorSchemeSelector;\n  let rule = selector;\n  if (selector === 'class') {\n    rule = '.%s';\n  }\n  if (selector === 'data') {\n    rule = '[data-%s]';\n  }\n  if (selector !== null && selector !== void 0 && selector.startsWith('data-') && !selector.includes('%s')) {\n    // 'data-mui-color-scheme' -> '[data-mui-color-scheme=\"%s\"]'\n    rule = \"[\".concat(selector, \"=\\\"%s\\\"]\");\n  }\n  if (theme.defaultColorScheme === colorScheme) {\n    if (colorScheme === 'dark') {\n      const excludedVariables = {};\n      excludeVariablesFromRoot(theme.cssVarPrefix).forEach(cssVar => {\n        excludedVariables[cssVar] = css[cssVar];\n        delete css[cssVar];\n      });\n      if (rule === 'media') {\n        return {\n          [root]: css,\n          [\"@media (prefers-color-scheme: dark)\"]: {\n            [root]: excludedVariables\n          }\n        };\n      }\n      if (rule) {\n        return {\n          [rule.replace('%s', colorScheme)]: excludedVariables,\n          [\"\".concat(root, \", \").concat(rule.replace('%s', colorScheme))]: css\n        };\n      }\n      return {\n        [root]: _objectSpread(_objectSpread({}, css), excludedVariables)\n      };\n    }\n    if (rule && rule !== 'media') {\n      return \"\".concat(root, \", \").concat(rule.replace('%s', String(colorScheme)));\n    }\n  } else if (colorScheme) {\n    if (rule === 'media') {\n      return {\n        [\"@media (prefers-color-scheme: \".concat(String(colorScheme), \")\")]: {\n          [root]: css\n        }\n      };\n    }\n    if (rule) {\n      return rule.replace('%s', String(colorScheme));\n    }\n  }\n  return root;\n};", "map": {"version": 3, "names": ["excludeVariablesFromRoot", "theme", "colorScheme", "css", "root", "rootSelector", "selector", "colorSchemeSelector", "rule", "startsWith", "includes", "concat", "defaultColorScheme", "excludedVariables", "cssVarPrefix", "for<PERSON>ach", "cssVar", "replace", "_objectSpread", "String"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/styles/createGetSelector.js"], "sourcesContent": ["import excludeVariablesFromRoot from \"./excludeVariablesFromRoot.js\";\nexport default theme => (colorScheme, css) => {\n  const root = theme.rootSelector || ':root';\n  const selector = theme.colorSchemeSelector;\n  let rule = selector;\n  if (selector === 'class') {\n    rule = '.%s';\n  }\n  if (selector === 'data') {\n    rule = '[data-%s]';\n  }\n  if (selector?.startsWith('data-') && !selector.includes('%s')) {\n    // 'data-mui-color-scheme' -> '[data-mui-color-scheme=\"%s\"]'\n    rule = `[${selector}=\"%s\"]`;\n  }\n  if (theme.defaultColorScheme === colorScheme) {\n    if (colorScheme === 'dark') {\n      const excludedVariables = {};\n      excludeVariablesFromRoot(theme.cssVarPrefix).forEach(cssVar => {\n        excludedVariables[cssVar] = css[cssVar];\n        delete css[cssVar];\n      });\n      if (rule === 'media') {\n        return {\n          [root]: css,\n          [`@media (prefers-color-scheme: dark)`]: {\n            [root]: excludedVariables\n          }\n        };\n      }\n      if (rule) {\n        return {\n          [rule.replace('%s', colorScheme)]: excludedVariables,\n          [`${root}, ${rule.replace('%s', colorScheme)}`]: css\n        };\n      }\n      return {\n        [root]: {\n          ...css,\n          ...excludedVariables\n        }\n      };\n    }\n    if (rule && rule !== 'media') {\n      return `${root}, ${rule.replace('%s', String(colorScheme))}`;\n    }\n  } else if (colorScheme) {\n    if (rule === 'media') {\n      return {\n        [`@media (prefers-color-scheme: ${String(colorScheme)})`]: {\n          [root]: css\n        }\n      };\n    }\n    if (rule) {\n      return rule.replace('%s', String(colorScheme));\n    }\n  }\n  return root;\n};"], "mappings": ";AAAA,OAAOA,wBAAwB,MAAM,+BAA+B;AACpE,eAAeC,KAAK,IAAI,CAACC,WAAW,EAAEC,GAAG,KAAK;EAC5C,MAAMC,IAAI,GAAGH,KAAK,CAACI,YAAY,IAAI,OAAO;EAC1C,MAAMC,QAAQ,GAAGL,KAAK,CAACM,mBAAmB;EAC1C,IAAIC,IAAI,GAAGF,QAAQ;EACnB,IAAIA,QAAQ,KAAK,OAAO,EAAE;IACxBE,IAAI,GAAG,KAAK;EACd;EACA,IAAIF,QAAQ,KAAK,MAAM,EAAE;IACvBE,IAAI,GAAG,WAAW;EACpB;EACA,IAAIF,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEG,UAAU,CAAC,OAAO,CAAC,IAAI,CAACH,QAAQ,CAACI,QAAQ,CAAC,IAAI,CAAC,EAAE;IAC7D;IACAF,IAAI,OAAAG,MAAA,CAAOL,QAAQ,aAAQ;EAC7B;EACA,IAAIL,KAAK,CAACW,kBAAkB,KAAKV,WAAW,EAAE;IAC5C,IAAIA,WAAW,KAAK,MAAM,EAAE;MAC1B,MAAMW,iBAAiB,GAAG,CAAC,CAAC;MAC5Bb,wBAAwB,CAACC,KAAK,CAACa,YAAY,CAAC,CAACC,OAAO,CAACC,MAAM,IAAI;QAC7DH,iBAAiB,CAACG,MAAM,CAAC,GAAGb,GAAG,CAACa,MAAM,CAAC;QACvC,OAAOb,GAAG,CAACa,MAAM,CAAC;MACpB,CAAC,CAAC;MACF,IAAIR,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO;UACL,CAACJ,IAAI,GAAGD,GAAG;UACX,yCAAyC;YACvC,CAACC,IAAI,GAAGS;UACV;QACF,CAAC;MACH;MACA,IAAIL,IAAI,EAAE;QACR,OAAO;UACL,CAACA,IAAI,CAACS,OAAO,CAAC,IAAI,EAAEf,WAAW,CAAC,GAAGW,iBAAiB;UACpD,IAAAF,MAAA,CAAIP,IAAI,QAAAO,MAAA,CAAKH,IAAI,CAACS,OAAO,CAAC,IAAI,EAAEf,WAAW,CAAC,IAAKC;QACnD,CAAC;MACH;MACA,OAAO;QACL,CAACC,IAAI,GAAAc,aAAA,CAAAA,aAAA,KACAf,GAAG,GACHU,iBAAiB;MAExB,CAAC;IACH;IACA,IAAIL,IAAI,IAAIA,IAAI,KAAK,OAAO,EAAE;MAC5B,UAAAG,MAAA,CAAUP,IAAI,QAAAO,MAAA,CAAKH,IAAI,CAACS,OAAO,CAAC,IAAI,EAAEE,MAAM,CAACjB,WAAW,CAAC,CAAC;IAC5D;EACF,CAAC,MAAM,IAAIA,WAAW,EAAE;IACtB,IAAIM,IAAI,KAAK,OAAO,EAAE;MACpB,OAAO;QACL,kCAAAG,MAAA,CAAkCQ,MAAM,CAACjB,WAAW,CAAC,SAAM;UACzD,CAACE,IAAI,GAAGD;QACV;MACF,CAAC;IACH;IACA,IAAIK,IAAI,EAAE;MACR,OAAOA,IAAI,CAACS,OAAO,CAAC,IAAI,EAAEE,MAAM,CAACjB,WAAW,CAAC,CAAC;IAChD;EACF;EACA,OAAOE,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}