{"ast": null, "code": "export{};", "map": {"version": 3, "names": [], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/models/JobDetail.ts"], "sourcesContent": ["import { WorkShift } from './WorkShift';\n\nexport interface JobDetail {\n  id?: number;\n  contractId?: number;\n  jobCategoryId: number;\n  jobCategoryName?: string; // For display purposes\n  startDate: string;\n  endDate: string;\n  workLocation: string;\n  workShifts: WorkShift[];\n  isDeleted?: boolean;\n  createdAt?: string;\n  updatedAt?: string;\n}\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}