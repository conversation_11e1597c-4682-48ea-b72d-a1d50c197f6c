{"ast": null, "code": "import React from'react';import{Box,Typography,Chip,Card,CardContent,Divider,useTheme,Avatar,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Paper}from'@mui/material';import PersonIcon from'@mui/icons-material/Person';import DateRangeIcon from'@mui/icons-material/DateRange';import DescriptionIcon from'@mui/icons-material/Description';import MonetizationOnIcon from'@mui/icons-material/MonetizationOn';import WorkIcon from'@mui/icons-material/Work';import CalendarMonthIcon from'@mui/icons-material/CalendarMonth';import{ContractStatusMap}from'../../models';import{formatDateLocalized}from'../../utils/dateUtils';import{calculateWorkingDates}from'../../utils/workingDaysUtils';import{formatCurrency}from'../../utils/currencyUtils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ContractDetails=_ref=>{let{contract}=_ref;const theme=useTheme();const getStatusColor=status=>{switch(status){case 0:// Pending\nreturn'warning';case 1:// Active\nreturn'success';case 2:// Completed\nreturn'info';case 3:// Cancelled\nreturn'error';default:return'default';}};const getStatusBgColor=status=>{switch(status){case 0:// Pending\nreturn theme.palette.warning.light;case 1:// Active\nreturn theme.palette.success.light;case 2:// Completed\nreturn theme.palette.info.light;case 3:// Cancelled\nreturn theme.palette.error.light;default:return theme.palette.grey[200];}};return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Card,{elevation:3,sx:{mb:4,borderRadius:'8px',border:'1px solid #e0e0e0',position:'relative',overflow:'hidden'},children:[/*#__PURE__*/_jsx(Box,{sx:{p:3,backgroundColor:getStatusBgColor(contract.status||0),borderBottom:'1px solid #e0e0e0'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Avatar,{sx:{bgcolor:theme.palette.primary.main,mr:2,width:56,height:56},children:/*#__PURE__*/_jsx(DescriptionIcon,{fontSize:\"large\"})}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h5\",sx:{fontWeight:'bold'},children:[\"H\\u1EE2P \\u0110\\u1ED2NG #\",contract.id]}),/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",color:\"text.secondary\",children:[\"M\\xE3 h\\u1EE3p \\u0111\\u1ED3ng: \",contract.id]})]})]}),/*#__PURE__*/_jsx(Chip,{label:ContractStatusMap[contract.status||0],color:getStatusColor(contract.status||0),sx:{fontSize:'1rem',py:2,px:3,fontWeight:'bold'}})]})}),/*#__PURE__*/_jsx(CardContent,{sx:{p:3},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexWrap:'wrap',gap:3},children:[/*#__PURE__*/_jsx(Box,{sx:{width:{xs:'100%',md:'48%'}},children:/*#__PURE__*/_jsx(Card,{variant:\"outlined\",sx:{mb:2,height:'100%'},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(PersonIcon,{sx:{mr:1,color:theme.palette.primary.main}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{fontWeight:'bold'},children:\"Th\\xF4ng tin kh\\xE1ch h\\xE0ng\"})]}),/*#__PURE__*/_jsx(Divider,{sx:{mb:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{fontWeight:'bold',mb:1},children:contract.customerName})]})})}),/*#__PURE__*/_jsx(Box,{sx:{width:{xs:'100%',md:'48%'}},children:/*#__PURE__*/_jsx(Card,{variant:\"outlined\",sx:{mb:2},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(DateRangeIcon,{sx:{mr:1,color:theme.palette.primary.main}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{fontWeight:'bold'},children:\"Th\\u1EDDi gian th\\u1EF1c hi\\u1EC7n\"})]}),/*#__PURE__*/_jsx(Divider,{sx:{mb:2}}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexWrap:'wrap',gap:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{width:{xs:'100%',sm:'30%'}},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Ng\\xE0y b\\u1EAFt \\u0111\\u1EA7u:\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{fontWeight:'medium'},children:formatDateLocalized(contract.startingDate)})]}),/*#__PURE__*/_jsxs(Box,{sx:{width:{xs:'100%',sm:'30%'}},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Ng\\xE0y k\\u1EBFt th\\xFAc:\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{fontWeight:'medium'},children:formatDateLocalized(contract.endingDate)})]})]})]})})}),/*#__PURE__*/_jsx(Box,{sx:{width:{xs:'100%',md:'48%'}},children:/*#__PURE__*/_jsx(Card,{variant:\"outlined\",sx:{mb:2},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(MonetizationOnIcon,{sx:{mr:1,color:theme.palette.primary.main}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{fontWeight:'bold'},children:\"Th\\xF4ng tin thanh to\\xE1n\"})]}),/*#__PURE__*/_jsx(Divider,{sx:{mb:2}}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexWrap:'wrap',gap:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{width:{xs:'100%',sm:'48%'}},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"T\\u1ED5ng gi\\xE1 tr\\u1ECB h\\u1EE3p \\u0111\\u1ED3ng:\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{fontWeight:'bold',color:theme.palette.primary.main},children:formatCurrency(contract.totalAmount)})]}),/*#__PURE__*/_jsxs(Box,{sx:{width:{xs:'100%',sm:'48%'}},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"\\u0110\\xE3 thanh to\\xE1n:\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{fontWeight:'bold',color:theme.palette.success.main},children:formatCurrency(contract.totalPaid||0)})]}),/*#__PURE__*/_jsxs(Box,{sx:{width:{xs:'100%',sm:'48%'}},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"C\\xF2n l\\u1EA1i:\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{fontWeight:'bold',color:theme.palette.error.main},children:formatCurrency(contract.totalAmount-(contract.totalPaid||0))})]})]})]})})}),contract.description&&/*#__PURE__*/_jsx(Box,{sx:{width:'100%'},children:/*#__PURE__*/_jsx(Card,{variant:\"outlined\",sx:{mb:2},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(DescriptionIcon,{sx:{mr:1,color:theme.palette.primary.main}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{fontWeight:'bold'},children:\"M\\xF4 t\\u1EA3 h\\u1EE3p \\u0111\\u1ED3ng\"})]}),/*#__PURE__*/_jsx(Divider,{sx:{mb:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:contract.description})]})})})]})})]}),/*#__PURE__*/_jsxs(Box,{sx:{mb:4},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(WorkIcon,{sx:{mr:1,color:theme.palette.secondary.main,fontSize:28}}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",sx:{fontWeight:'bold',color:theme.palette.secondary.main},children:\"CHI TI\\u1EBET C\\xD4NG VI\\u1EC6C\"})]}),contract.jobDetails.map((jobDetail,index)=>/*#__PURE__*/_jsx(Card,{variant:\"outlined\",sx:{mb:3,borderRadius:'8px',border:'1px solid #e0e0e0',position:'relative',overflow:'hidden','&::before':{content:'\"\"',position:'absolute',top:0,left:0,width:'100%',height:'6px',background:theme.palette.secondary.main}},children:/*#__PURE__*/_jsxs(CardContent,{sx:{p:3},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(WorkIcon,{sx:{mr:1,color:theme.palette.secondary.main}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:'bold'},children:jobDetail.jobCategoryName})]}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{fontWeight:'bold',mb:2,color:theme.palette.secondary.main},children:\"T\\u1ED5ng quan c\\xF4ng vi\\u1EC7c\"}),/*#__PURE__*/_jsx(TableContainer,{component:Paper,variant:\"outlined\",children:/*#__PURE__*/_jsx(Table,{size:\"small\",children:/*#__PURE__*/_jsxs(TableBody,{children:[/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{sx:{fontWeight:'bold',width:'30%',backgroundColor:theme.palette.grey[50]},children:\"\\u0110\\u1ECBa \\u0111i\\u1EC3m l\\xE0m vi\\u1EC7c\"}),/*#__PURE__*/_jsx(TableCell,{children:jobDetail.workLocation})]}),/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{sx:{fontWeight:'bold',backgroundColor:theme.palette.grey[50]},children:\"Th\\u1EDDi gian th\\u1EF1c hi\\u1EC7n\"}),/*#__PURE__*/_jsxs(TableCell,{children:[formatDateLocalized(jobDetail.startDate),\" - \",formatDateLocalized(jobDetail.endDate)]})]}),/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{sx:{fontWeight:'bold',backgroundColor:theme.palette.grey[50]},children:\"T\\u1ED5ng s\\u1ED1 ca l\\xE0m vi\\u1EC7c\"}),/*#__PURE__*/_jsxs(TableCell,{children:[(()=>{let totalShifts=0;jobDetail.workShifts.forEach(shift=>{const workingDates=calculateWorkingDates(jobDetail.startDate,jobDetail.endDate,shift.workingDays);totalShifts+=workingDates.length;});return totalShifts;})(),\" ca\"]})]}),/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{sx:{fontWeight:'bold',backgroundColor:theme.palette.grey[50]},children:\"T\\u1ED5ng quan ng\\xE0y l\\xE0m vi\\u1EC7c\"}),/*#__PURE__*/_jsx(TableCell,{children:(()=>{const allDays=new Set();jobDetail.workShifts.forEach(shift=>{const workingDates=calculateWorkingDates(jobDetail.startDate,jobDetail.endDate,shift.workingDays);workingDates.forEach(date=>allDays.add(date));});return\"\".concat(allDays.size,\" ng\\xE0y l\\xE0m vi\\u1EC7c\");})()})]})]})})})]}),/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(CalendarMonthIcon,{sx:{mr:1,color:theme.palette.primary.main}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{fontWeight:'bold',color:theme.palette.primary.main},children:\"L\\u1ECBch l\\xE0m vi\\u1EC7c chi ti\\u1EBFt\"})]}),(()=>{// Generate detailed work schedule for this job category\nlet allShifts=[];jobDetail.workShifts.forEach(shift=>{const workingDates=calculateWorkingDates(jobDetail.startDate,jobDetail.endDate,shift.workingDays);workingDates.forEach(date=>{allShifts.push({date,startTime:shift.startTime,endTime:shift.endTime,numberOfWorkers:shift.numberOfWorkers||0,salary:shift.salary||0});});});// Sort by date first, then by time\nallShifts.sort((a,b)=>{const[d1,m1,y1]=a.date.split('/').map(Number);const[d2,m2,y2]=b.date.split('/').map(Number);const dateCompare=new Date(y1,m1-1,d1).getTime()-new Date(y2,m2-1,d2).getTime();if(dateCompare!==0)return dateCompare;// If same date, sort by start time\nconst[h1,min1]=a.startTime.split(':').map(Number);const[h2,min2]=b.startTime.split(':').map(Number);return h1*60+min1-(h2*60+min2);});// Helper function to get Vietnamese day name\nconst getDayOfWeek=dateStr=>{const[d,m,y]=dateStr.split('/').map(Number);const date=new Date(y,m-1,d);const day=date.getDay();const dayNames=['chủ nhật','thứ hai','thứ ba','thứ tư','thứ năm','thứ sáu','thứ bảy'];return dayNames[day];};return allShifts.length===0?/*#__PURE__*/_jsx(Typography,{color:\"text.secondary\",children:\"Kh\\xF4ng c\\xF3 l\\u1ECBch l\\xE0m vi\\u1EC7c\"}):/*#__PURE__*/_jsx(TableContainer,{component:Paper,variant:\"outlined\",sx:{mt:2,maxHeight:400,overflow:'auto','&::-webkit-scrollbar':{width:'8px'},'&::-webkit-scrollbar-track':{backgroundColor:theme.palette.grey[100]},'&::-webkit-scrollbar-thumb':{backgroundColor:theme.palette.grey[400],borderRadius:'4px'}},children:/*#__PURE__*/_jsxs(Table,{size:\"small\",sx:{minWidth:700},stickyHeader:true,children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{sx:{fontWeight:'bold',fontSize:'0.9rem',backgroundColor:theme.palette.grey[50]},children:\"Ng\\xE0y\"}),/*#__PURE__*/_jsx(TableCell,{sx:{fontWeight:'bold',fontSize:'0.9rem',backgroundColor:theme.palette.grey[50]},children:\"Th\\u1EE9\"}),/*#__PURE__*/_jsx(TableCell,{sx:{fontWeight:'bold',fontSize:'0.9rem',backgroundColor:theme.palette.grey[50]},children:\"Ca l\\xE0m vi\\u1EC7c\"}),/*#__PURE__*/_jsx(TableCell,{sx:{fontWeight:'bold',fontSize:'0.9rem',backgroundColor:theme.palette.grey[50],textAlign:'center'},children:\"S\\u1ED1 c\\xF4ng nh\\xE2n\"}),/*#__PURE__*/_jsx(TableCell,{sx:{fontWeight:'bold',fontSize:'0.9rem',backgroundColor:theme.palette.grey[50],textAlign:'right'},children:\"L\\u01B0\\u01A1ng/ng\\u01B0\\u1EDDi\"}),/*#__PURE__*/_jsx(TableCell,{sx:{fontWeight:'bold',fontSize:'0.9rem',backgroundColor:theme.palette.grey[50],textAlign:'right'},children:\"T\\u1ED5ng chi ph\\xED\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:allShifts.map((item,idx)=>{const totalCost=item.numberOfWorkers*item.salary;return/*#__PURE__*/_jsxs(TableRow,{sx:{'&:nth-of-type(odd)':{backgroundColor:theme.palette.action.hover},'&:hover':{backgroundColor:theme.palette.action.selected}},children:[/*#__PURE__*/_jsx(TableCell,{sx:{fontSize:'0.9rem',py:1},children:item.date}),/*#__PURE__*/_jsx(TableCell,{sx:{fontSize:'0.9rem',py:1,textTransform:'capitalize'},children:getDayOfWeek(item.date)}),/*#__PURE__*/_jsxs(TableCell,{sx:{fontSize:'0.9rem',py:1},children:[item.startTime,\" - \",item.endTime]}),/*#__PURE__*/_jsxs(TableCell,{sx:{fontSize:'0.9rem',py:1,textAlign:'center'},children:[item.numberOfWorkers,\" ng\\u01B0\\u1EDDi\"]}),/*#__PURE__*/_jsxs(TableCell,{sx:{fontSize:'0.9rem',py:1,textAlign:'right'},children:[item.salary.toLocaleString('vi-VN'),\" \\u20AB\"]}),/*#__PURE__*/_jsxs(TableCell,{sx:{fontSize:'0.9rem',py:1,textAlign:'right',fontWeight:'medium'},children:[totalCost.toLocaleString('vi-VN'),\" \\u20AB\"]})]},idx);})})]})});})()]})]})},index))]})]});};export default ContractDetails;", "map": {"version": 3, "names": ["React", "Box", "Typography", "Chip", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Divider", "useTheme", "Avatar", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "PersonIcon", "DateRangeIcon", "DescriptionIcon", "MonetizationOnIcon", "WorkIcon", "CalendarMonthIcon", "ContractStatusMap", "formatDateLocalized", "calculateWorkingDates", "formatCurrency", "jsx", "_jsx", "jsxs", "_jsxs", "ContractDetails", "_ref", "contract", "theme", "getStatusColor", "status", "getStatusBgColor", "palette", "warning", "light", "success", "info", "error", "grey", "children", "elevation", "sx", "mb", "borderRadius", "border", "position", "overflow", "p", "backgroundColor", "borderBottom", "display", "justifyContent", "alignItems", "bgcolor", "primary", "main", "mr", "width", "height", "fontSize", "variant", "fontWeight", "id", "color", "label", "py", "px", "flexWrap", "gap", "xs", "md", "customerName", "sm", "startingDate", "endingDate", "totalAmount", "totalPaid", "description", "secondary", "jobDetails", "map", "jobDetail", "index", "content", "top", "left", "background", "jobCategoryName", "component", "size", "workLocation", "startDate", "endDate", "totalShifts", "workShifts", "for<PERSON>ach", "shift", "workingDates", "workingDays", "length", "allDays", "Set", "date", "add", "concat", "allShifts", "push", "startTime", "endTime", "numberOfWorkers", "salary", "sort", "a", "b", "d1", "m1", "y1", "split", "Number", "d2", "m2", "y2", "dateCompare", "Date", "getTime", "h1", "min1", "h2", "min2", "getDayOfWeek", "dateStr", "d", "m", "y", "day", "getDay", "dayNames", "mt", "maxHeight", "min<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "textAlign", "item", "idx", "totalCost", "action", "hover", "selected", "textTransform", "toLocaleString"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/contract/ContractDetails.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Box,\n  Typography,\n  Chip,\n  Card,\n  CardContent,\n  Divider,\n  useTheme,\n  Avatar,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n} from '@mui/material';\nimport PersonIcon from '@mui/icons-material/Person';\nimport DateRangeIcon from '@mui/icons-material/DateRange';\nimport DescriptionIcon from '@mui/icons-material/Description';\nimport MonetizationOnIcon from '@mui/icons-material/MonetizationOn';\nimport WorkIcon from '@mui/icons-material/Work';\n\nimport CalendarMonthIcon from '@mui/icons-material/CalendarMonth';\nimport { CustomerContract, ContractStatusMap } from '../../models';\nimport { formatDateLocalized } from '../../utils/dateUtils';\nimport { calculateWorkingDates } from '../../utils/workingDaysUtils';\nimport { formatCurrency } from '../../utils/currencyUtils';\n\n\n\ninterface ContractDetailsProps {\n  contract: CustomerContract;\n}\n\n\n\nconst ContractDetails: React.FC<ContractDetailsProps> = ({ contract }) => {\n  const theme = useTheme();\n\n  const getStatusColor = (status: number) => {\n    switch (status) {\n      case 0: // Pending\n        return 'warning';\n      case 1: // Active\n        return 'success';\n      case 2: // Completed\n        return 'info';\n      case 3: // Cancelled\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  const getStatusBgColor = (status: number) => {\n    switch (status) {\n      case 0: // Pending\n        return theme.palette.warning.light;\n      case 1: // Active\n        return theme.palette.success.light;\n      case 2: // Completed\n        return theme.palette.info.light;\n      case 3: // Cancelled\n        return theme.palette.error.light;\n      default:\n        return theme.palette.grey[200];\n    }\n  };\n\n  return (\n    <Box>\n      <Card\n        elevation={3}\n        sx={{\n          mb: 4,\n          borderRadius: '8px',\n          border: '1px solid #e0e0e0',\n          position: 'relative',\n          overflow: 'hidden',\n        }}\n      >\n        {/* Contract header with status */}\n        <Box\n          sx={{\n            p: 3,\n            backgroundColor: getStatusBgColor(contract.status || 0),\n            borderBottom: '1px solid #e0e0e0',\n          }}\n        >\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <Avatar\n                sx={{\n                  bgcolor: theme.palette.primary.main,\n                  mr: 2,\n                  width: 56,\n                  height: 56,\n                }}\n              >\n                <DescriptionIcon fontSize=\"large\" />\n              </Avatar>\n              <Box>\n                <Typography variant=\"h5\" sx={{ fontWeight: 'bold' }}>\n                  HỢP ĐỒNG #{contract.id}\n                </Typography>\n                <Typography variant=\"subtitle1\" color=\"text.secondary\">\n                  Mã hợp đồng: {contract.id}\n                </Typography>\n              </Box>\n            </Box>\n            <Chip\n              label={ContractStatusMap[contract.status || 0]}\n              color={getStatusColor(contract.status || 0)}\n              sx={{\n                fontSize: '1rem',\n                py: 2,\n                px: 3,\n                fontWeight: 'bold',\n              }}\n            />\n          </Box>\n        </Box>\n\n        <CardContent sx={{ p: 3 }}>\n          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n            {/* Customer information */}\n            <Box sx={{ width: { xs: '100%', md: '48%' } }}>\n              <Card variant=\"outlined\" sx={{ mb: 2, height: '100%' }}>\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <PersonIcon sx={{ mr: 1, color: theme.palette.primary.main }} />\n                    <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold' }}>\n                      Thông tin khách hàng\n                    </Typography>\n                  </Box>\n                  <Divider sx={{ mb: 2 }} />\n                  <Typography variant=\"body1\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                    {contract.customerName}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Box>\n\n\n\n            {/* Contract dates */}\n            <Box sx={{ width: { xs: '100%', md: '48%' } }}>\n              <Card variant=\"outlined\" sx={{ mb: 2 }}>\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <DateRangeIcon sx={{ mr: 1, color: theme.palette.primary.main }} />\n                    <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold' }}>\n                      Thời gian thực hiện\n                    </Typography>\n                  </Box>\n                  <Divider sx={{ mb: 2 }} />\n                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>\n                    <Box sx={{ width: { xs: '100%', sm: '30%' } }}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Ngày bắt đầu:\n                      </Typography>\n                      <Typography variant=\"body1\" sx={{ fontWeight: 'medium' }}>\n                        {formatDateLocalized(contract.startingDate)}\n                      </Typography>\n                    </Box>\n                    <Box sx={{ width: { xs: '100%', sm: '30%' } }}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Ngày kết thúc:\n                      </Typography>\n                      <Typography variant=\"body1\" sx={{ fontWeight: 'medium' }}>\n                        {formatDateLocalized(contract.endingDate)}\n                      </Typography>\n                    </Box>\n\n                  </Box>\n                </CardContent>\n              </Card>\n            </Box>\n\n            {/* Financial information */}\n            <Box sx={{ width: { xs: '100%', md: '48%' } }}>\n              <Card variant=\"outlined\" sx={{ mb: 2 }}>\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <MonetizationOnIcon sx={{ mr: 1, color: theme.palette.primary.main }} />\n                    <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold' }}>\n                      Thông tin thanh toán\n                    </Typography>\n                  </Box>\n                  <Divider sx={{ mb: 2 }} />\n                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>\n                    <Box sx={{ width: { xs: '100%', sm: '48%' } }}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Tổng giá trị hợp đồng:\n                      </Typography>\n                      <Typography variant=\"body1\" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>\n                        {formatCurrency(contract.totalAmount)}\n                      </Typography>\n                    </Box>\n                    <Box sx={{ width: { xs: '100%', sm: '48%' } }}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Đã thanh toán:\n                      </Typography>\n                      <Typography variant=\"body1\" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>\n                        {formatCurrency(contract.totalPaid || 0)}\n                      </Typography>\n                    </Box>\n                    <Box sx={{ width: { xs: '100%', sm: '48%' } }}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Còn lại:\n                      </Typography>\n                      <Typography variant=\"body1\" sx={{ fontWeight: 'bold', color: theme.palette.error.main }}>\n                        {formatCurrency(contract.totalAmount - (contract.totalPaid || 0))}\n                      </Typography>\n                    </Box>\n                  </Box>\n                </CardContent>\n              </Card>\n            </Box>\n\n            {/* Description if available */}\n            {contract.description && (\n              <Box sx={{ width: '100%' }}>\n                <Card variant=\"outlined\" sx={{ mb: 2 }}>\n                  <CardContent>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                      <DescriptionIcon sx={{ mr: 1, color: theme.palette.primary.main }} />\n                      <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold' }}>\n                        Mô tả hợp đồng\n                      </Typography>\n                    </Box>\n                    <Divider sx={{ mb: 2 }} />\n                    <Typography variant=\"body1\">\n                      {contract.description}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Box>\n            )}\n          </Box>\n        </CardContent>\n      </Card>\n\n      <Box sx={{ mb: 4 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n          <WorkIcon sx={{ mr: 1, color: theme.palette.secondary.main, fontSize: 28 }} />\n          <Typography variant=\"h5\" sx={{ fontWeight: 'bold', color: theme.palette.secondary.main }}>\n            CHI TIẾT CÔNG VIỆC\n          </Typography>\n        </Box>\n\n        {contract.jobDetails.map((jobDetail, index) => (\n          <Card\n            key={index}\n            variant=\"outlined\"\n            sx={{\n              mb: 3,\n              borderRadius: '8px',\n              border: '1px solid #e0e0e0',\n              position: 'relative',\n              overflow: 'hidden',\n              '&::before': {\n                content: '\"\"',\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                width: '100%',\n                height: '6px',\n                background: theme.palette.secondary.main,\n              }\n            }}\n          >\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <WorkIcon sx={{ mr: 1, color: theme.palette.secondary.main }} />\n                <Typography variant=\"h6\" sx={{ fontWeight: 'bold' }}>\n                  {jobDetail.jobCategoryName}\n                </Typography>\n              </Box>\n\n              <Divider sx={{ mb: 3 }} />\n\n              {/* Job Summary Table */}\n              <Box sx={{ mb: 3 }}>\n                <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold', mb: 2, color: theme.palette.secondary.main }}>\n                  Tổng quan công việc\n                </Typography>\n                <TableContainer component={Paper} variant=\"outlined\">\n                  <Table size=\"small\">\n                    <TableBody>\n                      <TableRow>\n                        <TableCell sx={{ fontWeight: 'bold', width: '30%', backgroundColor: theme.palette.grey[50] }}>\n                          Địa điểm làm việc\n                        </TableCell>\n                        <TableCell>{jobDetail.workLocation}</TableCell>\n                      </TableRow>\n                      <TableRow>\n                        <TableCell sx={{ fontWeight: 'bold', backgroundColor: theme.palette.grey[50] }}>\n                          Thời gian thực hiện\n                        </TableCell>\n                        <TableCell>\n                          {formatDateLocalized(jobDetail.startDate)} - {formatDateLocalized(jobDetail.endDate)}\n                        </TableCell>\n                      </TableRow>\n                      <TableRow>\n                        <TableCell sx={{ fontWeight: 'bold', backgroundColor: theme.palette.grey[50] }}>\n                          Tổng số ca làm việc\n                        </TableCell>\n                        <TableCell>\n                          {(() => {\n                            let totalShifts = 0;\n                            jobDetail.workShifts.forEach((shift: any) => {\n                              const workingDates = calculateWorkingDates(\n                                jobDetail.startDate,\n                                jobDetail.endDate,\n                                shift.workingDays\n                              );\n                              totalShifts += workingDates.length;\n                            });\n                            return totalShifts;\n                          })()} ca\n                        </TableCell>\n                      </TableRow>\n                      <TableRow>\n                        <TableCell sx={{ fontWeight: 'bold', backgroundColor: theme.palette.grey[50] }}>\n                          Tổng quan ngày làm việc\n                        </TableCell>\n                        <TableCell>\n                          {(() => {\n                            const allDays = new Set<string>();\n                            jobDetail.workShifts.forEach((shift: any) => {\n                              const workingDates = calculateWorkingDates(\n                                jobDetail.startDate,\n                                jobDetail.endDate,\n                                shift.workingDays\n                              );\n                              workingDates.forEach(date => allDays.add(date));\n                            });\n                            return `${allDays.size} ngày làm việc`;\n                          })()}\n                        </TableCell>\n                      </TableRow>\n                    </TableBody>\n                  </Table>\n                </TableContainer>\n              </Box>\n\n              {/* Lịch làm việc chi tiết cho loại công việc này */}\n              <Box sx={{ mb: 2 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                  <CalendarMonthIcon sx={{ mr: 1, color: theme.palette.primary.main }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>\n                    Lịch làm việc chi tiết\n                  </Typography>\n                </Box>\n                {(() => {\n                  // Generate detailed work schedule for this job category\n                  let allShifts: {\n                    date: string;\n                    startTime: string;\n                    endTime: string;\n                    numberOfWorkers: number;\n                    salary: number;\n                  }[] = [];\n\n                  jobDetail.workShifts.forEach((shift: any) => {\n                    const workingDates = calculateWorkingDates(\n                      jobDetail.startDate,\n                      jobDetail.endDate,\n                      shift.workingDays\n                    );\n                    workingDates.forEach(date => {\n                      allShifts.push({\n                        date,\n                        startTime: shift.startTime,\n                        endTime: shift.endTime,\n                        numberOfWorkers: shift.numberOfWorkers || 0,\n                        salary: shift.salary || 0\n                      });\n                    });\n                  });\n\n                  // Sort by date first, then by time\n                  allShifts.sort((a, b) => {\n                    const [d1, m1, y1] = a.date.split('/').map(Number);\n                    const [d2, m2, y2] = b.date.split('/').map(Number);\n                    const dateCompare = new Date(y1, m1 - 1, d1).getTime() - new Date(y2, m2 - 1, d2).getTime();\n\n                    if (dateCompare !== 0) return dateCompare;\n\n                    // If same date, sort by start time\n                    const [h1, min1] = a.startTime.split(':').map(Number);\n                    const [h2, min2] = b.startTime.split(':').map(Number);\n                    return (h1 * 60 + min1) - (h2 * 60 + min2);\n                  });\n\n                  // Helper function to get Vietnamese day name\n                  const getDayOfWeek = (dateStr: string) => {\n                    const [d, m, y] = dateStr.split('/').map(Number);\n                    const date = new Date(y, m - 1, d);\n                    const day = date.getDay();\n                    const dayNames = ['chủ nhật', 'thứ hai', 'thứ ba', 'thứ tư', 'thứ năm', 'thứ sáu', 'thứ bảy'];\n                    return dayNames[day];\n                  };\n\n                  return allShifts.length === 0 ? (\n                    <Typography color=\"text.secondary\">Không có lịch làm việc</Typography>\n                  ) : (\n                    <TableContainer\n                      component={Paper}\n                      variant=\"outlined\"\n                      sx={{\n                        mt: 2,\n                        maxHeight: 400,\n                        overflow: 'auto',\n                        '&::-webkit-scrollbar': {\n                          width: '8px',\n                        },\n                        '&::-webkit-scrollbar-track': {\n                          backgroundColor: theme.palette.grey[100],\n                        },\n                        '&::-webkit-scrollbar-thumb': {\n                          backgroundColor: theme.palette.grey[400],\n                          borderRadius: '4px',\n                        },\n                      }}\n                    >\n                      <Table size=\"small\" sx={{ minWidth: 700 }} stickyHeader>\n                        <TableHead>\n                          <TableRow>\n                            <TableCell sx={{ fontWeight: 'bold', fontSize: '0.9rem', backgroundColor: theme.palette.grey[50] }}>\n                              Ngày\n                            </TableCell>\n                            <TableCell sx={{ fontWeight: 'bold', fontSize: '0.9rem', backgroundColor: theme.palette.grey[50] }}>\n                              Thứ\n                            </TableCell>\n                            <TableCell sx={{ fontWeight: 'bold', fontSize: '0.9rem', backgroundColor: theme.palette.grey[50] }}>\n                              Ca làm việc\n                            </TableCell>\n                            <TableCell sx={{ fontWeight: 'bold', fontSize: '0.9rem', backgroundColor: theme.palette.grey[50], textAlign: 'center' }}>\n                              Số công nhân\n                            </TableCell>\n                            <TableCell sx={{ fontWeight: 'bold', fontSize: '0.9rem', backgroundColor: theme.palette.grey[50], textAlign: 'right' }}>\n                              Lương/người\n                            </TableCell>\n                            <TableCell sx={{ fontWeight: 'bold', fontSize: '0.9rem', backgroundColor: theme.palette.grey[50], textAlign: 'right' }}>\n                              Tổng chi phí\n                            </TableCell>\n                          </TableRow>\n                        </TableHead>\n                        <TableBody>\n                          {allShifts.map((item, idx) => {\n                            const totalCost = item.numberOfWorkers * item.salary;\n                            return (\n                              <TableRow\n                                key={idx}\n                                sx={{\n                                  '&:nth-of-type(odd)': {\n                                    backgroundColor: theme.palette.action.hover\n                                  },\n                                  '&:hover': {\n                                    backgroundColor: theme.palette.action.selected\n                                  }\n                                }}\n                              >\n                                <TableCell sx={{ fontSize: '0.9rem', py: 1 }}>\n                                  {item.date}\n                                </TableCell>\n                                <TableCell sx={{ fontSize: '0.9rem', py: 1, textTransform: 'capitalize' }}>\n                                  {getDayOfWeek(item.date)}\n                                </TableCell>\n                                <TableCell sx={{ fontSize: '0.9rem', py: 1 }}>\n                                  {item.startTime} - {item.endTime}\n                                </TableCell>\n                                <TableCell sx={{ fontSize: '0.9rem', py: 1, textAlign: 'center' }}>\n                                  {item.numberOfWorkers} người\n                                </TableCell>\n                                <TableCell sx={{ fontSize: '0.9rem', py: 1, textAlign: 'right' }}>\n                                  {item.salary.toLocaleString('vi-VN')} ₫\n                                </TableCell>\n                                <TableCell sx={{ fontSize: '0.9rem', py: 1, textAlign: 'right', fontWeight: 'medium' }}>\n                                  {totalCost.toLocaleString('vi-VN')} ₫\n                                </TableCell>\n                              </TableRow>\n                            );\n                          })}\n                        </TableBody>\n                      </Table>\n                    </TableContainer>\n                  );\n                })()}\n              </Box>\n            </CardContent>\n          </Card>\n        ))}\n      </Box>\n    </Box>\n  );\n};\n\nexport default ContractDetails;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,GAAG,CACHC,UAAU,CACVC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,OAAO,CACPC,QAAQ,CACRC,MAAM,CACNC,KAAK,CACLC,SAAS,CACTC,SAAS,CACTC,cAAc,CACdC,SAAS,CACTC,QAAQ,CACRC,KAAK,KACA,eAAe,CACtB,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,kBAAkB,KAAM,oCAAoC,CACnE,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAE/C,MAAO,CAAAC,iBAAiB,KAAM,mCAAmC,CACjE,OAA2BC,iBAAiB,KAAQ,cAAc,CAClE,OAASC,mBAAmB,KAAQ,uBAAuB,CAC3D,OAASC,qBAAqB,KAAQ,8BAA8B,CACpE,OAASC,cAAc,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAU3D,KAAM,CAAAC,eAA+C,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACnE,KAAM,CAAAE,KAAK,CAAG1B,QAAQ,CAAC,CAAC,CAExB,KAAM,CAAA2B,cAAc,CAAIC,MAAc,EAAK,CACzC,OAAQA,MAAM,EACZ,IAAK,EAAC,CAAE;AACN,MAAO,SAAS,CAClB,IAAK,EAAC,CAAE;AACN,MAAO,SAAS,CAClB,IAAK,EAAC,CAAE;AACN,MAAO,MAAM,CACf,IAAK,EAAC,CAAE;AACN,MAAO,OAAO,CAChB,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAID,MAAc,EAAK,CAC3C,OAAQA,MAAM,EACZ,IAAK,EAAC,CAAE;AACN,MAAO,CAAAF,KAAK,CAACI,OAAO,CAACC,OAAO,CAACC,KAAK,CACpC,IAAK,EAAC,CAAE;AACN,MAAO,CAAAN,KAAK,CAACI,OAAO,CAACG,OAAO,CAACD,KAAK,CACpC,IAAK,EAAC,CAAE;AACN,MAAO,CAAAN,KAAK,CAACI,OAAO,CAACI,IAAI,CAACF,KAAK,CACjC,IAAK,EAAC,CAAE;AACN,MAAO,CAAAN,KAAK,CAACI,OAAO,CAACK,KAAK,CAACH,KAAK,CAClC,QACE,MAAO,CAAAN,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAClC,CACF,CAAC,CAED,mBACEd,KAAA,CAAC5B,GAAG,EAAA2C,QAAA,eACFf,KAAA,CAACzB,IAAI,EACHyC,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,mBAAmB,CAC3BC,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,QACZ,CAAE,CAAAP,QAAA,eAGFjB,IAAA,CAAC1B,GAAG,EACF6C,EAAE,CAAE,CACFM,CAAC,CAAE,CAAC,CACJC,eAAe,CAAEjB,gBAAgB,CAACJ,QAAQ,CAACG,MAAM,EAAI,CAAC,CAAC,CACvDmB,YAAY,CAAE,mBAChB,CAAE,CAAAV,QAAA,cAEFf,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAb,QAAA,eAClFf,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAb,QAAA,eACjDjB,IAAA,CAACnB,MAAM,EACLsC,EAAE,CAAE,CACFY,OAAO,CAAEzB,KAAK,CAACI,OAAO,CAACsB,OAAO,CAACC,IAAI,CACnCC,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EACV,CAAE,CAAAnB,QAAA,cAEFjB,IAAA,CAACT,eAAe,EAAC8C,QAAQ,CAAC,OAAO,CAAE,CAAC,CAC9B,CAAC,cACTnC,KAAA,CAAC5B,GAAG,EAAA2C,QAAA,eACFf,KAAA,CAAC3B,UAAU,EAAC+D,OAAO,CAAC,IAAI,CAACnB,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAO,CAAE,CAAAtB,QAAA,EAAC,2BACzC,CAACZ,QAAQ,CAACmC,EAAE,EACZ,CAAC,cACbtC,KAAA,CAAC3B,UAAU,EAAC+D,OAAO,CAAC,WAAW,CAACG,KAAK,CAAC,gBAAgB,CAAAxB,QAAA,EAAC,iCACxC,CAACZ,QAAQ,CAACmC,EAAE,EACf,CAAC,EACV,CAAC,EACH,CAAC,cACNxC,IAAA,CAACxB,IAAI,EACHkE,KAAK,CAAE/C,iBAAiB,CAACU,QAAQ,CAACG,MAAM,EAAI,CAAC,CAAE,CAC/CiC,KAAK,CAAElC,cAAc,CAACF,QAAQ,CAACG,MAAM,EAAI,CAAC,CAAE,CAC5CW,EAAE,CAAE,CACFkB,QAAQ,CAAE,MAAM,CAChBM,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CAAC,CACLL,UAAU,CAAE,MACd,CAAE,CACH,CAAC,EACC,CAAC,CACH,CAAC,cAENvC,IAAA,CAACtB,WAAW,EAACyC,EAAE,CAAE,CAAEM,CAAC,CAAE,CAAE,CAAE,CAAAR,QAAA,cACxBf,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEiB,QAAQ,CAAE,MAAM,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAA7B,QAAA,eAErDjB,IAAA,CAAC1B,GAAG,EAAC6C,EAAE,CAAE,CAAEgB,KAAK,CAAE,CAAEY,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,KAAM,CAAE,CAAE,CAAA/B,QAAA,cAC5CjB,IAAA,CAACvB,IAAI,EAAC6D,OAAO,CAAC,UAAU,CAACnB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEgB,MAAM,CAAE,MAAO,CAAE,CAAAnB,QAAA,cACrDf,KAAA,CAACxB,WAAW,EAAAuC,QAAA,eACVf,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEV,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACxDjB,IAAA,CAACX,UAAU,EAAC8B,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAC,CAAEO,KAAK,CAAEnC,KAAK,CAACI,OAAO,CAACsB,OAAO,CAACC,IAAK,CAAE,CAAE,CAAC,cAChEjC,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,WAAW,CAACnB,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAO,CAAE,CAAAtB,QAAA,CAAC,+BAE5D,CAAY,CAAC,EACV,CAAC,cACNjB,IAAA,CAACrB,OAAO,EAACwC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC1BpB,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,OAAO,CAACnB,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEnB,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,CAC3DZ,QAAQ,CAAC4C,YAAY,CACZ,CAAC,EACF,CAAC,CACV,CAAC,CACJ,CAAC,cAKNjD,IAAA,CAAC1B,GAAG,EAAC6C,EAAE,CAAE,CAAEgB,KAAK,CAAE,CAAEY,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,KAAM,CAAE,CAAE,CAAA/B,QAAA,cAC5CjB,IAAA,CAACvB,IAAI,EAAC6D,OAAO,CAAC,UAAU,CAACnB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,cACrCf,KAAA,CAACxB,WAAW,EAAAuC,QAAA,eACVf,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEV,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACxDjB,IAAA,CAACV,aAAa,EAAC6B,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAC,CAAEO,KAAK,CAAEnC,KAAK,CAACI,OAAO,CAACsB,OAAO,CAACC,IAAK,CAAE,CAAE,CAAC,cACnEjC,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,WAAW,CAACnB,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAO,CAAE,CAAAtB,QAAA,CAAC,oCAE5D,CAAY,CAAC,EACV,CAAC,cACNjB,IAAA,CAACrB,OAAO,EAACwC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC1BlB,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEiB,QAAQ,CAAE,MAAM,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAA7B,QAAA,eACrDf,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAEgB,KAAK,CAAE,CAAEY,EAAE,CAAE,MAAM,CAAEG,EAAE,CAAE,KAAM,CAAE,CAAE,CAAAjC,QAAA,eAC5CjB,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,OAAO,CAACG,KAAK,CAAC,gBAAgB,CAAAxB,QAAA,CAAC,iCAEnD,CAAY,CAAC,cACbjB,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,OAAO,CAACnB,EAAE,CAAE,CAAEoB,UAAU,CAAE,QAAS,CAAE,CAAAtB,QAAA,CACtDrB,mBAAmB,CAACS,QAAQ,CAAC8C,YAAY,CAAC,CACjC,CAAC,EACV,CAAC,cACNjD,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAEgB,KAAK,CAAE,CAAEY,EAAE,CAAE,MAAM,CAAEG,EAAE,CAAE,KAAM,CAAE,CAAE,CAAAjC,QAAA,eAC5CjB,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,OAAO,CAACG,KAAK,CAAC,gBAAgB,CAAAxB,QAAA,CAAC,2BAEnD,CAAY,CAAC,cACbjB,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,OAAO,CAACnB,EAAE,CAAE,CAAEoB,UAAU,CAAE,QAAS,CAAE,CAAAtB,QAAA,CACtDrB,mBAAmB,CAACS,QAAQ,CAAC+C,UAAU,CAAC,CAC/B,CAAC,EACV,CAAC,EAEH,CAAC,EACK,CAAC,CACV,CAAC,CACJ,CAAC,cAGNpD,IAAA,CAAC1B,GAAG,EAAC6C,EAAE,CAAE,CAAEgB,KAAK,CAAE,CAAEY,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,KAAM,CAAE,CAAE,CAAA/B,QAAA,cAC5CjB,IAAA,CAACvB,IAAI,EAAC6D,OAAO,CAAC,UAAU,CAACnB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,cACrCf,KAAA,CAACxB,WAAW,EAAAuC,QAAA,eACVf,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEV,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACxDjB,IAAA,CAACR,kBAAkB,EAAC2B,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAC,CAAEO,KAAK,CAAEnC,KAAK,CAACI,OAAO,CAACsB,OAAO,CAACC,IAAK,CAAE,CAAE,CAAC,cACxEjC,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,WAAW,CAACnB,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAO,CAAE,CAAAtB,QAAA,CAAC,4BAE5D,CAAY,CAAC,EACV,CAAC,cACNjB,IAAA,CAACrB,OAAO,EAACwC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC1BlB,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEiB,QAAQ,CAAE,MAAM,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAA7B,QAAA,eACrDf,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAEgB,KAAK,CAAE,CAAEY,EAAE,CAAE,MAAM,CAAEG,EAAE,CAAE,KAAM,CAAE,CAAE,CAAAjC,QAAA,eAC5CjB,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,OAAO,CAACG,KAAK,CAAC,gBAAgB,CAAAxB,QAAA,CAAC,oDAEnD,CAAY,CAAC,cACbjB,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,OAAO,CAACnB,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEE,KAAK,CAAEnC,KAAK,CAACI,OAAO,CAACsB,OAAO,CAACC,IAAK,CAAE,CAAAhB,QAAA,CACvFnB,cAAc,CAACO,QAAQ,CAACgD,WAAW,CAAC,CAC3B,CAAC,EACV,CAAC,cACNnD,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAEgB,KAAK,CAAE,CAAEY,EAAE,CAAE,MAAM,CAAEG,EAAE,CAAE,KAAM,CAAE,CAAE,CAAAjC,QAAA,eAC5CjB,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,OAAO,CAACG,KAAK,CAAC,gBAAgB,CAAAxB,QAAA,CAAC,2BAEnD,CAAY,CAAC,cACbjB,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,OAAO,CAACnB,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEE,KAAK,CAAEnC,KAAK,CAACI,OAAO,CAACG,OAAO,CAACoB,IAAK,CAAE,CAAAhB,QAAA,CACvFnB,cAAc,CAACO,QAAQ,CAACiD,SAAS,EAAI,CAAC,CAAC,CAC9B,CAAC,EACV,CAAC,cACNpD,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAEgB,KAAK,CAAE,CAAEY,EAAE,CAAE,MAAM,CAAEG,EAAE,CAAE,KAAM,CAAE,CAAE,CAAAjC,QAAA,eAC5CjB,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,OAAO,CAACG,KAAK,CAAC,gBAAgB,CAAAxB,QAAA,CAAC,kBAEnD,CAAY,CAAC,cACbjB,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,OAAO,CAACnB,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEE,KAAK,CAAEnC,KAAK,CAACI,OAAO,CAACK,KAAK,CAACkB,IAAK,CAAE,CAAAhB,QAAA,CACrFnB,cAAc,CAACO,QAAQ,CAACgD,WAAW,EAAIhD,QAAQ,CAACiD,SAAS,EAAI,CAAC,CAAC,CAAC,CACvD,CAAC,EACV,CAAC,EACH,CAAC,EACK,CAAC,CACV,CAAC,CACJ,CAAC,CAGLjD,QAAQ,CAACkD,WAAW,eACnBvD,IAAA,CAAC1B,GAAG,EAAC6C,EAAE,CAAE,CAAEgB,KAAK,CAAE,MAAO,CAAE,CAAAlB,QAAA,cACzBjB,IAAA,CAACvB,IAAI,EAAC6D,OAAO,CAAC,UAAU,CAACnB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,cACrCf,KAAA,CAACxB,WAAW,EAAAuC,QAAA,eACVf,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEV,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACxDjB,IAAA,CAACT,eAAe,EAAC4B,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAC,CAAEO,KAAK,CAAEnC,KAAK,CAACI,OAAO,CAACsB,OAAO,CAACC,IAAK,CAAE,CAAE,CAAC,cACrEjC,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,WAAW,CAACnB,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAO,CAAE,CAAAtB,QAAA,CAAC,uCAE5D,CAAY,CAAC,EACV,CAAC,cACNjB,IAAA,CAACrB,OAAO,EAACwC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC1BpB,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,OAAO,CAAArB,QAAA,CACxBZ,QAAQ,CAACkD,WAAW,CACX,CAAC,EACF,CAAC,CACV,CAAC,CACJ,CACN,EACE,CAAC,CACK,CAAC,EACV,CAAC,cAEPrD,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACjBf,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEV,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACxDjB,IAAA,CAACP,QAAQ,EAAC0B,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAC,CAAEO,KAAK,CAAEnC,KAAK,CAACI,OAAO,CAAC8C,SAAS,CAACvB,IAAI,CAAEI,QAAQ,CAAE,EAAG,CAAE,CAAE,CAAC,cAC9ErC,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,IAAI,CAACnB,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEE,KAAK,CAAEnC,KAAK,CAACI,OAAO,CAAC8C,SAAS,CAACvB,IAAK,CAAE,CAAAhB,QAAA,CAAC,iCAE1F,CAAY,CAAC,EACV,CAAC,CAELZ,QAAQ,CAACoD,UAAU,CAACC,GAAG,CAAC,CAACC,SAAS,CAAEC,KAAK,gBACxC5D,IAAA,CAACvB,IAAI,EAEH6D,OAAO,CAAC,UAAU,CAClBnB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,mBAAmB,CAC3BC,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,QAAQ,CAClB,WAAW,CAAE,CACXqC,OAAO,CAAE,IAAI,CACbtC,QAAQ,CAAE,UAAU,CACpBuC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACP5B,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,KAAK,CACb4B,UAAU,CAAE1D,KAAK,CAACI,OAAO,CAAC8C,SAAS,CAACvB,IACtC,CACF,CAAE,CAAAhB,QAAA,cAEFf,KAAA,CAACxB,WAAW,EAACyC,EAAE,CAAE,CAAEM,CAAC,CAAE,CAAE,CAAE,CAAAR,QAAA,eACxBf,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEV,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACxDjB,IAAA,CAACP,QAAQ,EAAC0B,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAC,CAAEO,KAAK,CAAEnC,KAAK,CAACI,OAAO,CAAC8C,SAAS,CAACvB,IAAK,CAAE,CAAE,CAAC,cAChEjC,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,IAAI,CAACnB,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAO,CAAE,CAAAtB,QAAA,CACjD0C,SAAS,CAACM,eAAe,CAChB,CAAC,EACV,CAAC,cAENjE,IAAA,CAACrB,OAAO,EAACwC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BlB,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACjBjB,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,WAAW,CAACnB,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEnB,EAAE,CAAE,CAAC,CAAEqB,KAAK,CAAEnC,KAAK,CAACI,OAAO,CAAC8C,SAAS,CAACvB,IAAK,CAAE,CAAAhB,QAAA,CAAC,kCAExG,CAAY,CAAC,cACbjB,IAAA,CAACf,cAAc,EAACiF,SAAS,CAAE9E,KAAM,CAACkD,OAAO,CAAC,UAAU,CAAArB,QAAA,cAClDjB,IAAA,CAAClB,KAAK,EAACqF,IAAI,CAAC,OAAO,CAAAlD,QAAA,cACjBf,KAAA,CAACnB,SAAS,EAAAkC,QAAA,eACRf,KAAA,CAACf,QAAQ,EAAA8B,QAAA,eACPjB,IAAA,CAAChB,SAAS,EAACmC,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEJ,KAAK,CAAE,KAAK,CAAET,eAAe,CAAEpB,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,EAAE,CAAE,CAAE,CAAAC,QAAA,CAAC,+CAE9F,CAAW,CAAC,cACZjB,IAAA,CAAChB,SAAS,EAAAiC,QAAA,CAAE0C,SAAS,CAACS,YAAY,CAAY,CAAC,EACvC,CAAC,cACXlE,KAAA,CAACf,QAAQ,EAAA8B,QAAA,eACPjB,IAAA,CAAChB,SAAS,EAACmC,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEb,eAAe,CAAEpB,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,EAAE,CAAE,CAAE,CAAAC,QAAA,CAAC,oCAEhF,CAAW,CAAC,cACZf,KAAA,CAAClB,SAAS,EAAAiC,QAAA,EACPrB,mBAAmB,CAAC+D,SAAS,CAACU,SAAS,CAAC,CAAC,KAAG,CAACzE,mBAAmB,CAAC+D,SAAS,CAACW,OAAO,CAAC,EAC3E,CAAC,EACJ,CAAC,cACXpE,KAAA,CAACf,QAAQ,EAAA8B,QAAA,eACPjB,IAAA,CAAChB,SAAS,EAACmC,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEb,eAAe,CAAEpB,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,EAAE,CAAE,CAAE,CAAAC,QAAA,CAAC,uCAEhF,CAAW,CAAC,cACZf,KAAA,CAAClB,SAAS,EAAAiC,QAAA,EACP,CAAC,IAAM,CACN,GAAI,CAAAsD,WAAW,CAAG,CAAC,CACnBZ,SAAS,CAACa,UAAU,CAACC,OAAO,CAAEC,KAAU,EAAK,CAC3C,KAAM,CAAAC,YAAY,CAAG9E,qBAAqB,CACxC8D,SAAS,CAACU,SAAS,CACnBV,SAAS,CAACW,OAAO,CACjBI,KAAK,CAACE,WACR,CAAC,CACDL,WAAW,EAAII,YAAY,CAACE,MAAM,CACpC,CAAC,CAAC,CACF,MAAO,CAAAN,WAAW,CACpB,CAAC,EAAE,CAAC,CAAC,KACP,EAAW,CAAC,EACJ,CAAC,cACXrE,KAAA,CAACf,QAAQ,EAAA8B,QAAA,eACPjB,IAAA,CAAChB,SAAS,EAACmC,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEb,eAAe,CAAEpB,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,EAAE,CAAE,CAAE,CAAAC,QAAA,CAAC,yCAEhF,CAAW,CAAC,cACZjB,IAAA,CAAChB,SAAS,EAAAiC,QAAA,CACP,CAAC,IAAM,CACN,KAAM,CAAA6D,OAAO,CAAG,GAAI,CAAAC,GAAG,CAAS,CAAC,CACjCpB,SAAS,CAACa,UAAU,CAACC,OAAO,CAAEC,KAAU,EAAK,CAC3C,KAAM,CAAAC,YAAY,CAAG9E,qBAAqB,CACxC8D,SAAS,CAACU,SAAS,CACnBV,SAAS,CAACW,OAAO,CACjBI,KAAK,CAACE,WACR,CAAC,CACDD,YAAY,CAACF,OAAO,CAACO,IAAI,EAAIF,OAAO,CAACG,GAAG,CAACD,IAAI,CAAC,CAAC,CACjD,CAAC,CAAC,CACF,SAAAE,MAAA,CAAUJ,OAAO,CAACX,IAAI,8BACxB,CAAC,EAAE,CAAC,CACK,CAAC,EACJ,CAAC,EACF,CAAC,CACP,CAAC,CACM,CAAC,EACd,CAAC,cAGNjE,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACjBf,KAAA,CAAC5B,GAAG,EAAC6C,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEV,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACxDjB,IAAA,CAACN,iBAAiB,EAACyB,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAC,CAAEO,KAAK,CAAEnC,KAAK,CAACI,OAAO,CAACsB,OAAO,CAACC,IAAK,CAAE,CAAE,CAAC,cACvEjC,IAAA,CAACzB,UAAU,EAAC+D,OAAO,CAAC,WAAW,CAACnB,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEE,KAAK,CAAEnC,KAAK,CAACI,OAAO,CAACsB,OAAO,CAACC,IAAK,CAAE,CAAAhB,QAAA,CAAC,0CAE/F,CAAY,CAAC,EACV,CAAC,CACL,CAAC,IAAM,CACN;AACA,GAAI,CAAAkE,SAMD,CAAG,EAAE,CAERxB,SAAS,CAACa,UAAU,CAACC,OAAO,CAAEC,KAAU,EAAK,CAC3C,KAAM,CAAAC,YAAY,CAAG9E,qBAAqB,CACxC8D,SAAS,CAACU,SAAS,CACnBV,SAAS,CAACW,OAAO,CACjBI,KAAK,CAACE,WACR,CAAC,CACDD,YAAY,CAACF,OAAO,CAACO,IAAI,EAAI,CAC3BG,SAAS,CAACC,IAAI,CAAC,CACbJ,IAAI,CACJK,SAAS,CAAEX,KAAK,CAACW,SAAS,CAC1BC,OAAO,CAAEZ,KAAK,CAACY,OAAO,CACtBC,eAAe,CAAEb,KAAK,CAACa,eAAe,EAAI,CAAC,CAC3CC,MAAM,CAAEd,KAAK,CAACc,MAAM,EAAI,CAC1B,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF;AACAL,SAAS,CAACM,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CACvB,KAAM,CAACC,EAAE,CAAEC,EAAE,CAAEC,EAAE,CAAC,CAAGJ,CAAC,CAACV,IAAI,CAACe,KAAK,CAAC,GAAG,CAAC,CAACrC,GAAG,CAACsC,MAAM,CAAC,CAClD,KAAM,CAACC,EAAE,CAAEC,EAAE,CAAEC,EAAE,CAAC,CAAGR,CAAC,CAACX,IAAI,CAACe,KAAK,CAAC,GAAG,CAAC,CAACrC,GAAG,CAACsC,MAAM,CAAC,CAClD,KAAM,CAAAI,WAAW,CAAG,GAAI,CAAAC,IAAI,CAACP,EAAE,CAAED,EAAE,CAAG,CAAC,CAAED,EAAE,CAAC,CAACU,OAAO,CAAC,CAAC,CAAG,GAAI,CAAAD,IAAI,CAACF,EAAE,CAAED,EAAE,CAAG,CAAC,CAAED,EAAE,CAAC,CAACK,OAAO,CAAC,CAAC,CAE3F,GAAIF,WAAW,GAAK,CAAC,CAAE,MAAO,CAAAA,WAAW,CAEzC;AACA,KAAM,CAACG,EAAE,CAAEC,IAAI,CAAC,CAAGd,CAAC,CAACL,SAAS,CAACU,KAAK,CAAC,GAAG,CAAC,CAACrC,GAAG,CAACsC,MAAM,CAAC,CACrD,KAAM,CAACS,EAAE,CAAEC,IAAI,CAAC,CAAGf,CAAC,CAACN,SAAS,CAACU,KAAK,CAAC,GAAG,CAAC,CAACrC,GAAG,CAACsC,MAAM,CAAC,CACrD,MAAQ,CAAAO,EAAE,CAAG,EAAE,CAAGC,IAAI,EAAKC,EAAE,CAAG,EAAE,CAAGC,IAAI,CAAC,CAC5C,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,YAAY,CAAIC,OAAe,EAAK,CACxC,KAAM,CAACC,CAAC,CAAEC,CAAC,CAAEC,CAAC,CAAC,CAAGH,OAAO,CAACb,KAAK,CAAC,GAAG,CAAC,CAACrC,GAAG,CAACsC,MAAM,CAAC,CAChD,KAAM,CAAAhB,IAAI,CAAG,GAAI,CAAAqB,IAAI,CAACU,CAAC,CAAED,CAAC,CAAG,CAAC,CAAED,CAAC,CAAC,CAClC,KAAM,CAAAG,GAAG,CAAGhC,IAAI,CAACiC,MAAM,CAAC,CAAC,CACzB,KAAM,CAAAC,QAAQ,CAAG,CAAC,UAAU,CAAE,SAAS,CAAE,QAAQ,CAAE,QAAQ,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAC,CAC7F,MAAO,CAAAA,QAAQ,CAACF,GAAG,CAAC,CACtB,CAAC,CAED,MAAO,CAAA7B,SAAS,CAACN,MAAM,GAAK,CAAC,cAC3B7E,IAAA,CAACzB,UAAU,EAACkE,KAAK,CAAC,gBAAgB,CAAAxB,QAAA,CAAC,2CAAsB,CAAY,CAAC,cAEtEjB,IAAA,CAACf,cAAc,EACbiF,SAAS,CAAE9E,KAAM,CACjBkD,OAAO,CAAC,UAAU,CAClBnB,EAAE,CAAE,CACFgG,EAAE,CAAE,CAAC,CACLC,SAAS,CAAE,GAAG,CACd5F,QAAQ,CAAE,MAAM,CAChB,sBAAsB,CAAE,CACtBW,KAAK,CAAE,KACT,CAAC,CACD,4BAA4B,CAAE,CAC5BT,eAAe,CAAEpB,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CACzC,CAAC,CACD,4BAA4B,CAAE,CAC5BU,eAAe,CAAEpB,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CACxCK,YAAY,CAAE,KAChB,CACF,CAAE,CAAAJ,QAAA,cAEFf,KAAA,CAACpB,KAAK,EAACqF,IAAI,CAAC,OAAO,CAAChD,EAAE,CAAE,CAAEkG,QAAQ,CAAE,GAAI,CAAE,CAACC,YAAY,MAAArG,QAAA,eACrDjB,IAAA,CAACd,SAAS,EAAA+B,QAAA,cACRf,KAAA,CAACf,QAAQ,EAAA8B,QAAA,eACPjB,IAAA,CAAChB,SAAS,EAACmC,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEF,QAAQ,CAAE,QAAQ,CAAEX,eAAe,CAAEpB,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,EAAE,CAAE,CAAE,CAAAC,QAAA,CAAC,SAEpG,CAAW,CAAC,cACZjB,IAAA,CAAChB,SAAS,EAACmC,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEF,QAAQ,CAAE,QAAQ,CAAEX,eAAe,CAAEpB,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,EAAE,CAAE,CAAE,CAAAC,QAAA,CAAC,UAEpG,CAAW,CAAC,cACZjB,IAAA,CAAChB,SAAS,EAACmC,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEF,QAAQ,CAAE,QAAQ,CAAEX,eAAe,CAAEpB,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,EAAE,CAAE,CAAE,CAAAC,QAAA,CAAC,qBAEpG,CAAW,CAAC,cACZjB,IAAA,CAAChB,SAAS,EAACmC,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEF,QAAQ,CAAE,QAAQ,CAAEX,eAAe,CAAEpB,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,EAAE,CAAC,CAAEuG,SAAS,CAAE,QAAS,CAAE,CAAAtG,QAAA,CAAC,yBAEzH,CAAW,CAAC,cACZjB,IAAA,CAAChB,SAAS,EAACmC,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEF,QAAQ,CAAE,QAAQ,CAAEX,eAAe,CAAEpB,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,EAAE,CAAC,CAAEuG,SAAS,CAAE,OAAQ,CAAE,CAAAtG,QAAA,CAAC,iCAExH,CAAW,CAAC,cACZjB,IAAA,CAAChB,SAAS,EAACmC,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAM,CAAEF,QAAQ,CAAE,QAAQ,CAAEX,eAAe,CAAEpB,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,EAAE,CAAC,CAAEuG,SAAS,CAAE,OAAQ,CAAE,CAAAtG,QAAA,CAAC,sBAExH,CAAW,CAAC,EACJ,CAAC,CACF,CAAC,cACZjB,IAAA,CAACjB,SAAS,EAAAkC,QAAA,CACPkE,SAAS,CAACzB,GAAG,CAAC,CAAC8D,IAAI,CAAEC,GAAG,GAAK,CAC5B,KAAM,CAAAC,SAAS,CAAGF,IAAI,CAACjC,eAAe,CAAGiC,IAAI,CAAChC,MAAM,CACpD,mBACEtF,KAAA,CAACf,QAAQ,EAEPgC,EAAE,CAAE,CACF,oBAAoB,CAAE,CACpBO,eAAe,CAAEpB,KAAK,CAACI,OAAO,CAACiH,MAAM,CAACC,KACxC,CAAC,CACD,SAAS,CAAE,CACTlG,eAAe,CAAEpB,KAAK,CAACI,OAAO,CAACiH,MAAM,CAACE,QACxC,CACF,CAAE,CAAA5G,QAAA,eAEFjB,IAAA,CAAChB,SAAS,EAACmC,EAAE,CAAE,CAAEkB,QAAQ,CAAE,QAAQ,CAAEM,EAAE,CAAE,CAAE,CAAE,CAAA1B,QAAA,CAC1CuG,IAAI,CAACxC,IAAI,CACD,CAAC,cACZhF,IAAA,CAAChB,SAAS,EAACmC,EAAE,CAAE,CAAEkB,QAAQ,CAAE,QAAQ,CAAEM,EAAE,CAAE,CAAC,CAAEmF,aAAa,CAAE,YAAa,CAAE,CAAA7G,QAAA,CACvE0F,YAAY,CAACa,IAAI,CAACxC,IAAI,CAAC,CACf,CAAC,cACZ9E,KAAA,CAAClB,SAAS,EAACmC,EAAE,CAAE,CAAEkB,QAAQ,CAAE,QAAQ,CAAEM,EAAE,CAAE,CAAE,CAAE,CAAA1B,QAAA,EAC1CuG,IAAI,CAACnC,SAAS,CAAC,KAAG,CAACmC,IAAI,CAAClC,OAAO,EACvB,CAAC,cACZpF,KAAA,CAAClB,SAAS,EAACmC,EAAE,CAAE,CAAEkB,QAAQ,CAAE,QAAQ,CAAEM,EAAE,CAAE,CAAC,CAAE4E,SAAS,CAAE,QAAS,CAAE,CAAAtG,QAAA,EAC/DuG,IAAI,CAACjC,eAAe,CAAC,kBACxB,EAAW,CAAC,cACZrF,KAAA,CAAClB,SAAS,EAACmC,EAAE,CAAE,CAAEkB,QAAQ,CAAE,QAAQ,CAAEM,EAAE,CAAE,CAAC,CAAE4E,SAAS,CAAE,OAAQ,CAAE,CAAAtG,QAAA,EAC9DuG,IAAI,CAAChC,MAAM,CAACuC,cAAc,CAAC,OAAO,CAAC,CAAC,SACvC,EAAW,CAAC,cACZ7H,KAAA,CAAClB,SAAS,EAACmC,EAAE,CAAE,CAAEkB,QAAQ,CAAE,QAAQ,CAAEM,EAAE,CAAE,CAAC,CAAE4E,SAAS,CAAE,OAAO,CAAEhF,UAAU,CAAE,QAAS,CAAE,CAAAtB,QAAA,EACpFyG,SAAS,CAACK,cAAc,CAAC,OAAO,CAAC,CAAC,SACrC,EAAW,CAAC,GA3BPN,GA4BG,CAAC,CAEf,CAAC,CAAC,CACO,CAAC,EACP,CAAC,CACM,CACjB,CACH,CAAC,EAAE,CAAC,EACD,CAAC,EACK,CAAC,EA/OT7D,KAgPD,CACP,CAAC,EACC,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}