{"ast": null, "code": "/**\n * Format a number as Vietnamese currency (VND)\n * @param amount The amount to format\n * @returns Formatted currency string\n */export const formatCurrency=amount=>{if(amount===undefined||amount===null)return'0 VNĐ';return new Intl.NumberFormat('vi-VN',{style:'currency',currency:'VND',maximumFractionDigits:0}).format(amount);};/**\n * Format a number as Vietnamese currency without the currency symbol\n * @param amount The amount to format\n * @returns Formatted number string\n */export const formatNumber=amount=>{if(amount===undefined||amount===null)return'0';return new Intl.NumberFormat('vi-VN',{maximumFractionDigits:0}).format(amount);};", "map": {"version": 3, "names": ["formatCurrency", "amount", "undefined", "Intl", "NumberFormat", "style", "currency", "maximumFractionDigits", "format", "formatNumber"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/utils/currencyUtils.ts"], "sourcesContent": ["/**\n * Format a number as Vietnamese currency (VND)\n * @param amount The amount to format\n * @returns Formatted currency string\n */\nexport const formatCurrency = (amount: number | undefined): string => {\n  if (amount === undefined || amount === null) return '0 VNĐ';\n  \n  return new Intl.NumberFormat('vi-VN', {\n    style: 'currency',\n    currency: 'VND',\n    maximumFractionDigits: 0\n  }).format(amount);\n};\n\n/**\n * Format a number as Vietnamese currency without the currency symbol\n * @param amount The amount to format\n * @returns Formatted number string\n */\nexport const formatNumber = (amount: number | undefined): string => {\n  if (amount === undefined || amount === null) return '0';\n  \n  return new Intl.NumberFormat('vi-VN', {\n    maximumFractionDigits: 0\n  }).format(amount);\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAA,cAAc,CAAIC,MAA0B,EAAa,CACpE,GAAIA,MAAM,GAAKC,SAAS,EAAID,MAAM,GAAK,IAAI,CAAE,MAAO,OAAO,CAE3D,MAAO,IAAI,CAAAE,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,KAAK,CACfC,qBAAqB,CAAE,CACzB,CAAC,CAAC,CAACC,MAAM,CAACP,MAAM,CAAC,CACnB,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAQ,YAAY,CAAIR,MAA0B,EAAa,CAClE,GAAIA,MAAM,GAAKC,SAAS,EAAID,MAAM,GAAK,IAAI,CAAE,MAAO,GAAG,CAEvD,MAAO,IAAI,CAAAE,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCG,qBAAqB,CAAE,CACzB,CAAC,CAAC,CAACC,MAAM,CAACP,MAAM,CAAC,CACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}