{"ast": null, "code": "import _objectSpread from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"colorSchemeSelector\"];\nimport prepareCssVars from \"./prepareCssVars.js\";\nimport { createGetColorSchemeSelector } from \"./getColorSchemeSelector.js\";\nimport { DEFAULT_ATTRIBUTE } from \"../InitColorSchemeScript/InitColorSchemeScript.js\";\nfunction createCssVarsTheme(_ref) {\n  let {\n      colorSchemeSelector = \"[\".concat(DEFAULT_ATTRIBUTE, \"=\\\"%s\\\"]\")\n    } = _ref,\n    theme = _objectWithoutProperties(_ref, _excluded);\n  const output = theme;\n  const result = prepareCssVars(output, _objectSpread(_objectSpread({}, theme), {}, {\n    prefix: theme.cssVarPrefix,\n    colorSchemeSelector\n  }));\n  output.vars = result.vars;\n  output.generateThemeVars = result.generateThemeVars;\n  output.generateStyleSheets = result.generateStyleSheets;\n  output.colorSchemeSelector = colorSchemeSelector;\n  output.getColorSchemeSelector = createGetColorSchemeSelector(colorSchemeSelector);\n  return output;\n}\nexport default createCssVarsTheme;", "map": {"version": 3, "names": ["prepareCssVars", "createGetColorSchemeSelector", "DEFAULT_ATTRIBUTE", "createCssVarsTheme", "_ref", "colorSchemeSelector", "concat", "theme", "_objectWithoutProperties", "_excluded", "output", "result", "_objectSpread", "prefix", "cssVarPrefix", "vars", "generateThemeVars", "generateStyleSheets", "getColorSchemeSelector"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/system/esm/cssVars/createCssVarsTheme.js"], "sourcesContent": ["import prepareCssVars from \"./prepareCssVars.js\";\nimport { createGetColorSchemeSelector } from \"./getColorSchemeSelector.js\";\nimport { DEFAULT_ATTRIBUTE } from \"../InitColorSchemeScript/InitColorSchemeScript.js\";\nfunction createCssVarsTheme({\n  colorSchemeSelector = `[${DEFAULT_ATTRIBUTE}=\"%s\"]`,\n  ...theme\n}) {\n  const output = theme;\n  const result = prepareCssVars(output, {\n    ...theme,\n    prefix: theme.cssVarPrefix,\n    colorSchemeSelector\n  });\n  output.vars = result.vars;\n  output.generateThemeVars = result.generateThemeVars;\n  output.generateStyleSheets = result.generateStyleSheets;\n  output.colorSchemeSelector = colorSchemeSelector;\n  output.getColorSchemeSelector = createGetColorSchemeSelector(colorSchemeSelector);\n  return output;\n}\nexport default createCssVarsTheme;"], "mappings": ";;;AAAA,OAAOA,cAAc,MAAM,qBAAqB;AAChD,SAASC,4BAA4B,QAAQ,6BAA6B;AAC1E,SAASC,iBAAiB,QAAQ,mDAAmD;AACrF,SAASC,kBAAkBA,CAAAC,IAAA,EAGxB;EAAA,IAHyB;MAC1BC,mBAAmB,OAAAC,MAAA,CAAOJ,iBAAiB;IAE7C,CAAC,GAAAE,IAAA;IADIG,KAAK,GAAAC,wBAAA,CAAAJ,IAAA,EAAAK,SAAA;EAER,MAAMC,MAAM,GAAGH,KAAK;EACpB,MAAMI,MAAM,GAAGX,cAAc,CAACU,MAAM,EAAAE,aAAA,CAAAA,aAAA,KAC/BL,KAAK;IACRM,MAAM,EAAEN,KAAK,CAACO,YAAY;IAC1BT;EAAmB,EACpB,CAAC;EACFK,MAAM,CAACK,IAAI,GAAGJ,MAAM,CAACI,IAAI;EACzBL,MAAM,CAACM,iBAAiB,GAAGL,MAAM,CAACK,iBAAiB;EACnDN,MAAM,CAACO,mBAAmB,GAAGN,MAAM,CAACM,mBAAmB;EACvDP,MAAM,CAACL,mBAAmB,GAAGA,mBAAmB;EAChDK,MAAM,CAACQ,sBAAsB,GAAGjB,4BAA4B,CAACI,mBAAmB,CAAC;EACjF,OAAOK,MAAM;AACf;AACA,eAAeP,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}