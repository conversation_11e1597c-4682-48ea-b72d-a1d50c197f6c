{"ast": null, "code": "'use client';\n\nimport _objectSpread from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"BackdropProps\"],\n  _excluded2 = [\"anchor\", \"disableBackdropTransition\", \"disableDiscovery\", \"disableSwipeToOpen\", \"hideBackdrop\", \"hysteresis\", \"allowSwipeInChildren\", \"minFlingVelocity\", \"ModalProps\", \"onClose\", \"onOpen\", \"open\", \"PaperProps\", \"SwipeAreaProps\", \"swipeAreaWidth\", \"transitionDuration\", \"variant\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport NoSsr from \"../NoSsr/index.js\";\nimport Drawer, { getAnchor, isHorizontal } from \"../Drawer/Drawer.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport ownerWindow from \"../utils/ownerWindow.js\";\nimport useEventCallback from \"../utils/useEventCallback.js\";\nimport useEnhancedEffect from \"../utils/useEnhancedEffect.js\";\nimport { useTheme } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getTransitionProps } from \"../transitions/utils.js\";\nimport { mergeSlotProps } from \"../utils/index.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport SwipeArea from \"./SwipeArea.js\";\n\n// This value is closed to what browsers are using internally to\n// trigger a native scroll.\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst UNCERTAINTY_THRESHOLD = 3; // px\n\n// This is the part of the drawer displayed on touch start.\nconst DRAG_STARTED_SIGNAL = 20; // px\n\n// We can only have one instance at the time claiming ownership for handling the swipe.\n// Otherwise, the UX would be confusing.\n// That's why we use a singleton here.\nlet claimedSwipeInstance = null;\n\n// Exported for test purposes.\nexport function reset() {\n  claimedSwipeInstance = null;\n}\nfunction calculateCurrentX(anchor, touches, doc) {\n  return anchor === 'right' ? doc.body.offsetWidth - touches[0].pageX : touches[0].pageX;\n}\nfunction calculateCurrentY(anchor, touches, containerWindow) {\n  return anchor === 'bottom' ? containerWindow.innerHeight - touches[0].clientY : touches[0].clientY;\n}\nfunction getMaxTranslate(horizontalSwipe, paperInstance) {\n  return horizontalSwipe ? paperInstance.clientWidth : paperInstance.clientHeight;\n}\nfunction getTranslate(currentTranslate, startLocation, open, maxTranslate) {\n  return Math.min(Math.max(open ? startLocation - currentTranslate : maxTranslate + startLocation - currentTranslate, 0), maxTranslate);\n}\n\n/**\n * @param {Element | null} element\n * @param {Element} rootNode\n */\nfunction getDomTreeShapes(element, rootNode) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L129\n  const domTreeShapes = [];\n  while (element && element !== rootNode.parentElement) {\n    const style = ownerWindow(rootNode).getComputedStyle(element);\n    if (\n    // Ignore the scroll children if the element is absolute positioned.\n    style.getPropertyValue('position') === 'absolute' ||\n    // Ignore the scroll children if the element has an overflowX hidden\n    style.getPropertyValue('overflow-x') === 'hidden') {\n      // noop\n    } else if (element.clientWidth > 0 && element.scrollWidth > element.clientWidth || element.clientHeight > 0 && element.scrollHeight > element.clientHeight) {\n      // Ignore the nodes that have no width.\n      // Keep elements with a scroll\n      domTreeShapes.push(element);\n    }\n    element = element.parentElement;\n  }\n  return domTreeShapes;\n}\n\n/**\n * @param {object} param0\n * @param {ReturnType<getDomTreeShapes>} param0.domTreeShapes\n */\nfunction computeHasNativeHandler(_ref) {\n  let {\n    domTreeShapes,\n    start,\n    current,\n    anchor\n  } = _ref;\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L175\n  const axisProperties = {\n    scrollPosition: {\n      x: 'scrollLeft',\n      y: 'scrollTop'\n    },\n    scrollLength: {\n      x: 'scrollWidth',\n      y: 'scrollHeight'\n    },\n    clientLength: {\n      x: 'clientWidth',\n      y: 'clientHeight'\n    }\n  };\n  return domTreeShapes.some(shape => {\n    // Determine if we are going backward or forward.\n    let goingForward = current >= start;\n    if (anchor === 'top' || anchor === 'left') {\n      goingForward = !goingForward;\n    }\n    const axis = anchor === 'left' || anchor === 'right' ? 'x' : 'y';\n    const scrollPosition = Math.round(shape[axisProperties.scrollPosition[axis]]);\n    const areNotAtStart = scrollPosition > 0;\n    const areNotAtEnd = scrollPosition + shape[axisProperties.clientLength[axis]] < shape[axisProperties.scrollLength[axis]];\n    if (goingForward && areNotAtEnd || !goingForward && areNotAtStart) {\n      return true;\n    }\n    return false;\n  });\n}\nconst iOS = typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);\nconst SwipeableDrawer = /*#__PURE__*/React.forwardRef(function SwipeableDrawer(inProps, ref) {\n  var _slotProps$backdrop, _slotProps$paper;\n  const props = useDefaultProps({\n    name: 'MuiSwipeableDrawer',\n    props: inProps\n  });\n  const theme = useTheme();\n  const transitionDurationDefault = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      anchor = 'left',\n      disableBackdropTransition = false,\n      disableDiscovery = false,\n      disableSwipeToOpen = iOS,\n      hideBackdrop,\n      hysteresis = 0.52,\n      allowSwipeInChildren = false,\n      minFlingVelocity = 450,\n      ModalProps: {\n        BackdropProps\n      } = {},\n      onClose,\n      onOpen,\n      open = false,\n      PaperProps = {},\n      SwipeAreaProps,\n      swipeAreaWidth = 20,\n      transitionDuration = transitionDurationDefault,\n      variant = 'temporary',\n      // Mobile first.\n      slots = {},\n      slotProps = {}\n    } = props,\n    ModalPropsProp = _objectWithoutProperties(props.ModalProps, _excluded),\n    other = _objectWithoutProperties(props, _excluded2);\n  const [maybeSwiping, setMaybeSwiping] = React.useState(false);\n  const swipeInstance = React.useRef({\n    isSwiping: null\n  });\n  const swipeAreaRef = React.useRef();\n  const backdropRef = React.useRef();\n  const paperRef = React.useRef();\n  const handleRef = useForkRef(PaperProps.ref, paperRef);\n  const touchDetected = React.useRef(false);\n\n  // Ref for transition duration based on / to match swipe speed\n  const calculatedDurationRef = React.useRef();\n\n  // Use a ref so the open value used is always up to date inside useCallback.\n  useEnhancedEffect(() => {\n    calculatedDurationRef.current = null;\n  }, [open]);\n  const setPosition = React.useCallback(function (translate) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const {\n      mode = null,\n      changeTransition = true\n    } = options;\n    const anchorRtl = getAnchor(theme, anchor);\n    const rtlTranslateMultiplier = ['right', 'bottom'].includes(anchorRtl) ? 1 : -1;\n    const horizontalSwipe = isHorizontal(anchor);\n    const transform = horizontalSwipe ? \"translate(\".concat(rtlTranslateMultiplier * translate, \"px, 0)\") : \"translate(0, \".concat(rtlTranslateMultiplier * translate, \"px)\");\n    const drawerStyle = paperRef.current.style;\n    drawerStyle.webkitTransform = transform;\n    drawerStyle.transform = transform;\n    let transition = '';\n    if (mode) {\n      transition = theme.transitions.create('all', getTransitionProps({\n        easing: undefined,\n        style: undefined,\n        timeout: transitionDuration\n      }, {\n        mode\n      }));\n    }\n    if (changeTransition) {\n      drawerStyle.webkitTransition = transition;\n      drawerStyle.transition = transition;\n    }\n    if (!disableBackdropTransition && !hideBackdrop) {\n      const backdropStyle = backdropRef.current.style;\n      backdropStyle.opacity = 1 - translate / getMaxTranslate(horizontalSwipe, paperRef.current);\n      if (changeTransition) {\n        backdropStyle.webkitTransition = transition;\n        backdropStyle.transition = transition;\n      }\n    }\n  }, [anchor, disableBackdropTransition, hideBackdrop, theme, transitionDuration]);\n  const handleBodyTouchEnd = useEventCallback(nativeEvent => {\n    if (!touchDetected.current) {\n      return;\n    }\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- claimedSwipeInstance is a singleton\n    claimedSwipeInstance = null;\n    touchDetected.current = false;\n    ReactDOM.flushSync(() => {\n      setMaybeSwiping(false);\n    });\n\n    // The swipe wasn't started.\n    if (!swipeInstance.current.isSwiping) {\n      swipeInstance.current.isSwiping = null;\n      return;\n    }\n    swipeInstance.current.isSwiping = null;\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontal = isHorizontal(anchor);\n    let current;\n    if (horizontal) {\n      current = calculateCurrentX(anchorRtl, nativeEvent.changedTouches, ownerDocument(nativeEvent.currentTarget));\n    } else {\n      current = calculateCurrentY(anchorRtl, nativeEvent.changedTouches, ownerWindow(nativeEvent.currentTarget));\n    }\n    const startLocation = horizontal ? swipeInstance.current.startX : swipeInstance.current.startY;\n    const maxTranslate = getMaxTranslate(horizontal, paperRef.current);\n    const currentTranslate = getTranslate(current, startLocation, open, maxTranslate);\n    const translateRatio = currentTranslate / maxTranslate;\n    if (Math.abs(swipeInstance.current.velocity) > minFlingVelocity) {\n      // Calculate transition duration to match swipe speed\n      calculatedDurationRef.current = Math.abs((maxTranslate - currentTranslate) / swipeInstance.current.velocity) * 1000;\n    }\n    if (open) {\n      if (swipeInstance.current.velocity > minFlingVelocity || translateRatio > hysteresis) {\n        onClose();\n      } else {\n        // Reset the position, the swipe was aborted.\n        setPosition(0, {\n          mode: 'exit'\n        });\n      }\n      return;\n    }\n    if (swipeInstance.current.velocity < -minFlingVelocity || 1 - translateRatio > hysteresis) {\n      onOpen();\n    } else {\n      // Reset the position, the swipe was aborted.\n      setPosition(getMaxTranslate(horizontal, paperRef.current), {\n        mode: 'enter'\n      });\n    }\n  });\n  const startMaybeSwiping = function () {\n    let force = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    if (!maybeSwiping) {\n      // on Safari Mobile, if you want to be able to have the 'click' event fired on child elements, nothing in the DOM can be changed.\n      // this is because Safari Mobile will not fire any mouse events (still fires touch though) if the DOM changes during mousemove.\n      // so do this change on first touchmove instead of touchstart\n      if (force || !(disableDiscovery && allowSwipeInChildren)) {\n        ReactDOM.flushSync(() => {\n          setMaybeSwiping(true);\n        });\n      }\n      const horizontalSwipe = isHorizontal(anchor);\n      if (!open && paperRef.current) {\n        // The ref may be null when a parent component updates while swiping.\n        setPosition(getMaxTranslate(horizontalSwipe, paperRef.current) + (disableDiscovery ? 15 : -DRAG_STARTED_SIGNAL), {\n          changeTransition: false\n        });\n      }\n      swipeInstance.current.velocity = 0;\n      swipeInstance.current.lastTime = null;\n      swipeInstance.current.lastTranslate = null;\n      swipeInstance.current.paperHit = false;\n      touchDetected.current = true;\n    }\n  };\n  const handleBodyTouchMove = useEventCallback(nativeEvent => {\n    // the ref may be null when a parent component updates while swiping\n    if (!paperRef.current || !touchDetected.current) {\n      return;\n    }\n\n    // We are not supposed to handle this touch move because the swipe was started in a scrollable container in the drawer\n    if (claimedSwipeInstance !== null && claimedSwipeInstance !== swipeInstance.current) {\n      return;\n    }\n    startMaybeSwiping(true);\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (open && paperRef.current.contains(nativeEvent.target) && claimedSwipeInstance === null) {\n      const domTreeShapes = getDomTreeShapes(nativeEvent.target, paperRef.current);\n      const hasNativeHandler = computeHasNativeHandler({\n        domTreeShapes,\n        start: horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY,\n        current: horizontalSwipe ? currentX : currentY,\n        anchor\n      });\n      if (hasNativeHandler) {\n        claimedSwipeInstance = true;\n        return;\n      }\n      claimedSwipeInstance = swipeInstance.current;\n    }\n\n    // We don't know yet.\n    if (swipeInstance.current.isSwiping == null) {\n      const dx = Math.abs(currentX - swipeInstance.current.startX);\n      const dy = Math.abs(currentY - swipeInstance.current.startY);\n      const definitelySwiping = horizontalSwipe ? dx > dy && dx > UNCERTAINTY_THRESHOLD : dy > dx && dy > UNCERTAINTY_THRESHOLD;\n      if (definitelySwiping && nativeEvent.cancelable) {\n        nativeEvent.preventDefault();\n      }\n      if (definitelySwiping === true || (horizontalSwipe ? dy > UNCERTAINTY_THRESHOLD : dx > UNCERTAINTY_THRESHOLD)) {\n        swipeInstance.current.isSwiping = definitelySwiping;\n        if (!definitelySwiping) {\n          handleBodyTouchEnd(nativeEvent);\n          return;\n        }\n\n        // Shift the starting point.\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n\n        // Compensate for the part of the drawer displayed on touch start.\n        if (!disableDiscovery && !open) {\n          if (horizontalSwipe) {\n            swipeInstance.current.startX -= DRAG_STARTED_SIGNAL;\n          } else {\n            swipeInstance.current.startY -= DRAG_STARTED_SIGNAL;\n          }\n        }\n      }\n    }\n    if (!swipeInstance.current.isSwiping) {\n      return;\n    }\n    const maxTranslate = getMaxTranslate(horizontalSwipe, paperRef.current);\n    let startLocation = horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY;\n    if (open && !swipeInstance.current.paperHit) {\n      startLocation = Math.min(startLocation, maxTranslate);\n    }\n    const translate = getTranslate(horizontalSwipe ? currentX : currentY, startLocation, open, maxTranslate);\n    if (open) {\n      if (!swipeInstance.current.paperHit) {\n        const paperHit = horizontalSwipe ? currentX < maxTranslate : currentY < maxTranslate;\n        if (paperHit) {\n          swipeInstance.current.paperHit = true;\n          swipeInstance.current.startX = currentX;\n          swipeInstance.current.startY = currentY;\n        } else {\n          return;\n        }\n      } else if (translate === 0) {\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n      }\n    }\n    if (swipeInstance.current.lastTranslate === null) {\n      swipeInstance.current.lastTranslate = translate;\n      swipeInstance.current.lastTime = performance.now() + 1;\n    }\n    const velocity = (translate - swipeInstance.current.lastTranslate) / (performance.now() - swipeInstance.current.lastTime) * 1e3;\n\n    // Low Pass filter.\n    swipeInstance.current.velocity = swipeInstance.current.velocity * 0.4 + velocity * 0.6;\n    swipeInstance.current.lastTranslate = translate;\n    swipeInstance.current.lastTime = performance.now();\n\n    // We are swiping, let's prevent the scroll event on iOS.\n    if (nativeEvent.cancelable) {\n      nativeEvent.preventDefault();\n    }\n    setPosition(translate);\n  });\n  const handleBodyTouchStart = useEventCallback(nativeEvent => {\n    // We are not supposed to handle this touch move.\n    // Example of use case: ignore the event if there is a Slider.\n    if (nativeEvent.defaultPrevented) {\n      return;\n    }\n\n    // We can only have one node at the time claiming ownership for handling the swipe.\n    if (nativeEvent.defaultMuiPrevented) {\n      return;\n    }\n\n    // At least one element clogs the drawer interaction zone.\n    if (open && (hideBackdrop || !backdropRef.current.contains(nativeEvent.target)) && !paperRef.current.contains(nativeEvent.target)) {\n      return;\n    }\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (!open) {\n      var _paperRef$current;\n      // logic for if swipe should be ignored:\n      // if disableSwipeToOpen\n      // if target != swipeArea, and target is not a child of paper ref\n      // if is a child of paper ref, and `allowSwipeInChildren` does not allow it\n      if (disableSwipeToOpen || !(nativeEvent.target === swipeAreaRef.current || (_paperRef$current = paperRef.current) !== null && _paperRef$current !== void 0 && _paperRef$current.contains(nativeEvent.target) && (typeof allowSwipeInChildren === 'function' ? allowSwipeInChildren(nativeEvent, swipeAreaRef.current, paperRef.current) : allowSwipeInChildren))) {\n        return;\n      }\n      if (horizontalSwipe) {\n        if (currentX > swipeAreaWidth) {\n          return;\n        }\n      } else if (currentY > swipeAreaWidth) {\n        return;\n      }\n    }\n    nativeEvent.defaultMuiPrevented = true;\n    claimedSwipeInstance = null;\n    swipeInstance.current.startX = currentX;\n    swipeInstance.current.startY = currentY;\n    startMaybeSwiping();\n  });\n  React.useEffect(() => {\n    if (variant === 'temporary') {\n      const doc = ownerDocument(paperRef.current);\n      doc.addEventListener('touchstart', handleBodyTouchStart);\n      // A blocking listener prevents Firefox's navbar to auto-hide on scroll.\n      // It only needs to prevent scrolling on the drawer's content when open.\n      // When closed, the overlay prevents scrolling.\n      doc.addEventListener('touchmove', handleBodyTouchMove, {\n        passive: !open\n      });\n      doc.addEventListener('touchend', handleBodyTouchEnd);\n      return () => {\n        doc.removeEventListener('touchstart', handleBodyTouchStart);\n        doc.removeEventListener('touchmove', handleBodyTouchMove, {\n          passive: !open\n        });\n        doc.removeEventListener('touchend', handleBodyTouchEnd);\n      };\n    }\n    return undefined;\n  }, [variant, open, handleBodyTouchStart, handleBodyTouchMove, handleBodyTouchEnd]);\n  React.useEffect(() => () => {\n    // We need to release the lock.\n    if (claimedSwipeInstance === swipeInstance.current) {\n      claimedSwipeInstance = null;\n    }\n  }, []);\n  React.useEffect(() => {\n    if (!open) {\n      setMaybeSwiping(false);\n    }\n  }, [open]);\n  const [SwipeAreaSlot, swipeAreaSlotProps] = useSlot('swipeArea', {\n    ref: swipeAreaRef,\n    elementType: SwipeArea,\n    ownerState: props,\n    externalForwardedProps: {\n      slots,\n      slotProps: _objectSpread({\n        swipeArea: SwipeAreaProps\n      }, slotProps)\n    },\n    additionalProps: {\n      width: swipeAreaWidth,\n      anchor\n    }\n  });\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(Drawer, _objectSpread({\n      open: variant === 'temporary' && maybeSwiping ? true : open,\n      variant: variant,\n      ModalProps: _objectSpread(_objectSpread({\n        BackdropProps: _objectSpread(_objectSpread({}, BackdropProps), {}, {\n          ref: backdropRef\n        })\n      }, variant === 'temporary' && {\n        keepMounted: true\n      }), ModalPropsProp),\n      hideBackdrop: hideBackdrop,\n      anchor: anchor,\n      transitionDuration: calculatedDurationRef.current || transitionDuration,\n      onClose: onClose,\n      ref: ref,\n      slots: slots,\n      slotProps: _objectSpread(_objectSpread({}, slotProps), {}, {\n        backdrop: mergeSlotProps((_slotProps$backdrop = slotProps.backdrop) !== null && _slotProps$backdrop !== void 0 ? _slotProps$backdrop : BackdropProps, {\n          ref: backdropRef\n        }),\n        paper: mergeSlotProps((_slotProps$paper = slotProps.paper) !== null && _slotProps$paper !== void 0 ? _slotProps$paper : PaperProps, {\n          style: {\n            pointerEvents: variant === 'temporary' && !open && !allowSwipeInChildren ? 'none' : ''\n          },\n          ref: handleRef\n        })\n      })\n    }, other)), !disableSwipeToOpen && variant === 'temporary' && /*#__PURE__*/_jsx(NoSsr, {\n      children: /*#__PURE__*/_jsx(SwipeAreaSlot, _objectSpread({}, swipeAreaSlotProps))\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeableDrawer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If set to true, the swipe event will open the drawer even if the user begins the swipe on one of the drawer's children.\n   * This can be useful in scenarios where the drawer is partially visible.\n   * You can customize it further with a callback that determines which children the user can drag over to open the drawer\n   * (for example, to ignore other elements that handle touch move events, like sliders).\n   *\n   * @param {TouchEvent} event The 'touchstart' event\n   * @param {HTMLDivElement} swipeArea The swipe area element\n   * @param {HTMLDivElement} paper The drawer's paper element\n   *\n   * @default false\n   */\n  allowSwipeInChildren: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  anchor: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Disable the backdrop transition.\n   * This can improve the FPS on low-end devices.\n   * @default false\n   */\n  disableBackdropTransition: PropTypes.bool,\n  /**\n   * If `true`, touching the screen near the edge of the drawer will not slide in the drawer a bit\n   * to promote accidental discovery of the swipe gesture.\n   * @default false\n   */\n  disableDiscovery: PropTypes.bool,\n  /**\n   * If `true`, swipe to open is disabled. This is useful in browsers where swiping triggers\n   * navigation actions. Swipe to open is disabled on iOS browsers by default.\n   * @default typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent)\n   */\n  disableSwipeToOpen: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Affects how far the drawer must be opened/closed to change its state.\n   * Specified as percent (0-1) of the width of the drawer\n   * @default 0.52\n   */\n  hysteresis: PropTypes.number,\n  /**\n   * Defines, from which (average) velocity on, the swipe is\n   * defined as complete although hysteresis isn't reached.\n   * Good threshold is between 250 - 1000 px/s\n   * @default 450\n   */\n  minFlingVelocity: PropTypes.number,\n  /**\n   * @ignore\n   */\n  ModalProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    BackdropProps: PropTypes.shape({\n      component: elementTypeAcceptingRef\n    })\n  }),\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onClose: PropTypes.func.isRequired,\n  /**\n   * Callback fired when the component requests to be opened.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onOpen: PropTypes.func.isRequired,\n  /**\n   * If `true`, the component is shown.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef,\n    style: PropTypes.object\n  }),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    docked: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    swipeArea: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    docked: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType,\n    swipeArea: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The element is used to intercept the touch events on the edge.\n   * @deprecated use the `slotProps.swipeArea` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  SwipeAreaProps: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` that\n   * the drawer can be swiped open from.\n   * @default 20\n   */\n  swipeAreaWidth: PropTypes.number,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * @ignore\n   */\n  variant: PropTypes.oneOf(['permanent', 'persistent', 'temporary'])\n} : void 0;\nexport default SwipeableDrawer;", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "_excluded2", "React", "ReactDOM", "PropTypes", "elementTypeAcceptingRef", "NoSsr", "Drawer", "getAnchor", "isHorizontal", "useForkRef", "ownerDocument", "ownerWindow", "useEventCallback", "useEnhancedEffect", "useTheme", "useDefaultProps", "getTransitionProps", "mergeSlotProps", "useSlot", "SwipeArea", "jsx", "_jsx", "jsxs", "_jsxs", "UNCERTAINTY_THRESHOLD", "DRAG_STARTED_SIGNAL", "claimedSwipeInstance", "reset", "calculateCurrentX", "anchor", "touches", "doc", "body", "offsetWidth", "pageX", "calculateCurrentY", "containerWindow", "innerHeight", "clientY", "getMaxTranslate", "horizontalSwipe", "paperInstance", "clientWidth", "clientHeight", "getTranslate", "currentTranslate", "startLocation", "open", "maxTranslate", "Math", "min", "max", "getDomTreeShapes", "element", "rootNode", "domTreeShapes", "parentElement", "style", "getComputedStyle", "getPropertyValue", "scrollWidth", "scrollHeight", "push", "computeHasNativeHandler", "_ref", "start", "current", "axisProperties", "scrollPosition", "x", "y", "<PERSON><PERSON><PERSON><PERSON>", "clientLength", "some", "shape", "goingForward", "axis", "round", "areNotAtStart", "areNotAtEnd", "iOS", "navigator", "test", "userAgent", "SwipeableDrawer", "forwardRef", "inProps", "ref", "_slotProps$backdrop", "_slotProps$paper", "props", "name", "theme", "transitionDurationDefault", "enter", "transitions", "duration", "enteringScreen", "exit", "leavingScreen", "disableBackdropTransition", "disableDiscovery", "disableSwipeToOpen", "hideBackdrop", "hysteresis", "allowSwipeInChildren", "minFlingVelocity", "ModalProps", "BackdropProps", "onClose", "onOpen", "PaperProps", "SwipeAreaProps", "swipe<PERSON><PERSON><PERSON><PERSON><PERSON>", "transitionDuration", "variant", "slots", "slotProps", "ModalPropsProp", "other", "maybeSwiping", "setMaybeSwiping", "useState", "swipeInstance", "useRef", "isSwiping", "swipeAreaRef", "backdropRef", "paperRef", "handleRef", "touchDetected", "calculatedDurationRef", "setPosition", "useCallback", "translate", "options", "arguments", "length", "undefined", "mode", "changeTransition", "anchorRtl", "rtlTranslateMultiplier", "includes", "transform", "concat", "drawerStyle", "webkitTransform", "transition", "create", "easing", "timeout", "webkitTransition", "backdropStyle", "opacity", "handleBodyTouchEnd", "nativeEvent", "flushSync", "horizontal", "changedTouches", "currentTarget", "startX", "startY", "translateRatio", "abs", "velocity", "startMaybeSwiping", "force", "lastTime", "lastTranslate", "paperHit", "handleBodyTouchMove", "currentX", "currentY", "contains", "target", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dx", "dy", "definitelySwiping", "cancelable", "preventDefault", "performance", "now", "handleBodyTouchStart", "defaultPrevented", "defaultMuiPrevented", "_paperRef$current", "useEffect", "addEventListener", "passive", "removeEventListener", "SwipeAreaSlot", "swipeAreaSlotProps", "elementType", "ownerState", "externalForwardedProps", "swipeArea", "additionalProps", "width", "Fragment", "children", "keepMounted", "backdrop", "paper", "pointerEvents", "process", "env", "NODE_ENV", "propTypes", "oneOfType", "func", "bool", "oneOf", "node", "number", "component", "isRequired", "object", "docked", "root", "appear"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/SwipeableDrawer/SwipeableDrawer.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport NoSsr from \"../NoSsr/index.js\";\nimport Drawer, { getAnchor, isHorizontal } from \"../Drawer/Drawer.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport ownerWindow from \"../utils/ownerWindow.js\";\nimport useEventCallback from \"../utils/useEventCallback.js\";\nimport useEnhancedEffect from \"../utils/useEnhancedEffect.js\";\nimport { useTheme } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getTransitionProps } from \"../transitions/utils.js\";\nimport { mergeSlotProps } from \"../utils/index.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport SwipeArea from \"./SwipeArea.js\";\n\n// This value is closed to what browsers are using internally to\n// trigger a native scroll.\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst UNCERTAINTY_THRESHOLD = 3; // px\n\n// This is the part of the drawer displayed on touch start.\nconst DRAG_STARTED_SIGNAL = 20; // px\n\n// We can only have one instance at the time claiming ownership for handling the swipe.\n// Otherwise, the UX would be confusing.\n// That's why we use a singleton here.\nlet claimedSwipeInstance = null;\n\n// Exported for test purposes.\nexport function reset() {\n  claimedSwipeInstance = null;\n}\nfunction calculateCurrentX(anchor, touches, doc) {\n  return anchor === 'right' ? doc.body.offsetWidth - touches[0].pageX : touches[0].pageX;\n}\nfunction calculateCurrentY(anchor, touches, containerWindow) {\n  return anchor === 'bottom' ? containerWindow.innerHeight - touches[0].clientY : touches[0].clientY;\n}\nfunction getMaxTranslate(horizontalSwipe, paperInstance) {\n  return horizontalSwipe ? paperInstance.clientWidth : paperInstance.clientHeight;\n}\nfunction getTranslate(currentTranslate, startLocation, open, maxTranslate) {\n  return Math.min(Math.max(open ? startLocation - currentTranslate : maxTranslate + startLocation - currentTranslate, 0), maxTranslate);\n}\n\n/**\n * @param {Element | null} element\n * @param {Element} rootNode\n */\nfunction getDomTreeShapes(element, rootNode) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L129\n  const domTreeShapes = [];\n  while (element && element !== rootNode.parentElement) {\n    const style = ownerWindow(rootNode).getComputedStyle(element);\n    if (\n    // Ignore the scroll children if the element is absolute positioned.\n    style.getPropertyValue('position') === 'absolute' ||\n    // Ignore the scroll children if the element has an overflowX hidden\n    style.getPropertyValue('overflow-x') === 'hidden') {\n      // noop\n    } else if (element.clientWidth > 0 && element.scrollWidth > element.clientWidth || element.clientHeight > 0 && element.scrollHeight > element.clientHeight) {\n      // Ignore the nodes that have no width.\n      // Keep elements with a scroll\n      domTreeShapes.push(element);\n    }\n    element = element.parentElement;\n  }\n  return domTreeShapes;\n}\n\n/**\n * @param {object} param0\n * @param {ReturnType<getDomTreeShapes>} param0.domTreeShapes\n */\nfunction computeHasNativeHandler({\n  domTreeShapes,\n  start,\n  current,\n  anchor\n}) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L175\n  const axisProperties = {\n    scrollPosition: {\n      x: 'scrollLeft',\n      y: 'scrollTop'\n    },\n    scrollLength: {\n      x: 'scrollWidth',\n      y: 'scrollHeight'\n    },\n    clientLength: {\n      x: 'clientWidth',\n      y: 'clientHeight'\n    }\n  };\n  return domTreeShapes.some(shape => {\n    // Determine if we are going backward or forward.\n    let goingForward = current >= start;\n    if (anchor === 'top' || anchor === 'left') {\n      goingForward = !goingForward;\n    }\n    const axis = anchor === 'left' || anchor === 'right' ? 'x' : 'y';\n    const scrollPosition = Math.round(shape[axisProperties.scrollPosition[axis]]);\n    const areNotAtStart = scrollPosition > 0;\n    const areNotAtEnd = scrollPosition + shape[axisProperties.clientLength[axis]] < shape[axisProperties.scrollLength[axis]];\n    if (goingForward && areNotAtEnd || !goingForward && areNotAtStart) {\n      return true;\n    }\n    return false;\n  });\n}\nconst iOS = typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);\nconst SwipeableDrawer = /*#__PURE__*/React.forwardRef(function SwipeableDrawer(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiSwipeableDrawer',\n    props: inProps\n  });\n  const theme = useTheme();\n  const transitionDurationDefault = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n    anchor = 'left',\n    disableBackdropTransition = false,\n    disableDiscovery = false,\n    disableSwipeToOpen = iOS,\n    hideBackdrop,\n    hysteresis = 0.52,\n    allowSwipeInChildren = false,\n    minFlingVelocity = 450,\n    ModalProps: {\n      BackdropProps,\n      ...ModalPropsProp\n    } = {},\n    onClose,\n    onOpen,\n    open = false,\n    PaperProps = {},\n    SwipeAreaProps,\n    swipeAreaWidth = 20,\n    transitionDuration = transitionDurationDefault,\n    variant = 'temporary',\n    // Mobile first.\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const [maybeSwiping, setMaybeSwiping] = React.useState(false);\n  const swipeInstance = React.useRef({\n    isSwiping: null\n  });\n  const swipeAreaRef = React.useRef();\n  const backdropRef = React.useRef();\n  const paperRef = React.useRef();\n  const handleRef = useForkRef(PaperProps.ref, paperRef);\n  const touchDetected = React.useRef(false);\n\n  // Ref for transition duration based on / to match swipe speed\n  const calculatedDurationRef = React.useRef();\n\n  // Use a ref so the open value used is always up to date inside useCallback.\n  useEnhancedEffect(() => {\n    calculatedDurationRef.current = null;\n  }, [open]);\n  const setPosition = React.useCallback((translate, options = {}) => {\n    const {\n      mode = null,\n      changeTransition = true\n    } = options;\n    const anchorRtl = getAnchor(theme, anchor);\n    const rtlTranslateMultiplier = ['right', 'bottom'].includes(anchorRtl) ? 1 : -1;\n    const horizontalSwipe = isHorizontal(anchor);\n    const transform = horizontalSwipe ? `translate(${rtlTranslateMultiplier * translate}px, 0)` : `translate(0, ${rtlTranslateMultiplier * translate}px)`;\n    const drawerStyle = paperRef.current.style;\n    drawerStyle.webkitTransform = transform;\n    drawerStyle.transform = transform;\n    let transition = '';\n    if (mode) {\n      transition = theme.transitions.create('all', getTransitionProps({\n        easing: undefined,\n        style: undefined,\n        timeout: transitionDuration\n      }, {\n        mode\n      }));\n    }\n    if (changeTransition) {\n      drawerStyle.webkitTransition = transition;\n      drawerStyle.transition = transition;\n    }\n    if (!disableBackdropTransition && !hideBackdrop) {\n      const backdropStyle = backdropRef.current.style;\n      backdropStyle.opacity = 1 - translate / getMaxTranslate(horizontalSwipe, paperRef.current);\n      if (changeTransition) {\n        backdropStyle.webkitTransition = transition;\n        backdropStyle.transition = transition;\n      }\n    }\n  }, [anchor, disableBackdropTransition, hideBackdrop, theme, transitionDuration]);\n  const handleBodyTouchEnd = useEventCallback(nativeEvent => {\n    if (!touchDetected.current) {\n      return;\n    }\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- claimedSwipeInstance is a singleton\n    claimedSwipeInstance = null;\n    touchDetected.current = false;\n    ReactDOM.flushSync(() => {\n      setMaybeSwiping(false);\n    });\n\n    // The swipe wasn't started.\n    if (!swipeInstance.current.isSwiping) {\n      swipeInstance.current.isSwiping = null;\n      return;\n    }\n    swipeInstance.current.isSwiping = null;\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontal = isHorizontal(anchor);\n    let current;\n    if (horizontal) {\n      current = calculateCurrentX(anchorRtl, nativeEvent.changedTouches, ownerDocument(nativeEvent.currentTarget));\n    } else {\n      current = calculateCurrentY(anchorRtl, nativeEvent.changedTouches, ownerWindow(nativeEvent.currentTarget));\n    }\n    const startLocation = horizontal ? swipeInstance.current.startX : swipeInstance.current.startY;\n    const maxTranslate = getMaxTranslate(horizontal, paperRef.current);\n    const currentTranslate = getTranslate(current, startLocation, open, maxTranslate);\n    const translateRatio = currentTranslate / maxTranslate;\n    if (Math.abs(swipeInstance.current.velocity) > minFlingVelocity) {\n      // Calculate transition duration to match swipe speed\n      calculatedDurationRef.current = Math.abs((maxTranslate - currentTranslate) / swipeInstance.current.velocity) * 1000;\n    }\n    if (open) {\n      if (swipeInstance.current.velocity > minFlingVelocity || translateRatio > hysteresis) {\n        onClose();\n      } else {\n        // Reset the position, the swipe was aborted.\n        setPosition(0, {\n          mode: 'exit'\n        });\n      }\n      return;\n    }\n    if (swipeInstance.current.velocity < -minFlingVelocity || 1 - translateRatio > hysteresis) {\n      onOpen();\n    } else {\n      // Reset the position, the swipe was aborted.\n      setPosition(getMaxTranslate(horizontal, paperRef.current), {\n        mode: 'enter'\n      });\n    }\n  });\n  const startMaybeSwiping = (force = false) => {\n    if (!maybeSwiping) {\n      // on Safari Mobile, if you want to be able to have the 'click' event fired on child elements, nothing in the DOM can be changed.\n      // this is because Safari Mobile will not fire any mouse events (still fires touch though) if the DOM changes during mousemove.\n      // so do this change on first touchmove instead of touchstart\n      if (force || !(disableDiscovery && allowSwipeInChildren)) {\n        ReactDOM.flushSync(() => {\n          setMaybeSwiping(true);\n        });\n      }\n      const horizontalSwipe = isHorizontal(anchor);\n      if (!open && paperRef.current) {\n        // The ref may be null when a parent component updates while swiping.\n        setPosition(getMaxTranslate(horizontalSwipe, paperRef.current) + (disableDiscovery ? 15 : -DRAG_STARTED_SIGNAL), {\n          changeTransition: false\n        });\n      }\n      swipeInstance.current.velocity = 0;\n      swipeInstance.current.lastTime = null;\n      swipeInstance.current.lastTranslate = null;\n      swipeInstance.current.paperHit = false;\n      touchDetected.current = true;\n    }\n  };\n  const handleBodyTouchMove = useEventCallback(nativeEvent => {\n    // the ref may be null when a parent component updates while swiping\n    if (!paperRef.current || !touchDetected.current) {\n      return;\n    }\n\n    // We are not supposed to handle this touch move because the swipe was started in a scrollable container in the drawer\n    if (claimedSwipeInstance !== null && claimedSwipeInstance !== swipeInstance.current) {\n      return;\n    }\n    startMaybeSwiping(true);\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (open && paperRef.current.contains(nativeEvent.target) && claimedSwipeInstance === null) {\n      const domTreeShapes = getDomTreeShapes(nativeEvent.target, paperRef.current);\n      const hasNativeHandler = computeHasNativeHandler({\n        domTreeShapes,\n        start: horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY,\n        current: horizontalSwipe ? currentX : currentY,\n        anchor\n      });\n      if (hasNativeHandler) {\n        claimedSwipeInstance = true;\n        return;\n      }\n      claimedSwipeInstance = swipeInstance.current;\n    }\n\n    // We don't know yet.\n    if (swipeInstance.current.isSwiping == null) {\n      const dx = Math.abs(currentX - swipeInstance.current.startX);\n      const dy = Math.abs(currentY - swipeInstance.current.startY);\n      const definitelySwiping = horizontalSwipe ? dx > dy && dx > UNCERTAINTY_THRESHOLD : dy > dx && dy > UNCERTAINTY_THRESHOLD;\n      if (definitelySwiping && nativeEvent.cancelable) {\n        nativeEvent.preventDefault();\n      }\n      if (definitelySwiping === true || (horizontalSwipe ? dy > UNCERTAINTY_THRESHOLD : dx > UNCERTAINTY_THRESHOLD)) {\n        swipeInstance.current.isSwiping = definitelySwiping;\n        if (!definitelySwiping) {\n          handleBodyTouchEnd(nativeEvent);\n          return;\n        }\n\n        // Shift the starting point.\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n\n        // Compensate for the part of the drawer displayed on touch start.\n        if (!disableDiscovery && !open) {\n          if (horizontalSwipe) {\n            swipeInstance.current.startX -= DRAG_STARTED_SIGNAL;\n          } else {\n            swipeInstance.current.startY -= DRAG_STARTED_SIGNAL;\n          }\n        }\n      }\n    }\n    if (!swipeInstance.current.isSwiping) {\n      return;\n    }\n    const maxTranslate = getMaxTranslate(horizontalSwipe, paperRef.current);\n    let startLocation = horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY;\n    if (open && !swipeInstance.current.paperHit) {\n      startLocation = Math.min(startLocation, maxTranslate);\n    }\n    const translate = getTranslate(horizontalSwipe ? currentX : currentY, startLocation, open, maxTranslate);\n    if (open) {\n      if (!swipeInstance.current.paperHit) {\n        const paperHit = horizontalSwipe ? currentX < maxTranslate : currentY < maxTranslate;\n        if (paperHit) {\n          swipeInstance.current.paperHit = true;\n          swipeInstance.current.startX = currentX;\n          swipeInstance.current.startY = currentY;\n        } else {\n          return;\n        }\n      } else if (translate === 0) {\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n      }\n    }\n    if (swipeInstance.current.lastTranslate === null) {\n      swipeInstance.current.lastTranslate = translate;\n      swipeInstance.current.lastTime = performance.now() + 1;\n    }\n    const velocity = (translate - swipeInstance.current.lastTranslate) / (performance.now() - swipeInstance.current.lastTime) * 1e3;\n\n    // Low Pass filter.\n    swipeInstance.current.velocity = swipeInstance.current.velocity * 0.4 + velocity * 0.6;\n    swipeInstance.current.lastTranslate = translate;\n    swipeInstance.current.lastTime = performance.now();\n\n    // We are swiping, let's prevent the scroll event on iOS.\n    if (nativeEvent.cancelable) {\n      nativeEvent.preventDefault();\n    }\n    setPosition(translate);\n  });\n  const handleBodyTouchStart = useEventCallback(nativeEvent => {\n    // We are not supposed to handle this touch move.\n    // Example of use case: ignore the event if there is a Slider.\n    if (nativeEvent.defaultPrevented) {\n      return;\n    }\n\n    // We can only have one node at the time claiming ownership for handling the swipe.\n    if (nativeEvent.defaultMuiPrevented) {\n      return;\n    }\n\n    // At least one element clogs the drawer interaction zone.\n    if (open && (hideBackdrop || !backdropRef.current.contains(nativeEvent.target)) && !paperRef.current.contains(nativeEvent.target)) {\n      return;\n    }\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (!open) {\n      // logic for if swipe should be ignored:\n      // if disableSwipeToOpen\n      // if target != swipeArea, and target is not a child of paper ref\n      // if is a child of paper ref, and `allowSwipeInChildren` does not allow it\n      if (disableSwipeToOpen || !(nativeEvent.target === swipeAreaRef.current || paperRef.current?.contains(nativeEvent.target) && (typeof allowSwipeInChildren === 'function' ? allowSwipeInChildren(nativeEvent, swipeAreaRef.current, paperRef.current) : allowSwipeInChildren))) {\n        return;\n      }\n      if (horizontalSwipe) {\n        if (currentX > swipeAreaWidth) {\n          return;\n        }\n      } else if (currentY > swipeAreaWidth) {\n        return;\n      }\n    }\n    nativeEvent.defaultMuiPrevented = true;\n    claimedSwipeInstance = null;\n    swipeInstance.current.startX = currentX;\n    swipeInstance.current.startY = currentY;\n    startMaybeSwiping();\n  });\n  React.useEffect(() => {\n    if (variant === 'temporary') {\n      const doc = ownerDocument(paperRef.current);\n      doc.addEventListener('touchstart', handleBodyTouchStart);\n      // A blocking listener prevents Firefox's navbar to auto-hide on scroll.\n      // It only needs to prevent scrolling on the drawer's content when open.\n      // When closed, the overlay prevents scrolling.\n      doc.addEventListener('touchmove', handleBodyTouchMove, {\n        passive: !open\n      });\n      doc.addEventListener('touchend', handleBodyTouchEnd);\n      return () => {\n        doc.removeEventListener('touchstart', handleBodyTouchStart);\n        doc.removeEventListener('touchmove', handleBodyTouchMove, {\n          passive: !open\n        });\n        doc.removeEventListener('touchend', handleBodyTouchEnd);\n      };\n    }\n    return undefined;\n  }, [variant, open, handleBodyTouchStart, handleBodyTouchMove, handleBodyTouchEnd]);\n  React.useEffect(() => () => {\n    // We need to release the lock.\n    if (claimedSwipeInstance === swipeInstance.current) {\n      claimedSwipeInstance = null;\n    }\n  }, []);\n  React.useEffect(() => {\n    if (!open) {\n      setMaybeSwiping(false);\n    }\n  }, [open]);\n  const [SwipeAreaSlot, swipeAreaSlotProps] = useSlot('swipeArea', {\n    ref: swipeAreaRef,\n    elementType: SwipeArea,\n    ownerState: props,\n    externalForwardedProps: {\n      slots,\n      slotProps: {\n        swipeArea: SwipeAreaProps,\n        ...slotProps\n      }\n    },\n    additionalProps: {\n      width: swipeAreaWidth,\n      anchor\n    }\n  });\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(Drawer, {\n      open: variant === 'temporary' && maybeSwiping ? true : open,\n      variant: variant,\n      ModalProps: {\n        BackdropProps: {\n          ...BackdropProps,\n          ref: backdropRef\n        },\n        // Ensures that paperRef.current will be defined inside the touch start event handler\n        // See https://github.com/mui/material-ui/issues/30414 for more information\n        ...(variant === 'temporary' && {\n          keepMounted: true\n        }),\n        ...ModalPropsProp\n      },\n      hideBackdrop: hideBackdrop,\n      anchor: anchor,\n      transitionDuration: calculatedDurationRef.current || transitionDuration,\n      onClose: onClose,\n      ref: ref,\n      slots: slots,\n      slotProps: {\n        ...slotProps,\n        backdrop: mergeSlotProps(slotProps.backdrop ?? BackdropProps, {\n          ref: backdropRef\n        }),\n        paper: mergeSlotProps(slotProps.paper ?? PaperProps, {\n          style: {\n            pointerEvents: variant === 'temporary' && !open && !allowSwipeInChildren ? 'none' : ''\n          },\n          ref: handleRef\n        })\n      },\n      ...other\n    }), !disableSwipeToOpen && variant === 'temporary' && /*#__PURE__*/_jsx(NoSsr, {\n      children: /*#__PURE__*/_jsx(SwipeAreaSlot, {\n        ...swipeAreaSlotProps\n      })\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeableDrawer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If set to true, the swipe event will open the drawer even if the user begins the swipe on one of the drawer's children.\n   * This can be useful in scenarios where the drawer is partially visible.\n   * You can customize it further with a callback that determines which children the user can drag over to open the drawer\n   * (for example, to ignore other elements that handle touch move events, like sliders).\n   *\n   * @param {TouchEvent} event The 'touchstart' event\n   * @param {HTMLDivElement} swipeArea The swipe area element\n   * @param {HTMLDivElement} paper The drawer's paper element\n   *\n   * @default false\n   */\n  allowSwipeInChildren: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  anchor: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Disable the backdrop transition.\n   * This can improve the FPS on low-end devices.\n   * @default false\n   */\n  disableBackdropTransition: PropTypes.bool,\n  /**\n   * If `true`, touching the screen near the edge of the drawer will not slide in the drawer a bit\n   * to promote accidental discovery of the swipe gesture.\n   * @default false\n   */\n  disableDiscovery: PropTypes.bool,\n  /**\n   * If `true`, swipe to open is disabled. This is useful in browsers where swiping triggers\n   * navigation actions. Swipe to open is disabled on iOS browsers by default.\n   * @default typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent)\n   */\n  disableSwipeToOpen: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Affects how far the drawer must be opened/closed to change its state.\n   * Specified as percent (0-1) of the width of the drawer\n   * @default 0.52\n   */\n  hysteresis: PropTypes.number,\n  /**\n   * Defines, from which (average) velocity on, the swipe is\n   * defined as complete although hysteresis isn't reached.\n   * Good threshold is between 250 - 1000 px/s\n   * @default 450\n   */\n  minFlingVelocity: PropTypes.number,\n  /**\n   * @ignore\n   */\n  ModalProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    BackdropProps: PropTypes.shape({\n      component: elementTypeAcceptingRef\n    })\n  }),\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onClose: PropTypes.func.isRequired,\n  /**\n   * Callback fired when the component requests to be opened.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onOpen: PropTypes.func.isRequired,\n  /**\n   * If `true`, the component is shown.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef,\n    style: PropTypes.object\n  }),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    docked: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    swipeArea: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    docked: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType,\n    swipeArea: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The element is used to intercept the touch events on the edge.\n   * @deprecated use the `slotProps.swipeArea` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  SwipeAreaProps: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` that\n   * the drawer can be swiped open from.\n   * @default 20\n   */\n  swipeAreaWidth: PropTypes.number,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * @ignore\n   */\n  variant: PropTypes.oneOf(['permanent', 'persistent', 'temporary'])\n} : void 0;\nexport default SwipeableDrawer;"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;EAAAC,UAAA;AAEb,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,uBAAuB,MAAM,oCAAoC;AACxE,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,MAAM,IAAIC,SAAS,EAAEC,YAAY,QAAQ,qBAAqB;AACrE,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,qBAAqB,GAAG,CAAC,CAAC,CAAC;;AAEjC;AACA,MAAMC,mBAAmB,GAAG,EAAE,CAAC,CAAC;;AAEhC;AACA;AACA;AACA,IAAIC,oBAAoB,GAAG,IAAI;;AAE/B;AACA,OAAO,SAASC,KAAKA,CAAA,EAAG;EACtBD,oBAAoB,GAAG,IAAI;AAC7B;AACA,SAASE,iBAAiBA,CAACC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAE;EAC/C,OAAOF,MAAM,KAAK,OAAO,GAAGE,GAAG,CAACC,IAAI,CAACC,WAAW,GAAGH,OAAO,CAAC,CAAC,CAAC,CAACI,KAAK,GAAGJ,OAAO,CAAC,CAAC,CAAC,CAACI,KAAK;AACxF;AACA,SAASC,iBAAiBA,CAACN,MAAM,EAAEC,OAAO,EAAEM,eAAe,EAAE;EAC3D,OAAOP,MAAM,KAAK,QAAQ,GAAGO,eAAe,CAACC,WAAW,GAAGP,OAAO,CAAC,CAAC,CAAC,CAACQ,OAAO,GAAGR,OAAO,CAAC,CAAC,CAAC,CAACQ,OAAO;AACpG;AACA,SAASC,eAAeA,CAACC,eAAe,EAAEC,aAAa,EAAE;EACvD,OAAOD,eAAe,GAAGC,aAAa,CAACC,WAAW,GAAGD,aAAa,CAACE,YAAY;AACjF;AACA,SAASC,YAAYA,CAACC,gBAAgB,EAAEC,aAAa,EAAEC,IAAI,EAAEC,YAAY,EAAE;EACzE,OAAOC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACJ,IAAI,GAAGD,aAAa,GAAGD,gBAAgB,GAAGG,YAAY,GAAGF,aAAa,GAAGD,gBAAgB,EAAE,CAAC,CAAC,EAAEG,YAAY,CAAC;AACvI;;AAEA;AACA;AACA;AACA;AACA,SAASI,gBAAgBA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EAC3C;EACA,MAAMC,aAAa,GAAG,EAAE;EACxB,OAAOF,OAAO,IAAIA,OAAO,KAAKC,QAAQ,CAACE,aAAa,EAAE;IACpD,MAAMC,KAAK,GAAG9C,WAAW,CAAC2C,QAAQ,CAAC,CAACI,gBAAgB,CAACL,OAAO,CAAC;IAC7D;IACA;IACAI,KAAK,CAACE,gBAAgB,CAAC,UAAU,CAAC,KAAK,UAAU;IACjD;IACAF,KAAK,CAACE,gBAAgB,CAAC,YAAY,CAAC,KAAK,QAAQ,EAAE;MACjD;IAAA,CACD,MAAM,IAAIN,OAAO,CAACX,WAAW,GAAG,CAAC,IAAIW,OAAO,CAACO,WAAW,GAAGP,OAAO,CAACX,WAAW,IAAIW,OAAO,CAACV,YAAY,GAAG,CAAC,IAAIU,OAAO,CAACQ,YAAY,GAAGR,OAAO,CAACV,YAAY,EAAE;MAC1J;MACA;MACAY,aAAa,CAACO,IAAI,CAACT,OAAO,CAAC;IAC7B;IACAA,OAAO,GAAGA,OAAO,CAACG,aAAa;EACjC;EACA,OAAOD,aAAa;AACtB;;AAEA;AACA;AACA;AACA;AACA,SAASQ,uBAAuBA,CAAAC,IAAA,EAK7B;EAAA,IAL8B;IAC/BT,aAAa;IACbU,KAAK;IACLC,OAAO;IACPrC;EACF,CAAC,GAAAmC,IAAA;EACC;EACA,MAAMG,cAAc,GAAG;IACrBC,cAAc,EAAE;MACdC,CAAC,EAAE,YAAY;MACfC,CAAC,EAAE;IACL,CAAC;IACDC,YAAY,EAAE;MACZF,CAAC,EAAE,aAAa;MAChBC,CAAC,EAAE;IACL,CAAC;IACDE,YAAY,EAAE;MACZH,CAAC,EAAE,aAAa;MAChBC,CAAC,EAAE;IACL;EACF,CAAC;EACD,OAAOf,aAAa,CAACkB,IAAI,CAACC,KAAK,IAAI;IACjC;IACA,IAAIC,YAAY,GAAGT,OAAO,IAAID,KAAK;IACnC,IAAIpC,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,MAAM,EAAE;MACzC8C,YAAY,GAAG,CAACA,YAAY;IAC9B;IACA,MAAMC,IAAI,GAAG/C,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG;IAChE,MAAMuC,cAAc,GAAGnB,IAAI,CAAC4B,KAAK,CAACH,KAAK,CAACP,cAAc,CAACC,cAAc,CAACQ,IAAI,CAAC,CAAC,CAAC;IAC7E,MAAME,aAAa,GAAGV,cAAc,GAAG,CAAC;IACxC,MAAMW,WAAW,GAAGX,cAAc,GAAGM,KAAK,CAACP,cAAc,CAACK,YAAY,CAACI,IAAI,CAAC,CAAC,GAAGF,KAAK,CAACP,cAAc,CAACI,YAAY,CAACK,IAAI,CAAC,CAAC;IACxH,IAAID,YAAY,IAAII,WAAW,IAAI,CAACJ,YAAY,IAAIG,aAAa,EAAE;MACjE,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC;AACJ;AACA,MAAME,GAAG,GAAG,OAAOC,SAAS,KAAK,WAAW,IAAI,kBAAkB,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC;AAC5F,MAAMC,eAAe,GAAG,aAAanF,KAAK,CAACoF,UAAU,CAAC,SAASD,eAAeA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAAA,IAAAC,mBAAA,EAAAC,gBAAA;EAC3F,MAAMC,KAAK,GAAG3E,eAAe,CAAC;IAC5B4E,IAAI,EAAE,oBAAoB;IAC1BD,KAAK,EAAEJ;EACT,CAAC,CAAC;EACF,MAAMM,KAAK,GAAG9E,QAAQ,CAAC,CAAC;EACxB,MAAM+E,yBAAyB,GAAG;IAChCC,KAAK,EAAEF,KAAK,CAACG,WAAW,CAACC,QAAQ,CAACC,cAAc;IAChDC,IAAI,EAAEN,KAAK,CAACG,WAAW,CAACC,QAAQ,CAACG;EACnC,CAAC;EACD,MAAM;MACJtE,MAAM,GAAG,MAAM;MACfuE,yBAAyB,GAAG,KAAK;MACjCC,gBAAgB,GAAG,KAAK;MACxBC,kBAAkB,GAAGtB,GAAG;MACxBuB,YAAY;MACZC,UAAU,GAAG,IAAI;MACjBC,oBAAoB,GAAG,KAAK;MAC5BC,gBAAgB,GAAG,GAAG;MACtBC,UAAU,EAAE;QACVC;MAEF,CAAC,GAAG,CAAC,CAAC;MACNC,OAAO;MACPC,MAAM;MACN/D,IAAI,GAAG,KAAK;MACZgE,UAAU,GAAG,CAAC,CAAC;MACfC,cAAc;MACdC,cAAc,GAAG,EAAE;MACnBC,kBAAkB,GAAGrB,yBAAyB;MAC9CsB,OAAO,GAAG,WAAW;MACrB;MACAC,KAAK,GAAG,CAAC,CAAC;MACVC,SAAS,GAAG,CAAC;IAEf,CAAC,GAAG3B,KAAK;IAdF4B,cAAc,GAAAxH,wBAAA,CAcjB4F,KAAK,CAhBPiB,UAAU,EAAA5G,SAAA;IAePwH,KAAK,GAAAzH,wBAAA,CACN4F,KAAK,EAAA1F,UAAA;EACT,MAAM,CAACwH,YAAY,EAAEC,eAAe,CAAC,GAAGxH,KAAK,CAACyH,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMC,aAAa,GAAG1H,KAAK,CAAC2H,MAAM,CAAC;IACjCC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMC,YAAY,GAAG7H,KAAK,CAAC2H,MAAM,CAAC,CAAC;EACnC,MAAMG,WAAW,GAAG9H,KAAK,CAAC2H,MAAM,CAAC,CAAC;EAClC,MAAMI,QAAQ,GAAG/H,KAAK,CAAC2H,MAAM,CAAC,CAAC;EAC/B,MAAMK,SAAS,GAAGxH,UAAU,CAACsG,UAAU,CAACxB,GAAG,EAAEyC,QAAQ,CAAC;EACtD,MAAME,aAAa,GAAGjI,KAAK,CAAC2H,MAAM,CAAC,KAAK,CAAC;;EAEzC;EACA,MAAMO,qBAAqB,GAAGlI,KAAK,CAAC2H,MAAM,CAAC,CAAC;;EAE5C;EACA/G,iBAAiB,CAAC,MAAM;IACtBsH,qBAAqB,CAACjE,OAAO,GAAG,IAAI;EACtC,CAAC,EAAE,CAACnB,IAAI,CAAC,CAAC;EACV,MAAMqF,WAAW,GAAGnI,KAAK,CAACoI,WAAW,CAAC,UAACC,SAAS,EAAmB;IAAA,IAAjBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC5D,MAAM;MACJG,IAAI,GAAG,IAAI;MACXC,gBAAgB,GAAG;IACrB,CAAC,GAAGL,OAAO;IACX,MAAMM,SAAS,GAAGtI,SAAS,CAACqF,KAAK,EAAE/D,MAAM,CAAC;IAC1C,MAAMiH,sBAAsB,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACF,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/E,MAAMrG,eAAe,GAAGhC,YAAY,CAACqB,MAAM,CAAC;IAC5C,MAAMmH,SAAS,GAAGxG,eAAe,gBAAAyG,MAAA,CAAgBH,sBAAsB,GAAGR,SAAS,8BAAAW,MAAA,CAA2BH,sBAAsB,GAAGR,SAAS,QAAK;IACrJ,MAAMY,WAAW,GAAGlB,QAAQ,CAAC9D,OAAO,CAACT,KAAK;IAC1CyF,WAAW,CAACC,eAAe,GAAGH,SAAS;IACvCE,WAAW,CAACF,SAAS,GAAGA,SAAS;IACjC,IAAII,UAAU,GAAG,EAAE;IACnB,IAAIT,IAAI,EAAE;MACRS,UAAU,GAAGxD,KAAK,CAACG,WAAW,CAACsD,MAAM,CAAC,KAAK,EAAErI,kBAAkB,CAAC;QAC9DsI,MAAM,EAAEZ,SAAS;QACjBjF,KAAK,EAAEiF,SAAS;QAChBa,OAAO,EAAErC;MACX,CAAC,EAAE;QACDyB;MACF,CAAC,CAAC,CAAC;IACL;IACA,IAAIC,gBAAgB,EAAE;MACpBM,WAAW,CAACM,gBAAgB,GAAGJ,UAAU;MACzCF,WAAW,CAACE,UAAU,GAAGA,UAAU;IACrC;IACA,IAAI,CAAChD,yBAAyB,IAAI,CAACG,YAAY,EAAE;MAC/C,MAAMkD,aAAa,GAAG1B,WAAW,CAAC7D,OAAO,CAACT,KAAK;MAC/CgG,aAAa,CAACC,OAAO,GAAG,CAAC,GAAGpB,SAAS,GAAG/F,eAAe,CAACC,eAAe,EAAEwF,QAAQ,CAAC9D,OAAO,CAAC;MAC1F,IAAI0E,gBAAgB,EAAE;QACpBa,aAAa,CAACD,gBAAgB,GAAGJ,UAAU;QAC3CK,aAAa,CAACL,UAAU,GAAGA,UAAU;MACvC;IACF;EACF,CAAC,EAAE,CAACvH,MAAM,EAAEuE,yBAAyB,EAAEG,YAAY,EAAEX,KAAK,EAAEsB,kBAAkB,CAAC,CAAC;EAChF,MAAMyC,kBAAkB,GAAG/I,gBAAgB,CAACgJ,WAAW,IAAI;IACzD,IAAI,CAAC1B,aAAa,CAAChE,OAAO,EAAE;MAC1B;IACF;IACA;IACAxC,oBAAoB,GAAG,IAAI;IAC3BwG,aAAa,CAAChE,OAAO,GAAG,KAAK;IAC7BhE,QAAQ,CAAC2J,SAAS,CAAC,MAAM;MACvBpC,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACE,aAAa,CAACzD,OAAO,CAAC2D,SAAS,EAAE;MACpCF,aAAa,CAACzD,OAAO,CAAC2D,SAAS,GAAG,IAAI;MACtC;IACF;IACAF,aAAa,CAACzD,OAAO,CAAC2D,SAAS,GAAG,IAAI;IACtC,MAAMgB,SAAS,GAAGtI,SAAS,CAACqF,KAAK,EAAE/D,MAAM,CAAC;IAC1C,MAAMiI,UAAU,GAAGtJ,YAAY,CAACqB,MAAM,CAAC;IACvC,IAAIqC,OAAO;IACX,IAAI4F,UAAU,EAAE;MACd5F,OAAO,GAAGtC,iBAAiB,CAACiH,SAAS,EAAEe,WAAW,CAACG,cAAc,EAAErJ,aAAa,CAACkJ,WAAW,CAACI,aAAa,CAAC,CAAC;IAC9G,CAAC,MAAM;MACL9F,OAAO,GAAG/B,iBAAiB,CAAC0G,SAAS,EAAEe,WAAW,CAACG,cAAc,EAAEpJ,WAAW,CAACiJ,WAAW,CAACI,aAAa,CAAC,CAAC;IAC5G;IACA,MAAMlH,aAAa,GAAGgH,UAAU,GAAGnC,aAAa,CAACzD,OAAO,CAAC+F,MAAM,GAAGtC,aAAa,CAACzD,OAAO,CAACgG,MAAM;IAC9F,MAAMlH,YAAY,GAAGT,eAAe,CAACuH,UAAU,EAAE9B,QAAQ,CAAC9D,OAAO,CAAC;IAClE,MAAMrB,gBAAgB,GAAGD,YAAY,CAACsB,OAAO,EAAEpB,aAAa,EAAEC,IAAI,EAAEC,YAAY,CAAC;IACjF,MAAMmH,cAAc,GAAGtH,gBAAgB,GAAGG,YAAY;IACtD,IAAIC,IAAI,CAACmH,GAAG,CAACzC,aAAa,CAACzD,OAAO,CAACmG,QAAQ,CAAC,GAAG3D,gBAAgB,EAAE;MAC/D;MACAyB,qBAAqB,CAACjE,OAAO,GAAGjB,IAAI,CAACmH,GAAG,CAAC,CAACpH,YAAY,GAAGH,gBAAgB,IAAI8E,aAAa,CAACzD,OAAO,CAACmG,QAAQ,CAAC,GAAG,IAAI;IACrH;IACA,IAAItH,IAAI,EAAE;MACR,IAAI4E,aAAa,CAACzD,OAAO,CAACmG,QAAQ,GAAG3D,gBAAgB,IAAIyD,cAAc,GAAG3D,UAAU,EAAE;QACpFK,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACL;QACAuB,WAAW,CAAC,CAAC,EAAE;UACbO,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACA;IACF;IACA,IAAIhB,aAAa,CAACzD,OAAO,CAACmG,QAAQ,GAAG,CAAC3D,gBAAgB,IAAI,CAAC,GAAGyD,cAAc,GAAG3D,UAAU,EAAE;MACzFM,MAAM,CAAC,CAAC;IACV,CAAC,MAAM;MACL;MACAsB,WAAW,CAAC7F,eAAe,CAACuH,UAAU,EAAE9B,QAAQ,CAAC9D,OAAO,CAAC,EAAE;QACzDyE,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,MAAM2B,iBAAiB,GAAG,SAAAA,CAAA,EAAmB;IAAA,IAAlBC,KAAK,GAAA/B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IACtC,IAAI,CAAChB,YAAY,EAAE;MACjB;MACA;MACA;MACA,IAAI+C,KAAK,IAAI,EAAElE,gBAAgB,IAAII,oBAAoB,CAAC,EAAE;QACxDvG,QAAQ,CAAC2J,SAAS,CAAC,MAAM;UACvBpC,eAAe,CAAC,IAAI,CAAC;QACvB,CAAC,CAAC;MACJ;MACA,MAAMjF,eAAe,GAAGhC,YAAY,CAACqB,MAAM,CAAC;MAC5C,IAAI,CAACkB,IAAI,IAAIiF,QAAQ,CAAC9D,OAAO,EAAE;QAC7B;QACAkE,WAAW,CAAC7F,eAAe,CAACC,eAAe,EAAEwF,QAAQ,CAAC9D,OAAO,CAAC,IAAImC,gBAAgB,GAAG,EAAE,GAAG,CAAC5E,mBAAmB,CAAC,EAAE;UAC/GmH,gBAAgB,EAAE;QACpB,CAAC,CAAC;MACJ;MACAjB,aAAa,CAACzD,OAAO,CAACmG,QAAQ,GAAG,CAAC;MAClC1C,aAAa,CAACzD,OAAO,CAACsG,QAAQ,GAAG,IAAI;MACrC7C,aAAa,CAACzD,OAAO,CAACuG,aAAa,GAAG,IAAI;MAC1C9C,aAAa,CAACzD,OAAO,CAACwG,QAAQ,GAAG,KAAK;MACtCxC,aAAa,CAAChE,OAAO,GAAG,IAAI;IAC9B;EACF,CAAC;EACD,MAAMyG,mBAAmB,GAAG/J,gBAAgB,CAACgJ,WAAW,IAAI;IAC1D;IACA,IAAI,CAAC5B,QAAQ,CAAC9D,OAAO,IAAI,CAACgE,aAAa,CAAChE,OAAO,EAAE;MAC/C;IACF;;IAEA;IACA,IAAIxC,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAKiG,aAAa,CAACzD,OAAO,EAAE;MACnF;IACF;IACAoG,iBAAiB,CAAC,IAAI,CAAC;IACvB,MAAMzB,SAAS,GAAGtI,SAAS,CAACqF,KAAK,EAAE/D,MAAM,CAAC;IAC1C,MAAMW,eAAe,GAAGhC,YAAY,CAACqB,MAAM,CAAC;IAC5C,MAAM+I,QAAQ,GAAGhJ,iBAAiB,CAACiH,SAAS,EAAEe,WAAW,CAAC9H,OAAO,EAAEpB,aAAa,CAACkJ,WAAW,CAACI,aAAa,CAAC,CAAC;IAC5G,MAAMa,QAAQ,GAAG1I,iBAAiB,CAAC0G,SAAS,EAAEe,WAAW,CAAC9H,OAAO,EAAEnB,WAAW,CAACiJ,WAAW,CAACI,aAAa,CAAC,CAAC;IAC1G,IAAIjH,IAAI,IAAIiF,QAAQ,CAAC9D,OAAO,CAAC4G,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,IAAIrJ,oBAAoB,KAAK,IAAI,EAAE;MAC1F,MAAM6B,aAAa,GAAGH,gBAAgB,CAACwG,WAAW,CAACmB,MAAM,EAAE/C,QAAQ,CAAC9D,OAAO,CAAC;MAC5E,MAAM8G,gBAAgB,GAAGjH,uBAAuB,CAAC;QAC/CR,aAAa;QACbU,KAAK,EAAEzB,eAAe,GAAGmF,aAAa,CAACzD,OAAO,CAAC+F,MAAM,GAAGtC,aAAa,CAACzD,OAAO,CAACgG,MAAM;QACpFhG,OAAO,EAAE1B,eAAe,GAAGoI,QAAQ,GAAGC,QAAQ;QAC9ChJ;MACF,CAAC,CAAC;MACF,IAAImJ,gBAAgB,EAAE;QACpBtJ,oBAAoB,GAAG,IAAI;QAC3B;MACF;MACAA,oBAAoB,GAAGiG,aAAa,CAACzD,OAAO;IAC9C;;IAEA;IACA,IAAIyD,aAAa,CAACzD,OAAO,CAAC2D,SAAS,IAAI,IAAI,EAAE;MAC3C,MAAMoD,EAAE,GAAGhI,IAAI,CAACmH,GAAG,CAACQ,QAAQ,GAAGjD,aAAa,CAACzD,OAAO,CAAC+F,MAAM,CAAC;MAC5D,MAAMiB,EAAE,GAAGjI,IAAI,CAACmH,GAAG,CAACS,QAAQ,GAAGlD,aAAa,CAACzD,OAAO,CAACgG,MAAM,CAAC;MAC5D,MAAMiB,iBAAiB,GAAG3I,eAAe,GAAGyI,EAAE,GAAGC,EAAE,IAAID,EAAE,GAAGzJ,qBAAqB,GAAG0J,EAAE,GAAGD,EAAE,IAAIC,EAAE,GAAG1J,qBAAqB;MACzH,IAAI2J,iBAAiB,IAAIvB,WAAW,CAACwB,UAAU,EAAE;QAC/CxB,WAAW,CAACyB,cAAc,CAAC,CAAC;MAC9B;MACA,IAAIF,iBAAiB,KAAK,IAAI,KAAK3I,eAAe,GAAG0I,EAAE,GAAG1J,qBAAqB,GAAGyJ,EAAE,GAAGzJ,qBAAqB,CAAC,EAAE;QAC7GmG,aAAa,CAACzD,OAAO,CAAC2D,SAAS,GAAGsD,iBAAiB;QACnD,IAAI,CAACA,iBAAiB,EAAE;UACtBxB,kBAAkB,CAACC,WAAW,CAAC;UAC/B;QACF;;QAEA;QACAjC,aAAa,CAACzD,OAAO,CAAC+F,MAAM,GAAGW,QAAQ;QACvCjD,aAAa,CAACzD,OAAO,CAACgG,MAAM,GAAGW,QAAQ;;QAEvC;QACA,IAAI,CAACxE,gBAAgB,IAAI,CAACtD,IAAI,EAAE;UAC9B,IAAIP,eAAe,EAAE;YACnBmF,aAAa,CAACzD,OAAO,CAAC+F,MAAM,IAAIxI,mBAAmB;UACrD,CAAC,MAAM;YACLkG,aAAa,CAACzD,OAAO,CAACgG,MAAM,IAAIzI,mBAAmB;UACrD;QACF;MACF;IACF;IACA,IAAI,CAACkG,aAAa,CAACzD,OAAO,CAAC2D,SAAS,EAAE;MACpC;IACF;IACA,MAAM7E,YAAY,GAAGT,eAAe,CAACC,eAAe,EAAEwF,QAAQ,CAAC9D,OAAO,CAAC;IACvE,IAAIpB,aAAa,GAAGN,eAAe,GAAGmF,aAAa,CAACzD,OAAO,CAAC+F,MAAM,GAAGtC,aAAa,CAACzD,OAAO,CAACgG,MAAM;IACjG,IAAInH,IAAI,IAAI,CAAC4E,aAAa,CAACzD,OAAO,CAACwG,QAAQ,EAAE;MAC3C5H,aAAa,GAAGG,IAAI,CAACC,GAAG,CAACJ,aAAa,EAAEE,YAAY,CAAC;IACvD;IACA,MAAMsF,SAAS,GAAG1F,YAAY,CAACJ,eAAe,GAAGoI,QAAQ,GAAGC,QAAQ,EAAE/H,aAAa,EAAEC,IAAI,EAAEC,YAAY,CAAC;IACxG,IAAID,IAAI,EAAE;MACR,IAAI,CAAC4E,aAAa,CAACzD,OAAO,CAACwG,QAAQ,EAAE;QACnC,MAAMA,QAAQ,GAAGlI,eAAe,GAAGoI,QAAQ,GAAG5H,YAAY,GAAG6H,QAAQ,GAAG7H,YAAY;QACpF,IAAI0H,QAAQ,EAAE;UACZ/C,aAAa,CAACzD,OAAO,CAACwG,QAAQ,GAAG,IAAI;UACrC/C,aAAa,CAACzD,OAAO,CAAC+F,MAAM,GAAGW,QAAQ;UACvCjD,aAAa,CAACzD,OAAO,CAACgG,MAAM,GAAGW,QAAQ;QACzC,CAAC,MAAM;UACL;QACF;MACF,CAAC,MAAM,IAAIvC,SAAS,KAAK,CAAC,EAAE;QAC1BX,aAAa,CAACzD,OAAO,CAAC+F,MAAM,GAAGW,QAAQ;QACvCjD,aAAa,CAACzD,OAAO,CAACgG,MAAM,GAAGW,QAAQ;MACzC;IACF;IACA,IAAIlD,aAAa,CAACzD,OAAO,CAACuG,aAAa,KAAK,IAAI,EAAE;MAChD9C,aAAa,CAACzD,OAAO,CAACuG,aAAa,GAAGnC,SAAS;MAC/CX,aAAa,CAACzD,OAAO,CAACsG,QAAQ,GAAGc,WAAW,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;IACxD;IACA,MAAMlB,QAAQ,GAAG,CAAC/B,SAAS,GAAGX,aAAa,CAACzD,OAAO,CAACuG,aAAa,KAAKa,WAAW,CAACC,GAAG,CAAC,CAAC,GAAG5D,aAAa,CAACzD,OAAO,CAACsG,QAAQ,CAAC,GAAG,GAAG;;IAE/H;IACA7C,aAAa,CAACzD,OAAO,CAACmG,QAAQ,GAAG1C,aAAa,CAACzD,OAAO,CAACmG,QAAQ,GAAG,GAAG,GAAGA,QAAQ,GAAG,GAAG;IACtF1C,aAAa,CAACzD,OAAO,CAACuG,aAAa,GAAGnC,SAAS;IAC/CX,aAAa,CAACzD,OAAO,CAACsG,QAAQ,GAAGc,WAAW,CAACC,GAAG,CAAC,CAAC;;IAElD;IACA,IAAI3B,WAAW,CAACwB,UAAU,EAAE;MAC1BxB,WAAW,CAACyB,cAAc,CAAC,CAAC;IAC9B;IACAjD,WAAW,CAACE,SAAS,CAAC;EACxB,CAAC,CAAC;EACF,MAAMkD,oBAAoB,GAAG5K,gBAAgB,CAACgJ,WAAW,IAAI;IAC3D;IACA;IACA,IAAIA,WAAW,CAAC6B,gBAAgB,EAAE;MAChC;IACF;;IAEA;IACA,IAAI7B,WAAW,CAAC8B,mBAAmB,EAAE;MACnC;IACF;;IAEA;IACA,IAAI3I,IAAI,KAAKwD,YAAY,IAAI,CAACwB,WAAW,CAAC7D,OAAO,CAAC4G,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,CAAC,IAAI,CAAC/C,QAAQ,CAAC9D,OAAO,CAAC4G,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,EAAE;MACjI;IACF;IACA,MAAMlC,SAAS,GAAGtI,SAAS,CAACqF,KAAK,EAAE/D,MAAM,CAAC;IAC1C,MAAMW,eAAe,GAAGhC,YAAY,CAACqB,MAAM,CAAC;IAC5C,MAAM+I,QAAQ,GAAGhJ,iBAAiB,CAACiH,SAAS,EAAEe,WAAW,CAAC9H,OAAO,EAAEpB,aAAa,CAACkJ,WAAW,CAACI,aAAa,CAAC,CAAC;IAC5G,MAAMa,QAAQ,GAAG1I,iBAAiB,CAAC0G,SAAS,EAAEe,WAAW,CAAC9H,OAAO,EAAEnB,WAAW,CAACiJ,WAAW,CAACI,aAAa,CAAC,CAAC;IAC1G,IAAI,CAACjH,IAAI,EAAE;MAAA,IAAA4I,iBAAA;MACT;MACA;MACA;MACA;MACA,IAAIrF,kBAAkB,IAAI,EAAEsD,WAAW,CAACmB,MAAM,KAAKjD,YAAY,CAAC5D,OAAO,IAAI,CAAAyH,iBAAA,GAAA3D,QAAQ,CAAC9D,OAAO,cAAAyH,iBAAA,eAAhBA,iBAAA,CAAkBb,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,KAAK,OAAOtE,oBAAoB,KAAK,UAAU,GAAGA,oBAAoB,CAACmD,WAAW,EAAE9B,YAAY,CAAC5D,OAAO,EAAE8D,QAAQ,CAAC9D,OAAO,CAAC,GAAGuC,oBAAoB,CAAC,CAAC,EAAE;QAC7Q;MACF;MACA,IAAIjE,eAAe,EAAE;QACnB,IAAIoI,QAAQ,GAAG3D,cAAc,EAAE;UAC7B;QACF;MACF,CAAC,MAAM,IAAI4D,QAAQ,GAAG5D,cAAc,EAAE;QACpC;MACF;IACF;IACA2C,WAAW,CAAC8B,mBAAmB,GAAG,IAAI;IACtChK,oBAAoB,GAAG,IAAI;IAC3BiG,aAAa,CAACzD,OAAO,CAAC+F,MAAM,GAAGW,QAAQ;IACvCjD,aAAa,CAACzD,OAAO,CAACgG,MAAM,GAAGW,QAAQ;IACvCP,iBAAiB,CAAC,CAAC;EACrB,CAAC,CAAC;EACFrK,KAAK,CAAC2L,SAAS,CAAC,MAAM;IACpB,IAAIzE,OAAO,KAAK,WAAW,EAAE;MAC3B,MAAMpF,GAAG,GAAGrB,aAAa,CAACsH,QAAQ,CAAC9D,OAAO,CAAC;MAC3CnC,GAAG,CAAC8J,gBAAgB,CAAC,YAAY,EAAEL,oBAAoB,CAAC;MACxD;MACA;MACA;MACAzJ,GAAG,CAAC8J,gBAAgB,CAAC,WAAW,EAAElB,mBAAmB,EAAE;QACrDmB,OAAO,EAAE,CAAC/I;MACZ,CAAC,CAAC;MACFhB,GAAG,CAAC8J,gBAAgB,CAAC,UAAU,EAAElC,kBAAkB,CAAC;MACpD,OAAO,MAAM;QACX5H,GAAG,CAACgK,mBAAmB,CAAC,YAAY,EAAEP,oBAAoB,CAAC;QAC3DzJ,GAAG,CAACgK,mBAAmB,CAAC,WAAW,EAAEpB,mBAAmB,EAAE;UACxDmB,OAAO,EAAE,CAAC/I;QACZ,CAAC,CAAC;QACFhB,GAAG,CAACgK,mBAAmB,CAAC,UAAU,EAAEpC,kBAAkB,CAAC;MACzD,CAAC;IACH;IACA,OAAOjB,SAAS;EAClB,CAAC,EAAE,CAACvB,OAAO,EAAEpE,IAAI,EAAEyI,oBAAoB,EAAEb,mBAAmB,EAAEhB,kBAAkB,CAAC,CAAC;EAClF1J,KAAK,CAAC2L,SAAS,CAAC,MAAM,MAAM;IAC1B;IACA,IAAIlK,oBAAoB,KAAKiG,aAAa,CAACzD,OAAO,EAAE;MAClDxC,oBAAoB,GAAG,IAAI;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EACNzB,KAAK,CAAC2L,SAAS,CAAC,MAAM;IACpB,IAAI,CAAC7I,IAAI,EAAE;MACT0E,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAAC1E,IAAI,CAAC,CAAC;EACV,MAAM,CAACiJ,aAAa,EAAEC,kBAAkB,CAAC,GAAG/K,OAAO,CAAC,WAAW,EAAE;IAC/DqE,GAAG,EAAEuC,YAAY;IACjBoE,WAAW,EAAE/K,SAAS;IACtBgL,UAAU,EAAEzG,KAAK;IACjB0G,sBAAsB,EAAE;MACtBhF,KAAK;MACLC,SAAS,EAAAxH,aAAA;QACPwM,SAAS,EAAErF;MAAc,GACtBK,SAAS;IAEhB,CAAC;IACDiF,eAAe,EAAE;MACfC,KAAK,EAAEtF,cAAc;MACrBpF;IACF;EACF,CAAC,CAAC;EACF,OAAO,aAAaN,KAAK,CAACtB,KAAK,CAACuM,QAAQ,EAAE;IACxCC,QAAQ,EAAE,CAAC,aAAapL,IAAI,CAACf,MAAM,EAAAT,aAAA;MACjCkD,IAAI,EAAEoE,OAAO,KAAK,WAAW,IAAIK,YAAY,GAAG,IAAI,GAAGzE,IAAI;MAC3DoE,OAAO,EAAEA,OAAO;MAChBR,UAAU,EAAA9G,aAAA,CAAAA,aAAA;QACR+G,aAAa,EAAA/G,aAAA,CAAAA,aAAA,KACR+G,aAAa;UAChBrB,GAAG,EAAEwC;QAAW;MACjB,GAGGZ,OAAO,KAAK,WAAW,IAAI;QAC7BuF,WAAW,EAAE;MACf,CAAC,GACEpF,cAAc,CAClB;MACDf,YAAY,EAAEA,YAAY;MAC1B1E,MAAM,EAAEA,MAAM;MACdqF,kBAAkB,EAAEiB,qBAAqB,CAACjE,OAAO,IAAIgD,kBAAkB;MACvEL,OAAO,EAAEA,OAAO;MAChBtB,GAAG,EAAEA,GAAG;MACR6B,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAAxH,aAAA,CAAAA,aAAA,KACJwH,SAAS;QACZsF,QAAQ,EAAE1L,cAAc,EAAAuE,mBAAA,GAAC6B,SAAS,CAACsF,QAAQ,cAAAnH,mBAAA,cAAAA,mBAAA,GAAIoB,aAAa,EAAE;UAC5DrB,GAAG,EAAEwC;QACP,CAAC,CAAC;QACF6E,KAAK,EAAE3L,cAAc,EAAAwE,gBAAA,GAAC4B,SAAS,CAACuF,KAAK,cAAAnH,gBAAA,cAAAA,gBAAA,GAAIsB,UAAU,EAAE;UACnDtD,KAAK,EAAE;YACLoJ,aAAa,EAAE1F,OAAO,KAAK,WAAW,IAAI,CAACpE,IAAI,IAAI,CAAC0D,oBAAoB,GAAG,MAAM,GAAG;UACtF,CAAC;UACDlB,GAAG,EAAE0C;QACP,CAAC;MAAC;IACH,GACEV,KAAK,CACT,CAAC,EAAE,CAACjB,kBAAkB,IAAIa,OAAO,KAAK,WAAW,IAAI,aAAa9F,IAAI,CAAChB,KAAK,EAAE;MAC7EoM,QAAQ,EAAE,aAAapL,IAAI,CAAC2K,aAAa,EAAAnM,aAAA,KACpCoM,kBAAkB,CACtB;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5H,eAAe,CAAC6H,SAAS,CAAC,yBAAyB;EACzF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACExG,oBAAoB,EAAEtG,SAAS,CAAC+M,SAAS,CAAC,CAAC/M,SAAS,CAACgN,IAAI,EAAEhN,SAAS,CAACiN,IAAI,CAAC,CAAC;EAC3E;AACF;AACA;EACEvL,MAAM,EAAE1B,SAAS,CAACkN,KAAK,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC3D;AACF;AACA;EACEZ,QAAQ,EAAEtM,SAAS,CAACmN,IAAI;EACxB;AACF;AACA;AACA;AACA;EACElH,yBAAyB,EAAEjG,SAAS,CAACiN,IAAI;EACzC;AACF;AACA;AACA;AACA;EACE/G,gBAAgB,EAAElG,SAAS,CAACiN,IAAI;EAChC;AACF;AACA;AACA;AACA;EACE9G,kBAAkB,EAAEnG,SAAS,CAACiN,IAAI;EAClC;AACF;AACA;EACE7G,YAAY,EAAEpG,SAAS,CAACiN,IAAI;EAC5B;AACF;AACA;AACA;AACA;EACE5G,UAAU,EAAErG,SAAS,CAACoN,MAAM;EAC5B;AACF;AACA;AACA;AACA;AACA;EACE7G,gBAAgB,EAAEvG,SAAS,CAACoN,MAAM;EAClC;AACF;AACA;EACE5G,UAAU,EAAExG,SAAS,CAAC,sCAAsCuE,KAAK,CAAC;IAChEkC,aAAa,EAAEzG,SAAS,CAACuE,KAAK,CAAC;MAC7B8I,SAAS,EAAEpN;IACb,CAAC;EACH,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEyG,OAAO,EAAE1G,SAAS,CAACgN,IAAI,CAACM,UAAU;EAClC;AACF;AACA;AACA;AACA;EACE3G,MAAM,EAAE3G,SAAS,CAACgN,IAAI,CAACM,UAAU;EACjC;AACF;AACA;AACA;EACE1K,IAAI,EAAE5C,SAAS,CAACiN,IAAI;EACpB;AACF;AACA;EACErG,UAAU,EAAE5G,SAAS,CAAC,sCAAsCuE,KAAK,CAAC;IAChE8I,SAAS,EAAEpN,uBAAuB;IAClCqD,KAAK,EAAEtD,SAAS,CAACuN;EACnB,CAAC,CAAC;EACF;AACF;AACA;AACA;EACErG,SAAS,EAAElH,SAAS,CAACuE,KAAK,CAAC;IACzBiI,QAAQ,EAAExM,SAAS,CAAC+M,SAAS,CAAC,CAAC/M,SAAS,CAACgN,IAAI,EAAEhN,SAAS,CAACuN,MAAM,CAAC,CAAC;IACjEC,MAAM,EAAExN,SAAS,CAAC+M,SAAS,CAAC,CAAC/M,SAAS,CAACgN,IAAI,EAAEhN,SAAS,CAACuN,MAAM,CAAC,CAAC;IAC/Dd,KAAK,EAAEzM,SAAS,CAAC+M,SAAS,CAAC,CAAC/M,SAAS,CAACgN,IAAI,EAAEhN,SAAS,CAACuN,MAAM,CAAC,CAAC;IAC9DE,IAAI,EAAEzN,SAAS,CAAC+M,SAAS,CAAC,CAAC/M,SAAS,CAACgN,IAAI,EAAEhN,SAAS,CAACuN,MAAM,CAAC,CAAC;IAC7DrB,SAAS,EAAElM,SAAS,CAAC+M,SAAS,CAAC,CAAC/M,SAAS,CAACgN,IAAI,EAAEhN,SAAS,CAACuN,MAAM,CAAC,CAAC;IAClEtE,UAAU,EAAEjJ,SAAS,CAAC+M,SAAS,CAAC,CAAC/M,SAAS,CAACgN,IAAI,EAAEhN,SAAS,CAACuN,MAAM,CAAC;EACpE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEtG,KAAK,EAAEjH,SAAS,CAACuE,KAAK,CAAC;IACrBiI,QAAQ,EAAExM,SAAS,CAAC+L,WAAW;IAC/ByB,MAAM,EAAExN,SAAS,CAAC+L,WAAW;IAC7BU,KAAK,EAAEzM,SAAS,CAAC+L,WAAW;IAC5B0B,IAAI,EAAEzN,SAAS,CAAC+L,WAAW;IAC3BG,SAAS,EAAElM,SAAS,CAAC+L,WAAW;IAChC9C,UAAU,EAAEjJ,SAAS,CAAC+L;EACxB,CAAC,CAAC;EACF;AACF;AACA;AACA;EACElF,cAAc,EAAE7G,SAAS,CAACuN,MAAM;EAChC;AACF;AACA;AACA;AACA;EACEzG,cAAc,EAAE9G,SAAS,CAACoN,MAAM;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACErG,kBAAkB,EAAE/G,SAAS,CAAC+M,SAAS,CAAC,CAAC/M,SAAS,CAACoN,MAAM,EAAEpN,SAAS,CAACuE,KAAK,CAAC;IACzEmJ,MAAM,EAAE1N,SAAS,CAACoN,MAAM;IACxBzH,KAAK,EAAE3F,SAAS,CAACoN,MAAM;IACvBrH,IAAI,EAAE/F,SAAS,CAACoN;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;EACEpG,OAAO,EAAEhH,SAAS,CAACkN,KAAK,CAAC,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;AACnE,CAAC,GAAG,KAAK,CAAC;AACV,eAAejI,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}