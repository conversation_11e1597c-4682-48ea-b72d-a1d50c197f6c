{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getMenuUtilityClass(slot) {\n  return generateUtilityClass('MuiMenu', slot);\n}\nconst menuClasses = generateUtilityClasses('MuiMenu', ['root', 'paper', 'list']);\nexport default menuClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getMenuUtilityClass", "slot", "menuClasses"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/Menu/menuClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getMenuUtilityClass(slot) {\n  return generateUtilityClass('MuiMenu', slot);\n}\nconst menuClasses = generateUtilityClasses('MuiMenu', ['root', 'paper', 'list']);\nexport default menuClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAOF,oBAAoB,CAAC,SAAS,EAAEE,IAAI,CAAC;AAC9C;AACA,MAAMC,WAAW,GAAGJ,sBAAsB,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AAChF,eAAeI,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}