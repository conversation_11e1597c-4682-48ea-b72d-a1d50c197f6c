{"ast": null, "code": "'use client';\n\nimport _objectSpread from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"elementType\", \"externalSlotProps\", \"ownerState\", \"skipResolvingSlotProps\"];\nimport useForkRef from \"../useForkRef/index.js\";\nimport appendOwnerState from \"../appendOwnerState/index.js\";\nimport mergeSlotProps from \"../mergeSlotProps/index.js\";\nimport resolveComponentProps from \"../resolveComponentProps/index.js\";\n/**\n * @ignore - do not document.\n * Builds the props to be passed into the slot of an unstyled component.\n * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.\n * If the slot component is not a host component, it also merges in the `ownerState`.\n *\n * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.\n */\nfunction useSlotProps(parameters) {\n  var _parameters$additiona;\n  const {\n      elementType,\n      externalSlotProps,\n      ownerState,\n      skipResolvingSlotProps = false\n    } = parameters,\n    other = _objectWithoutProperties(parameters, _excluded);\n  const resolvedComponentsProps = skipResolvingSlotProps ? {} : resolveComponentProps(externalSlotProps, ownerState);\n  const {\n    props: mergedProps,\n    internalRef\n  } = mergeSlotProps(_objectSpread(_objectSpread({}, other), {}, {\n    externalSlotProps: resolvedComponentsProps\n  }));\n  const ref = useForkRef(internalRef, resolvedComponentsProps === null || resolvedComponentsProps === void 0 ? void 0 : resolvedComponentsProps.ref, (_parameters$additiona = parameters.additionalProps) === null || _parameters$additiona === void 0 ? void 0 : _parameters$additiona.ref);\n  const props = appendOwnerState(elementType, _objectSpread(_objectSpread({}, mergedProps), {}, {\n    ref\n  }), ownerState);\n  return props;\n}\nexport default useSlotProps;", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "useForkRef", "appendOwnerState", "mergeSlotProps", "resolveComponentProps", "useSlotProps", "parameters", "_parameters$additiona", "elementType", "externalSlotProps", "ownerState", "skipResolvingSlotProps", "other", "resolvedComponentsProps", "props", "mergedProps", "internalRef", "ref", "additionalProps"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js"], "sourcesContent": ["'use client';\n\nimport useForkRef from \"../useForkRef/index.js\";\nimport appendOwnerState from \"../appendOwnerState/index.js\";\nimport mergeSlotProps from \"../mergeSlotProps/index.js\";\nimport resolveComponentProps from \"../resolveComponentProps/index.js\";\n/**\n * @ignore - do not document.\n * Builds the props to be passed into the slot of an unstyled component.\n * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.\n * If the slot component is not a host component, it also merges in the `ownerState`.\n *\n * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.\n */\nfunction useSlotProps(parameters) {\n  const {\n    elementType,\n    externalSlotProps,\n    ownerState,\n    skipResolvingSlotProps = false,\n    ...other\n  } = parameters;\n  const resolvedComponentsProps = skipResolvingSlotProps ? {} : resolveComponentProps(externalSlotProps, ownerState);\n  const {\n    props: mergedProps,\n    internalRef\n  } = mergeSlotProps({\n    ...other,\n    externalSlotProps: resolvedComponentsProps\n  });\n  const ref = useForkRef(internalRef, resolvedComponentsProps?.ref, parameters.additionalProps?.ref);\n  const props = appendOwnerState(elementType, {\n    ...mergedProps,\n    ref\n  }, ownerState);\n  return props;\n}\nexport default useSlotProps;"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;AAEb,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,qBAAqB,MAAM,mCAAmC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,UAAU,EAAE;EAAA,IAAAC,qBAAA;EAChC,MAAM;MACJC,WAAW;MACXC,iBAAiB;MACjBC,UAAU;MACVC,sBAAsB,GAAG;IAE3B,CAAC,GAAGL,UAAU;IADTM,KAAK,GAAAb,wBAAA,CACNO,UAAU,EAAAN,SAAA;EACd,MAAMa,uBAAuB,GAAGF,sBAAsB,GAAG,CAAC,CAAC,GAAGP,qBAAqB,CAACK,iBAAiB,EAAEC,UAAU,CAAC;EAClH,MAAM;IACJI,KAAK,EAAEC,WAAW;IAClBC;EACF,CAAC,GAAGb,cAAc,CAAAL,aAAA,CAAAA,aAAA,KACbc,KAAK;IACRH,iBAAiB,EAAEI;EAAuB,EAC3C,CAAC;EACF,MAAMI,GAAG,GAAGhB,UAAU,CAACe,WAAW,EAAEH,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEI,GAAG,GAAAV,qBAAA,GAAED,UAAU,CAACY,eAAe,cAAAX,qBAAA,uBAA1BA,qBAAA,CAA4BU,GAAG,CAAC;EAClG,MAAMH,KAAK,GAAGZ,gBAAgB,CAACM,WAAW,EAAAV,aAAA,CAAAA,aAAA,KACrCiB,WAAW;IACdE;EAAG,IACFP,UAAU,CAAC;EACd,OAAOI,KAAK;AACd;AACA,eAAeT,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}