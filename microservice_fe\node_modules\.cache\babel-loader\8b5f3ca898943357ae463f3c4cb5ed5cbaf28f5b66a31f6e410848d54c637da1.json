{"ast": null, "code": "// Helper function to normalize TimeBasedRevenue data from API\nexport const normalizeTimeBasedRevenue=data=>{try{if(!data){console.error('Invalid time-based revenue data: data is null or undefined');// Return a default object instead of throwing an error\nreturn{date:new Date().toISOString().split('T')[0],label:'Không xác định',totalRevenue:0,invoiceCount:0,periodType:'daily'};}// Ensure totalRevenue is a number\nlet totalRevenue=0;if(typeof data.totalRevenue==='number'){totalRevenue=data.totalRevenue;}else if(typeof data.totalRevenue==='string'){totalRevenue=parseFloat(data.totalRevenue)||0;}// Ensure invoiceCount is a number\nlet invoiceCount=0;if(typeof data.invoiceCount==='number'){invoiceCount=data.invoiceCount;}else if(typeof data.invoiceCount==='string'){invoiceCount=parseInt(data.invoiceCount,10)||0;}// Create a normalized time-based revenue object\nreturn{date:data.date||new Date().toISOString().split('T')[0],label:data.label||'Không xác định',totalRevenue:totalRevenue,invoiceCount:invoiceCount,periodType:data.periodType||'daily'};}catch(error){console.error('Error normalizing time-based revenue data:',error);return{date:new Date().toISOString().split('T')[0],label:'Lỗi dữ liệu',totalRevenue:0,invoiceCount:0,periodType:'daily'};}};", "map": {"version": 3, "names": ["normalizeTimeBasedRevenue", "data", "console", "error", "date", "Date", "toISOString", "split", "label", "totalRevenue", "invoiceCount", "periodType", "parseFloat", "parseInt"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/models/TimeBasedRevenue.ts"], "sourcesContent": ["export interface TimeBasedRevenue {\n  date: string;\n  label: string;\n  totalRevenue: number;\n  invoiceCount: number;\n  periodType: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';\n}\n\n// Helper function to normalize TimeBasedRevenue data from API\nexport const normalizeTimeBasedRevenue = (data: any): TimeBasedRevenue => {\n  try {\n    if (!data) {\n      console.error('Invalid time-based revenue data: data is null or undefined');\n      // Return a default object instead of throwing an error\n      return {\n        date: new Date().toISOString().split('T')[0],\n        label: 'Không xác định',\n        totalRevenue: 0,\n        invoiceCount: 0,\n        periodType: 'daily'\n      } as TimeBasedRevenue;\n    }\n\n    // Ensure totalRevenue is a number\n    let totalRevenue = 0;\n    if (typeof data.totalRevenue === 'number') {\n      totalRevenue = data.totalRevenue;\n    } else if (typeof data.totalRevenue === 'string') {\n      totalRevenue = parseFloat(data.totalRevenue) || 0;\n    }\n\n    // Ensure invoiceCount is a number\n    let invoiceCount = 0;\n    if (typeof data.invoiceCount === 'number') {\n      invoiceCount = data.invoiceCount;\n    } else if (typeof data.invoiceCount === 'string') {\n      invoiceCount = parseInt(data.invoiceCount, 10) || 0;\n    }\n\n    // Create a normalized time-based revenue object\n    return {\n      date: data.date || new Date().toISOString().split('T')[0],\n      label: data.label || 'Không xác định',\n      totalRevenue: totalRevenue,\n      invoiceCount: invoiceCount,\n      periodType: data.periodType || 'daily'\n    };\n  } catch (error) {\n    console.error('Error normalizing time-based revenue data:', error);\n    return {\n      date: new Date().toISOString().split('T')[0],\n      label: 'Lỗi dữ liệu',\n      totalRevenue: 0,\n      invoiceCount: 0,\n      periodType: 'daily'\n    };\n  }\n};\n"], "mappings": "AAQA;AACA,MAAO,MAAM,CAAAA,yBAAyB,CAAIC,IAAS,EAAuB,CACxE,GAAI,CACF,GAAI,CAACA,IAAI,CAAE,CACTC,OAAO,CAACC,KAAK,CAAC,4DAA4D,CAAC,CAC3E;AACA,MAAO,CACLC,IAAI,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC5CC,KAAK,CAAE,gBAAgB,CACvBC,YAAY,CAAE,CAAC,CACfC,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,OACd,CAAC,CACH,CAEA;AACA,GAAI,CAAAF,YAAY,CAAG,CAAC,CACpB,GAAI,MAAO,CAAAR,IAAI,CAACQ,YAAY,GAAK,QAAQ,CAAE,CACzCA,YAAY,CAAGR,IAAI,CAACQ,YAAY,CAClC,CAAC,IAAM,IAAI,MAAO,CAAAR,IAAI,CAACQ,YAAY,GAAK,QAAQ,CAAE,CAChDA,YAAY,CAAGG,UAAU,CAACX,IAAI,CAACQ,YAAY,CAAC,EAAI,CAAC,CACnD,CAEA;AACA,GAAI,CAAAC,YAAY,CAAG,CAAC,CACpB,GAAI,MAAO,CAAAT,IAAI,CAACS,YAAY,GAAK,QAAQ,CAAE,CACzCA,YAAY,CAAGT,IAAI,CAACS,YAAY,CAClC,CAAC,IAAM,IAAI,MAAO,CAAAT,IAAI,CAACS,YAAY,GAAK,QAAQ,CAAE,CAChDA,YAAY,CAAGG,QAAQ,CAACZ,IAAI,CAACS,YAAY,CAAE,EAAE,CAAC,EAAI,CAAC,CACrD,CAEA;AACA,MAAO,CACLN,IAAI,CAAEH,IAAI,CAACG,IAAI,EAAI,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACzDC,KAAK,CAAEP,IAAI,CAACO,KAAK,EAAI,gBAAgB,CACrCC,YAAY,CAAEA,YAAY,CAC1BC,YAAY,CAAEA,YAAY,CAC1BC,UAAU,CAAEV,IAAI,CAACU,UAAU,EAAI,OACjC,CAAC,CACH,CAAE,MAAOR,KAAK,CAAE,CACdD,OAAO,CAACC,KAAK,CAAC,4CAA4C,CAAEA,KAAK,CAAC,CAClE,MAAO,CACLC,IAAI,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC5CC,KAAK,CAAE,aAAa,CACpBC,YAAY,CAAE,CAAC,CACfC,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,OACd,CAAC,CACH,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}