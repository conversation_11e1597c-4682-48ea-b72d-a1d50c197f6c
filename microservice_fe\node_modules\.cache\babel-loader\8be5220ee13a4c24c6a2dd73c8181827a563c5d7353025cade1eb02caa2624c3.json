{"ast": null, "code": "import React from'react';import{Box,Paper,Typography,useTheme}from'@mui/material';import{Chart as ChartJS,CategoryScale,LinearScale,BarElement,Title,Tooltip,Legend}from'chart.js';import{Bar}from'react-chartjs-2';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";// Register ChartJS components\nChartJS.register(CategoryScale,LinearScale,BarElement,Title,Tooltip,Legend);const BarChartDisplay=_ref=>{let{data,periodType,title='Biểu đồ doanh thu'}=_ref;const theme=useTheme();// Get period type label\nconst getPeriodTypeLabel=()=>{switch(periodType){case'daily':return'Doanh thu theo ngày';case'monthly':return'Doanh thu theo tháng';case'yearly':return'Doanh thu theo năm';default:return'Doanh thu theo thời gian';}};// Format currency for tooltip\nconst formatCurrency=value=>{return new Intl.NumberFormat('vi-VN',{style:'currency',currency:'VND',maximumFractionDigits:0}).format(value);};// Prepare chart data\nconst chartData={labels:data.map(item=>item.label),datasets:[{label:'Doanh thu',data:data.map(item=>item.totalRevenue),backgroundColor:theme.palette.primary.main,borderColor:theme.palette.primary.dark,borderWidth:1}]};// Chart options\nconst chartOptions={responsive:true,maintainAspectRatio:false,plugins:{legend:{position:'top'},title:{display:true,text:getPeriodTypeLabel(),font:{size:16,weight:'bold'}},tooltip:{callbacks:{label:function(context){let label=context.dataset.label||'';if(label){label+=': ';}if(context.parsed.y!==null){label+=formatCurrency(context.parsed.y);}return label;}}}},scales:{y:{beginAtZero:true,ticks:{callback:function(value){return formatCurrency(value);}}}}};return/*#__PURE__*/_jsxs(Paper,{elevation:2,sx:{p:2,mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:title}),/*#__PURE__*/_jsx(Box,{sx:{height:400,position:'relative'},children:data.length>0?/*#__PURE__*/_jsx(Bar,{data:chartData,options:chartOptions}):/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',alignItems:'center',height:'100%'},children:/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",children:\"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u \\u0111\\u1EC3 hi\\u1EC3n th\\u1ECB\"})})})]});};export default BarChartDisplay;", "map": {"version": 3, "names": ["React", "Box", "Paper", "Typography", "useTheme", "Chart", "ChartJS", "CategoryScale", "LinearScale", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "Bar", "jsx", "_jsx", "jsxs", "_jsxs", "register", "BarChartDisplay", "_ref", "data", "periodType", "title", "theme", "getPeriodTypeLabel", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "maximumFractionDigits", "format", "chartData", "labels", "map", "item", "label", "datasets", "totalRevenue", "backgroundColor", "palette", "primary", "main", "borderColor", "dark", "borderWidth", "chartOptions", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "display", "text", "font", "size", "weight", "tooltip", "callbacks", "context", "dataset", "parsed", "y", "scales", "beginAtZero", "ticks", "callback", "elevation", "sx", "p", "mb", "children", "variant", "gutterBottom", "height", "length", "options", "justifyContent", "alignItems", "color"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/statistics/BarChartDisplay.tsx"], "sourcesContent": ["import React from 'react';\nimport { Box, Paper, Typography, useTheme } from '@mui/material';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend\n} from 'chart.js';\nimport { Bar } from 'react-chartjs-2';\nimport { TimeBasedRevenue } from '../../models';\n\n// Register ChartJS components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\ninterface BarChartDisplayProps {\n  data: TimeBasedRevenue[];\n  periodType: string;\n  title?: string;\n}\n\nconst BarChartDisplay: React.FC<BarChartDisplayProps> = ({\n  data,\n  periodType,\n  title = 'Biểu đồ doanh thu'\n}) => {\n  const theme = useTheme();\n\n  // Get period type label\n  const getPeriodTypeLabel = (): string => {\n    switch (periodType) {\n      case 'daily':\n        return 'Doanh thu theo ngày';\n      case 'monthly':\n        return 'Doanh thu theo tháng';\n      case 'yearly':\n        return '<PERSON>anh thu theo năm';\n      default:\n        return '<PERSON>anh thu theo thời gian';\n    }\n  };\n\n  // Format currency for tooltip\n  const formatCurrency = (value: number): string => {\n    return new Intl.NumberFormat('vi-VN', {\n      style: 'currency',\n      currency: 'VND',\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n\n  // Prepare chart data\n  const chartData = {\n    labels: data.map(item => item.label),\n    datasets: [\n      {\n        label: 'Doanh thu',\n        data: data.map(item => item.totalRevenue),\n        backgroundColor: theme.palette.primary.main,\n        borderColor: theme.palette.primary.dark,\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  // Chart options\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top' as const,\n      },\n      title: {\n        display: true,\n        text: getPeriodTypeLabel(),\n        font: {\n          size: 16,\n          weight: 'bold' as const,\n        },\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context: any) {\n            let label = context.dataset.label || '';\n            if (label) {\n              label += ': ';\n            }\n            if (context.parsed.y !== null) {\n              label += formatCurrency(context.parsed.y);\n            }\n            return label;\n          }\n        }\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function(value: any) {\n            return formatCurrency(value);\n          }\n        }\n      }\n    },\n  };\n\n  return (\n    <Paper elevation={2} sx={{ p: 2, mb: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        {title}\n      </Typography>\n      <Box sx={{ height: 400, position: 'relative' }}>\n        {data.length > 0 ? (\n          <Bar data={chartData} options={chartOptions} />\n        ) : (\n          <Box\n            sx={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              height: '100%'\n            }}\n          >\n            <Typography variant=\"subtitle1\" color=\"text.secondary\">\n              Không có dữ liệu để hiển thị\n            </Typography>\n          </Box>\n        )}\n      </Box>\n    </Paper>\n  );\n};\n\nexport default BarChartDisplay;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,GAAG,CAAEC,KAAK,CAAEC,UAAU,CAAEC,QAAQ,KAAQ,eAAe,CAChE,OACEC,KAAK,GAAI,CAAAC,OAAO,CAChBC,aAAa,CACbC,WAAW,CACXC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,MAAM,KACD,UAAU,CACjB,OAASC,GAAG,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGtC;AACAX,OAAO,CAACY,QAAQ,CACdX,aAAa,CACbC,WAAW,CACXC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,MACF,CAAC,CAQD,KAAM,CAAAO,eAA+C,CAAGC,IAAA,EAIlD,IAJmD,CACvDC,IAAI,CACJC,UAAU,CACVC,KAAK,CAAG,mBACV,CAAC,CAAAH,IAAA,CACC,KAAM,CAAAI,KAAK,CAAGpB,QAAQ,CAAC,CAAC,CAExB;AACA,KAAM,CAAAqB,kBAAkB,CAAGA,CAAA,GAAc,CACvC,OAAQH,UAAU,EAChB,IAAK,OAAO,CACV,MAAO,qBAAqB,CAC9B,IAAK,SAAS,CACZ,MAAO,sBAAsB,CAC/B,IAAK,QAAQ,CACX,MAAO,oBAAoB,CAC7B,QACE,MAAO,0BAA0B,CACrC,CACF,CAAC,CAED;AACA,KAAM,CAAAI,cAAc,CAAIC,KAAa,EAAa,CAChD,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,KAAK,CACfC,qBAAqB,CAAE,CACzB,CAAC,CAAC,CAACC,MAAM,CAACN,KAAK,CAAC,CAClB,CAAC,CAED;AACA,KAAM,CAAAO,SAAS,CAAG,CAChBC,MAAM,CAAEd,IAAI,CAACe,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAE,CACR,CACED,KAAK,CAAE,WAAW,CAClBjB,IAAI,CAAEA,IAAI,CAACe,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACG,YAAY,CAAC,CACzCC,eAAe,CAAEjB,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,CAC3CC,WAAW,CAAErB,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACG,IAAI,CACvCC,WAAW,CAAE,CACf,CAAC,CAEL,CAAC,CAED;AACA,KAAM,CAAAC,YAAY,CAAG,CACnBC,UAAU,CAAE,IAAI,CAChBC,mBAAmB,CAAE,KAAK,CAC1BC,OAAO,CAAE,CACPC,MAAM,CAAE,CACNC,QAAQ,CAAE,KACZ,CAAC,CACD9B,KAAK,CAAE,CACL+B,OAAO,CAAE,IAAI,CACbC,IAAI,CAAE9B,kBAAkB,CAAC,CAAC,CAC1B+B,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CACRC,MAAM,CAAE,MACV,CACF,CAAC,CACDC,OAAO,CAAE,CACPC,SAAS,CAAE,CACTtB,KAAK,CAAE,QAAAA,CAASuB,OAAY,CAAE,CAC5B,GAAI,CAAAvB,KAAK,CAAGuB,OAAO,CAACC,OAAO,CAACxB,KAAK,EAAI,EAAE,CACvC,GAAIA,KAAK,CAAE,CACTA,KAAK,EAAI,IAAI,CACf,CACA,GAAIuB,OAAO,CAACE,MAAM,CAACC,CAAC,GAAK,IAAI,CAAE,CAC7B1B,KAAK,EAAIZ,cAAc,CAACmC,OAAO,CAACE,MAAM,CAACC,CAAC,CAAC,CAC3C,CACA,MAAO,CAAA1B,KAAK,CACd,CACF,CACF,CACF,CAAC,CACD2B,MAAM,CAAE,CACND,CAAC,CAAE,CACDE,WAAW,CAAE,IAAI,CACjBC,KAAK,CAAE,CACLC,QAAQ,CAAE,QAAAA,CAASzC,KAAU,CAAE,CAC7B,MAAO,CAAAD,cAAc,CAACC,KAAK,CAAC,CAC9B,CACF,CACF,CACF,CACF,CAAC,CAED,mBACEV,KAAA,CAACf,KAAK,EAACmE,SAAS,CAAE,CAAE,CAACC,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eACvC1D,IAAA,CAACZ,UAAU,EAACuE,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAClClD,KAAK,CACI,CAAC,cACbR,IAAA,CAACd,GAAG,EAACqE,EAAE,CAAE,CAAEM,MAAM,CAAE,GAAG,CAAEvB,QAAQ,CAAE,UAAW,CAAE,CAAAoB,QAAA,CAC5CpD,IAAI,CAACwD,MAAM,CAAG,CAAC,cACd9D,IAAA,CAACF,GAAG,EAACQ,IAAI,CAAEa,SAAU,CAAC4C,OAAO,CAAE9B,YAAa,CAAE,CAAC,cAE/CjC,IAAA,CAACd,GAAG,EACFqE,EAAE,CAAE,CACFhB,OAAO,CAAE,MAAM,CACfyB,cAAc,CAAE,QAAQ,CACxBC,UAAU,CAAE,QAAQ,CACpBJ,MAAM,CAAE,MACV,CAAE,CAAAH,QAAA,cAEF1D,IAAA,CAACZ,UAAU,EAACuE,OAAO,CAAC,WAAW,CAACO,KAAK,CAAC,gBAAgB,CAAAR,QAAA,CAAC,kEAEvD,CAAY,CAAC,CACV,CACN,CACE,CAAC,EACD,CAAC,CAEZ,CAAC,CAED,cAAe,CAAAtD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}