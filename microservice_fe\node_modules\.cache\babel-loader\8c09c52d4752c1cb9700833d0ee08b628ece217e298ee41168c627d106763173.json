{"ast": null, "code": "import { useFieldV7TextField } from \"./useFieldV7TextField.js\";\nimport { useFieldV6TextField } from \"./useFieldV6TextField.js\";\nimport { useNullableFieldPrivateContext } from \"../useNullableFieldPrivateContext.js\";\nexport const useField = parameters => {\n  var _ref, _parameters$props$ena;\n  const fieldPrivateContext = useNullableFieldPrivateContext();\n  const enableAccessibleFieldDOMStructure = (_ref = (_parameters$props$ena = parameters.props.enableAccessibleFieldDOMStructure) !== null && _parameters$props$ena !== void 0 ? _parameters$props$ena : fieldPrivateContext === null || fieldPrivateContext === void 0 ? void 0 : fieldPrivateContext.enableAccessibleFieldDOMStructure) !== null && _ref !== void 0 ? _ref : true;\n  const useFieldTextField = enableAccessibleFieldDOMStructure ? useFieldV7TextField : useFieldV6TextField;\n  return useFieldTextField(parameters);\n};", "map": {"version": 3, "names": ["useFieldV7TextField", "useFieldV6TextField", "useNullableFieldPrivateContext", "useField", "parameters", "_ref", "_parameters$props$ena", "fieldPrivateContext", "enableAccessibleFieldDOMStructure", "props", "useFieldTextField"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-date-pickers/esm/internals/hooks/useField/useField.js"], "sourcesContent": ["import { useFieldV7TextField } from \"./useFieldV7TextField.js\";\nimport { useFieldV6TextField } from \"./useFieldV6TextField.js\";\nimport { useNullableFieldPrivateContext } from \"../useNullableFieldPrivateContext.js\";\nexport const useField = parameters => {\n  const fieldPrivateContext = useNullableFieldPrivateContext();\n  const enableAccessibleFieldDOMStructure = parameters.props.enableAccessibleFieldDOMStructure ?? fieldPrivateContext?.enableAccessibleFieldDOMStructure ?? true;\n  const useFieldTextField = enableAccessibleFieldDOMStructure ? useFieldV7TextField : useFieldV6TextField;\n  return useFieldTextField(parameters);\n};"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,8BAA8B,QAAQ,sCAAsC;AACrF,OAAO,MAAMC,QAAQ,GAAGC,UAAU,IAAI;EAAA,IAAAC,IAAA,EAAAC,qBAAA;EACpC,MAAMC,mBAAmB,GAAGL,8BAA8B,CAAC,CAAC;EAC5D,MAAMM,iCAAiC,IAAAH,IAAA,IAAAC,qBAAA,GAAGF,UAAU,CAACK,KAAK,CAACD,iCAAiC,cAAAF,qBAAA,cAAAA,qBAAA,GAAIC,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEC,iCAAiC,cAAAH,IAAA,cAAAA,IAAA,GAAI,IAAI;EAC9J,MAAMK,iBAAiB,GAAGF,iCAAiC,GAAGR,mBAAmB,GAAGC,mBAAmB;EACvG,OAAOS,iBAAiB,CAACN,UAAU,CAAC;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}