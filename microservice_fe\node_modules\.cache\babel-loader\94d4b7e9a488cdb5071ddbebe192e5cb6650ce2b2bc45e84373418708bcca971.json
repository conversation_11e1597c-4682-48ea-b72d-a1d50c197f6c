{"ast": null, "code": "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getWeekYear} function options.\n */\n\n/**\n * @name getWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Get the local week-numbering year of the given date.\n *\n * @description\n * Get the local week-numbering year of the given date.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The local week-numbering year\n *\n * @example\n * // Which week numbering year is 26 December 2004 with the default settings?\n * const result = getWeekYear(new Date(2004, 11, 26))\n * //=> 2005\n *\n * @example\n * // Which week numbering year is 26 December 2004 if week starts on Saturday?\n * const result = getWeekYear(new Date(2004, 11, 26), { weekStartsOn: 6 })\n * //=> 2004\n *\n * @example\n * // Which week numbering year is 26 December 2004 if the first week contains 4 January?\n * const result = getWeekYear(new Date(2004, 11, 26), { firstWeekContainsDate: 4 })\n * //=> 2004\n */\nexport function getWeekYear(date, options) {\n  var _ref, _ref2, _ref3, _options$firstWeekCon, _options$locale, _defaultOptions$local;\n  const _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);\n  const year = _date.getFullYear();\n  const defaultOptions = getDefaultOptions();\n  const firstWeekContainsDate = (_ref = (_ref2 = (_ref3 = (_options$firstWeekCon = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon !== void 0 ? _options$firstWeekCon : options === null || options === void 0 || (_options$locale = options.locale) === null || _options$locale === void 0 || (_options$locale = _options$locale.options) === null || _options$locale === void 0 ? void 0 : _options$locale.firstWeekContainsDate) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.firstWeekContainsDate) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 || (_defaultOptions$local = _defaultOptions$local.options) === null || _defaultOptions$local === void 0 ? void 0 : _defaultOptions$local.firstWeekContainsDate) !== null && _ref !== void 0 ? _ref : 1;\n  const firstWeekOfNextYear = constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, 0);\n  firstWeekOfNextYear.setFullYear(year + 1, 0, firstWeekContainsDate);\n  firstWeekOfNextYear.setHours(0, 0, 0, 0);\n  const startOfNextYear = startOfWeek(firstWeekOfNextYear, options);\n  const firstWeekOfThisYear = constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, 0);\n  firstWeekOfThisYear.setFullYear(year, 0, firstWeekContainsDate);\n  firstWeekOfThisYear.setHours(0, 0, 0, 0);\n  const startOfThisYear = startOfWeek(firstWeekOfThisYear, options);\n  if (+_date >= +startOfNextYear) {\n    return year + 1;\n  } else if (+_date >= +startOfThisYear) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}\n\n// Fallback for modularized imports:\nexport default getWeekYear;", "map": {"version": 3, "names": ["getDefaultOptions", "constructFrom", "startOfWeek", "toDate", "getWeekYear", "date", "options", "_ref", "_ref2", "_ref3", "_options$firstWeekCon", "_options$locale", "_defaultOptions$local", "_date", "in", "year", "getFullYear", "defaultOptions", "firstWeekContainsDate", "locale", "firstWeekOfNextYear", "setFullYear", "setHours", "startOfNextYear", "firstWeekOfThisYear", "startOfThisYear"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/date-fns/getWeekYear.js"], "sourcesContent": ["import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getWeekYear} function options.\n */\n\n/**\n * @name getWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Get the local week-numbering year of the given date.\n *\n * @description\n * Get the local week-numbering year of the given date.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The local week-numbering year\n *\n * @example\n * // Which week numbering year is 26 December 2004 with the default settings?\n * const result = getWeekYear(new Date(2004, 11, 26))\n * //=> 2005\n *\n * @example\n * // Which week numbering year is 26 December 2004 if week starts on Saturday?\n * const result = getWeekYear(new Date(2004, 11, 26), { weekStartsOn: 6 })\n * //=> 2004\n *\n * @example\n * // Which week numbering year is 26 December 2004 if the first week contains 4 January?\n * const result = getWeekYear(new Date(2004, 11, 26), { firstWeekContainsDate: 4 })\n * //=> 2004\n */\nexport function getWeekYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n\n  const defaultOptions = getDefaultOptions();\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const firstWeekOfNextYear = constructFrom(options?.in || date, 0);\n  firstWeekOfNextYear.setFullYear(year + 1, 0, firstWeekContainsDate);\n  firstWeekOfNextYear.setHours(0, 0, 0, 0);\n  const startOfNextYear = startOfWeek(firstWeekOfNextYear, options);\n\n  const firstWeekOfThisYear = constructFrom(options?.in || date, 0);\n  firstWeekOfThisYear.setFullYear(year, 0, firstWeekContainsDate);\n  firstWeekOfThisYear.setHours(0, 0, 0, 0);\n  const startOfThisYear = startOfWeek(firstWeekOfThisYear, options);\n\n  if (+_date >= +startOfNextYear) {\n    return year + 1;\n  } else if (+_date >= +startOfThisYear) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}\n\n// Fallback for modularized imports:\nexport default getWeekYear;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAAA,IAAAC,IAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA;EACzC,MAAMC,KAAK,GAAGV,MAAM,CAACE,IAAI,EAAEC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,EAAE,CAAC;EACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;EAEhC,MAAMC,cAAc,GAAGjB,iBAAiB,CAAC,CAAC;EAC1C,MAAMkB,qBAAqB,IAAAX,IAAA,IAAAC,KAAA,IAAAC,KAAA,IAAAC,qBAAA,GACzBJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEY,qBAAqB,cAAAR,qBAAA,cAAAA,qBAAA,GAC9BJ,OAAO,aAAPA,OAAO,gBAAAK,eAAA,GAAPL,OAAO,CAAEa,MAAM,cAAAR,eAAA,gBAAAA,eAAA,GAAfA,eAAA,CAAiBL,OAAO,cAAAK,eAAA,uBAAxBA,eAAA,CAA0BO,qBAAqB,cAAAT,KAAA,cAAAA,KAAA,GAC/CQ,cAAc,CAACC,qBAAqB,cAAAV,KAAA,cAAAA,KAAA,IAAAI,qBAAA,GACpCK,cAAc,CAACE,MAAM,cAAAP,qBAAA,gBAAAA,qBAAA,GAArBA,qBAAA,CAAuBN,OAAO,cAAAM,qBAAA,uBAA9BA,qBAAA,CAAgCM,qBAAqB,cAAAX,IAAA,cAAAA,IAAA,GACrD,CAAC;EAEH,MAAMa,mBAAmB,GAAGnB,aAAa,CAAC,CAAAK,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,EAAE,KAAIT,IAAI,EAAE,CAAC,CAAC;EACjEe,mBAAmB,CAACC,WAAW,CAACN,IAAI,GAAG,CAAC,EAAE,CAAC,EAAEG,qBAAqB,CAAC;EACnEE,mBAAmB,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACxC,MAAMC,eAAe,GAAGrB,WAAW,CAACkB,mBAAmB,EAAEd,OAAO,CAAC;EAEjE,MAAMkB,mBAAmB,GAAGvB,aAAa,CAAC,CAAAK,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,EAAE,KAAIT,IAAI,EAAE,CAAC,CAAC;EACjEmB,mBAAmB,CAACH,WAAW,CAACN,IAAI,EAAE,CAAC,EAAEG,qBAAqB,CAAC;EAC/DM,mBAAmB,CAACF,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACxC,MAAMG,eAAe,GAAGvB,WAAW,CAACsB,mBAAmB,EAAElB,OAAO,CAAC;EAEjE,IAAI,CAACO,KAAK,IAAI,CAACU,eAAe,EAAE;IAC9B,OAAOR,IAAI,GAAG,CAAC;EACjB,CAAC,MAAM,IAAI,CAACF,KAAK,IAAI,CAACY,eAAe,EAAE;IACrC,OAAOV,IAAI;EACb,CAAC,MAAM;IACL,OAAOA,IAAI,GAAG,CAAC;EACjB;AACF;;AAEA;AACA,eAAeX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}