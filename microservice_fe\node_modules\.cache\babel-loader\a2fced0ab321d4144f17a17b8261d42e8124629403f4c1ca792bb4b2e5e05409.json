{"ast": null, "code": "import{get,post}from'../api/apiClient';const BASE_URL='/api/customer-payment';export const customerPaymentService={// <PERSON><PERSON><PERSON> tất cả các thanh toán\ngetAllPayments:async()=>{return get(BASE_URL);},// L<PERSON>y thanh toán theo ID\ngetPaymentById:async id=>{return get(\"\".concat(BASE_URL,\"/payment/\").concat(id));},// Tạo thanh toán mới\ncreatePayment:async payment=>{console.log('🚀 Payment service: Creating payment...',{contractId:payment.customerContractId,customerId:payment.customerId,amount:payment.paymentAmount,method:payment.paymentMethod,paymentDate:payment.paymentDate});const result=await post(BASE_URL,payment);console.log('✅ Payment service: Payment created successfully:',{id:result.id,amount:result.paymentAmount,contractId:result.customerContractId});return result;},// <PERSON><PERSON><PERSON> kiếm kh<PERSON>ch hàng\nsearchCustomers:async(fullName,phoneNumber)=>{let url=\"\".concat(BASE_URL,\"/customer/search\");const params=[];if(fullName)params.push(\"fullName=\".concat(encodeURIComponent(fullName)));if(phoneNumber)params.push(\"phoneNumber=\".concat(encodeURIComponent(phoneNumber)));if(params.length>0)url+=\"?\".concat(params.join('&'));return get(url);},// Lấy thanh toán theo khách hàng\ngetPaymentsByCustomerId:async customerId=>{return get(\"\".concat(BASE_URL,\"/customer/\").concat(customerId));},// Lấy thanh toán theo hợp đồng\ngetPaymentsByContractId:async contractId=>{return get(\"\".concat(BASE_URL,\"/contract/\").concat(contractId));},// Lấy hợp đồng đang hoạt động của khách hàng\ngetActiveContractsByCustomerId:async customerId=>{return get(\"\".concat(BASE_URL,\"/customer/\").concat(customerId,\"/active-contracts\"));},// Lấy thông tin thanh toán của hợp đồng\ngetContractPaymentInfo:async contractId=>{return get(\"\".concat(BASE_URL,\"/contract/\").concat(contractId,\"/payment-info\"));},// Lấy tổng số tiền đã thanh toán của hợp đồng\ngetTotalPaidAmountByContractId:async contractId=>{return get(\"\".concat(BASE_URL,\"/contract/\").concat(contractId,\"/total-paid\"));},// Lấy số tiền còn lại của hợp đồng\ngetRemainingAmountByContractId:async contractId=>{return get(\"\".concat(BASE_URL,\"/contract/\").concat(contractId,\"/remaining-amount\"));}};", "map": {"version": 3, "names": ["get", "post", "BASE_URL", "customerPaymentService", "getAllPayments", "getPaymentById", "id", "concat", "createPayment", "payment", "console", "log", "contractId", "customerContractId", "customerId", "amount", "paymentAmount", "method", "paymentMethod", "paymentDate", "result", "searchCustomers", "fullName", "phoneNumber", "url", "params", "push", "encodeURIComponent", "length", "join", "getPaymentsByCustomerId", "getPaymentsByContractId", "getActiveContractsByCustomerId", "getContractPaymentInfo", "getTotalPaidAmountByContractId", "getRemainingAmountByContractId"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/services/payment/customerPaymentService.ts"], "sourcesContent": ["import { CustomerPayment, Customer, CustomerContract } from '../../models';\nimport { get, post } from '../api/apiClient';\n\nconst BASE_URL = '/api/customer-payment';\n\nexport const customerPaymentService = {\n  // <PERSON><PERSON><PERSON> tất cả các thanh toán\n  getAllPayments: async (): Promise<CustomerPayment[]> => {\n    return get<CustomerPayment[]>(BASE_URL);\n  },\n\n  // Lấy thanh toán theo ID\n  getPaymentById: async (id: number): Promise<CustomerPayment> => {\n    return get<CustomerPayment>(`${BASE_URL}/payment/${id}`);\n  },\n\n  // Tạo thanh toán mới\n  createPayment: async (payment: CustomerPayment): Promise<CustomerPayment> => {\n    console.log('🚀 Payment service: Creating payment...', {\n      contractId: payment.customerContractId,\n      customerId: payment.customerId,\n      amount: payment.paymentAmount,\n      method: payment.paymentMethod,\n      paymentDate: payment.paymentDate\n    });\n\n    const result = await post<CustomerPayment>(BASE_URL, payment);\n\n    console.log('✅ Payment service: Payment created successfully:', {\n      id: result.id,\n      amount: result.paymentAmount,\n      contractId: result.customerContractId\n    });\n\n    return result;\n  },\n\n  // Tìm kiếm khách hàng\n  searchCustomers: async (fullName?: string, phoneNumber?: string): Promise<Customer[]> => {\n    let url = `${BASE_URL}/customer/search`;\n    const params = [];\n    if (fullName) params.push(`fullName=${encodeURIComponent(fullName)}`);\n    if (phoneNumber) params.push(`phoneNumber=${encodeURIComponent(phoneNumber)}`);\n    if (params.length > 0) url += `?${params.join('&')}`;\n    return get<Customer[]>(url);\n  },\n\n  // Lấy thanh toán theo khách hàng\n  getPaymentsByCustomerId: async (customerId: number): Promise<CustomerPayment[]> => {\n    return get<CustomerPayment[]>(`${BASE_URL}/customer/${customerId}`);\n  },\n\n  // Lấy thanh toán theo hợp đồng\n  getPaymentsByContractId: async (contractId: number): Promise<CustomerPayment[]> => {\n    return get<CustomerPayment[]>(`${BASE_URL}/contract/${contractId}`);\n  },\n\n  // Lấy hợp đồng đang hoạt động của khách hàng\n  getActiveContractsByCustomerId: async (customerId: number): Promise<CustomerContract[]> => {\n    return get<CustomerContract[]>(`${BASE_URL}/customer/${customerId}/active-contracts`);\n  },\n\n  // Lấy thông tin thanh toán của hợp đồng\n  getContractPaymentInfo: async (contractId: number): Promise<CustomerContract> => {\n    return get<CustomerContract>(`${BASE_URL}/contract/${contractId}/payment-info`);\n  },\n\n  // Lấy tổng số tiền đã thanh toán của hợp đồng\n  getTotalPaidAmountByContractId: async (contractId: number): Promise<number> => {\n    return get<number>(`${BASE_URL}/contract/${contractId}/total-paid`);\n  },\n\n  // Lấy số tiền còn lại của hợp đồng\n  getRemainingAmountByContractId: async (contractId: number): Promise<number> => {\n    return get<number>(`${BASE_URL}/contract/${contractId}/remaining-amount`);\n  },\n};\n"], "mappings": "AACA,OAASA,GAAG,CAAEC,IAAI,KAAQ,kBAAkB,CAE5C,KAAM,CAAAC,QAAQ,CAAG,uBAAuB,CAExC,MAAO,MAAM,CAAAC,sBAAsB,CAAG,CACpC;AACAC,cAAc,CAAE,KAAAA,CAAA,GAAwC,CACtD,MAAO,CAAAJ,GAAG,CAAoBE,QAAQ,CAAC,CACzC,CAAC,CAED;AACAG,cAAc,CAAE,KAAO,CAAAC,EAAU,EAA+B,CAC9D,MAAO,CAAAN,GAAG,IAAAO,MAAA,CAAqBL,QAAQ,cAAAK,MAAA,CAAYD,EAAE,CAAE,CAAC,CAC1D,CAAC,CAED;AACAE,aAAa,CAAE,KAAO,CAAAC,OAAwB,EAA+B,CAC3EC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAE,CACrDC,UAAU,CAAEH,OAAO,CAACI,kBAAkB,CACtCC,UAAU,CAAEL,OAAO,CAACK,UAAU,CAC9BC,MAAM,CAAEN,OAAO,CAACO,aAAa,CAC7BC,MAAM,CAAER,OAAO,CAACS,aAAa,CAC7BC,WAAW,CAAEV,OAAO,CAACU,WACvB,CAAC,CAAC,CAEF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAnB,IAAI,CAAkBC,QAAQ,CAAEO,OAAO,CAAC,CAE7DC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAE,CAC9DL,EAAE,CAAEc,MAAM,CAACd,EAAE,CACbS,MAAM,CAAEK,MAAM,CAACJ,aAAa,CAC5BJ,UAAU,CAAEQ,MAAM,CAACP,kBACrB,CAAC,CAAC,CAEF,MAAO,CAAAO,MAAM,CACf,CAAC,CAED;AACAC,eAAe,CAAE,KAAAA,CAAOC,QAAiB,CAAEC,WAAoB,GAA0B,CACvF,GAAI,CAAAC,GAAG,IAAAjB,MAAA,CAAML,QAAQ,oBAAkB,CACvC,KAAM,CAAAuB,MAAM,CAAG,EAAE,CACjB,GAAIH,QAAQ,CAAEG,MAAM,CAACC,IAAI,aAAAnB,MAAA,CAAaoB,kBAAkB,CAACL,QAAQ,CAAC,CAAE,CAAC,CACrE,GAAIC,WAAW,CAAEE,MAAM,CAACC,IAAI,gBAAAnB,MAAA,CAAgBoB,kBAAkB,CAACJ,WAAW,CAAC,CAAE,CAAC,CAC9E,GAAIE,MAAM,CAACG,MAAM,CAAG,CAAC,CAAEJ,GAAG,MAAAjB,MAAA,CAAQkB,MAAM,CAACI,IAAI,CAAC,GAAG,CAAC,CAAE,CACpD,MAAO,CAAA7B,GAAG,CAAawB,GAAG,CAAC,CAC7B,CAAC,CAED;AACAM,uBAAuB,CAAE,KAAO,CAAAhB,UAAkB,EAAiC,CACjF,MAAO,CAAAd,GAAG,IAAAO,MAAA,CAAuBL,QAAQ,eAAAK,MAAA,CAAaO,UAAU,CAAE,CAAC,CACrE,CAAC,CAED;AACAiB,uBAAuB,CAAE,KAAO,CAAAnB,UAAkB,EAAiC,CACjF,MAAO,CAAAZ,GAAG,IAAAO,MAAA,CAAuBL,QAAQ,eAAAK,MAAA,CAAaK,UAAU,CAAE,CAAC,CACrE,CAAC,CAED;AACAoB,8BAA8B,CAAE,KAAO,CAAAlB,UAAkB,EAAkC,CACzF,MAAO,CAAAd,GAAG,IAAAO,MAAA,CAAwBL,QAAQ,eAAAK,MAAA,CAAaO,UAAU,qBAAmB,CAAC,CACvF,CAAC,CAED;AACAmB,sBAAsB,CAAE,KAAO,CAAArB,UAAkB,EAAgC,CAC/E,MAAO,CAAAZ,GAAG,IAAAO,MAAA,CAAsBL,QAAQ,eAAAK,MAAA,CAAaK,UAAU,iBAAe,CAAC,CACjF,CAAC,CAED;AACAsB,8BAA8B,CAAE,KAAO,CAAAtB,UAAkB,EAAsB,CAC7E,MAAO,CAAAZ,GAAG,IAAAO,MAAA,CAAYL,QAAQ,eAAAK,MAAA,CAAaK,UAAU,eAAa,CAAC,CACrE,CAAC,CAED;AACAuB,8BAA8B,CAAE,KAAO,CAAAvB,UAAkB,EAAsB,CAC7E,MAAO,CAAAZ,GAAG,IAAAO,MAAA,CAAYL,QAAQ,eAAAK,MAAA,CAAaK,UAAU,qBAAmB,CAAC,CAC3E,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}