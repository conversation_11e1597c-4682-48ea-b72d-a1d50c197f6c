{"ast": null, "code": "import React from'react';import{Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography,Box,Chip}from'@mui/material';import{formatDateForDisplay}from'../../utils/dateUtils';import{PaymentMethodMap}from'../../models/CustomerPayment';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CustomerInvoiceList=_ref=>{let{invoices}=_ref;// Format currency to VND\nconst formatCurrency=amount=>{return new Intl.NumberFormat('vi-VN',{style:'currency',currency:'VND'}).format(amount);};// Format date to Vietnamese format (DD/MM/YYYY)\nconst formatDate=dateValue=>{if(!dateValue){return'Không xác định';}try{return formatDateForDisplay(dateValue)||'Không xác định';}catch(error){console.error('Error formatting date:',error,dateValue);return'Không xác định';}};// Get payment method name\nconst getPaymentMethodName=methodId=>{return PaymentMethodMap[methodId]||'Không xác định';};return/*#__PURE__*/_jsx(Paper,{elevation:2,children:/*#__PURE__*/_jsx(TableContainer,{children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"M\\xE3 h\\xF3a \\u0111\\u01A1n\"}),/*#__PURE__*/_jsx(TableCell,{children:\"M\\xE3 h\\u1EE3p \\u0111\\u1ED3ng\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Ng\\xE0y thanh to\\xE1n\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Ph\\u01B0\\u01A1ng th\\u1EE9c\"}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:\"S\\u1ED1 ti\\u1EC1n\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Ghi ch\\xFA\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:invoices.length>0?invoices.map(invoice=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsxs(TableCell,{children:[\"#\",invoice.id]}),/*#__PURE__*/_jsx(TableCell,{children:\"H\\u0110-\".concat(invoice.customerContractId)}),/*#__PURE__*/_jsx(TableCell,{children:formatDate(invoice.paymentDate)}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Chip,{label:getPaymentMethodName(invoice.paymentMethod),color:invoice.paymentMethod===0?\"primary\":\"default\",size:\"small\"})}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",sx:{fontWeight:'bold'},children:formatCurrency(invoice.paymentAmount)}),/*#__PURE__*/_jsx(TableCell,{children:invoice.note||'-'})]},invoice.id)):/*#__PURE__*/_jsx(TableRow,{children:/*#__PURE__*/_jsx(TableCell,{colSpan:6,align:\"center\",children:/*#__PURE__*/_jsx(Box,{sx:{py:3},children:/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",children:\"Kh\\xF4ng c\\xF3 h\\xF3a \\u0111\\u01A1n n\\xE0o trong kho\\u1EA3ng th\\u1EDDi gian \\u0111\\xE3 ch\\u1ECDn\"})})})})})]})})});};export default CustomerInvoiceList;", "map": {"version": 3, "names": ["React", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Typography", "Box", "Chip", "formatDateForDisplay", "PaymentMethodMap", "jsx", "_jsx", "jsxs", "_jsxs", "CustomerInvoiceList", "_ref", "invoices", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "formatDate", "dateValue", "error", "console", "getPaymentMethodName", "methodId", "elevation", "children", "align", "length", "map", "invoice", "id", "concat", "customerContractId", "paymentDate", "label", "paymentMethod", "color", "size", "sx", "fontWeight", "paymentAmount", "note", "colSpan", "py", "variant"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/statistics/CustomerInvoiceList.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Typography,\n  Box,\n  Chip\n} from '@mui/material';\nimport { formatDateForDisplay } from '../../utils/dateUtils';\nimport { CustomerPayment, PaymentMethodMap } from '../../models/CustomerPayment';\n\ninterface CustomerInvoiceListProps {\n  invoices: CustomerPayment[];\n  customerName: string;\n}\n\nconst CustomerInvoiceList: React.FC<CustomerInvoiceListProps> = ({ invoices }) => {\n  // Format currency to VND\n  const formatCurrency = (amount: number): string => {\n    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);\n  };\n\n  // Format date to Vietnamese format (DD/MM/YYYY)\n  const formatDate = (dateValue: string | Date): string => {\n    if (!dateValue) {\n      return 'Không xác định';\n    }\n\n    try {\n      return formatDateForDisplay(dateValue) || 'Không xác định';\n    } catch (error) {\n      console.error('Error formatting date:', error, dateValue);\n      return 'Không xác định';\n    }\n  };\n\n  // Get payment method name\n  const getPaymentMethodName = (methodId: number): string => {\n    return PaymentMethodMap[methodId] || 'Không xác định';\n  };\n\n  return (\n    <Paper elevation={2}>\n      <TableContainer>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>Mã hóa đơn</TableCell>\n              <TableCell>Mã hợp đồng</TableCell>\n              <TableCell>Ngày thanh toán</TableCell>\n              <TableCell>Phương thức</TableCell>\n              <TableCell align=\"right\">Số tiền</TableCell>\n              <TableCell>Ghi chú</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {invoices.length > 0 ? (\n              invoices.map((invoice) => (\n                <TableRow key={invoice.id}>\n                  <TableCell>#{invoice.id}</TableCell>\n                  <TableCell>{`HĐ-${invoice.customerContractId}`}</TableCell>\n                  <TableCell>{formatDate(invoice.paymentDate)}</TableCell>\n                  <TableCell>\n                    <Chip\n                      label={getPaymentMethodName(invoice.paymentMethod)}\n                      color={invoice.paymentMethod === 0 ? \"primary\" : \"default\"}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell align=\"right\" sx={{ fontWeight: 'bold' }}>\n                    {formatCurrency(invoice.paymentAmount)}\n                  </TableCell>\n                  <TableCell>{invoice.note || '-'}</TableCell>\n                </TableRow>\n              ))\n            ) : (\n              <TableRow>\n                <TableCell colSpan={6} align=\"center\">\n                  <Box sx={{ py: 3 }}>\n                    <Typography variant=\"subtitle1\">\n                      Không có hóa đơn nào trong khoảng thời gian đã chọn\n                    </Typography>\n                  </Box>\n                </TableCell>\n              </TableRow>\n            )}\n          </TableBody>\n        </Table>\n      </TableContainer>\n    </Paper>\n  );\n};\n\nexport default CustomerInvoiceList;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,KAAK,CACLC,KAAK,CACLC,SAAS,CACTC,SAAS,CACTC,cAAc,CACdC,SAAS,CACTC,QAAQ,CACRC,UAAU,CACVC,GAAG,CACHC,IAAI,KACC,eAAe,CACtB,OAASC,oBAAoB,KAAQ,uBAAuB,CAC5D,OAA0BC,gBAAgB,KAAQ,8BAA8B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOjF,KAAM,CAAAC,mBAAuD,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC3E;AACA,KAAM,CAAAE,cAAc,CAAIC,MAAc,EAAa,CACjD,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CAAEC,KAAK,CAAE,UAAU,CAAEC,QAAQ,CAAE,KAAM,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC,CAC9F,CAAC,CAED;AACA,KAAM,CAAAM,UAAU,CAAIC,SAAwB,EAAa,CACvD,GAAI,CAACA,SAAS,CAAE,CACd,MAAO,gBAAgB,CACzB,CAEA,GAAI,CACF,MAAO,CAAAjB,oBAAoB,CAACiB,SAAS,CAAC,EAAI,gBAAgB,CAC5D,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAED,SAAS,CAAC,CACzD,MAAO,gBAAgB,CACzB,CACF,CAAC,CAED;AACA,KAAM,CAAAG,oBAAoB,CAAIC,QAAgB,EAAa,CACzD,MAAO,CAAApB,gBAAgB,CAACoB,QAAQ,CAAC,EAAI,gBAAgB,CACvD,CAAC,CAED,mBACElB,IAAA,CAACb,KAAK,EAACgC,SAAS,CAAE,CAAE,CAAAC,QAAA,cAClBpB,IAAA,CAACT,cAAc,EAAA6B,QAAA,cACblB,KAAA,CAACd,KAAK,EAAAgC,QAAA,eACJpB,IAAA,CAACR,SAAS,EAAA4B,QAAA,cACRlB,KAAA,CAACT,QAAQ,EAAA2B,QAAA,eACPpB,IAAA,CAACV,SAAS,EAAA8B,QAAA,CAAC,4BAAU,CAAW,CAAC,cACjCpB,IAAA,CAACV,SAAS,EAAA8B,QAAA,CAAC,+BAAW,CAAW,CAAC,cAClCpB,IAAA,CAACV,SAAS,EAAA8B,QAAA,CAAC,uBAAe,CAAW,CAAC,cACtCpB,IAAA,CAACV,SAAS,EAAA8B,QAAA,CAAC,4BAAW,CAAW,CAAC,cAClCpB,IAAA,CAACV,SAAS,EAAC+B,KAAK,CAAC,OAAO,CAAAD,QAAA,CAAC,mBAAO,CAAW,CAAC,cAC5CpB,IAAA,CAACV,SAAS,EAAA8B,QAAA,CAAC,YAAO,CAAW,CAAC,EACtB,CAAC,CACF,CAAC,cACZpB,IAAA,CAACX,SAAS,EAAA+B,QAAA,CACPf,QAAQ,CAACiB,MAAM,CAAG,CAAC,CAClBjB,QAAQ,CAACkB,GAAG,CAAEC,OAAO,eACnBtB,KAAA,CAACT,QAAQ,EAAA2B,QAAA,eACPlB,KAAA,CAACZ,SAAS,EAAA8B,QAAA,EAAC,GAAC,CAACI,OAAO,CAACC,EAAE,EAAY,CAAC,cACpCzB,IAAA,CAACV,SAAS,EAAA8B,QAAA,YAAAM,MAAA,CAAQF,OAAO,CAACG,kBAAkB,EAAc,CAAC,cAC3D3B,IAAA,CAACV,SAAS,EAAA8B,QAAA,CAAEP,UAAU,CAACW,OAAO,CAACI,WAAW,CAAC,CAAY,CAAC,cACxD5B,IAAA,CAACV,SAAS,EAAA8B,QAAA,cACRpB,IAAA,CAACJ,IAAI,EACHiC,KAAK,CAAEZ,oBAAoB,CAACO,OAAO,CAACM,aAAa,CAAE,CACnDC,KAAK,CAAEP,OAAO,CAACM,aAAa,GAAK,CAAC,CAAG,SAAS,CAAG,SAAU,CAC3DE,IAAI,CAAC,OAAO,CACb,CAAC,CACO,CAAC,cACZhC,IAAA,CAACV,SAAS,EAAC+B,KAAK,CAAC,OAAO,CAACY,EAAE,CAAE,CAAEC,UAAU,CAAE,MAAO,CAAE,CAAAd,QAAA,CACjDd,cAAc,CAACkB,OAAO,CAACW,aAAa,CAAC,CAC7B,CAAC,cACZnC,IAAA,CAACV,SAAS,EAAA8B,QAAA,CAAEI,OAAO,CAACY,IAAI,EAAI,GAAG,CAAY,CAAC,GAd/BZ,OAAO,CAACC,EAeb,CACX,CAAC,cAEFzB,IAAA,CAACP,QAAQ,EAAA2B,QAAA,cACPpB,IAAA,CAACV,SAAS,EAAC+C,OAAO,CAAE,CAAE,CAAChB,KAAK,CAAC,QAAQ,CAAAD,QAAA,cACnCpB,IAAA,CAACL,GAAG,EAACsC,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAE,CAAE,CAAAlB,QAAA,cACjBpB,IAAA,CAACN,UAAU,EAAC6C,OAAO,CAAC,WAAW,CAAAnB,QAAA,CAAC,kGAEhC,CAAY,CAAC,CACV,CAAC,CACG,CAAC,CACJ,CACX,CACQ,CAAC,EACP,CAAC,CACM,CAAC,CACZ,CAAC,CAEZ,CAAC,CAED,cAAe,CAAAjB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}