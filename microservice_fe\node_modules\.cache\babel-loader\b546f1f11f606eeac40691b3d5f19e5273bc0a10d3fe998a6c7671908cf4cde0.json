{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\n\n/**\n * @name transpose\n * @category Generic Helpers\n * @summary Transpose the date to the given constructor.\n *\n * @description\n * The function transposes the date to the given constructor. It helps you\n * to transpose the date in the system time zone to say `UTCDate` or any other\n * date extension.\n *\n * @typeParam InputDate - The input `Date` type derived from the passed argument.\n * @typeParam ResultDate - The result `Date` type derived from the passed constructor.\n *\n * @param date - The date to use values from\n * @param constructor - The date constructor to use\n *\n * @returns Date transposed to the given constructor\n *\n * @example\n * // Create July 10, 2022 00:00 in locale time zone\n * const date = new Date(2022, 6, 10)\n * //=> 'Sun Jul 10 2022 00:00:00 GMT+0800 (Singapore Standard Time)'\n *\n * @example\n * // Transpose the date to July 10, 2022 00:00 in UTC\n * transpose(date, UTCDate)\n * //=> 'Sun Jul 10 2022 00:00:00 GMT+0000 (Coordinated Universal Time)'\n */\nexport function transpose(date, constructor) {\n  const date_ = isConstructor(constructor) ? new constructor(0) : constructFrom(constructor, 0);\n  date_.setFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n  date_.setHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n  return date_;\n}\nfunction isConstructor(constructor) {\n  var _constructor$prototyp;\n  return typeof constructor === \"function\" && ((_constructor$prototyp = constructor.prototype) === null || _constructor$prototyp === void 0 ? void 0 : _constructor$prototyp.constructor) === constructor;\n}\n\n// Fallback for modularized imports:\nexport default transpose;", "map": {"version": 3, "names": ["constructFrom", "transpose", "date", "constructor", "date_", "isConstructor", "setFullYear", "getFullYear", "getMonth", "getDate", "setHours", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "_constructor$prototyp", "prototype"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/date-fns/transpose.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\n\n/**\n * @name transpose\n * @category Generic Helpers\n * @summary Transpose the date to the given constructor.\n *\n * @description\n * The function transposes the date to the given constructor. It helps you\n * to transpose the date in the system time zone to say `UTCDate` or any other\n * date extension.\n *\n * @typeParam InputDate - The input `Date` type derived from the passed argument.\n * @typeParam ResultDate - The result `Date` type derived from the passed constructor.\n *\n * @param date - The date to use values from\n * @param constructor - The date constructor to use\n *\n * @returns Date transposed to the given constructor\n *\n * @example\n * // Create July 10, 2022 00:00 in locale time zone\n * const date = new Date(2022, 6, 10)\n * //=> 'Sun Jul 10 2022 00:00:00 GMT+0800 (Singapore Standard Time)'\n *\n * @example\n * // Transpose the date to July 10, 2022 00:00 in UTC\n * transpose(date, UTCDate)\n * //=> 'Sun Jul 10 2022 00:00:00 GMT+0000 (Coordinated Universal Time)'\n */\nexport function transpose(date, constructor) {\n  const date_ = isConstructor(constructor)\n    ? new constructor(0)\n    : constructFrom(constructor, 0);\n  date_.setFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n  date_.setHours(\n    date.getHours(),\n    date.getMinutes(),\n    date.getSeconds(),\n    date.getMilliseconds(),\n  );\n  return date_;\n}\n\nfunction isConstructor(constructor) {\n  return (\n    typeof constructor === \"function\" &&\n    constructor.prototype?.constructor === constructor\n  );\n}\n\n// Fallback for modularized imports:\nexport default transpose;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAEC,WAAW,EAAE;EAC3C,MAAMC,KAAK,GAAGC,aAAa,CAACF,WAAW,CAAC,GACpC,IAAIA,WAAW,CAAC,CAAC,CAAC,GAClBH,aAAa,CAACG,WAAW,EAAE,CAAC,CAAC;EACjCC,KAAK,CAACE,WAAW,CAACJ,IAAI,CAACK,WAAW,CAAC,CAAC,EAAEL,IAAI,CAACM,QAAQ,CAAC,CAAC,EAAEN,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC;EACtEL,KAAK,CAACM,QAAQ,CACZR,IAAI,CAACS,QAAQ,CAAC,CAAC,EACfT,IAAI,CAACU,UAAU,CAAC,CAAC,EACjBV,IAAI,CAACW,UAAU,CAAC,CAAC,EACjBX,IAAI,CAACY,eAAe,CAAC,CACvB,CAAC;EACD,OAAOV,KAAK;AACd;AAEA,SAASC,aAAaA,CAACF,WAAW,EAAE;EAAA,IAAAY,qBAAA;EAClC,OACE,OAAOZ,WAAW,KAAK,UAAU,IACjC,EAAAY,qBAAA,GAAAZ,WAAW,CAACa,SAAS,cAAAD,qBAAA,uBAArBA,qBAAA,CAAuBZ,WAAW,MAAKA,WAAW;AAEtD;;AAEA;AACA,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}