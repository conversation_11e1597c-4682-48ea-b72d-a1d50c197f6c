{"ast": null, "code": "import * as React from 'react';\nexport default function isMuiElement(element, muiNames) {\n  var _element$type$muiName, _element$type;\n  return /*#__PURE__*/React.isValidElement(element) && muiNames.indexOf(// For server components `muiName` is avaialble in element.type._payload.value.muiName\n  // relevant info - https://github.com/facebook/react/blob/2807d781a08db8e9873687fccc25c0f12b4fb3d4/packages/react/src/ReactLazy.js#L45\n  // eslint-disable-next-line no-underscore-dangle\n  (_element$type$muiName = element.type.muiName) !== null && _element$type$muiName !== void 0 ? _element$type$muiName : (_element$type = element.type) === null || _element$type === void 0 || (_element$type = _element$type._payload) === null || _element$type === void 0 || (_element$type = _element$type.value) === null || _element$type === void 0 ? void 0 : _element$type.muiName) !== -1;\n}", "map": {"version": 3, "names": ["React", "isMuiElement", "element", "muiNames", "_element$type$muiName", "_element$type", "isValidElement", "indexOf", "type", "mui<PERSON><PERSON>", "_payload", "value"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/utils/esm/isMuiElement/isMuiElement.js"], "sourcesContent": ["import * as React from 'react';\nexport default function isMuiElement(element, muiNames) {\n  return /*#__PURE__*/React.isValidElement(element) && muiNames.indexOf(\n  // For server components `muiName` is avaialble in element.type._payload.value.muiName\n  // relevant info - https://github.com/facebook/react/blob/2807d781a08db8e9873687fccc25c0f12b4fb3d4/packages/react/src/ReactLazy.js#L45\n  // eslint-disable-next-line no-underscore-dangle\n  element.type.muiName ?? element.type?._payload?.value?.muiName) !== -1;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,YAAYA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EAAA,IAAAC,qBAAA,EAAAC,aAAA;EACtD,OAAO,aAAaL,KAAK,CAACM,cAAc,CAACJ,OAAO,CAAC,IAAIC,QAAQ,CAACI,OAAO,CACrE;EACA;EACA;EAAA,CAAAH,qBAAA,GACAF,OAAO,CAACM,IAAI,CAACC,OAAO,cAAAL,qBAAA,cAAAA,qBAAA,IAAAC,aAAA,GAAIH,OAAO,CAACM,IAAI,cAAAH,aAAA,gBAAAA,aAAA,GAAZA,aAAA,CAAcK,QAAQ,cAAAL,aAAA,gBAAAA,aAAA,GAAtBA,aAAA,CAAwBM,KAAK,cAAAN,aAAA,uBAA7BA,aAAA,CAA+BI,OAAO,CAAC,KAAK,CAAC,CAAC;AACxE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}