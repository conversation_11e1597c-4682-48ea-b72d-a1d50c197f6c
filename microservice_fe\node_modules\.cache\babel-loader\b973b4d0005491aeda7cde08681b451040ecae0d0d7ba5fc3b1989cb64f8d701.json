{"ast": null, "code": "import { createStepNavigation } from \"./createStepNavigation.js\";\nexport function createNonRangePickerStepNavigation(parameters) {\n  const {\n    steps\n  } = parameters;\n  return createStepNavigation({\n    steps,\n    isViewMatchingStep: (view, step) => {\n      return step.views == null || step.views.includes(view);\n    },\n    onStepChange: _ref => {\n      let {\n        step,\n        defaultView,\n        setView,\n        view,\n        views\n      } = _ref;\n      const targetView = step.views == null ? defaultView : step.views.find(viewBis => views.includes(viewBis));\n      if (targetView !== view) {\n        setView(targetView);\n      }\n    }\n  });\n}", "map": {"version": 3, "names": ["createStepNavigation", "createNonRangePickerStepNavigation", "parameters", "steps", "isViewMatchingStep", "view", "step", "views", "includes", "onStepChange", "_ref", "defaultView", "<PERSON><PERSON><PERSON><PERSON>", "targetView", "find", "viewBis"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-date-pickers/esm/internals/utils/createNonRangePickerStepNavigation.js"], "sourcesContent": ["import { createStepNavigation } from \"./createStepNavigation.js\";\nexport function createNonRangePickerStepNavigation(parameters) {\n  const {\n    steps\n  } = parameters;\n  return createStepNavigation({\n    steps,\n    isViewMatchingStep: (view, step) => {\n      return step.views == null || step.views.includes(view);\n    },\n    onStepChange: ({\n      step,\n      defaultView,\n      setView,\n      view,\n      views\n    }) => {\n      const targetView = step.views == null ? defaultView : step.views.find(viewBis => views.includes(viewBis));\n      if (targetView !== view) {\n        setView(targetView);\n      }\n    }\n  });\n}"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,OAAO,SAASC,kCAAkCA,CAACC,UAAU,EAAE;EAC7D,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,OAAOF,oBAAoB,CAAC;IAC1BG,KAAK;IACLC,kBAAkB,EAAEA,CAACC,IAAI,EAAEC,IAAI,KAAK;MAClC,OAAOA,IAAI,CAACC,KAAK,IAAI,IAAI,IAAID,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACH,IAAI,CAAC;IACxD,CAAC;IACDI,YAAY,EAAEC,IAAA,IAMR;MAAA,IANS;QACbJ,IAAI;QACJK,WAAW;QACXC,OAAO;QACPP,IAAI;QACJE;MACF,CAAC,GAAAG,IAAA;MACC,MAAMG,UAAU,GAAGP,IAAI,CAACC,KAAK,IAAI,IAAI,GAAGI,WAAW,GAAGL,IAAI,CAACC,KAAK,CAACO,IAAI,CAACC,OAAO,IAAIR,KAAK,CAACC,QAAQ,CAACO,OAAO,CAAC,CAAC;MACzG,IAAIF,UAAU,KAAKR,IAAI,EAAE;QACvBO,OAAO,CAACC,UAAU,CAAC;MACrB;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}