{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getButtonBaseUtilityClass(slot) {\n  return generateUtilityClass('MuiButtonBase', slot);\n}\nconst buttonBaseClasses = generateUtilityClasses('MuiButtonBase', ['root', 'disabled', 'focusVisible']);\nexport default buttonBaseClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getButtonBaseUtilityClass", "slot", "buttonBaseClasses"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/ButtonBase/buttonBaseClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getButtonBaseUtilityClass(slot) {\n  return generateUtilityClass('MuiButtonBase', slot);\n}\nconst buttonBaseClasses = generateUtilityClasses('MuiButtonBase', ['root', 'disabled', 'focusVisible']);\nexport default buttonBaseClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAOF,oBAAoB,CAAC,eAAe,EAAEE,IAAI,CAAC;AACpD;AACA,MAAMC,iBAAiB,GAAGJ,sBAAsB,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;AACvG,eAAeI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}