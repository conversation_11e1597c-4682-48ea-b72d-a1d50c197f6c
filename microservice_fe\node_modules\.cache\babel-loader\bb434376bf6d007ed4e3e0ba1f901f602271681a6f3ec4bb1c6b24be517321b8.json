{"ast": null, "code": "import React from'react';import{Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Button,Typography,Box}from'@mui/material';import VisibilityIcon from'@mui/icons-material/Visibility';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CustomerRevenueList=_ref=>{let{customers,onViewInvoices}=_ref;// Format currency to VND\nconst formatCurrency=amount=>{return new Intl.NumberFormat('vi-VN',{style:'currency',currency:'VND'}).format(amount);};return/*#__PURE__*/_jsx(Paper,{elevation:2,children:/*#__PURE__*/_jsx(TableContainer,{children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"M\\xE3 KH\"}),/*#__PURE__*/_jsx(TableCell,{children:\"T\\xEAn kh\\xE1ch h\\xE0ng\"}),/*#__PURE__*/_jsx(TableCell,{children:\"C\\xF4ng ty\"}),/*#__PURE__*/_jsx(TableCell,{children:\"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i\"}),/*#__PURE__*/_jsx(TableCell,{children:\"\\u0110\\u1ECBa ch\\u1EC9\"}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:\"T\\u1ED5ng doanh thu\"}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:\"Thao t\\xE1c\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:customers.length>0?customers.map(customer=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:customer.id}),/*#__PURE__*/_jsx(TableCell,{children:customer.fullName}),/*#__PURE__*/_jsx(TableCell,{children:customer.companyName||'-'}),/*#__PURE__*/_jsx(TableCell,{children:customer.phoneNumber}),/*#__PURE__*/_jsx(TableCell,{children:customer.address}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",sx:{fontWeight:'bold'},children:formatCurrency(customer.totalRevenue)}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(Button,{variant:\"outlined\",size:\"small\",startIcon:/*#__PURE__*/_jsx(VisibilityIcon,{}),onClick:()=>onViewInvoices(customer.id),children:\"Xem h\\xF3a \\u0111\\u01A1n\"})})]},customer.id)):/*#__PURE__*/_jsx(TableRow,{children:/*#__PURE__*/_jsx(TableCell,{colSpan:7,align:\"center\",children:/*#__PURE__*/_jsx(Box,{sx:{py:3},children:/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",children:\"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u doanh thu trong kho\\u1EA3ng th\\u1EDDi gian \\u0111\\xE3 ch\\u1ECDn\"})})})})})]})})});};export default CustomerRevenueList;", "map": {"version": 3, "names": ["React", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "<PERSON><PERSON>", "Typography", "Box", "VisibilityIcon", "jsx", "_jsx", "jsxs", "_jsxs", "CustomerRevenueList", "_ref", "customers", "onViewInvoices", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "elevation", "children", "align", "length", "map", "customer", "id", "fullName", "companyName", "phoneNumber", "address", "sx", "fontWeight", "totalRevenue", "variant", "size", "startIcon", "onClick", "colSpan", "py"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/statistics/CustomerRevenueList.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Button,\n  Typography,\n  Box\n} from '@mui/material';\nimport VisibilityIcon from '@mui/icons-material/Visibility';\nimport { CustomerRevenue } from '../../models';\n\ninterface CustomerRevenueListProps {\n  customers: CustomerRevenue[];\n  onViewInvoices: (customerId: number) => void;\n}\n\nconst CustomerRevenueList: React.FC<CustomerRevenueListProps> = ({ customers, onViewInvoices }) => {\n  // Format currency to VND\n  const formatCurrency = (amount: number): string => {\n    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);\n  };\n\n  return (\n    <Paper elevation={2}>\n      <TableContainer>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>Mã KH</TableCell>\n              <TableCell>Tên khách hàng</TableCell>\n              <TableCell>Công ty</TableCell>\n              <TableCell><PERSON><PERSON> điện thoại</TableCell>\n              <TableCell>Địa chỉ</TableCell>\n              <TableCell align=\"right\">Tổng doanh thu</TableCell>\n              <TableCell align=\"center\">Thao tác</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {customers.length > 0 ? (\n              customers.map((customer) => (\n                <TableRow key={customer.id}>\n                  <TableCell>{customer.id}</TableCell>\n                  <TableCell>{customer.fullName}</TableCell>\n                  <TableCell>{customer.companyName || '-'}</TableCell>\n                  <TableCell>{customer.phoneNumber}</TableCell>\n                  <TableCell>{customer.address}</TableCell>\n                  <TableCell align=\"right\" sx={{ fontWeight: 'bold' }}>\n                    {formatCurrency(customer.totalRevenue)}\n                  </TableCell>\n                  <TableCell align=\"center\">\n                    <Button\n                      variant=\"outlined\"\n                      size=\"small\"\n                      startIcon={<VisibilityIcon />}\n                      onClick={() => onViewInvoices(customer.id!)}\n                    >\n                      Xem hóa đơn\n                    </Button>\n                  </TableCell>\n                </TableRow>\n              ))\n            ) : (\n              <TableRow>\n                <TableCell colSpan={7} align=\"center\">\n                  <Box sx={{ py: 3 }}>\n                    <Typography variant=\"subtitle1\">\n                      Không có dữ liệu doanh thu trong khoảng thời gian đã chọn\n                    </Typography>\n                  </Box>\n                </TableCell>\n              </TableRow>\n            )}\n          </TableBody>\n        </Table>\n      </TableContainer>\n    </Paper>\n  );\n};\n\nexport default CustomerRevenueList;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,KAAK,CACLC,KAAK,CACLC,SAAS,CACTC,SAAS,CACTC,cAAc,CACdC,SAAS,CACTC,QAAQ,CACRC,MAAM,CACNC,UAAU,CACVC,GAAG,KACE,eAAe,CACtB,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQ5D,KAAM,CAAAC,mBAAuD,CAAGC,IAAA,EAAmC,IAAlC,CAAEC,SAAS,CAAEC,cAAe,CAAC,CAAAF,IAAA,CAC5F;AACA,KAAM,CAAAG,cAAc,CAAIC,MAAc,EAAa,CACjD,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CAAEC,KAAK,CAAE,UAAU,CAAEC,QAAQ,CAAE,KAAM,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC,CAC9F,CAAC,CAED,mBACER,IAAA,CAACZ,KAAK,EAAC0B,SAAS,CAAE,CAAE,CAAAC,QAAA,cAClBf,IAAA,CAACR,cAAc,EAAAuB,QAAA,cACbb,KAAA,CAACb,KAAK,EAAA0B,QAAA,eACJf,IAAA,CAACP,SAAS,EAAAsB,QAAA,cACRb,KAAA,CAACR,QAAQ,EAAAqB,QAAA,eACPf,IAAA,CAACT,SAAS,EAAAwB,QAAA,CAAC,UAAK,CAAW,CAAC,cAC5Bf,IAAA,CAACT,SAAS,EAAAwB,QAAA,CAAC,yBAAc,CAAW,CAAC,cACrCf,IAAA,CAACT,SAAS,EAAAwB,QAAA,CAAC,YAAO,CAAW,CAAC,cAC9Bf,IAAA,CAACT,SAAS,EAAAwB,QAAA,CAAC,mCAAa,CAAW,CAAC,cACpCf,IAAA,CAACT,SAAS,EAAAwB,QAAA,CAAC,wBAAO,CAAW,CAAC,cAC9Bf,IAAA,CAACT,SAAS,EAACyB,KAAK,CAAC,OAAO,CAAAD,QAAA,CAAC,qBAAc,CAAW,CAAC,cACnDf,IAAA,CAACT,SAAS,EAACyB,KAAK,CAAC,QAAQ,CAAAD,QAAA,CAAC,aAAQ,CAAW,CAAC,EACtC,CAAC,CACF,CAAC,cACZf,IAAA,CAACV,SAAS,EAAAyB,QAAA,CACPV,SAAS,CAACY,MAAM,CAAG,CAAC,CACnBZ,SAAS,CAACa,GAAG,CAAEC,QAAQ,eACrBjB,KAAA,CAACR,QAAQ,EAAAqB,QAAA,eACPf,IAAA,CAACT,SAAS,EAAAwB,QAAA,CAAEI,QAAQ,CAACC,EAAE,CAAY,CAAC,cACpCpB,IAAA,CAACT,SAAS,EAAAwB,QAAA,CAAEI,QAAQ,CAACE,QAAQ,CAAY,CAAC,cAC1CrB,IAAA,CAACT,SAAS,EAAAwB,QAAA,CAAEI,QAAQ,CAACG,WAAW,EAAI,GAAG,CAAY,CAAC,cACpDtB,IAAA,CAACT,SAAS,EAAAwB,QAAA,CAAEI,QAAQ,CAACI,WAAW,CAAY,CAAC,cAC7CvB,IAAA,CAACT,SAAS,EAAAwB,QAAA,CAAEI,QAAQ,CAACK,OAAO,CAAY,CAAC,cACzCxB,IAAA,CAACT,SAAS,EAACyB,KAAK,CAAC,OAAO,CAACS,EAAE,CAAE,CAAEC,UAAU,CAAE,MAAO,CAAE,CAAAX,QAAA,CACjDR,cAAc,CAACY,QAAQ,CAACQ,YAAY,CAAC,CAC7B,CAAC,cACZ3B,IAAA,CAACT,SAAS,EAACyB,KAAK,CAAC,QAAQ,CAAAD,QAAA,cACvBf,IAAA,CAACL,MAAM,EACLiC,OAAO,CAAC,UAAU,CAClBC,IAAI,CAAC,OAAO,CACZC,SAAS,cAAE9B,IAAA,CAACF,cAAc,GAAE,CAAE,CAC9BiC,OAAO,CAAEA,CAAA,GAAMzB,cAAc,CAACa,QAAQ,CAACC,EAAG,CAAE,CAAAL,QAAA,CAC7C,0BAED,CAAQ,CAAC,CACA,CAAC,GAlBCI,QAAQ,CAACC,EAmBd,CACX,CAAC,cAEFpB,IAAA,CAACN,QAAQ,EAAAqB,QAAA,cACPf,IAAA,CAACT,SAAS,EAACyC,OAAO,CAAE,CAAE,CAAChB,KAAK,CAAC,QAAQ,CAAAD,QAAA,cACnCf,IAAA,CAACH,GAAG,EAAC4B,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAlB,QAAA,cACjBf,IAAA,CAACJ,UAAU,EAACgC,OAAO,CAAC,WAAW,CAAAb,QAAA,CAAC,kGAEhC,CAAY,CAAC,CACV,CAAC,CACG,CAAC,CACJ,CACX,CACQ,CAAC,EACP,CAAC,CACM,CAAC,CACZ,CAAC,CAEZ,CAAC,CAED,cAAe,CAAAZ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}