{"ast": null, "code": "/**\n * This function create an object from keys, value and then assign to target\n *\n * @param {Object} obj : the target object to be assigned\n * @param {string[]} keys\n * @param {string | number} value\n *\n * @example\n * const source = {}\n * assignNestedKeys(source, ['palette', 'primary'], 'var(--palette-primary)')\n * console.log(source) // { palette: { primary: 'var(--palette-primary)' } }\n *\n * @example\n * const source = { palette: { primary: 'var(--palette-primary)' } }\n * assignNestedKeys(source, ['palette', 'secondary'], 'var(--palette-secondary)')\n * console.log(source) // { palette: { primary: 'var(--palette-primary)', secondary: 'var(--palette-secondary)' } }\n */\nexport const assignNestedKeys = function (obj, keys, value) {\n  let arrayKeys = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : [];\n  let temp = obj;\n  keys.forEach((k, index) => {\n    if (index === keys.length - 1) {\n      if (Array.isArray(temp)) {\n        temp[Number(k)] = value;\n      } else if (temp && typeof temp === 'object') {\n        temp[k] = value;\n      }\n    } else if (temp && typeof temp === 'object') {\n      if (!temp[k]) {\n        temp[k] = arrayKeys.includes(k) ? [] : {};\n      }\n      temp = temp[k];\n    }\n  });\n};\n\n/**\n *\n * @param {Object} obj : source object\n * @param {Function} callback : a function that will be called when\n *                   - the deepest key in source object is reached\n *                   - the value of the deepest key is NOT `undefined` | `null`\n *\n * @example\n * walkObjectDeep({ palette: { primary: { main: '#000000' } } }, console.log)\n * // ['palette', 'primary', 'main'] '#000000'\n */\nexport const walkObjectDeep = (obj, callback, shouldSkipPaths) => {\n  function recurse(object) {\n    let parentKeys = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    let arrayKeys = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n    Object.entries(object).forEach(_ref => {\n      let [key, value] = _ref;\n      if (!shouldSkipPaths || shouldSkipPaths && !shouldSkipPaths([...parentKeys, key])) {\n        if (value !== undefined && value !== null) {\n          if (typeof value === 'object' && Object.keys(value).length > 0) {\n            recurse(value, [...parentKeys, key], Array.isArray(value) ? [...arrayKeys, key] : arrayKeys);\n          } else {\n            callback([...parentKeys, key], value, arrayKeys);\n          }\n        }\n      }\n    });\n  }\n  recurse(obj);\n};\nconst getCssValue = (keys, value) => {\n  if (typeof value === 'number') {\n    if (['lineHeight', 'fontWeight', 'opacity', 'zIndex'].some(prop => keys.includes(prop))) {\n      // CSS property that are unitless\n      return value;\n    }\n    const lastKey = keys[keys.length - 1];\n    if (lastKey.toLowerCase().includes('opacity')) {\n      // opacity values are unitless\n      return value;\n    }\n    return \"\".concat(value, \"px\");\n  }\n  return value;\n};\n\n/**\n * a function that parse theme and return { css, vars }\n *\n * @param {Object} theme\n * @param {{\n *  prefix?: string,\n *  shouldSkipGeneratingVar?: (objectPathKeys: Array<string>, value: string | number) => boolean\n * }} options.\n *  `prefix`: The prefix of the generated CSS variables. This function does not change the value.\n *\n * @returns {{ css: Object, vars: Object }} `css` is the stylesheet, `vars` is an object to get css variable (same structure as theme).\n *\n * @example\n * const { css, vars } = parser({\n *   fontSize: 12,\n *   lineHeight: 1.2,\n *   palette: { primary: { 500: 'var(--color)' } }\n * }, { prefix: 'foo' })\n *\n * console.log(css) // { '--foo-fontSize': '12px', '--foo-lineHeight': 1.2, '--foo-palette-primary-500': 'var(--color)' }\n * console.log(vars) // { fontSize: 'var(--foo-fontSize)', lineHeight: 'var(--foo-lineHeight)', palette: { primary: { 500: 'var(--foo-palette-primary-500)' } } }\n */\nexport default function cssVarsParser(theme, options) {\n  const {\n    prefix,\n    shouldSkipGeneratingVar\n  } = options || {};\n  const css = {};\n  const vars = {};\n  const varsWithDefaults = {};\n  walkObjectDeep(theme, (keys, value, arrayKeys) => {\n    if (typeof value === 'string' || typeof value === 'number') {\n      if (!shouldSkipGeneratingVar || !shouldSkipGeneratingVar(keys, value)) {\n        // only create css & var if `shouldSkipGeneratingVar` return false\n        const cssVar = \"--\".concat(prefix ? \"\".concat(prefix, \"-\") : '').concat(keys.join('-'));\n        const resolvedValue = getCssValue(keys, value);\n        Object.assign(css, {\n          [cssVar]: resolvedValue\n        });\n        assignNestedKeys(vars, keys, \"var(\".concat(cssVar, \")\"), arrayKeys);\n        assignNestedKeys(varsWithDefaults, keys, \"var(\".concat(cssVar, \", \").concat(resolvedValue, \")\"), arrayKeys);\n      }\n    }\n  }, keys => keys[0] === 'vars' // skip 'vars/*' paths\n  );\n  return {\n    css,\n    vars,\n    varsWithDefaults\n  };\n}", "map": {"version": 3, "names": ["assignNestedKeys", "obj", "keys", "value", "arrayKeys", "arguments", "length", "undefined", "temp", "for<PERSON>ach", "k", "index", "Array", "isArray", "Number", "includes", "walkObjectDeep", "callback", "shouldSkipPaths", "recurse", "object", "parentKeys", "Object", "entries", "_ref", "key", "getCssValue", "some", "prop", "last<PERSON>ey", "toLowerCase", "concat", "cssVarsParser", "theme", "options", "prefix", "shouldSkipGeneratingVar", "css", "vars", "varsWithDefaults", "cssVar", "join", "resolvedValue", "assign"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/system/esm/cssVars/cssVarsParser.js"], "sourcesContent": ["/**\n * This function create an object from keys, value and then assign to target\n *\n * @param {Object} obj : the target object to be assigned\n * @param {string[]} keys\n * @param {string | number} value\n *\n * @example\n * const source = {}\n * assignNestedKeys(source, ['palette', 'primary'], 'var(--palette-primary)')\n * console.log(source) // { palette: { primary: 'var(--palette-primary)' } }\n *\n * @example\n * const source = { palette: { primary: 'var(--palette-primary)' } }\n * assignNestedKeys(source, ['palette', 'secondary'], 'var(--palette-secondary)')\n * console.log(source) // { palette: { primary: 'var(--palette-primary)', secondary: 'var(--palette-secondary)' } }\n */\nexport const assignNestedKeys = (obj, keys, value, arrayKeys = []) => {\n  let temp = obj;\n  keys.forEach((k, index) => {\n    if (index === keys.length - 1) {\n      if (Array.isArray(temp)) {\n        temp[Number(k)] = value;\n      } else if (temp && typeof temp === 'object') {\n        temp[k] = value;\n      }\n    } else if (temp && typeof temp === 'object') {\n      if (!temp[k]) {\n        temp[k] = arrayKeys.includes(k) ? [] : {};\n      }\n      temp = temp[k];\n    }\n  });\n};\n\n/**\n *\n * @param {Object} obj : source object\n * @param {Function} callback : a function that will be called when\n *                   - the deepest key in source object is reached\n *                   - the value of the deepest key is NOT `undefined` | `null`\n *\n * @example\n * walkObjectDeep({ palette: { primary: { main: '#000000' } } }, console.log)\n * // ['palette', 'primary', 'main'] '#000000'\n */\nexport const walkObjectDeep = (obj, callback, shouldSkipPaths) => {\n  function recurse(object, parentKeys = [], arrayKeys = []) {\n    Object.entries(object).forEach(([key, value]) => {\n      if (!shouldSkipPaths || shouldSkipPaths && !shouldSkipPaths([...parentKeys, key])) {\n        if (value !== undefined && value !== null) {\n          if (typeof value === 'object' && Object.keys(value).length > 0) {\n            recurse(value, [...parentKeys, key], Array.isArray(value) ? [...arrayKeys, key] : arrayKeys);\n          } else {\n            callback([...parentKeys, key], value, arrayKeys);\n          }\n        }\n      }\n    });\n  }\n  recurse(obj);\n};\nconst getCssValue = (keys, value) => {\n  if (typeof value === 'number') {\n    if (['lineHeight', 'fontWeight', 'opacity', 'zIndex'].some(prop => keys.includes(prop))) {\n      // CSS property that are unitless\n      return value;\n    }\n    const lastKey = keys[keys.length - 1];\n    if (lastKey.toLowerCase().includes('opacity')) {\n      // opacity values are unitless\n      return value;\n    }\n    return `${value}px`;\n  }\n  return value;\n};\n\n/**\n * a function that parse theme and return { css, vars }\n *\n * @param {Object} theme\n * @param {{\n *  prefix?: string,\n *  shouldSkipGeneratingVar?: (objectPathKeys: Array<string>, value: string | number) => boolean\n * }} options.\n *  `prefix`: The prefix of the generated CSS variables. This function does not change the value.\n *\n * @returns {{ css: Object, vars: Object }} `css` is the stylesheet, `vars` is an object to get css variable (same structure as theme).\n *\n * @example\n * const { css, vars } = parser({\n *   fontSize: 12,\n *   lineHeight: 1.2,\n *   palette: { primary: { 500: 'var(--color)' } }\n * }, { prefix: 'foo' })\n *\n * console.log(css) // { '--foo-fontSize': '12px', '--foo-lineHeight': 1.2, '--foo-palette-primary-500': 'var(--color)' }\n * console.log(vars) // { fontSize: 'var(--foo-fontSize)', lineHeight: 'var(--foo-lineHeight)', palette: { primary: { 500: 'var(--foo-palette-primary-500)' } } }\n */\nexport default function cssVarsParser(theme, options) {\n  const {\n    prefix,\n    shouldSkipGeneratingVar\n  } = options || {};\n  const css = {};\n  const vars = {};\n  const varsWithDefaults = {};\n  walkObjectDeep(theme, (keys, value, arrayKeys) => {\n    if (typeof value === 'string' || typeof value === 'number') {\n      if (!shouldSkipGeneratingVar || !shouldSkipGeneratingVar(keys, value)) {\n        // only create css & var if `shouldSkipGeneratingVar` return false\n        const cssVar = `--${prefix ? `${prefix}-` : ''}${keys.join('-')}`;\n        const resolvedValue = getCssValue(keys, value);\n        Object.assign(css, {\n          [cssVar]: resolvedValue\n        });\n        assignNestedKeys(vars, keys, `var(${cssVar})`, arrayKeys);\n        assignNestedKeys(varsWithDefaults, keys, `var(${cssVar}, ${resolvedValue})`, arrayKeys);\n      }\n    }\n  }, keys => keys[0] === 'vars' // skip 'vars/*' paths\n  );\n  return {\n    css,\n    vars,\n    varsWithDefaults\n  };\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,gBAAgB,GAAG,SAAAA,CAACC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAqB;EAAA,IAAnBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAC/D,IAAIG,IAAI,GAAGP,GAAG;EACdC,IAAI,CAACO,OAAO,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;IACzB,IAAIA,KAAK,KAAKT,IAAI,CAACI,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAIM,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE;QACvBA,IAAI,CAACM,MAAM,CAACJ,CAAC,CAAC,CAAC,GAAGP,KAAK;MACzB,CAAC,MAAM,IAAIK,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC3CA,IAAI,CAACE,CAAC,CAAC,GAAGP,KAAK;MACjB;IACF,CAAC,MAAM,IAAIK,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC3C,IAAI,CAACA,IAAI,CAACE,CAAC,CAAC,EAAE;QACZF,IAAI,CAACE,CAAC,CAAC,GAAGN,SAAS,CAACW,QAAQ,CAACL,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MAC3C;MACAF,IAAI,GAAGA,IAAI,CAACE,CAAC,CAAC;IAChB;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,cAAc,GAAGA,CAACf,GAAG,EAAEgB,QAAQ,EAAEC,eAAe,KAAK;EAChE,SAASC,OAAOA,CAACC,MAAM,EAAmC;IAAA,IAAjCC,UAAU,GAAAhB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;IAAA,IAAED,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;IACtDiB,MAAM,CAACC,OAAO,CAACH,MAAM,CAAC,CAACX,OAAO,CAACe,IAAA,IAAkB;MAAA,IAAjB,CAACC,GAAG,EAAEtB,KAAK,CAAC,GAAAqB,IAAA;MAC1C,IAAI,CAACN,eAAe,IAAIA,eAAe,IAAI,CAACA,eAAe,CAAC,CAAC,GAAGG,UAAU,EAAEI,GAAG,CAAC,CAAC,EAAE;QACjF,IAAItB,KAAK,KAAKI,SAAS,IAAIJ,KAAK,KAAK,IAAI,EAAE;UACzC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAImB,MAAM,CAACpB,IAAI,CAACC,KAAK,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;YAC9Da,OAAO,CAAChB,KAAK,EAAE,CAAC,GAAGkB,UAAU,EAAEI,GAAG,CAAC,EAAEb,KAAK,CAACC,OAAO,CAACV,KAAK,CAAC,GAAG,CAAC,GAAGC,SAAS,EAAEqB,GAAG,CAAC,GAAGrB,SAAS,CAAC;UAC9F,CAAC,MAAM;YACLa,QAAQ,CAAC,CAAC,GAAGI,UAAU,EAAEI,GAAG,CAAC,EAAEtB,KAAK,EAAEC,SAAS,CAAC;UAClD;QACF;MACF;IACF,CAAC,CAAC;EACJ;EACAe,OAAO,CAAClB,GAAG,CAAC;AACd,CAAC;AACD,MAAMyB,WAAW,GAAGA,CAACxB,IAAI,EAAEC,KAAK,KAAK;EACnC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAI,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,CAAC,CAACwB,IAAI,CAACC,IAAI,IAAI1B,IAAI,CAACa,QAAQ,CAACa,IAAI,CAAC,CAAC,EAAE;MACvF;MACA,OAAOzB,KAAK;IACd;IACA,MAAM0B,OAAO,GAAG3B,IAAI,CAACA,IAAI,CAACI,MAAM,GAAG,CAAC,CAAC;IACrC,IAAIuB,OAAO,CAACC,WAAW,CAAC,CAAC,CAACf,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC7C;MACA,OAAOZ,KAAK;IACd;IACA,UAAA4B,MAAA,CAAU5B,KAAK;EACjB;EACA,OAAOA,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAAS6B,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACpD,MAAM;IACJC,MAAM;IACNC;EACF,CAAC,GAAGF,OAAO,IAAI,CAAC,CAAC;EACjB,MAAMG,GAAG,GAAG,CAAC,CAAC;EACd,MAAMC,IAAI,GAAG,CAAC,CAAC;EACf,MAAMC,gBAAgB,GAAG,CAAC,CAAC;EAC3BvB,cAAc,CAACiB,KAAK,EAAE,CAAC/B,IAAI,EAAEC,KAAK,EAAEC,SAAS,KAAK;IAChD,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC1D,IAAI,CAACiC,uBAAuB,IAAI,CAACA,uBAAuB,CAAClC,IAAI,EAAEC,KAAK,CAAC,EAAE;QACrE;QACA,MAAMqC,MAAM,QAAAT,MAAA,CAAQI,MAAM,MAAAJ,MAAA,CAAMI,MAAM,SAAM,EAAE,EAAAJ,MAAA,CAAG7B,IAAI,CAACuC,IAAI,CAAC,GAAG,CAAC,CAAE;QACjE,MAAMC,aAAa,GAAGhB,WAAW,CAACxB,IAAI,EAAEC,KAAK,CAAC;QAC9CmB,MAAM,CAACqB,MAAM,CAACN,GAAG,EAAE;UACjB,CAACG,MAAM,GAAGE;QACZ,CAAC,CAAC;QACF1C,gBAAgB,CAACsC,IAAI,EAAEpC,IAAI,SAAA6B,MAAA,CAASS,MAAM,QAAKpC,SAAS,CAAC;QACzDJ,gBAAgB,CAACuC,gBAAgB,EAAErC,IAAI,SAAA6B,MAAA,CAASS,MAAM,QAAAT,MAAA,CAAKW,aAAa,QAAKtC,SAAS,CAAC;MACzF;IACF;EACF,CAAC,EAAEF,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC;EAC9B,CAAC;EACD,OAAO;IACLmC,GAAG;IACHC,IAAI;IACJC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}