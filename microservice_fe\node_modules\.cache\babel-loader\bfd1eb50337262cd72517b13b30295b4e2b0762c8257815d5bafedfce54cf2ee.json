{"ast": null, "code": "export{};", "map": {"version": 3, "names": [], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/models/Customer.ts"], "sourcesContent": ["export interface Customer {\n  id?: number;\n  fullName: string;\n  companyName?: string;\n  email?: string;\n  phoneNumber: string;\n  address: string;\n  isDeleted?: boolean;\n  createdAt?: string | number[] | Date;\n  updatedAt?: string | number[] | Date;\n}\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}