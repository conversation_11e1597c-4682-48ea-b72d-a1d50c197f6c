{"ast": null, "code": "// Format currency in VND\nexport const formatCurrency=value=>{return new Intl.NumberFormat('vi-VN',{style:'currency',currency:'VND',minimumFractionDigits:0,maximumFractionDigits:0}).format(value);};// Format date to Vietnamese format (DD/MM/YYYY)\nexport const formatDate=dateString=>{try{const date=new Date(dateString);if(isNaN(date.getTime())){return dateString;}const day=String(date.getDate()).padStart(2,'0');const month=String(date.getMonth()+1).padStart(2,'0');const year=date.getFullYear();return\"\".concat(day,\"/\").concat(month,\"/\").concat(year);}catch(error){return dateString;}};// Format date and time to Vietnamese format (HH:MM DD/MM/YYYY)\nexport const formatDateTime=dateString=>{try{const date=new Date(dateString);if(isNaN(date.getTime())){return dateString;}const hours=String(date.getHours()).padStart(2,'0');const minutes=String(date.getMinutes()).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');const month=String(date.getMonth()+1).padStart(2,'0');const year=date.getFullYear();return\"\".concat(hours,\":\").concat(minutes,\" \").concat(day,\"/\").concat(month,\"/\").concat(year);}catch(error){return dateString;}};", "map": {"version": 3, "names": ["formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDate", "dateString", "date", "Date", "isNaN", "getTime", "day", "String", "getDate", "padStart", "month", "getMonth", "year", "getFullYear", "concat", "error", "formatDateTime", "hours", "getHours", "minutes", "getMinutes"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/utils/formatters.ts"], "sourcesContent": ["// Format currency in VND\nexport const formatCurrency = (value: number): string => {\n  return new Intl.NumberFormat('vi-VN', {\n    style: 'currency',\n    currency: 'VND',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0,\n  }).format(value);\n};\n\n// Format date to Vietnamese format (DD/MM/YYYY)\nexport const formatDate = (dateString: string): string => {\n  try {\n    const date = new Date(dateString);\n    if (isNaN(date.getTime())) {\n      return dateString;\n    }\n    const day = String(date.getDate()).padStart(2, '0');\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const year = date.getFullYear();\n    return `${day}/${month}/${year}`;\n  } catch (error) {\n    return dateString;\n  }\n};\n\n// Format date and time to Vietnamese format (HH:MM DD/MM/YYYY)\nexport const formatDateTime = (dateString: string): string => {\n  try {\n    const date = new Date(dateString);\n    if (isNaN(date.getTime())) {\n      return dateString;\n    }\n    const hours = String(date.getHours()).padStart(2, '0');\n    const minutes = String(date.getMinutes()).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const year = date.getFullYear();\n    return `${hours}:${minutes} ${day}/${month}/${year}`;\n  } catch (error) {\n    return dateString;\n  }\n};\n"], "mappings": "AAAA;AACA,MAAO,MAAM,CAAAA,cAAc,CAAIC,KAAa,EAAa,CACvD,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,KAAK,CACfC,qBAAqB,CAAE,CAAC,CACxBC,qBAAqB,CAAE,CACzB,CAAC,CAAC,CAACC,MAAM,CAACP,KAAK,CAAC,CAClB,CAAC,CAED;AACA,MAAO,MAAM,CAAAQ,UAAU,CAAIC,UAAkB,EAAa,CACxD,GAAI,CACF,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,GAAIG,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,CAAE,CACzB,MAAO,CAAAJ,UAAU,CACnB,CACA,KAAM,CAAAK,GAAG,CAAGC,MAAM,CAACL,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACnD,KAAM,CAAAC,KAAK,CAAGH,MAAM,CAACL,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,KAAM,CAAAG,IAAI,CAAGV,IAAI,CAACW,WAAW,CAAC,CAAC,CAC/B,SAAAC,MAAA,CAAUR,GAAG,MAAAQ,MAAA,CAAIJ,KAAK,MAAAI,MAAA,CAAIF,IAAI,EAChC,CAAE,MAAOG,KAAK,CAAE,CACd,MAAO,CAAAd,UAAU,CACnB,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAe,cAAc,CAAIf,UAAkB,EAAa,CAC5D,GAAI,CACF,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,GAAIG,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,CAAE,CACzB,MAAO,CAAAJ,UAAU,CACnB,CACA,KAAM,CAAAgB,KAAK,CAAGV,MAAM,CAACL,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAAC,CAACT,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACtD,KAAM,CAAAU,OAAO,CAAGZ,MAAM,CAACL,IAAI,CAACkB,UAAU,CAAC,CAAC,CAAC,CAACX,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,KAAM,CAAAH,GAAG,CAAGC,MAAM,CAACL,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACnD,KAAM,CAAAC,KAAK,CAAGH,MAAM,CAACL,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,KAAM,CAAAG,IAAI,CAAGV,IAAI,CAACW,WAAW,CAAC,CAAC,CAC/B,SAAAC,MAAA,CAAUG,KAAK,MAAAH,MAAA,CAAIK,OAAO,MAAAL,MAAA,CAAIR,GAAG,MAAAQ,MAAA,CAAIJ,KAAK,MAAAI,MAAA,CAAIF,IAAI,EACpD,CAAE,MAAOG,KAAK,CAAE,CACd,MAAO,CAAAd,UAAU,CACnB,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}