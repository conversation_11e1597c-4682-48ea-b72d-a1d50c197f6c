{"ast": null, "code": "export const ContractStatusMap={0:'Chờ duyệt',1:'Đang hoạt động',2:'<PERSON><PERSON> hoàn thành',3:'Đã hủy'};", "map": {"version": 3, "names": ["ContractStatusMap"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/models/CustomerContract.ts"], "sourcesContent": ["import { JobDetail } from './JobDetail';\n\nexport interface CustomerContract {\n  id?: number;\n  startingDate: string;\n  endingDate: string;\n  totalAmount: number;\n  totalPaid?: number;\n  description?: string;\n  customerId: number;\n  customerName?: string; // For display purposes\n  jobDetails: JobDetail[];\n  status?: number; // 0: Pending, 1: Active, 2: Completed, 3: Cancelled\n  isDeleted?: boolean;\n  createdAt?: string;\n  updatedAt?: string;\n}\n\nexport const ContractStatusMap: Record<number, string> = {\n  0: 'Chờ duyệt',\n  1: 'Đang hoạt động',\n  2: 'Đã hoàn thành',\n  3: 'Đã hủy'\n};\n"], "mappings": "AAkBA,MAAO,MAAM,CAAAA,iBAAyC,CAAG,CACvD,CAAC,CAAE,WAAW,CACd,CAAC,CAAE,gBAAgB,CACnB,CAAC,CAAE,eAAe,CAClB,CAAC,CAAE,QACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}