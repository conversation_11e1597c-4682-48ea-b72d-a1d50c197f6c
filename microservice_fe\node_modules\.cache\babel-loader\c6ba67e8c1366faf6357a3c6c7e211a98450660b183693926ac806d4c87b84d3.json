{"ast": null, "code": "import{calculateWorkingDates}from'./workingDaysUtils';/**\n * Calculate the total contract amount based on work shifts and contract dates\n * @param contract The customer contract with job details and work shifts\n * @returns Object containing total amount and calculation breakdown\n */export const calculateContractAmount=contract=>{if(!contract.jobDetails||contract.jobDetails.length===0){return{totalAmount:0,breakdown:[],summary:{totalWorkShifts:0,totalWorkers:0,totalWorkingDays:0,contractDuration:0}};}const breakdown=[];let totalAmount=0;let totalWorkShifts=0;let totalWorkers=0;let totalWorkingDays=0;// Calculate contract duration in days\nconst contractDuration=contract.startingDate&&contract.endingDate?Math.ceil((new Date(contract.endingDate).getTime()-new Date(contract.startingDate).getTime())/(1000*60*60*24))+1:0;contract.jobDetails.forEach((jobDetail,jobIndex)=>{if(!jobDetail.workShifts||jobDetail.workShifts.length===0)return;const jobBreakdown={jobDetailIndex:jobIndex,jobCategoryName:jobDetail.jobCategoryName||\"C\\xF4ng vi\\u1EC7c \".concat(jobIndex+1),workShifts:[],jobTotal:0};jobDetail.workShifts.forEach((workShift,shiftIndex)=>{if(!workShift.salary||!workShift.numberOfWorkers||!workShift.workingDays)return;// Use job detail dates if available, otherwise use contract dates\nconst startDate=jobDetail.startDate||contract.startingDate;const endDate=jobDetail.endDate||contract.endingDate;if(!startDate||!endDate)return;// Calculate actual working dates for this shift\nconst workingDates=calculateWorkingDates(startDate,endDate,workShift.workingDays);const workingDaysCount=workingDates.length;// Calculate amount for this shift\nconst shiftAmount=workShift.salary*workShift.numberOfWorkers*workingDaysCount;const shiftBreakdown={shiftIndex,startTime:workShift.startTime,endTime:workShift.endTime,salary:workShift.salary,numberOfWorkers:workShift.numberOfWorkers,workingDaysCount,workingDates,shiftAmount};jobBreakdown.workShifts.push(shiftBreakdown);jobBreakdown.jobTotal+=shiftAmount;totalAmount+=shiftAmount;totalWorkShifts++;totalWorkers+=workShift.numberOfWorkers;totalWorkingDays+=workingDaysCount;});if(jobBreakdown.workShifts.length>0){breakdown.push(jobBreakdown);}});return{totalAmount,breakdown,summary:{totalWorkShifts,totalWorkers,totalWorkingDays,contractDuration}};};/**\n * Interface for contract calculation breakdown\n *//**\n * Interface for work shift calculation breakdown\n *//**\n * Interface for contract calculation summary\n *//**\n * Format calculation breakdown for display\n * @param breakdown The calculation breakdown\n * @returns Formatted string for display\n */export const formatCalculationBreakdown=breakdown=>{if(breakdown.length===0)return'Chưa có thông tin tính toán';let result='Chi tiết tính toán:\\n\\n';breakdown.forEach((job,index)=>{result+=\"\".concat(job.jobCategoryName,\":\\n\");job.workShifts.forEach((shift,shiftIndex)=>{result+=\"  Ca \".concat(shiftIndex+1,\" (\").concat(shift.startTime,\" - \").concat(shift.endTime,\"):\\n\");result+=\"    L\\u01B0\\u01A1ng: \".concat(shift.salary.toLocaleString('vi-VN'),\" VN\\u0110/ca\\n\");result+=\"    S\\u1ED1 ng\\u01B0\\u1EDDi: \".concat(shift.numberOfWorkers,\"\\n\");result+=\"    S\\u1ED1 ng\\xE0y l\\xE0m vi\\u1EC7c: \".concat(shift.workingDaysCount,\"\\n\");result+=\"    Th\\xE0nh ti\\u1EC1n: \".concat(shift.shiftAmount.toLocaleString('vi-VN'),\" VN\\u0110\\n\\n\");});result+=\"  T\\u1ED5ng \".concat(job.jobCategoryName,\": \").concat(job.jobTotal.toLocaleString('vi-VN'),\" VN\\u0110\\n\\n\");});return result;};/**\n * Calculate contract start and end dates automatically from job details\n * @param contract The customer contract with job details\n * @returns Object containing calculated start and end dates\n */export const calculateContractDates=contract=>{if(!contract.jobDetails||contract.jobDetails.length===0){return{startingDate:'',endingDate:''};}let earliestStart='';let latestEnd='';contract.jobDetails.forEach(jobDetail=>{if(jobDetail.startDate){if(!earliestStart||jobDetail.startDate<earliestStart){earliestStart=jobDetail.startDate;}}if(jobDetail.endDate){if(!latestEnd||jobDetail.endDate>latestEnd){latestEnd=jobDetail.endDate;}}});return{startingDate:earliestStart,endingDate:latestEnd};};", "map": {"version": 3, "names": ["calculateWorkingDates", "calculateContractAmount", "contract", "jobDetails", "length", "totalAmount", "breakdown", "summary", "totalWorkShifts", "totalWorkers", "totalWorkingDays", "contractDuration", "startingDate", "endingDate", "Math", "ceil", "Date", "getTime", "for<PERSON>ach", "jobDetail", "jobIndex", "workShifts", "jobBreakdown", "jobDetailIndex", "jobCategoryName", "concat", "jobTotal", "workShift", "shiftIndex", "salary", "numberOfWorkers", "workingDays", "startDate", "endDate", "workingDates", "workingDaysCount", "shiftAmount", "shiftBreakdown", "startTime", "endTime", "push", "formatCalculationBreakdown", "result", "job", "index", "shift", "toLocaleString", "calculateContractDates", "earliestStart", "latestEnd"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/utils/contractCalculationUtils.ts"], "sourcesContent": ["import { CustomerContract, JobDetail, WorkShift } from '../models';\nimport { calculateWorkingDates } from './workingDaysUtils';\n\n/**\n * Calculate the total contract amount based on work shifts and contract dates\n * @param contract The customer contract with job details and work shifts\n * @returns Object containing total amount and calculation breakdown\n */\nexport const calculateContractAmount = (contract: Partial<CustomerContract>): {\n  totalAmount: number;\n  breakdown: ContractCalculationBreakdown[];\n  summary: ContractCalculationSummary;\n} => {\n  if (!contract.jobDetails || contract.jobDetails.length === 0) {\n    return {\n      totalAmount: 0,\n      breakdown: [],\n      summary: {\n        totalWorkShifts: 0,\n        totalWorkers: 0,\n        totalWorkingDays: 0,\n        contractDuration: 0\n      }\n    };\n  }\n\n  const breakdown: ContractCalculationBreakdown[] = [];\n  let totalAmount = 0;\n  let totalWorkShifts = 0;\n  let totalWorkers = 0;\n  let totalWorkingDays = 0;\n\n  // Calculate contract duration in days\n  const contractDuration = contract.startingDate && contract.endingDate\n    ? Math.ceil((new Date(contract.endingDate).getTime() - new Date(contract.startingDate).getTime()) / (1000 * 60 * 60 * 24)) + 1\n    : 0;\n\n  contract.jobDetails.forEach((jobDetail, jobIndex) => {\n    if (!jobDetail.workShifts || jobDetail.workShifts.length === 0) return;\n\n    const jobBreakdown: ContractCalculationBreakdown = {\n      jobDetailIndex: jobIndex,\n      jobCategoryName: jobDetail.jobCategoryName || `Công việc ${jobIndex + 1}`,\n      workShifts: [],\n      jobTotal: 0\n    };\n\n    jobDetail.workShifts.forEach((workShift, shiftIndex) => {\n      if (!workShift.salary || !workShift.numberOfWorkers || !workShift.workingDays) return;\n\n      // Use job detail dates if available, otherwise use contract dates\n      const startDate = jobDetail.startDate || contract.startingDate;\n      const endDate = jobDetail.endDate || contract.endingDate;\n\n      if (!startDate || !endDate) return;\n\n      // Calculate actual working dates for this shift\n      const workingDates = calculateWorkingDates(startDate, endDate, workShift.workingDays);\n      const workingDaysCount = workingDates.length;\n\n      // Calculate amount for this shift\n      const shiftAmount = workShift.salary * workShift.numberOfWorkers * workingDaysCount;\n\n      const shiftBreakdown: WorkShiftCalculationBreakdown = {\n        shiftIndex,\n        startTime: workShift.startTime,\n        endTime: workShift.endTime,\n        salary: workShift.salary,\n        numberOfWorkers: workShift.numberOfWorkers,\n        workingDaysCount,\n        workingDates,\n        shiftAmount\n      };\n\n      jobBreakdown.workShifts.push(shiftBreakdown);\n      jobBreakdown.jobTotal += shiftAmount;\n      totalAmount += shiftAmount;\n      totalWorkShifts++;\n      totalWorkers += workShift.numberOfWorkers;\n      totalWorkingDays += workingDaysCount;\n    });\n\n    if (jobBreakdown.workShifts.length > 0) {\n      breakdown.push(jobBreakdown);\n    }\n  });\n\n  return {\n    totalAmount,\n    breakdown,\n    summary: {\n      totalWorkShifts,\n      totalWorkers,\n      totalWorkingDays,\n      contractDuration\n    }\n  };\n};\n\n/**\n * Interface for contract calculation breakdown\n */\nexport interface ContractCalculationBreakdown {\n  jobDetailIndex: number;\n  jobCategoryName: string;\n  workShifts: WorkShiftCalculationBreakdown[];\n  jobTotal: number;\n}\n\n/**\n * Interface for work shift calculation breakdown\n */\nexport interface WorkShiftCalculationBreakdown {\n  shiftIndex: number;\n  startTime: string;\n  endTime: string;\n  salary: number;\n  numberOfWorkers: number;\n  workingDaysCount: number;\n  workingDates: string[];\n  shiftAmount: number;\n}\n\n/**\n * Interface for contract calculation summary\n */\nexport interface ContractCalculationSummary {\n  totalWorkShifts: number;\n  totalWorkers: number;\n  totalWorkingDays: number;\n  contractDuration: number;\n}\n\n/**\n * Format calculation breakdown for display\n * @param breakdown The calculation breakdown\n * @returns Formatted string for display\n */\nexport const formatCalculationBreakdown = (breakdown: ContractCalculationBreakdown[]): string => {\n  if (breakdown.length === 0) return 'Chưa có thông tin tính toán';\n\n  let result = 'Chi tiết tính toán:\\n\\n';\n\n  breakdown.forEach((job, index) => {\n    result += `${job.jobCategoryName}:\\n`;\n\n    job.workShifts.forEach((shift, shiftIndex) => {\n      result += `  Ca ${shiftIndex + 1} (${shift.startTime} - ${shift.endTime}):\\n`;\n      result += `    Lương: ${shift.salary.toLocaleString('vi-VN')} VNĐ/ca\\n`;\n      result += `    Số người: ${shift.numberOfWorkers}\\n`;\n      result += `    Số ngày làm việc: ${shift.workingDaysCount}\\n`;\n      result += `    Thành tiền: ${shift.shiftAmount.toLocaleString('vi-VN')} VNĐ\\n\\n`;\n    });\n\n    result += `  Tổng ${job.jobCategoryName}: ${job.jobTotal.toLocaleString('vi-VN')} VNĐ\\n\\n`;\n  });\n\n  return result;\n};\n\n/**\n * Calculate contract start and end dates automatically from job details\n * @param contract The customer contract with job details\n * @returns Object containing calculated start and end dates\n */\nexport const calculateContractDates = (contract: Partial<CustomerContract>): {\n  startingDate: string;\n  endingDate: string;\n} => {\n  if (!contract.jobDetails || contract.jobDetails.length === 0) {\n    return {\n      startingDate: '',\n      endingDate: ''\n    };\n  }\n\n  let earliestStart: string = '';\n  let latestEnd: string = '';\n\n  contract.jobDetails.forEach(jobDetail => {\n    if (jobDetail.startDate) {\n      if (!earliestStart || jobDetail.startDate < earliestStart) {\n        earliestStart = jobDetail.startDate;\n      }\n    }\n\n    if (jobDetail.endDate) {\n      if (!latestEnd || jobDetail.endDate > latestEnd) {\n        latestEnd = jobDetail.endDate;\n      }\n    }\n  });\n\n  return {\n    startingDate: earliestStart,\n    endingDate: latestEnd\n  };\n};\n"], "mappings": "AACA,OAASA,qBAAqB,KAAQ,oBAAoB,CAE1D;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,uBAAuB,CAAIC,QAAmC,EAItE,CACH,GAAI,CAACA,QAAQ,CAACC,UAAU,EAAID,QAAQ,CAACC,UAAU,CAACC,MAAM,GAAK,CAAC,CAAE,CAC5D,MAAO,CACLC,WAAW,CAAE,CAAC,CACdC,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,CACPC,eAAe,CAAE,CAAC,CAClBC,YAAY,CAAE,CAAC,CACfC,gBAAgB,CAAE,CAAC,CACnBC,gBAAgB,CAAE,CACpB,CACF,CAAC,CACH,CAEA,KAAM,CAAAL,SAAyC,CAAG,EAAE,CACpD,GAAI,CAAAD,WAAW,CAAG,CAAC,CACnB,GAAI,CAAAG,eAAe,CAAG,CAAC,CACvB,GAAI,CAAAC,YAAY,CAAG,CAAC,CACpB,GAAI,CAAAC,gBAAgB,CAAG,CAAC,CAExB;AACA,KAAM,CAAAC,gBAAgB,CAAGT,QAAQ,CAACU,YAAY,EAAIV,QAAQ,CAACW,UAAU,CACjEC,IAAI,CAACC,IAAI,CAAC,CAAC,GAAI,CAAAC,IAAI,CAACd,QAAQ,CAACW,UAAU,CAAC,CAACI,OAAO,CAAC,CAAC,CAAG,GAAI,CAAAD,IAAI,CAACd,QAAQ,CAACU,YAAY,CAAC,CAACK,OAAO,CAAC,CAAC,GAAK,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAC,CAAC,CAAG,CAAC,CAC5H,CAAC,CAELf,QAAQ,CAACC,UAAU,CAACe,OAAO,CAAC,CAACC,SAAS,CAAEC,QAAQ,GAAK,CACnD,GAAI,CAACD,SAAS,CAACE,UAAU,EAAIF,SAAS,CAACE,UAAU,CAACjB,MAAM,GAAK,CAAC,CAAE,OAEhE,KAAM,CAAAkB,YAA0C,CAAG,CACjDC,cAAc,CAAEH,QAAQ,CACxBI,eAAe,CAAEL,SAAS,CAACK,eAAe,uBAAAC,MAAA,CAAiBL,QAAQ,CAAG,CAAC,CAAE,CACzEC,UAAU,CAAE,EAAE,CACdK,QAAQ,CAAE,CACZ,CAAC,CAEDP,SAAS,CAACE,UAAU,CAACH,OAAO,CAAC,CAACS,SAAS,CAAEC,UAAU,GAAK,CACtD,GAAI,CAACD,SAAS,CAACE,MAAM,EAAI,CAACF,SAAS,CAACG,eAAe,EAAI,CAACH,SAAS,CAACI,WAAW,CAAE,OAE/E;AACA,KAAM,CAAAC,SAAS,CAAGb,SAAS,CAACa,SAAS,EAAI9B,QAAQ,CAACU,YAAY,CAC9D,KAAM,CAAAqB,OAAO,CAAGd,SAAS,CAACc,OAAO,EAAI/B,QAAQ,CAACW,UAAU,CAExD,GAAI,CAACmB,SAAS,EAAI,CAACC,OAAO,CAAE,OAE5B;AACA,KAAM,CAAAC,YAAY,CAAGlC,qBAAqB,CAACgC,SAAS,CAAEC,OAAO,CAAEN,SAAS,CAACI,WAAW,CAAC,CACrF,KAAM,CAAAI,gBAAgB,CAAGD,YAAY,CAAC9B,MAAM,CAE5C;AACA,KAAM,CAAAgC,WAAW,CAAGT,SAAS,CAACE,MAAM,CAAGF,SAAS,CAACG,eAAe,CAAGK,gBAAgB,CAEnF,KAAM,CAAAE,cAA6C,CAAG,CACpDT,UAAU,CACVU,SAAS,CAAEX,SAAS,CAACW,SAAS,CAC9BC,OAAO,CAAEZ,SAAS,CAACY,OAAO,CAC1BV,MAAM,CAAEF,SAAS,CAACE,MAAM,CACxBC,eAAe,CAAEH,SAAS,CAACG,eAAe,CAC1CK,gBAAgB,CAChBD,YAAY,CACZE,WACF,CAAC,CAEDd,YAAY,CAACD,UAAU,CAACmB,IAAI,CAACH,cAAc,CAAC,CAC5Cf,YAAY,CAACI,QAAQ,EAAIU,WAAW,CACpC/B,WAAW,EAAI+B,WAAW,CAC1B5B,eAAe,EAAE,CACjBC,YAAY,EAAIkB,SAAS,CAACG,eAAe,CACzCpB,gBAAgB,EAAIyB,gBAAgB,CACtC,CAAC,CAAC,CAEF,GAAIb,YAAY,CAACD,UAAU,CAACjB,MAAM,CAAG,CAAC,CAAE,CACtCE,SAAS,CAACkC,IAAI,CAAClB,YAAY,CAAC,CAC9B,CACF,CAAC,CAAC,CAEF,MAAO,CACLjB,WAAW,CACXC,SAAS,CACTC,OAAO,CAAE,CACPC,eAAe,CACfC,YAAY,CACZC,gBAAgB,CAChBC,gBACF,CACF,CAAC,CACH,CAAC,CAED;AACA;AACA,GAQA;AACA;AACA,GAYA;AACA;AACA,GAQA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAA8B,0BAA0B,CAAInC,SAAyC,EAAa,CAC/F,GAAIA,SAAS,CAACF,MAAM,GAAK,CAAC,CAAE,MAAO,6BAA6B,CAEhE,GAAI,CAAAsC,MAAM,CAAG,yBAAyB,CAEtCpC,SAAS,CAACY,OAAO,CAAC,CAACyB,GAAG,CAAEC,KAAK,GAAK,CAChCF,MAAM,KAAAjB,MAAA,CAAOkB,GAAG,CAACnB,eAAe,OAAK,CAErCmB,GAAG,CAACtB,UAAU,CAACH,OAAO,CAAC,CAAC2B,KAAK,CAAEjB,UAAU,GAAK,CAC5Cc,MAAM,UAAAjB,MAAA,CAAYG,UAAU,CAAG,CAAC,OAAAH,MAAA,CAAKoB,KAAK,CAACP,SAAS,QAAAb,MAAA,CAAMoB,KAAK,CAACN,OAAO,QAAM,CAC7EG,MAAM,0BAAAjB,MAAA,CAAkBoB,KAAK,CAAChB,MAAM,CAACiB,cAAc,CAAC,OAAO,CAAC,kBAAW,CACvEJ,MAAM,kCAAAjB,MAAA,CAAqBoB,KAAK,CAACf,eAAe,MAAI,CACpDY,MAAM,2CAAAjB,MAAA,CAA6BoB,KAAK,CAACV,gBAAgB,MAAI,CAC7DO,MAAM,6BAAAjB,MAAA,CAAuBoB,KAAK,CAACT,WAAW,CAACU,cAAc,CAAC,OAAO,CAAC,iBAAU,CAClF,CAAC,CAAC,CAEFJ,MAAM,iBAAAjB,MAAA,CAAckB,GAAG,CAACnB,eAAe,OAAAC,MAAA,CAAKkB,GAAG,CAACjB,QAAQ,CAACoB,cAAc,CAAC,OAAO,CAAC,iBAAU,CAC5F,CAAC,CAAC,CAEF,MAAO,CAAAJ,MAAM,CACf,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAK,sBAAsB,CAAI7C,QAAmC,EAGrE,CACH,GAAI,CAACA,QAAQ,CAACC,UAAU,EAAID,QAAQ,CAACC,UAAU,CAACC,MAAM,GAAK,CAAC,CAAE,CAC5D,MAAO,CACLQ,YAAY,CAAE,EAAE,CAChBC,UAAU,CAAE,EACd,CAAC,CACH,CAEA,GAAI,CAAAmC,aAAqB,CAAG,EAAE,CAC9B,GAAI,CAAAC,SAAiB,CAAG,EAAE,CAE1B/C,QAAQ,CAACC,UAAU,CAACe,OAAO,CAACC,SAAS,EAAI,CACvC,GAAIA,SAAS,CAACa,SAAS,CAAE,CACvB,GAAI,CAACgB,aAAa,EAAI7B,SAAS,CAACa,SAAS,CAAGgB,aAAa,CAAE,CACzDA,aAAa,CAAG7B,SAAS,CAACa,SAAS,CACrC,CACF,CAEA,GAAIb,SAAS,CAACc,OAAO,CAAE,CACrB,GAAI,CAACgB,SAAS,EAAI9B,SAAS,CAACc,OAAO,CAAGgB,SAAS,CAAE,CAC/CA,SAAS,CAAG9B,SAAS,CAACc,OAAO,CAC/B,CACF,CACF,CAAC,CAAC,CAEF,MAAO,CACLrB,YAAY,CAAEoC,aAAa,CAC3BnC,UAAU,CAAEoC,SACd,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}