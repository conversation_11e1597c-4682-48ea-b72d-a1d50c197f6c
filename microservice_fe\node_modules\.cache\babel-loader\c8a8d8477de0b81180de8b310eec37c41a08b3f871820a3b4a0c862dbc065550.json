{"ast": null, "code": "'use client';\n\nimport _objectSpread from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"BackdropComponent\", \"BackdropProps\", \"classes\", \"className\", \"closeAfterTransition\", \"children\", \"container\", \"component\", \"components\", \"componentsProps\", \"disableAutoFocus\", \"disableEnforceFocus\", \"disableEscapeKeyDown\", \"disablePortal\", \"disableRestoreFocus\", \"disableScrollLock\", \"hideBackdrop\", \"keepMounted\", \"onClose\", \"onTransitionEnter\", \"onTransitionExited\", \"open\", \"slotProps\", \"slots\", \"theme\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport FocusTrap from \"../Unstable_TrapFocus/index.js\";\nimport Portal from \"../Portal/index.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Backdrop from \"../Backdrop/index.js\";\nimport useModal from \"./useModal.js\";\nimport { getModalUtilityClass } from \"./modalClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    open,\n    exited,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', !open && exited && 'hidden'],\n    backdrop: ['backdrop']\n  };\n  return composeClasses(slots, getModalUtilityClass, classes);\n};\nconst ModalRoot = styled('div', {\n  name: 'MuiModal',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.open && ownerState.exited && styles.hidden];\n  }\n})(memoTheme(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    position: 'fixed',\n    zIndex: (theme.vars || theme).zIndex.modal,\n    right: 0,\n    bottom: 0,\n    top: 0,\n    left: 0,\n    variants: [{\n      props: _ref2 => {\n        let {\n          ownerState\n        } = _ref2;\n        return !ownerState.open && ownerState.exited;\n      },\n      style: {\n        visibility: 'hidden'\n      }\n    }]\n  };\n}));\nconst ModalBackdrop = styled(Backdrop, {\n  name: 'MuiModal',\n  slot: 'Backdrop'\n})({\n  zIndex: -1\n});\n\n/**\n * Modal is a lower-level construct that is leveraged by the following components:\n *\n * - [Dialog](/material-ui/api/dialog/)\n * - [Drawer](/material-ui/api/drawer/)\n * - [Menu](/material-ui/api/menu/)\n * - [Popover](/material-ui/api/popover/)\n *\n * If you are creating a modal dialog, you probably want to use the [Dialog](/material-ui/api/dialog/) component\n * rather than directly using Modal.\n *\n * This component shares many concepts with [react-overlays](https://react-bootstrap.github.io/react-overlays/#modals).\n */\nconst Modal = /*#__PURE__*/React.forwardRef(function Modal(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiModal',\n    props: inProps\n  });\n  const {\n      BackdropComponent = ModalBackdrop,\n      BackdropProps,\n      classes: classesProp,\n      className,\n      closeAfterTransition = false,\n      children,\n      container,\n      component,\n      components = {},\n      componentsProps = {},\n      disableAutoFocus = false,\n      disableEnforceFocus = false,\n      disableEscapeKeyDown = false,\n      disablePortal = false,\n      disableRestoreFocus = false,\n      disableScrollLock = false,\n      hideBackdrop = false,\n      keepMounted = false,\n      onClose,\n      onTransitionEnter,\n      onTransitionExited,\n      open,\n      slotProps = {},\n      slots = {},\n      // eslint-disable-next-line react/prop-types\n      theme\n    } = props,\n    other = _objectWithoutProperties(props, _excluded);\n  const propsWithDefaults = _objectSpread(_objectSpread({}, props), {}, {\n    closeAfterTransition,\n    disableAutoFocus,\n    disableEnforceFocus,\n    disableEscapeKeyDown,\n    disablePortal,\n    disableRestoreFocus,\n    disableScrollLock,\n    hideBackdrop,\n    keepMounted\n  });\n  const {\n    getRootProps,\n    getBackdropProps,\n    getTransitionProps,\n    portalRef,\n    isTopModal,\n    exited,\n    hasTransition\n  } = useModal(_objectSpread(_objectSpread({}, propsWithDefaults), {}, {\n    rootRef: ref\n  }));\n  const ownerState = _objectSpread(_objectSpread({}, propsWithDefaults), {}, {\n    exited\n  });\n  const classes = useUtilityClasses(ownerState);\n  const childProps = {};\n  if (children.props.tabIndex === undefined) {\n    childProps.tabIndex = '-1';\n  }\n\n  // It's a Transition like component\n  if (hasTransition) {\n    const {\n      onEnter,\n      onExited\n    } = getTransitionProps();\n    childProps.onEnter = onEnter;\n    childProps.onExited = onExited;\n  }\n  const externalForwardedProps = {\n    slots: _objectSpread({\n      root: components.Root,\n      backdrop: components.Backdrop\n    }, slots),\n    slotProps: _objectSpread(_objectSpread({}, componentsProps), slotProps)\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    ref,\n    elementType: ModalRoot,\n    externalForwardedProps: _objectSpread(_objectSpread(_objectSpread({}, externalForwardedProps), other), {}, {\n      component\n    }),\n    getSlotProps: getRootProps,\n    ownerState,\n    className: clsx(className, classes === null || classes === void 0 ? void 0 : classes.root, !ownerState.open && ownerState.exited && (classes === null || classes === void 0 ? void 0 : classes.hidden))\n  });\n  const [BackdropSlot, backdropProps] = useSlot('backdrop', {\n    ref: BackdropProps === null || BackdropProps === void 0 ? void 0 : BackdropProps.ref,\n    elementType: BackdropComponent,\n    externalForwardedProps,\n    shouldForwardComponentProp: true,\n    additionalProps: BackdropProps,\n    getSlotProps: otherHandlers => {\n      return getBackdropProps(_objectSpread(_objectSpread({}, otherHandlers), {}, {\n        onClick: event => {\n          if (otherHandlers !== null && otherHandlers !== void 0 && otherHandlers.onClick) {\n            otherHandlers.onClick(event);\n          }\n        }\n      }));\n    },\n    className: clsx(BackdropProps === null || BackdropProps === void 0 ? void 0 : BackdropProps.className, classes === null || classes === void 0 ? void 0 : classes.backdrop),\n    ownerState\n  });\n  if (!keepMounted && !open && (!hasTransition || exited)) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(Portal, {\n    ref: portalRef,\n    container: container,\n    disablePortal: disablePortal,\n    children: /*#__PURE__*/_jsxs(RootSlot, _objectSpread(_objectSpread({}, rootProps), {}, {\n      children: [!hideBackdrop && BackdropComponent ? /*#__PURE__*/_jsx(BackdropSlot, _objectSpread({}, backdropProps)) : null, /*#__PURE__*/_jsx(FocusTrap, {\n        disableEnforceFocus: disableEnforceFocus,\n        disableAutoFocus: disableAutoFocus,\n        disableRestoreFocus: disableRestoreFocus,\n        isEnabled: isTopModal,\n        open: open,\n        children: /*#__PURE__*/React.cloneElement(children, childProps)\n      })]\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Modal.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. While this prop currently works, it will be removed in the next major version.\n   * Use the `slots.backdrop` prop to make your application ready for the next version of Material UI.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Backdrop`](https://mui.com/material-ui/api/backdrop/) element.\n   * @deprecated Use `slotProps.backdrop` instead.\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * When set to true the Modal waits until a nested Transition is completed before closing.\n   * @default false\n   */\n  closeAfterTransition: PropTypes.bool,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated Use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Backdrop: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated Use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * You can also provide a callback, which is called in a React layout effect.\n   * This lets you set the container from a ref, and also makes server-side rendering possible.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * If `true`, the modal will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any modal children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n  /**\n   * If `true`, the modal will not prevent focus from leaving the modal while open.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n  /**\n   * If `true`, hitting escape will not fire the `onClose` callback.\n   * @default false\n   */\n  disableEscapeKeyDown: PropTypes.bool,\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * If `true`, the modal will not restore focus to previously focused element once\n   * modal is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * If `true`, the backdrop is not rendered.\n   * @default false\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Always keep the children in the DOM.\n   * This prop can be useful in SEO situation or\n   * when you want to maximize the responsiveness of the Modal.\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * A function called when a transition enters.\n   */\n  onTransitionEnter: PropTypes.func,\n  /**\n   * A function called when a transition has exited.\n   */\n  onTransitionExited: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The props used for each slot inside the Modal.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Modal.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Modal;", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "React", "PropTypes", "clsx", "HTMLElementType", "elementAcceptingRef", "composeClasses", "FocusTrap", "Portal", "styled", "memoTheme", "useDefaultProps", "Backdrop", "useModal", "getModalUtilityClass", "useSlot", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "open", "exited", "classes", "slots", "root", "backdrop", "ModalRoot", "name", "slot", "overridesResolver", "props", "styles", "hidden", "_ref", "theme", "position", "zIndex", "vars", "modal", "right", "bottom", "top", "left", "variants", "_ref2", "style", "visibility", "ModalBackdrop", "Modal", "forwardRef", "inProps", "ref", "BackdropComponent", "BackdropProps", "classesProp", "className", "closeAfterTransition", "children", "container", "component", "components", "componentsProps", "disableAutoFocus", "disableEnforceFocus", "disableEscapeKeyDown", "disable<PERSON><PERSON><PERSON>", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableScrollLock", "hideBackdrop", "keepMounted", "onClose", "onTransitionEnter", "onTransitionExited", "slotProps", "other", "propsWithDefaults", "getRootProps", "getBackdropProps", "getTransitionProps", "portalRef", "isTopModal", "hasTransition", "rootRef", "childProps", "tabIndex", "undefined", "onEnter", "onExited", "externalForwardedProps", "Root", "RootSlot", "rootProps", "elementType", "getSlotProps", "BackdropSlot", "backdropProps", "shouldForwardComponentProp", "additionalProps", "otherHandlers", "onClick", "event", "isEnabled", "cloneElement", "process", "env", "NODE_ENV", "propTypes", "object", "isRequired", "string", "bool", "shape", "oneOfType", "func", "sx", "arrayOf"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/Modal/Modal.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport FocusTrap from \"../Unstable_TrapFocus/index.js\";\nimport Portal from \"../Portal/index.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Backdrop from \"../Backdrop/index.js\";\nimport useModal from \"./useModal.js\";\nimport { getModalUtilityClass } from \"./modalClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    open,\n    exited,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', !open && exited && 'hidden'],\n    backdrop: ['backdrop']\n  };\n  return composeClasses(slots, getModalUtilityClass, classes);\n};\nconst ModalRoot = styled('div', {\n  name: 'MuiModal',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.open && ownerState.exited && styles.hidden];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'fixed',\n  zIndex: (theme.vars || theme).zIndex.modal,\n  right: 0,\n  bottom: 0,\n  top: 0,\n  left: 0,\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.open && ownerState.exited,\n    style: {\n      visibility: 'hidden'\n    }\n  }]\n})));\nconst ModalBackdrop = styled(Backdrop, {\n  name: 'MuiModal',\n  slot: 'Backdrop'\n})({\n  zIndex: -1\n});\n\n/**\n * Modal is a lower-level construct that is leveraged by the following components:\n *\n * - [Dialog](/material-ui/api/dialog/)\n * - [Drawer](/material-ui/api/drawer/)\n * - [Menu](/material-ui/api/menu/)\n * - [Popover](/material-ui/api/popover/)\n *\n * If you are creating a modal dialog, you probably want to use the [Dialog](/material-ui/api/dialog/) component\n * rather than directly using Modal.\n *\n * This component shares many concepts with [react-overlays](https://react-bootstrap.github.io/react-overlays/#modals).\n */\nconst Modal = /*#__PURE__*/React.forwardRef(function Modal(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiModal',\n    props: inProps\n  });\n  const {\n    BackdropComponent = ModalBackdrop,\n    BackdropProps,\n    classes: classesProp,\n    className,\n    closeAfterTransition = false,\n    children,\n    container,\n    component,\n    components = {},\n    componentsProps = {},\n    disableAutoFocus = false,\n    disableEnforceFocus = false,\n    disableEscapeKeyDown = false,\n    disablePortal = false,\n    disableRestoreFocus = false,\n    disableScrollLock = false,\n    hideBackdrop = false,\n    keepMounted = false,\n    onClose,\n    onTransitionEnter,\n    onTransitionExited,\n    open,\n    slotProps = {},\n    slots = {},\n    // eslint-disable-next-line react/prop-types\n    theme,\n    ...other\n  } = props;\n  const propsWithDefaults = {\n    ...props,\n    closeAfterTransition,\n    disableAutoFocus,\n    disableEnforceFocus,\n    disableEscapeKeyDown,\n    disablePortal,\n    disableRestoreFocus,\n    disableScrollLock,\n    hideBackdrop,\n    keepMounted\n  };\n  const {\n    getRootProps,\n    getBackdropProps,\n    getTransitionProps,\n    portalRef,\n    isTopModal,\n    exited,\n    hasTransition\n  } = useModal({\n    ...propsWithDefaults,\n    rootRef: ref\n  });\n  const ownerState = {\n    ...propsWithDefaults,\n    exited\n  };\n  const classes = useUtilityClasses(ownerState);\n  const childProps = {};\n  if (children.props.tabIndex === undefined) {\n    childProps.tabIndex = '-1';\n  }\n\n  // It's a Transition like component\n  if (hasTransition) {\n    const {\n      onEnter,\n      onExited\n    } = getTransitionProps();\n    childProps.onEnter = onEnter;\n    childProps.onExited = onExited;\n  }\n  const externalForwardedProps = {\n    slots: {\n      root: components.Root,\n      backdrop: components.Backdrop,\n      ...slots\n    },\n    slotProps: {\n      ...componentsProps,\n      ...slotProps\n    }\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    ref,\n    elementType: ModalRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other,\n      component\n    },\n    getSlotProps: getRootProps,\n    ownerState,\n    className: clsx(className, classes?.root, !ownerState.open && ownerState.exited && classes?.hidden)\n  });\n  const [BackdropSlot, backdropProps] = useSlot('backdrop', {\n    ref: BackdropProps?.ref,\n    elementType: BackdropComponent,\n    externalForwardedProps,\n    shouldForwardComponentProp: true,\n    additionalProps: BackdropProps,\n    getSlotProps: otherHandlers => {\n      return getBackdropProps({\n        ...otherHandlers,\n        onClick: event => {\n          if (otherHandlers?.onClick) {\n            otherHandlers.onClick(event);\n          }\n        }\n      });\n    },\n    className: clsx(BackdropProps?.className, classes?.backdrop),\n    ownerState\n  });\n  if (!keepMounted && !open && (!hasTransition || exited)) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(Portal, {\n    ref: portalRef,\n    container: container,\n    disablePortal: disablePortal,\n    children: /*#__PURE__*/_jsxs(RootSlot, {\n      ...rootProps,\n      children: [!hideBackdrop && BackdropComponent ? /*#__PURE__*/_jsx(BackdropSlot, {\n        ...backdropProps\n      }) : null, /*#__PURE__*/_jsx(FocusTrap, {\n        disableEnforceFocus: disableEnforceFocus,\n        disableAutoFocus: disableAutoFocus,\n        disableRestoreFocus: disableRestoreFocus,\n        isEnabled: isTopModal,\n        open: open,\n        children: /*#__PURE__*/React.cloneElement(children, childProps)\n      })]\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Modal.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. While this prop currently works, it will be removed in the next major version.\n   * Use the `slots.backdrop` prop to make your application ready for the next version of Material UI.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Backdrop`](https://mui.com/material-ui/api/backdrop/) element.\n   * @deprecated Use `slotProps.backdrop` instead.\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * When set to true the Modal waits until a nested Transition is completed before closing.\n   * @default false\n   */\n  closeAfterTransition: PropTypes.bool,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated Use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Backdrop: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated Use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * You can also provide a callback, which is called in a React layout effect.\n   * This lets you set the container from a ref, and also makes server-side rendering possible.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * If `true`, the modal will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any modal children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n  /**\n   * If `true`, the modal will not prevent focus from leaving the modal while open.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n  /**\n   * If `true`, hitting escape will not fire the `onClose` callback.\n   * @default false\n   */\n  disableEscapeKeyDown: PropTypes.bool,\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * If `true`, the modal will not restore focus to previously focused element once\n   * modal is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * If `true`, the backdrop is not rendered.\n   * @default false\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Always keep the children in the DOM.\n   * This prop can be useful in SEO situation or\n   * when you want to maximize the responsiveness of the Modal.\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * A function called when a transition enters.\n   */\n  onTransitionEnter: PropTypes.func,\n  /**\n   * A function called when a transition has exited.\n   */\n  onTransitionExited: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The props used for each slot inside the Modal.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Modal.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Modal;"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;AAEb,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,mBAAmB,MAAM,gCAAgC;AAChE,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,SAAS,MAAM,gCAAgC;AACtD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,QAAQ,MAAM,eAAe;AACpC,SAASC,oBAAoB,QAAQ,mBAAmB;AACxD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,IAAI;IACJC,MAAM;IACNC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,CAACJ,IAAI,IAAIC,MAAM,IAAI,QAAQ,CAAC;IAC3CI,QAAQ,EAAE,CAAC,UAAU;EACvB,CAAC;EACD,OAAOrB,cAAc,CAACmB,KAAK,EAAEX,oBAAoB,EAAEU,OAAO,CAAC;AAC7D,CAAC;AACD,MAAMI,SAAS,GAAGnB,MAAM,CAAC,KAAK,EAAE;EAC9BoB,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJZ;IACF,CAAC,GAAGW,KAAK;IACT,OAAO,CAACC,MAAM,CAACP,IAAI,EAAE,CAACL,UAAU,CAACC,IAAI,IAAID,UAAU,CAACE,MAAM,IAAIU,MAAM,CAACC,MAAM,CAAC;EAC9E;AACF,CAAC,CAAC,CAACxB,SAAS,CAACyB,IAAA;EAAA,IAAC;IACZC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACLE,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,CAACF,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEE,MAAM,CAACE,KAAK;IAC1CC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,CAAC;MACTb,KAAK,EAAEc,KAAA;QAAA,IAAC;UACNzB;QACF,CAAC,GAAAyB,KAAA;QAAA,OAAK,CAACzB,UAAU,CAACC,IAAI,IAAID,UAAU,CAACE,MAAM;MAAA;MAC3CwB,KAAK,EAAE;QACLC,UAAU,EAAE;MACd;IACF,CAAC;EACH,CAAC;AAAA,CAAC,CAAC,CAAC;AACJ,MAAMC,aAAa,GAAGxC,MAAM,CAACG,QAAQ,EAAE;EACrCiB,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDQ,MAAM,EAAE,CAAC;AACX,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,KAAK,GAAG,aAAajD,KAAK,CAACkD,UAAU,CAAC,SAASD,KAAKA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvE,MAAMrB,KAAK,GAAGrB,eAAe,CAAC;IAC5BkB,IAAI,EAAE,UAAU;IAChBG,KAAK,EAAEoB;EACT,CAAC,CAAC;EACF,MAAM;MACJE,iBAAiB,GAAGL,aAAa;MACjCM,aAAa;MACb/B,OAAO,EAAEgC,WAAW;MACpBC,SAAS;MACTC,oBAAoB,GAAG,KAAK;MAC5BC,QAAQ;MACRC,SAAS;MACTC,SAAS;MACTC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpBC,gBAAgB,GAAG,KAAK;MACxBC,mBAAmB,GAAG,KAAK;MAC3BC,oBAAoB,GAAG,KAAK;MAC5BC,aAAa,GAAG,KAAK;MACrBC,mBAAmB,GAAG,KAAK;MAC3BC,iBAAiB,GAAG,KAAK;MACzBC,YAAY,GAAG,KAAK;MACpBC,WAAW,GAAG,KAAK;MACnBC,OAAO;MACPC,iBAAiB;MACjBC,kBAAkB;MAClBpD,IAAI;MACJqD,SAAS,GAAG,CAAC,CAAC;MACdlD,KAAK,GAAG,CAAC,CAAC;MACV;MACAW;IAEF,CAAC,GAAGJ,KAAK;IADJ4C,KAAK,GAAA7E,wBAAA,CACNiC,KAAK,EAAAhC,SAAA;EACT,MAAM6E,iBAAiB,GAAA/E,aAAA,CAAAA,aAAA,KAClBkC,KAAK;IACR0B,oBAAoB;IACpBM,gBAAgB;IAChBC,mBAAmB;IACnBC,oBAAoB;IACpBC,aAAa;IACbC,mBAAmB;IACnBC,iBAAiB;IACjBC,YAAY;IACZC;EAAW,EACZ;EACD,MAAM;IACJO,YAAY;IACZC,gBAAgB;IAChBC,kBAAkB;IAClBC,SAAS;IACTC,UAAU;IACV3D,MAAM;IACN4D;EACF,CAAC,GAAGtE,QAAQ,CAAAf,aAAA,CAAAA,aAAA,KACP+E,iBAAiB;IACpBO,OAAO,EAAE/B;EAAG,EACb,CAAC;EACF,MAAMhC,UAAU,GAAAvB,aAAA,CAAAA,aAAA,KACX+E,iBAAiB;IACpBtD;EAAM,EACP;EACD,MAAMC,OAAO,GAAGJ,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMgE,UAAU,GAAG,CAAC,CAAC;EACrB,IAAI1B,QAAQ,CAAC3B,KAAK,CAACsD,QAAQ,KAAKC,SAAS,EAAE;IACzCF,UAAU,CAACC,QAAQ,GAAG,IAAI;EAC5B;;EAEA;EACA,IAAIH,aAAa,EAAE;IACjB,MAAM;MACJK,OAAO;MACPC;IACF,CAAC,GAAGT,kBAAkB,CAAC,CAAC;IACxBK,UAAU,CAACG,OAAO,GAAGA,OAAO;IAC5BH,UAAU,CAACI,QAAQ,GAAGA,QAAQ;EAChC;EACA,MAAMC,sBAAsB,GAAG;IAC7BjE,KAAK,EAAA3B,aAAA;MACH4B,IAAI,EAAEoC,UAAU,CAAC6B,IAAI;MACrBhE,QAAQ,EAAEmC,UAAU,CAAClD;IAAQ,GAC1Ba,KAAK,CACT;IACDkD,SAAS,EAAA7E,aAAA,CAAAA,aAAA,KACJiE,eAAe,GACfY,SAAS;EAEhB,CAAC;EACD,MAAM,CAACiB,QAAQ,EAAEC,SAAS,CAAC,GAAG9E,OAAO,CAAC,MAAM,EAAE;IAC5CsC,GAAG;IACHyC,WAAW,EAAElE,SAAS;IACtB8D,sBAAsB,EAAA5F,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACjB4F,sBAAsB,GACtBd,KAAK;MACRf;IAAS,EACV;IACDkC,YAAY,EAAEjB,YAAY;IAC1BzD,UAAU;IACVoC,SAAS,EAAEtD,IAAI,CAACsD,SAAS,EAAEjC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,IAAI,EAAE,CAACL,UAAU,CAACC,IAAI,IAAID,UAAU,CAACE,MAAM,KAAIC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEU,MAAM;EACpG,CAAC,CAAC;EACF,MAAM,CAAC8D,YAAY,EAAEC,aAAa,CAAC,GAAGlF,OAAO,CAAC,UAAU,EAAE;IACxDsC,GAAG,EAAEE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEF,GAAG;IACvByC,WAAW,EAAExC,iBAAiB;IAC9BoC,sBAAsB;IACtBQ,0BAA0B,EAAE,IAAI;IAChCC,eAAe,EAAE5C,aAAa;IAC9BwC,YAAY,EAAEK,aAAa,IAAI;MAC7B,OAAOrB,gBAAgB,CAAAjF,aAAA,CAAAA,aAAA,KAClBsG,aAAa;QAChBC,OAAO,EAAEC,KAAK,IAAI;UAChB,IAAIF,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEC,OAAO,EAAE;YAC1BD,aAAa,CAACC,OAAO,CAACC,KAAK,CAAC;UAC9B;QACF;MAAC,EACF,CAAC;IACJ,CAAC;IACD7C,SAAS,EAAEtD,IAAI,CAACoD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEE,SAAS,EAAEjC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,QAAQ,CAAC;IAC5DN;EACF,CAAC,CAAC;EACF,IAAI,CAACkD,WAAW,IAAI,CAACjD,IAAI,KAAK,CAAC6D,aAAa,IAAI5D,MAAM,CAAC,EAAE;IACvD,OAAO,IAAI;EACb;EACA,OAAO,aAAaN,IAAI,CAACT,MAAM,EAAE;IAC/B6C,GAAG,EAAE4B,SAAS;IACdrB,SAAS,EAAEA,SAAS;IACpBO,aAAa,EAAEA,aAAa;IAC5BR,QAAQ,EAAE,aAAaxC,KAAK,CAACyE,QAAQ,EAAA9F,aAAA,CAAAA,aAAA,KAChC+F,SAAS;MACZlC,QAAQ,EAAE,CAAC,CAACW,YAAY,IAAIhB,iBAAiB,GAAG,aAAarC,IAAI,CAAC+E,YAAY,EAAAlG,aAAA,KACzEmG,aAAa,CACjB,CAAC,GAAG,IAAI,EAAE,aAAahF,IAAI,CAACV,SAAS,EAAE;QACtC0D,mBAAmB,EAAEA,mBAAmB;QACxCD,gBAAgB,EAAEA,gBAAgB;QAClCI,mBAAmB,EAAEA,mBAAmB;QACxCmC,SAAS,EAAErB,UAAU;QACrB5D,IAAI,EAAEA,IAAI;QACVqC,QAAQ,EAAE,aAAa1D,KAAK,CAACuG,YAAY,CAAC7C,QAAQ,EAAE0B,UAAU;MAChE,CAAC,CAAC;IAAC,EACJ;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzD,KAAK,CAAC0D,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtD,iBAAiB,EAAEpD,SAAS,CAAC4F,WAAW;EACxC;AACF;AACA;AACA;EACEvC,aAAa,EAAErD,SAAS,CAAC2G,MAAM;EAC/B;AACF;AACA;EACElD,QAAQ,EAAEtD,mBAAmB,CAACyG,UAAU;EACxC;AACF;AACA;EACEtF,OAAO,EAAEtB,SAAS,CAAC2G,MAAM;EACzB;AACF;AACA;EACEpD,SAAS,EAAEvD,SAAS,CAAC6G,MAAM;EAC3B;AACF;AACA;AACA;EACErD,oBAAoB,EAAExD,SAAS,CAAC8G,IAAI;EACpC;AACF;AACA;AACA;EACEnD,SAAS,EAAE3D,SAAS,CAAC4F,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;EACEhC,UAAU,EAAE5D,SAAS,CAAC+G,KAAK,CAAC;IAC1BrG,QAAQ,EAAEV,SAAS,CAAC4F,WAAW;IAC/BH,IAAI,EAAEzF,SAAS,CAAC4F;EAClB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE/B,eAAe,EAAE7D,SAAS,CAAC+G,KAAK,CAAC;IAC/BtF,QAAQ,EAAEzB,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACiH,IAAI,EAAEjH,SAAS,CAAC2G,MAAM,CAAC,CAAC;IACjEnF,IAAI,EAAExB,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACiH,IAAI,EAAEjH,SAAS,CAAC2G,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEjD,SAAS,EAAE1D,SAAS,CAAC,sCAAsCgH,SAAS,CAAC,CAAC9G,eAAe,EAAEF,SAAS,CAACiH,IAAI,CAAC,CAAC;EACvG;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEnD,gBAAgB,EAAE9D,SAAS,CAAC8G,IAAI;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;EACE/C,mBAAmB,EAAE/D,SAAS,CAAC8G,IAAI;EACnC;AACF;AACA;AACA;EACE9C,oBAAoB,EAAEhE,SAAS,CAAC8G,IAAI;EACpC;AACF;AACA;AACA;EACE7C,aAAa,EAAEjE,SAAS,CAAC8G,IAAI;EAC7B;AACF;AACA;AACA;AACA;EACE5C,mBAAmB,EAAElE,SAAS,CAAC8G,IAAI;EACnC;AACF;AACA;AACA;EACE3C,iBAAiB,EAAEnE,SAAS,CAAC8G,IAAI;EACjC;AACF;AACA;AACA;EACE1C,YAAY,EAAEpE,SAAS,CAAC8G,IAAI;EAC5B;AACF;AACA;AACA;AACA;AACA;EACEzC,WAAW,EAAErE,SAAS,CAAC8G,IAAI;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;EACExC,OAAO,EAAEtE,SAAS,CAACiH,IAAI;EACvB;AACF;AACA;EACE1C,iBAAiB,EAAEvE,SAAS,CAACiH,IAAI;EACjC;AACF;AACA;EACEzC,kBAAkB,EAAExE,SAAS,CAACiH,IAAI;EAClC;AACF;AACA;EACE7F,IAAI,EAAEpB,SAAS,CAAC8G,IAAI,CAACF,UAAU;EAC/B;AACF;AACA;AACA;EACEnC,SAAS,EAAEzE,SAAS,CAAC+G,KAAK,CAAC;IACzBtF,QAAQ,EAAEzB,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACiH,IAAI,EAAEjH,SAAS,CAAC2G,MAAM,CAAC,CAAC;IACjEnF,IAAI,EAAExB,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACiH,IAAI,EAAEjH,SAAS,CAAC2G,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEpF,KAAK,EAAEvB,SAAS,CAAC+G,KAAK,CAAC;IACrBtF,QAAQ,EAAEzB,SAAS,CAAC4F,WAAW;IAC/BpE,IAAI,EAAExB,SAAS,CAAC4F;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEsB,EAAE,EAAElH,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACmH,OAAO,CAACnH,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACiH,IAAI,EAAEjH,SAAS,CAAC2G,MAAM,EAAE3G,SAAS,CAAC8G,IAAI,CAAC,CAAC,CAAC,EAAE9G,SAAS,CAACiH,IAAI,EAAEjH,SAAS,CAAC2G,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAe3D,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}