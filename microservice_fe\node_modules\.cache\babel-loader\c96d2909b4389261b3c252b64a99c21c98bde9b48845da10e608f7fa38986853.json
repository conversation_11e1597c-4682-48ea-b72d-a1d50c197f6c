{"ast": null, "code": "/**\n * Format a date string to YYYY-MM-DD format for HTML input fields\n * @param dateString The date string to format\n * @returns Formatted date string in YYYY-MM-DD format\n */export const formatDateForInput=dateString=>{if(!dateString)return'';try{const date=dateString instanceof Date?dateString:new Date(dateString);if(isNaN(date.getTime())){console.error('Invalid date provided to formatDateForInput:',dateString);return'';}const year=date.getFullYear();const month=String(date.getMonth()+1).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');return\"\".concat(year,\"-\").concat(month,\"-\").concat(day);}catch(error){console.error('Error in formatDateForInput:',error);return'';}};/**\n * Format a date string to a localized Vietnamese date format (DD/MM/YYYY)\n * @param dateString The date string to format\n * @returns Formatted date string in DD/MM/YYYY format\n */export const formatDateLocalized=dateString=>{if(!dateString)return'';try{const date=dateString instanceof Date?dateString:new Date(dateString);if(isNaN(date.getTime())){console.error('Invalid date provided to formatDateLocalized:',dateString);return'';}// Format date as DD/MM/YYYY for Vietnamese format\nconst day=String(date.getDate()).padStart(2,'0');const month=String(date.getMonth()+1).padStart(2,'0');const year=date.getFullYear();return\"\".concat(day,\"/\").concat(month,\"/\").concat(year);}catch(error){console.error('Error in formatDateLocalized:',error);return'';}};/**\n * Get today's date in YYYY-MM-DD format for input fields\n * @returns Today's date string in YYYY-MM-DD format\n */export const getTodayDate=()=>{return formatDateForInput(new Date());};/**\n * Format a date string to display format (DD/MM/YYYY)\n * This is an alias for formatDateLocalized for better semantic naming\n * @param dateString The date string to format\n * @returns Formatted date string in DD/MM/YYYY format\n */export const formatDateForDisplay=dateString=>{return formatDateLocalized(dateString);};/**\n * Check if a date is in the future\n * @param dateString The date string to check\n * @returns True if the date is in the future\n */export const isFutureDate=dateString=>{const date=new Date(dateString);const today=new Date();today.setHours(0,0,0,0);return date>today;};/**\n * Check if a date is in the past\n * @param dateString The date string to check\n * @returns True if the date is in the past\n */export const isPastDate=dateString=>{const date=new Date(dateString);const today=new Date();today.setHours(0,0,0,0);return date<today;};/**\n * Chuyển đổi giá trị ngày tháng từ nhiều định dạng khác nhau thành đối tượng Date\n * Hỗ trợ chuỗi ISO, đối tượng Date, và mảng số [năm, tháng, ngày, giờ, phút, giây, nano]\n * @param dateValue Giá trị ngày tháng cần chuyển đổi\n * @returns Đối tượng Date\n */export const parseDate=dateValue=>{if(!dateValue){return new Date();// Trả về ngày hiện tại nếu không có giá trị\n}// Nếu đã là đối tượng Date, trả về luôn\nif(dateValue instanceof Date){return dateValue;}// Nếu là mảng số [năm, tháng, ngày, giờ, phút, giây, nano]\nif(Array.isArray(dateValue)){try{const[year,month,day,hour=0,minute=0,second=0]=dateValue;// Lưu ý: Tháng trong JavaScript bắt đầu từ 0 (0 = tháng 1, 11 = tháng 12)\nreturn new Date(year,month-1,day,hour,minute,second);}catch(error){console.error('Lỗi khi chuyển đổi mảng thành Date:',error,dateValue);return new Date();// Trả về ngày hiện tại nếu có lỗi\n}}// Nếu là chuỗi\ntry{return new Date(dateValue);}catch(error){console.error('Lỗi khi chuyển đổi chuỗi thành Date:',error,dateValue);return new Date();// Trả về ngày hiện tại nếu có lỗi\n}};", "map": {"version": 3, "names": ["formatDateForInput", "dateString", "date", "Date", "isNaN", "getTime", "console", "error", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "concat", "formatDateLocalized", "getTodayDate", "formatDateForDisplay", "isFutureDate", "today", "setHours", "isPastDate", "parseDate", "dateValue", "Array", "isArray", "hour", "minute", "second"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/utils/dateUtils.ts"], "sourcesContent": ["/**\n * Format a date string to YYYY-MM-DD format for HTML input fields\n * @param dateString The date string to format\n * @returns Formatted date string in YYYY-MM-DD format\n */\nexport const formatDateForInput = (dateString: string | Date | undefined): string => {\n  if (!dateString) return '';\n\n  try {\n    const date = dateString instanceof Date ? dateString : new Date(dateString);\n\n    if (isNaN(date.getTime())) {\n      console.error('Invalid date provided to formatDateForInput:', dateString);\n      return '';\n    }\n\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n\n    return `${year}-${month}-${day}`;\n  } catch (error) {\n    console.error('Error in formatDateForInput:', error);\n    return '';\n  }\n};\n\n/**\n * Format a date string to a localized Vietnamese date format (DD/MM/YYYY)\n * @param dateString The date string to format\n * @returns Formatted date string in DD/MM/YYYY format\n */\nexport const formatDateLocalized = (dateString: string | Date | undefined): string => {\n  if (!dateString) return '';\n\n  try {\n    const date = dateString instanceof Date ? dateString : new Date(dateString);\n\n    if (isNaN(date.getTime())) {\n      console.error('Invalid date provided to formatDateLocalized:', dateString);\n      return '';\n    }\n\n    // Format date as DD/MM/YYYY for Vietnamese format\n    const day = String(date.getDate()).padStart(2, '0');\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const year = date.getFullYear();\n\n    return `${day}/${month}/${year}`;\n  } catch (error) {\n    console.error('Error in formatDateLocalized:', error);\n    return '';\n  }\n};\n\n/**\n * Get today's date in YYYY-MM-DD format for input fields\n * @returns Today's date string in YYYY-MM-DD format\n */\nexport const getTodayDate = (): string => {\n  return formatDateForInput(new Date());\n};\n\n/**\n * Format a date string to display format (DD/MM/YYYY)\n * This is an alias for formatDateLocalized for better semantic naming\n * @param dateString The date string to format\n * @returns Formatted date string in DD/MM/YYYY format\n */\nexport const formatDateForDisplay = (dateString: string | Date | undefined): string => {\n  return formatDateLocalized(dateString);\n};\n\n/**\n * Check if a date is in the future\n * @param dateString The date string to check\n * @returns True if the date is in the future\n */\nexport const isFutureDate = (dateString: string): boolean => {\n  const date = new Date(dateString);\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  return date > today;\n};\n\n/**\n * Check if a date is in the past\n * @param dateString The date string to check\n * @returns True if the date is in the past\n */\nexport const isPastDate = (dateString: string): boolean => {\n  const date = new Date(dateString);\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  return date < today;\n};\n\n/**\n * Chuyển đổi giá trị ngày tháng từ nhiều định dạng khác nhau thành đối tượng Date\n * Hỗ trợ chuỗi ISO, đối tượng Date, và mảng số [năm, tháng, ngày, giờ, phút, giây, nano]\n * @param dateValue Giá trị ngày tháng cần chuyển đổi\n * @returns Đối tượng Date\n */\nexport const parseDate = (dateValue: string | Date | number[] | undefined): Date => {\n  if (!dateValue) {\n    return new Date(); // Trả về ngày hiện tại nếu không có giá trị\n  }\n\n  // Nếu đã là đối tượng Date, trả về luôn\n  if (dateValue instanceof Date) {\n    return dateValue;\n  }\n\n  // Nếu là mảng số [năm, tháng, ngày, giờ, phút, giây, nano]\n  if (Array.isArray(dateValue)) {\n    try {\n      const [year, month, day, hour = 0, minute = 0, second = 0] = dateValue;\n      // Lưu ý: Tháng trong JavaScript bắt đầu từ 0 (0 = tháng 1, 11 = tháng 12)\n      return new Date(year, month - 1, day, hour, minute, second);\n    } catch (error) {\n      console.error('Lỗi khi chuyển đổi mảng thành Date:', error, dateValue);\n      return new Date(); // Trả về ngày hiện tại nếu có lỗi\n    }\n  }\n\n  // Nếu là chuỗi\n  try {\n    return new Date(dateValue);\n  } catch (error) {\n    console.error('Lỗi khi chuyển đổi chuỗi thành Date:', error, dateValue);\n    return new Date(); // Trả về ngày hiện tại nếu có lỗi\n  }\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAA,kBAAkB,CAAIC,UAAqC,EAAa,CACnF,GAAI,CAACA,UAAU,CAAE,MAAO,EAAE,CAE1B,GAAI,CACF,KAAM,CAAAC,IAAI,CAAGD,UAAU,WAAY,CAAAE,IAAI,CAAGF,UAAU,CAAG,GAAI,CAAAE,IAAI,CAACF,UAAU,CAAC,CAE3E,GAAIG,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,CAAE,CACzBC,OAAO,CAACC,KAAK,CAAC,8CAA8C,CAAEN,UAAU,CAAC,CACzE,MAAO,EAAE,CACX,CAEA,KAAM,CAAAO,IAAI,CAAGN,IAAI,CAACO,WAAW,CAAC,CAAC,CAC/B,KAAM,CAAAC,KAAK,CAAGC,MAAM,CAACT,IAAI,CAACU,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,KAAM,CAAAC,GAAG,CAAGH,MAAM,CAACT,IAAI,CAACa,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAEnD,SAAAG,MAAA,CAAUR,IAAI,MAAAQ,MAAA,CAAIN,KAAK,MAAAM,MAAA,CAAIF,GAAG,EAChC,CAAE,MAAOP,KAAK,CAAE,CACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,MAAO,EAAE,CACX,CACF,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAU,mBAAmB,CAAIhB,UAAqC,EAAa,CACpF,GAAI,CAACA,UAAU,CAAE,MAAO,EAAE,CAE1B,GAAI,CACF,KAAM,CAAAC,IAAI,CAAGD,UAAU,WAAY,CAAAE,IAAI,CAAGF,UAAU,CAAG,GAAI,CAAAE,IAAI,CAACF,UAAU,CAAC,CAE3E,GAAIG,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,CAAE,CACzBC,OAAO,CAACC,KAAK,CAAC,+CAA+C,CAAEN,UAAU,CAAC,CAC1E,MAAO,EAAE,CACX,CAEA;AACA,KAAM,CAAAa,GAAG,CAAGH,MAAM,CAACT,IAAI,CAACa,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACnD,KAAM,CAAAH,KAAK,CAAGC,MAAM,CAACT,IAAI,CAACU,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,KAAM,CAAAL,IAAI,CAAGN,IAAI,CAACO,WAAW,CAAC,CAAC,CAE/B,SAAAO,MAAA,CAAUF,GAAG,MAAAE,MAAA,CAAIN,KAAK,MAAAM,MAAA,CAAIR,IAAI,EAChC,CAAE,MAAOD,KAAK,CAAE,CACdD,OAAO,CAACC,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,MAAO,EAAE,CACX,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAW,YAAY,CAAGA,CAAA,GAAc,CACxC,MAAO,CAAAlB,kBAAkB,CAAC,GAAI,CAAAG,IAAI,CAAC,CAAC,CAAC,CACvC,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAgB,oBAAoB,CAAIlB,UAAqC,EAAa,CACrF,MAAO,CAAAgB,mBAAmB,CAAChB,UAAU,CAAC,CACxC,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAmB,YAAY,CAAInB,UAAkB,EAAc,CAC3D,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,KAAM,CAAAoB,KAAK,CAAG,GAAI,CAAAlB,IAAI,CAAC,CAAC,CACxBkB,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC1B,MAAO,CAAApB,IAAI,CAAGmB,KAAK,CACrB,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAE,UAAU,CAAItB,UAAkB,EAAc,CACzD,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,KAAM,CAAAoB,KAAK,CAAG,GAAI,CAAAlB,IAAI,CAAC,CAAC,CACxBkB,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC1B,MAAO,CAAApB,IAAI,CAAGmB,KAAK,CACrB,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAG,SAAS,CAAIC,SAA+C,EAAW,CAClF,GAAI,CAACA,SAAS,CAAE,CACd,MAAO,IAAI,CAAAtB,IAAI,CAAC,CAAC,CAAE;AACrB,CAEA;AACA,GAAIsB,SAAS,WAAY,CAAAtB,IAAI,CAAE,CAC7B,MAAO,CAAAsB,SAAS,CAClB,CAEA;AACA,GAAIC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,CAAE,CAC5B,GAAI,CACF,KAAM,CAACjB,IAAI,CAAEE,KAAK,CAAEI,GAAG,CAAEc,IAAI,CAAG,CAAC,CAAEC,MAAM,CAAG,CAAC,CAAEC,MAAM,CAAG,CAAC,CAAC,CAAGL,SAAS,CACtE;AACA,MAAO,IAAI,CAAAtB,IAAI,CAACK,IAAI,CAAEE,KAAK,CAAG,CAAC,CAAEI,GAAG,CAAEc,IAAI,CAAEC,MAAM,CAAEC,MAAM,CAAC,CAC7D,CAAE,MAAOvB,KAAK,CAAE,CACdD,OAAO,CAACC,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAEkB,SAAS,CAAC,CACtE,MAAO,IAAI,CAAAtB,IAAI,CAAC,CAAC,CAAE;AACrB,CACF,CAEA;AACA,GAAI,CACF,MAAO,IAAI,CAAAA,IAAI,CAACsB,SAAS,CAAC,CAC5B,CAAE,MAAOlB,KAAK,CAAE,CACdD,OAAO,CAACC,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAEkB,SAAS,CAAC,CACvE,MAAO,IAAI,CAAAtB,IAAI,CAAC,CAAC,CAAE;AACrB,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}