{"ast": null, "code": "import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n  var _ref = isElement(element) ? getWindow(element) : window,\n    visualViewport = _ref.visualViewport;\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}", "map": {"version": 3, "names": ["isElement", "isHTMLElement", "round", "getWindow", "isLayoutViewport", "getBoundingClientRect", "element", "includeScale", "isFixedStrategy", "clientRect", "scaleX", "scaleY", "offsetWidth", "width", "offsetHeight", "height", "_ref", "window", "visualViewport", "addVisualOffsets", "x", "left", "offsetLeft", "y", "top", "offsetTop", "right", "bottom"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js"], "sourcesContent": ["import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}"], "mappings": "AAAA,SAASA,SAAS,EAAEC,aAAa,QAAQ,iBAAiB;AAC1D,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,eAAe,SAASC,qBAAqBA,CAACC,OAAO,EAAEC,YAAY,EAAEC,eAAe,EAAE;EACpF,IAAID,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,KAAK;EACtB;EAEA,IAAIC,eAAe,KAAK,KAAK,CAAC,EAAE;IAC9BA,eAAe,GAAG,KAAK;EACzB;EAEA,IAAIC,UAAU,GAAGH,OAAO,CAACD,qBAAqB,CAAC,CAAC;EAChD,IAAIK,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAG,CAAC;EAEd,IAAIJ,YAAY,IAAIN,aAAa,CAACK,OAAO,CAAC,EAAE;IAC1CI,MAAM,GAAGJ,OAAO,CAACM,WAAW,GAAG,CAAC,GAAGV,KAAK,CAACO,UAAU,CAACI,KAAK,CAAC,GAAGP,OAAO,CAACM,WAAW,IAAI,CAAC,GAAG,CAAC;IACzFD,MAAM,GAAGL,OAAO,CAACQ,YAAY,GAAG,CAAC,GAAGZ,KAAK,CAACO,UAAU,CAACM,MAAM,CAAC,GAAGT,OAAO,CAACQ,YAAY,IAAI,CAAC,GAAG,CAAC;EAC9F;EAEA,IAAIE,IAAI,GAAGhB,SAAS,CAACM,OAAO,CAAC,GAAGH,SAAS,CAACG,OAAO,CAAC,GAAGW,MAAM;IACvDC,cAAc,GAAGF,IAAI,CAACE,cAAc;EAExC,IAAIC,gBAAgB,GAAG,CAACf,gBAAgB,CAAC,CAAC,IAAII,eAAe;EAC7D,IAAIY,CAAC,GAAG,CAACX,UAAU,CAACY,IAAI,IAAIF,gBAAgB,IAAID,cAAc,GAAGA,cAAc,CAACI,UAAU,GAAG,CAAC,CAAC,IAAIZ,MAAM;EACzG,IAAIa,CAAC,GAAG,CAACd,UAAU,CAACe,GAAG,IAAIL,gBAAgB,IAAID,cAAc,GAAGA,cAAc,CAACO,SAAS,GAAG,CAAC,CAAC,IAAId,MAAM;EACvG,IAAIE,KAAK,GAAGJ,UAAU,CAACI,KAAK,GAAGH,MAAM;EACrC,IAAIK,MAAM,GAAGN,UAAU,CAACM,MAAM,GAAGJ,MAAM;EACvC,OAAO;IACLE,KAAK,EAAEA,KAAK;IACZE,MAAM,EAAEA,MAAM;IACdS,GAAG,EAAED,CAAC;IACNG,KAAK,EAAEN,CAAC,GAAGP,KAAK;IAChBc,MAAM,EAAEJ,CAAC,GAAGR,MAAM;IAClBM,IAAI,EAAED,CAAC;IACPA,CAAC,EAAEA,CAAC;IACJG,CAAC,EAAEA;EACL,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}