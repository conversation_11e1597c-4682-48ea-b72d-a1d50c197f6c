{"ast": null, "code": "/**\n * Convert a comma-separated string of day numbers to day names\n * @param workingDays Comma-separated string of day numbers (1-7)\n * @returns Array of day names\n */export const workingDaysToNames=workingDays=>{if(!workingDays)return[];const dayMap={'1':'<PERSON><PERSON><PERSON>','2':'<PERSON><PERSON><PERSON>','3':'<PERSON><PERSON><PERSON>','4':'<PERSON><PERSON><PERSON>','5':'Th<PERSON>','6':'Thứ Bảy','7':'Ch<PERSON> Nh<PERSON>'};return workingDays.split(',').map(day=>dayMap[day]||'');};/**\n * Convert a comma-separated string of day numbers to a formatted string\n * @param workingDays Comma-separated string of day numbers (1-7)\n * @returns Formatted string of day names\n */export const formatWorkingDays=workingDays=>{return workingDaysToNames(workingDays).join(', ');};/**\n * Convert an array of day numbers to a comma-separated string\n * @param days Array of day numbers (1-7)\n * @returns Comma-separated string of day numbers\n */export const daysArrayToString=days=>{return days.join(',');};/**\n * Convert a comma-separated string of day numbers to an array of numbers\n * @param workingDays Comma-separated string of day numbers (1-7)\n * @returns Array of day numbers\n */export const stringToDaysArray=workingDays=>{if(!workingDays)return[];return workingDays.split(',').map(day=>parseInt(day,10));};/**\n * Get all available working days as options\n * @returns Array of day options with value and label\n */export const getWorkingDayOptions=()=>{return[{value:1,label:'Thứ Hai'},{value:2,label:'Thứ Ba'},{value:3,label:'Thứ Tư'},{value:4,label:'Thứ Năm'},{value:5,label:'Thứ Sáu'},{value:6,label:'Thứ Bảy'},{value:7,label:'Chủ Nhật'}];};/**\n * Calculate actual working dates based on contract start/end dates and selected working days\n * @param startDate Contract start date (YYYY-MM-DD format)\n * @param endDate Contract end date (YYYY-MM-DD format)\n * @param workingDays Comma-separated string of day numbers (1-7)\n * @returns Array of date strings in DD/MM/YYYY format\n */export const calculateWorkingDates=(startDate,endDate,workingDays)=>{if(!startDate||!endDate||!workingDays)return[];const start=new Date(startDate);const end=new Date(endDate);const workingDaysArray=stringToDaysArray(workingDays);// Ensure valid dates\nif(isNaN(start.getTime())||isNaN(end.getTime()))return[];const result=[];const currentDate=new Date(start);// Loop through all days from start to end\nwhile(currentDate<=end){// JavaScript getDay() returns 0 for Sunday, 1 for Monday, etc.\n// Convert to our format where 1 = Monday, 7 = Sunday\nlet dayOfWeek=currentDate.getDay();dayOfWeek=dayOfWeek===0?7:dayOfWeek;// Convert Sunday from 0 to 7\n// Check if this day is in our working days\nif(workingDaysArray.includes(dayOfWeek)){// Format date as DD/MM/YYYY\nconst day=String(currentDate.getDate()).padStart(2,'0');const month=String(currentDate.getMonth()+1).padStart(2,'0');const year=currentDate.getFullYear();result.push(\"\".concat(day,\"/\").concat(month,\"/\").concat(year));}// Move to next day\ncurrentDate.setDate(currentDate.getDate()+1);}return result;};", "map": {"version": 3, "names": ["workingDaysToNames", "workingDays", "dayMap", "split", "map", "day", "formatWorkingDays", "join", "daysArrayToString", "days", "stringToDaysArray", "parseInt", "getWorkingDayOptions", "value", "label", "calculateWorkingDates", "startDate", "endDate", "start", "Date", "end", "workingDaysArray", "isNaN", "getTime", "result", "currentDate", "dayOfWeek", "getDay", "includes", "String", "getDate", "padStart", "month", "getMonth", "year", "getFullYear", "push", "concat", "setDate"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/utils/workingDaysUtils.ts"], "sourcesContent": ["/**\n * Convert a comma-separated string of day numbers to day names\n * @param workingDays Comma-separated string of day numbers (1-7)\n * @returns Array of day names\n */\nexport const workingDaysToNames = (workingDays: string): string[] => {\n  if (!workingDays) return [];\n\n  const dayMap: Record<string, string> = {\n    '1': 'Th<PERSON> Hai',\n    '2': 'Th<PERSON> Ba',\n    '3': 'Th<PERSON> Tư',\n    '4': 'Thứ <PERSON>ăm',\n    '5': '<PERSON>h<PERSON>',\n    '6': 'Th<PERSON>',\n    '7': '<PERSON><PERSON>'\n  };\n\n  return workingDays.split(',').map(day => dayMap[day] || '');\n};\n\n/**\n * Convert a comma-separated string of day numbers to a formatted string\n * @param workingDays Comma-separated string of day numbers (1-7)\n * @returns Formatted string of day names\n */\nexport const formatWorkingDays = (workingDays: string): string => {\n  return workingDaysToNames(workingDays).join(', ');\n};\n\n/**\n * Convert an array of day numbers to a comma-separated string\n * @param days Array of day numbers (1-7)\n * @returns Comma-separated string of day numbers\n */\nexport const daysArrayToString = (days: number[]): string => {\n  return days.join(',');\n};\n\n/**\n * Convert a comma-separated string of day numbers to an array of numbers\n * @param workingDays Comma-separated string of day numbers (1-7)\n * @returns Array of day numbers\n */\nexport const stringToDaysArray = (workingDays: string): number[] => {\n  if (!workingDays) return [];\n  return workingDays.split(',').map(day => parseInt(day, 10));\n};\n\n/**\n * Get all available working days as options\n * @returns Array of day options with value and label\n */\nexport const getWorkingDayOptions = (): { value: number; label: string }[] => {\n  return [\n    { value: 1, label: 'Thứ Hai' },\n    { value: 2, label: 'Thứ Ba' },\n    { value: 3, label: 'Thứ Tư' },\n    { value: 4, label: 'Thứ Năm' },\n    { value: 5, label: 'Thứ Sáu' },\n    { value: 6, label: 'Thứ Bảy' },\n    { value: 7, label: 'Chủ Nhật' }\n  ];\n};\n\n/**\n * Calculate actual working dates based on contract start/end dates and selected working days\n * @param startDate Contract start date (YYYY-MM-DD format)\n * @param endDate Contract end date (YYYY-MM-DD format)\n * @param workingDays Comma-separated string of day numbers (1-7)\n * @returns Array of date strings in DD/MM/YYYY format\n */\nexport const calculateWorkingDates = (\n  startDate: string,\n  endDate: string,\n  workingDays: string\n): string[] => {\n  if (!startDate || !endDate || !workingDays) return [];\n\n  const start = new Date(startDate);\n  const end = new Date(endDate);\n  const workingDaysArray = stringToDaysArray(workingDays);\n\n  // Ensure valid dates\n  if (isNaN(start.getTime()) || isNaN(end.getTime())) return [];\n\n  const result: string[] = [];\n  const currentDate = new Date(start);\n\n  // Loop through all days from start to end\n  while (currentDate <= end) {\n    // JavaScript getDay() returns 0 for Sunday, 1 for Monday, etc.\n    // Convert to our format where 1 = Monday, 7 = Sunday\n    let dayOfWeek = currentDate.getDay();\n    dayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek; // Convert Sunday from 0 to 7\n\n    // Check if this day is in our working days\n    if (workingDaysArray.includes(dayOfWeek)) {\n      // Format date as DD/MM/YYYY\n      const day = String(currentDate.getDate()).padStart(2, '0');\n      const month = String(currentDate.getMonth() + 1).padStart(2, '0');\n      const year = currentDate.getFullYear();\n      result.push(`${day}/${month}/${year}`);\n    }\n\n    // Move to next day\n    currentDate.setDate(currentDate.getDate() + 1);\n  }\n\n  return result;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAA,kBAAkB,CAAIC,WAAmB,EAAe,CACnE,GAAI,CAACA,WAAW,CAAE,MAAO,EAAE,CAE3B,KAAM,CAAAC,MAA8B,CAAG,CACrC,GAAG,CAAE,SAAS,CACd,GAAG,CAAE,QAAQ,CACb,GAAG,CAAE,QAAQ,CACb,GAAG,CAAE,SAAS,CACd,GAAG,CAAE,SAAS,CACd,GAAG,CAAE,SAAS,CACd,GAAG,CAAE,UACP,CAAC,CAED,MAAO,CAAAD,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,GAAG,EAAIH,MAAM,CAACG,GAAG,CAAC,EAAI,EAAE,CAAC,CAC7D,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,iBAAiB,CAAIL,WAAmB,EAAa,CAChE,MAAO,CAAAD,kBAAkB,CAACC,WAAW,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC,CACnD,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,iBAAiB,CAAIC,IAAc,EAAa,CAC3D,MAAO,CAAAA,IAAI,CAACF,IAAI,CAAC,GAAG,CAAC,CACvB,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAG,iBAAiB,CAAIT,WAAmB,EAAe,CAClE,GAAI,CAACA,WAAW,CAAE,MAAO,EAAE,CAC3B,MAAO,CAAAA,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,GAAG,EAAIM,QAAQ,CAACN,GAAG,CAAE,EAAE,CAAC,CAAC,CAC7D,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAO,oBAAoB,CAAGA,CAAA,GAA0C,CAC5E,MAAO,CACL,CAAEC,KAAK,CAAE,CAAC,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC9B,CAAED,KAAK,CAAE,CAAC,CAAEC,KAAK,CAAE,QAAS,CAAC,CAC7B,CAAED,KAAK,CAAE,CAAC,CAAEC,KAAK,CAAE,QAAS,CAAC,CAC7B,CAAED,KAAK,CAAE,CAAC,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC9B,CAAED,KAAK,CAAE,CAAC,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC9B,CAAED,KAAK,CAAE,CAAC,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC9B,CAAED,KAAK,CAAE,CAAC,CAAEC,KAAK,CAAE,UAAW,CAAC,CAChC,CACH,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,qBAAqB,CAAGA,CACnCC,SAAiB,CACjBC,OAAe,CACfhB,WAAmB,GACN,CACb,GAAI,CAACe,SAAS,EAAI,CAACC,OAAO,EAAI,CAAChB,WAAW,CAAE,MAAO,EAAE,CAErD,KAAM,CAAAiB,KAAK,CAAG,GAAI,CAAAC,IAAI,CAACH,SAAS,CAAC,CACjC,KAAM,CAAAI,GAAG,CAAG,GAAI,CAAAD,IAAI,CAACF,OAAO,CAAC,CAC7B,KAAM,CAAAI,gBAAgB,CAAGX,iBAAiB,CAACT,WAAW,CAAC,CAEvD;AACA,GAAIqB,KAAK,CAACJ,KAAK,CAACK,OAAO,CAAC,CAAC,CAAC,EAAID,KAAK,CAACF,GAAG,CAACG,OAAO,CAAC,CAAC,CAAC,CAAE,MAAO,EAAE,CAE7D,KAAM,CAAAC,MAAgB,CAAG,EAAE,CAC3B,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAN,IAAI,CAACD,KAAK,CAAC,CAEnC;AACA,MAAOO,WAAW,EAAIL,GAAG,CAAE,CACzB;AACA;AACA,GAAI,CAAAM,SAAS,CAAGD,WAAW,CAACE,MAAM,CAAC,CAAC,CACpCD,SAAS,CAAGA,SAAS,GAAK,CAAC,CAAG,CAAC,CAAGA,SAAS,CAAE;AAE7C;AACA,GAAIL,gBAAgB,CAACO,QAAQ,CAACF,SAAS,CAAC,CAAE,CACxC;AACA,KAAM,CAAArB,GAAG,CAAGwB,MAAM,CAACJ,WAAW,CAACK,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,KAAM,CAAAC,KAAK,CAAGH,MAAM,CAACJ,WAAW,CAACQ,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACjE,KAAM,CAAAG,IAAI,CAAGT,WAAW,CAACU,WAAW,CAAC,CAAC,CACtCX,MAAM,CAACY,IAAI,IAAAC,MAAA,CAAIhC,GAAG,MAAAgC,MAAA,CAAIL,KAAK,MAAAK,MAAA,CAAIH,IAAI,CAAE,CAAC,CACxC,CAEA;AACAT,WAAW,CAACa,OAAO,CAACb,WAAW,CAACK,OAAO,CAAC,CAAC,CAAG,CAAC,CAAC,CAChD,CAEA,MAAO,CAAAN,MAAM,CACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}