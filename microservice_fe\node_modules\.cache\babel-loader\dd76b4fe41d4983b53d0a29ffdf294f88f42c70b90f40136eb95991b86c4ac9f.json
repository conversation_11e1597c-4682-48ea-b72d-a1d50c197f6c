{"ast": null, "code": "import React,{useState}from'react';import{Box,TextField,Button,Typography,Paper,List,ListItem,ListItemText,ListItemButton,Divider,CircularProgress}from'@mui/material';import SearchIcon from'@mui/icons-material/Search';import PersonIcon from'@mui/icons-material/Person';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CustomerSearchForm=_ref=>{let{onSelectCustomer,onSearch,loading}=_ref;const[fullname,setFullname]=useState('');const[phoneNumber,setPhoneNumber]=useState('');const[searchResults,setSearchResults]=useState([]);const[error,setError]=useState(null);const handleSearch=async()=>{if(!fullname&&!phoneNumber){setError('Vui lòng nhập tên hoặc số điện thoại để tìm kiếm');return;}setError(null);try{const results=await onSearch(fullname,phoneNumber);setSearchResults(results);if(results.length===0){setError('Không tìm thấy khách hàng nào');}}catch(err){console.error('Error searching customers:',err);setError('Đã xảy ra lỗi khi tìm kiếm khách hàng');}};const handleSelectCustomer=customer=>{onSelectCustomer(customer);setSearchResults([]);setFullname('');setPhoneNumber('');};return/*#__PURE__*/_jsxs(Paper,{elevation:2,sx:{p:3,mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"T\\xECm ki\\u1EBFm kh\\xE1ch h\\xE0ng\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexWrap:'wrap',gap:2,mb:2},children:[/*#__PURE__*/_jsx(TextField,{label:\"T\\xEAn kh\\xE1ch h\\xE0ng\",value:fullname,onChange:e=>setFullname(e.target.value),sx:{flex:'1 1 45%',minWidth:'250px'}}),/*#__PURE__*/_jsx(TextField,{label:\"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i\",value:phoneNumber,onChange:e=>setPhoneNumber(e.target.value),sx:{flex:'1 1 45%',minWidth:'250px'}}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20,color:\"inherit\"}):/*#__PURE__*/_jsx(SearchIcon,{}),onClick:handleSearch,disabled:loading,sx:{flex:'1 1 auto',minWidth:'120px',height:'56px'},children:\"T\\xECm ki\\u1EBFm\"})]}),error&&/*#__PURE__*/_jsx(Typography,{color:\"error\",sx:{mb:2},children:error}),searchResults.length>0&&/*#__PURE__*/_jsxs(Box,{sx:{mt:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"K\\u1EBFt qu\\u1EA3 t\\xECm ki\\u1EBFm\"}),/*#__PURE__*/_jsx(List,{sx:{width:'100%',bgcolor:'background.paper'},children:searchResults.map((customer,index)=>/*#__PURE__*/_jsxs(React.Fragment,{children:[/*#__PURE__*/_jsx(ListItem,{disablePadding:true,children:/*#__PURE__*/_jsxs(ListItemButton,{onClick:()=>handleSelectCustomer(customer),children:[/*#__PURE__*/_jsx(PersonIcon,{sx:{mr:2}}),/*#__PURE__*/_jsx(ListItemText,{primary:customer.fullName,secondary:/*#__PURE__*/_jsxs(_Fragment,{children:[customer.phoneNumber,customer.companyName&&\" | \".concat(customer.companyName)]})})]})}),index<searchResults.length-1&&/*#__PURE__*/_jsx(Divider,{})]},customer.id))})]})]});};export default CustomerSearchForm;", "map": {"version": 3, "names": ["React", "useState", "Box", "TextField", "<PERSON><PERSON>", "Typography", "Paper", "List", "ListItem", "ListItemText", "ListItemButton", "Divider", "CircularProgress", "SearchIcon", "PersonIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "CustomerSearchForm", "_ref", "onSelectCustomer", "onSearch", "loading", "fullname", "setFullname", "phoneNumber", "setPhoneNumber", "searchResults", "setSearchResults", "error", "setError", "handleSearch", "results", "length", "err", "console", "handleSelectCustomer", "customer", "elevation", "sx", "p", "mb", "children", "variant", "gutterBottom", "display", "flexWrap", "gap", "label", "value", "onChange", "e", "target", "flex", "min<PERSON><PERSON><PERSON>", "startIcon", "size", "color", "onClick", "disabled", "height", "mt", "width", "bgcolor", "map", "index", "disablePadding", "mr", "primary", "fullName", "secondary", "companyName", "concat", "id"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/payment/CustomerSearchForm.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Typography,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemButton,\n  Divider,\n  CircularProgress,\n} from '@mui/material';\nimport SearchIcon from '@mui/icons-material/Search';\nimport PersonIcon from '@mui/icons-material/Person';\nimport { Customer } from '../../models';\n\ninterface CustomerSearchFormProps {\n  onSelectCustomer: (customer: Customer) => void;\n  onSearch: (fullname?: string, phoneNumber?: string) => Promise<Customer[]>;\n  loading: boolean;\n}\n\nconst CustomerSearchForm: React.FC<CustomerSearchFormProps> = ({\n  onSelectCustomer,\n  onSearch,\n  loading,\n}) => {\n  const [fullname, setFullname] = useState<string>('');\n  const [phoneNumber, setPhoneNumber] = useState<string>('');\n  const [searchResults, setSearchResults] = useState<Customer[]>([]);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleSearch = async () => {\n    if (!fullname && !phoneNumber) {\n      setError('Vui lòng nhập tên hoặc số điện thoại để tìm kiếm');\n      return;\n    }\n\n    setError(null);\n    try {\n      const results = await onSearch(fullname, phoneNumber);\n      setSearchResults(results);\n      if (results.length === 0) {\n        setError('Không tìm thấy khách hàng nào');\n      }\n    } catch (err) {\n      console.error('Error searching customers:', err);\n      setError('Đã xảy ra lỗi khi tìm kiếm khách hàng');\n    }\n  };\n\n  const handleSelectCustomer = (customer: Customer) => {\n    onSelectCustomer(customer);\n    setSearchResults([]);\n    setFullname('');\n    setPhoneNumber('');\n  };\n\n  return (\n    <Paper elevation={2} sx={{ p: 3, mb: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        Tìm kiếm khách hàng\n      </Typography>\n      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>\n        <TextField\n          label=\"Tên khách hàng\"\n          value={fullname}\n          onChange={(e) => setFullname(e.target.value)}\n          sx={{ flex: '1 1 45%', minWidth: '250px' }}\n        />\n        <TextField\n          label=\"Số điện thoại\"\n          value={phoneNumber}\n          onChange={(e) => setPhoneNumber(e.target.value)}\n          sx={{ flex: '1 1 45%', minWidth: '250px' }}\n        />\n        <Button\n          variant=\"contained\"\n          startIcon={loading ? <CircularProgress size={20} color=\"inherit\" /> : <SearchIcon />}\n          onClick={handleSearch}\n          disabled={loading}\n          sx={{ flex: '1 1 auto', minWidth: '120px', height: '56px' }}\n        >\n          Tìm kiếm\n        </Button>\n      </Box>\n\n      {error && (\n        <Typography color=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Typography>\n      )}\n\n      {searchResults.length > 0 && (\n        <Box sx={{ mt: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Kết quả tìm kiếm\n          </Typography>\n          <List sx={{ width: '100%', bgcolor: 'background.paper' }}>\n            {searchResults.map((customer, index) => (\n              <React.Fragment key={customer.id}>\n                <ListItem disablePadding>\n                  <ListItemButton onClick={() => handleSelectCustomer(customer)}>\n                    <PersonIcon sx={{ mr: 2 }} />\n                    <ListItemText\n                      primary={customer.fullName}\n                      secondary={\n                        <>\n                          {customer.phoneNumber}\n                          {customer.companyName && ` | ${customer.companyName}`}\n                        </>\n                      }\n                    />\n                  </ListItemButton>\n                </ListItem>\n                {index < searchResults.length - 1 && <Divider />}\n              </React.Fragment>\n            ))}\n          </List>\n        </Box>\n      )}\n    </Paper>\n  );\n};\n\nexport default CustomerSearchForm;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,GAAG,CACHC,SAAS,CACTC,MAAM,CACNC,UAAU,CACVC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,cAAc,CACdC,OAAO,CACPC,gBAAgB,KACX,eAAe,CACtB,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBASpD,KAAM,CAAAC,kBAAqD,CAAGC,IAAA,EAIxD,IAJyD,CAC7DC,gBAAgB,CAChBC,QAAQ,CACRC,OACF,CAAC,CAAAH,IAAA,CACC,KAAM,CAACI,QAAQ,CAAEC,WAAW,CAAC,CAAG1B,QAAQ,CAAS,EAAE,CAAC,CACpD,KAAM,CAAC2B,WAAW,CAAEC,cAAc,CAAC,CAAG5B,QAAQ,CAAS,EAAE,CAAC,CAC1D,KAAM,CAAC6B,aAAa,CAAEC,gBAAgB,CAAC,CAAG9B,QAAQ,CAAa,EAAE,CAAC,CAClE,KAAM,CAAC+B,KAAK,CAAEC,QAAQ,CAAC,CAAGhC,QAAQ,CAAgB,IAAI,CAAC,CAEvD,KAAM,CAAAiC,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CAACR,QAAQ,EAAI,CAACE,WAAW,CAAE,CAC7BK,QAAQ,CAAC,kDAAkD,CAAC,CAC5D,OACF,CAEAA,QAAQ,CAAC,IAAI,CAAC,CACd,GAAI,CACF,KAAM,CAAAE,OAAO,CAAG,KAAM,CAAAX,QAAQ,CAACE,QAAQ,CAAEE,WAAW,CAAC,CACrDG,gBAAgB,CAACI,OAAO,CAAC,CACzB,GAAIA,OAAO,CAACC,MAAM,GAAK,CAAC,CAAE,CACxBH,QAAQ,CAAC,+BAA+B,CAAC,CAC3C,CACF,CAAE,MAAOI,GAAG,CAAE,CACZC,OAAO,CAACN,KAAK,CAAC,4BAA4B,CAAEK,GAAG,CAAC,CAChDJ,QAAQ,CAAC,uCAAuC,CAAC,CACnD,CACF,CAAC,CAED,KAAM,CAAAM,oBAAoB,CAAIC,QAAkB,EAAK,CACnDjB,gBAAgB,CAACiB,QAAQ,CAAC,CAC1BT,gBAAgB,CAAC,EAAE,CAAC,CACpBJ,WAAW,CAAC,EAAE,CAAC,CACfE,cAAc,CAAC,EAAE,CAAC,CACpB,CAAC,CAED,mBACEX,KAAA,CAACZ,KAAK,EAACmC,SAAS,CAAE,CAAE,CAACC,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eACvC7B,IAAA,CAACX,UAAU,EAACyC,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,mCAEtC,CAAY,CAAC,cACb3B,KAAA,CAAChB,GAAG,EAACwC,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAM,CAAEC,GAAG,CAAE,CAAC,CAAEN,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eAC5D7B,IAAA,CAACb,SAAS,EACRgD,KAAK,CAAC,yBAAgB,CACtBC,KAAK,CAAE1B,QAAS,CAChB2B,QAAQ,CAAGC,CAAC,EAAK3B,WAAW,CAAC2B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC7CV,EAAE,CAAE,CAAEc,IAAI,CAAE,SAAS,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAC5C,CAAC,cACFzC,IAAA,CAACb,SAAS,EACRgD,KAAK,CAAC,mCAAe,CACrBC,KAAK,CAAExB,WAAY,CACnByB,QAAQ,CAAGC,CAAC,EAAKzB,cAAc,CAACyB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChDV,EAAE,CAAE,CAAEc,IAAI,CAAE,SAAS,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAC5C,CAAC,cACFzC,IAAA,CAACZ,MAAM,EACL0C,OAAO,CAAC,WAAW,CACnBY,SAAS,CAAEjC,OAAO,cAAGT,IAAA,CAACJ,gBAAgB,EAAC+C,IAAI,CAAE,EAAG,CAACC,KAAK,CAAC,SAAS,CAAE,CAAC,cAAG5C,IAAA,CAACH,UAAU,GAAE,CAAE,CACrFgD,OAAO,CAAE3B,YAAa,CACtB4B,QAAQ,CAAErC,OAAQ,CAClBiB,EAAE,CAAE,CAAEc,IAAI,CAAE,UAAU,CAAEC,QAAQ,CAAE,OAAO,CAAEM,MAAM,CAAE,MAAO,CAAE,CAAAlB,QAAA,CAC7D,kBAED,CAAQ,CAAC,EACN,CAAC,CAELb,KAAK,eACJhB,IAAA,CAACX,UAAU,EAACuD,KAAK,CAAC,OAAO,CAAClB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,CACrCb,KAAK,CACI,CACb,CAEAF,aAAa,CAACM,MAAM,CAAG,CAAC,eACvBlB,KAAA,CAAChB,GAAG,EAACwC,EAAE,CAAE,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACjB7B,IAAA,CAACX,UAAU,EAACyC,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAF,QAAA,CAAC,oCAE7C,CAAY,CAAC,cACb7B,IAAA,CAACT,IAAI,EAACmC,EAAE,CAAE,CAAEuB,KAAK,CAAE,MAAM,CAAEC,OAAO,CAAE,kBAAmB,CAAE,CAAArB,QAAA,CACtDf,aAAa,CAACqC,GAAG,CAAC,CAAC3B,QAAQ,CAAE4B,KAAK,gBACjClD,KAAA,CAAClB,KAAK,CAACmB,QAAQ,EAAA0B,QAAA,eACb7B,IAAA,CAACR,QAAQ,EAAC6D,cAAc,MAAAxB,QAAA,cACtB3B,KAAA,CAACR,cAAc,EAACmD,OAAO,CAAEA,CAAA,GAAMtB,oBAAoB,CAACC,QAAQ,CAAE,CAAAK,QAAA,eAC5D7B,IAAA,CAACF,UAAU,EAAC4B,EAAE,CAAE,CAAE4B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC7BtD,IAAA,CAACP,YAAY,EACX8D,OAAO,CAAE/B,QAAQ,CAACgC,QAAS,CAC3BC,SAAS,cACPvD,KAAA,CAAAE,SAAA,EAAAyB,QAAA,EACGL,QAAQ,CAACZ,WAAW,CACpBY,QAAQ,CAACkC,WAAW,QAAAC,MAAA,CAAUnC,QAAQ,CAACkC,WAAW,CAAE,EACrD,CACH,CACF,CAAC,EACY,CAAC,CACT,CAAC,CACVN,KAAK,CAAGtC,aAAa,CAACM,MAAM,CAAG,CAAC,eAAIpB,IAAA,CAACL,OAAO,GAAE,CAAC,GAf7B6B,QAAQ,CAACoC,EAgBd,CACjB,CAAC,CACE,CAAC,EACJ,CACN,EACI,CAAC,CAEZ,CAAC,CAED,cAAe,CAAAvD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}