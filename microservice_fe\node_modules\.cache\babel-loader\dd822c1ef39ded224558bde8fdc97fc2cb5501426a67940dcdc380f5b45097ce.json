{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Alert,AlertTitle,Box,Collapse,IconButton}from'@mui/material';import CloseIcon from'@mui/icons-material/Close';import CheckCircleIcon from'@mui/icons-material/CheckCircle';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SuccessAlert=_ref=>{let{title='Thành công',message,autoHideDuration=5000,onClose,showIcon=true}=_ref;const[open,setOpen]=useState(true);useEffect(()=>{const timer=setTimeout(()=>{setOpen(false);if(onClose){onClose();}},autoHideDuration);return()=>{clearTimeout(timer);};},[autoHideDuration,onClose]);const handleClose=()=>{setOpen(false);if(onClose){onClose();}};return/*#__PURE__*/_jsx(Box,{sx:{my:2},children:/*#__PURE__*/_jsx(Collapse,{in:open,children:/*#__PURE__*/_jsxs(Alert,{severity:\"success\",icon:showIcon?/*#__PURE__*/_jsx(CheckCircleIcon,{fontSize:\"inherit\"}):false,action:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"close\",color:\"inherit\",size:\"small\",onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{fontSize:\"inherit\"})}),sx:{'& .MuiAlert-message':{wordBreak:'break-word',whiteSpace:'pre-wrap'}},children:[/*#__PURE__*/_jsx(AlertTitle,{children:title}),message]})})});};export default SuccessAlert;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Box", "Collapse", "IconButton", "CloseIcon", "CheckCircleIcon", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "title", "message", "autoHideDuration", "onClose", "showIcon", "open", "<PERSON><PERSON><PERSON>", "timer", "setTimeout", "clearTimeout", "handleClose", "sx", "my", "children", "in", "severity", "icon", "fontSize", "action", "color", "size", "onClick", "wordBreak", "whiteSpace"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/common/SuccessAlert.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Alert, AlertTitle, Box, Collapse, IconButton } from '@mui/material';\nimport CloseIcon from '@mui/icons-material/Close';\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\n\ninterface SuccessAlertProps {\n  title?: string;\n  message: string;\n  autoHideDuration?: number;\n  onClose?: () => void;\n  showIcon?: boolean;\n}\n\nconst SuccessAlert: React.FC<SuccessAlertProps> = ({\n  title = 'Thành công',\n  message,\n  autoHideDuration = 5000,\n  onClose,\n  showIcon = true\n}) => {\n  const [open, setOpen] = useState(true);\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setOpen(false);\n      if (onClose) {\n        onClose();\n      }\n    }, autoHideDuration);\n\n    return () => {\n      clearTimeout(timer);\n    };\n  }, [autoHideDuration, onClose]);\n\n  const handleClose = () => {\n    setOpen(false);\n    if (onClose) {\n      onClose();\n    }\n  };\n\n  return (\n    <Box sx={{ my: 2 }}>\n      <Collapse in={open}>\n        <Alert\n          severity=\"success\"\n          icon={showIcon ? <CheckCircleIcon fontSize=\"inherit\" /> : false}\n          action={\n            <IconButton\n              aria-label=\"close\"\n              color=\"inherit\"\n              size=\"small\"\n              onClick={handleClose}\n            >\n              <CloseIcon fontSize=\"inherit\" />\n            </IconButton>\n          }\n          sx={{\n            '& .MuiAlert-message': {\n              wordBreak: 'break-word',\n              whiteSpace: 'pre-wrap'\n            }\n          }}\n        >\n          <AlertTitle>{title}</AlertTitle>\n          {message}\n        </Alert>\n      </Collapse>\n    </Box>\n  );\n};\n\nexport default SuccessAlert;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,KAAK,CAAEC,UAAU,CAAEC,GAAG,CAAEC,QAAQ,CAAEC,UAAU,KAAQ,eAAe,CAC5E,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAU9D,KAAM,CAAAC,YAAyC,CAAGC,IAAA,EAM5C,IAN6C,CACjDC,KAAK,CAAG,YAAY,CACpBC,OAAO,CACPC,gBAAgB,CAAG,IAAI,CACvBC,OAAO,CACPC,QAAQ,CAAG,IACb,CAAC,CAAAL,IAAA,CACC,KAAM,CAACM,IAAI,CAAEC,OAAO,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CAEtCC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAqB,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BF,OAAO,CAAC,KAAK,CAAC,CACd,GAAIH,OAAO,CAAE,CACXA,OAAO,CAAC,CAAC,CACX,CACF,CAAC,CAAED,gBAAgB,CAAC,CAEpB,MAAO,IAAM,CACXO,YAAY,CAACF,KAAK,CAAC,CACrB,CAAC,CACH,CAAC,CAAE,CAACL,gBAAgB,CAAEC,OAAO,CAAC,CAAC,CAE/B,KAAM,CAAAO,WAAW,CAAGA,CAAA,GAAM,CACxBJ,OAAO,CAAC,KAAK,CAAC,CACd,GAAIH,OAAO,CAAE,CACXA,OAAO,CAAC,CAAC,CACX,CACF,CAAC,CAED,mBACER,IAAA,CAACN,GAAG,EAACsB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,cACjBlB,IAAA,CAACL,QAAQ,EAACwB,EAAE,CAAET,IAAK,CAAAQ,QAAA,cACjBhB,KAAA,CAACV,KAAK,EACJ4B,QAAQ,CAAC,SAAS,CAClBC,IAAI,CAAEZ,QAAQ,cAAGT,IAAA,CAACF,eAAe,EAACwB,QAAQ,CAAC,SAAS,CAAE,CAAC,CAAG,KAAM,CAChEC,MAAM,cACJvB,IAAA,CAACJ,UAAU,EACT,aAAW,OAAO,CAClB4B,KAAK,CAAC,SAAS,CACfC,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEX,WAAY,CAAAG,QAAA,cAErBlB,IAAA,CAACH,SAAS,EAACyB,QAAQ,CAAC,SAAS,CAAE,CAAC,CACtB,CACb,CACDN,EAAE,CAAE,CACF,qBAAqB,CAAE,CACrBW,SAAS,CAAE,YAAY,CACvBC,UAAU,CAAE,UACd,CACF,CAAE,CAAAV,QAAA,eAEFlB,IAAA,CAACP,UAAU,EAAAyB,QAAA,CAAEb,KAAK,CAAa,CAAC,CAC/BC,OAAO,EACH,CAAC,CACA,CAAC,CACR,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}