{"ast": null, "code": "import _objectSpread from\"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{normalizeCustomerRevenue,normalizeTimeBasedRevenue}from'../../models';import{get}from'../api/apiClient';import axios from'axios';import{formatDateForInput}from'../../utils/dateUtils';// Đả<PERSON> bảo URL đúng, thêm kiểm tra môi trường\nconst API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:8080';const BASE_URL='/api/customer-statistics';// Hàm kiểm tra kết nối đến API\nconst checkApiConnection=async()=>{try{console.log(\"Checking API connection to: \".concat(API_BASE_URL).concat(BASE_URL,\"/health\"));// Thử kết nối trực tiếp đến API Gateway\nconst response=await axios.get(\"\".concat(API_BASE_URL).concat(BASE_URL,\"/health\"),{timeout:5000,headers:{'Accept':'application/json','Content-Type':'application/json','Cache-Control':'no-cache'}});console.log('API health check response:',response.status,response.data);return response.status===200;}catch(error){console.error('API connection check failed:',error);// Thử kết nối trực tiếp đến service\ntry{console.log('Trying direct connection to service at http://localhost:8085/api/customer-statistics/health');const directResponse=await axios.get('http://localhost:8085/api/customer-statistics/health',{timeout:5000,headers:{'Accept':'application/json','Content-Type':'application/json'}});console.log('Direct API health check response:',directResponse.status,directResponse.data);return directResponse.status===200;}catch(directError){console.error('Direct API connection check failed:',directError);return false;}}};export const customerStatisticsService={// Get customer revenue statistics\ngetCustomerRevenueStatistics:async(startDate,endDate)=>{try{// Kiểm tra kết nối API trước khi gọi\nconst isConnected=await checkApiConnection();if(!isConnected){console.error('API connection failed. Service might be down.');throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');}const formattedStartDate=formatDateToString(startDate);const formattedEndDate=formatDateToString(endDate);// Log chi tiết thông tin request\nconsole.log(\"Calling API: \".concat(BASE_URL,\"/revenue?startDate=\").concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate));console.log('Request parameters:',{startDate:formattedStartDate,endDate:formattedEndDate});// Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\nlet result;try{// Tăng timeout để tránh lỗi timeout\nresult=await get(\"\".concat(BASE_URL,\"/revenue?startDate=\").concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate),{timeout:30000,// 30 second timeout\nheaders:{'Accept':'application/json','Content-Type':'application/json','Cache-Control':'no-cache'}});}catch(gatewayError){console.error('Failed to get data through API Gateway:',gatewayError);console.log('Trying direct connection to service...');// Thử kết nối trực tiếp đến service\nconst directResponse=await axios.get(\"http://localhost:8085/api/customer-statistics/revenue?startDate=\".concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate),{timeout:30000,headers:{'Accept':'application/json','Content-Type':'application/json'}});result=directResponse.data;}console.log('API result:',result);// Kiểm tra kết quả trả về\nif(!result){console.error('API returned null or undefined result');return[];}// Normalize the data using our helper function\nif(Array.isArray(result)){try{// Xử lý từng phần tử một cách an toàn\nconst normalizedData=result.map(customer=>{// Đảm bảo customer không null\nif(!customer)return normalizeCustomerRevenue(null);return normalizeCustomerRevenue(customer);}).filter(customer=>customer!==null);console.log('Normalized data:',normalizedData);return normalizedData;}catch(err){console.error('Error mapping customer revenue data:',err);return[];}}// If result is not an array, try to convert it\nif(typeof result==='object'){console.warn('Result is not an array, attempting to convert:',result);try{// Nếu là object, thử chuyển thành array\nconst singleItem=normalizeCustomerRevenue(result);return[singleItem];}catch(err){console.error('Failed to convert object to array:',err);}}console.error('Invalid result format:',result);return[];}catch(error){console.error('Error in getCustomerRevenueStatistics:',error);if(axios.isAxiosError(error)){var _error$response,_error$response2,_error$config;console.error('Response data:',(_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.data);console.error('Response status:',(_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.status);console.error('Request URL:',(_error$config=error.config)===null||_error$config===void 0?void 0:_error$config.url);// Kiểm tra lỗi CORS\nif(error.message.includes('Network Error')||!error.response){console.error('Possible CORS or network issue');}}throw error;}},// Get customer invoices\ngetCustomerInvoices:async(customerId,startDate,endDate)=>{try{// Kiểm tra kết nối API trước khi gọi\nconst isConnected=await checkApiConnection();if(!isConnected){console.error('API connection failed. Service might be down.');throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');}const formattedStartDate=formatDateToString(startDate);const formattedEndDate=formatDateToString(endDate);// Log chi tiết thông tin request\nconsole.log(\"Calling API: \".concat(BASE_URL,\"/customer/\").concat(customerId,\"/invoices?startDate=\").concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate));console.log('Request parameters:',{customerId,startDate:formattedStartDate,endDate:formattedEndDate});// Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\nlet result;try{result=await get(\"\".concat(BASE_URL,\"/customer/\").concat(customerId,\"/invoices?startDate=\").concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate),{timeout:30000,// 30 second timeout\nheaders:{'Accept':'application/json','Content-Type':'application/json','Cache-Control':'no-cache'}});}catch(gatewayError){console.error('Failed to get invoices through API Gateway:',gatewayError);console.log('Trying direct connection to service...');// Thử kết nối trực tiếp đến service\nconst directResponse=await axios.get(\"http://localhost:8085/api/customer-statistics/customer/\".concat(customerId,\"/invoices?startDate=\").concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate),{timeout:30000,headers:{'Accept':'application/json','Content-Type':'application/json'}});result=directResponse.data;}console.log('Invoices result:',result);// Kiểm tra kết quả trả về\nif(!result){console.error('API returned null or undefined result for invoices');return[];}// Ensure we have a valid array\nif(Array.isArray(result)){// Xử lý dữ liệu trả về để đảm bảo các trường cần thiết\nreturn result.map(invoice=>_objectSpread(_objectSpread({},invoice),{},{// Đảm bảo các trường quan trọng có giá trị mặc định nếu null\nid:invoice.id||0,paymentCode:invoice.paymentCode||'',paymentAmount:typeof invoice.paymentAmount==='number'?invoice.paymentAmount:0,contractCode:invoice.contractCode||'Không xác định',paymentDate:invoice.paymentDate?new Date(invoice.paymentDate):new Date()})).filter(invoice=>invoice!==null);}// If result is not an array but is an object, try to convert it\nif(typeof result==='object'&&result!==null){console.warn('Invoices result is not an array, attempting to convert:',result);try{// Nếu là object, thử chuyển thành array\nreturn[result];}catch(err){console.error('Failed to convert invoice object to array:',err);}}// If result is not an array, return empty array\nconsole.error('Invalid invoices result format:',result);return[];}catch(error){console.error('Error in getCustomerInvoices:',error);if(axios.isAxiosError(error)){var _error$response3,_error$response4,_error$config2;console.error('Response data:',(_error$response3=error.response)===null||_error$response3===void 0?void 0:_error$response3.data);console.error('Response status:',(_error$response4=error.response)===null||_error$response4===void 0?void 0:_error$response4.status);console.error('Request URL:',(_error$config2=error.config)===null||_error$config2===void 0?void 0:_error$config2.url);// Kiểm tra lỗi CORS\nif(error.message.includes('Network Error')||!error.response){console.error('Possible CORS or network issue in getCustomerInvoices');}}throw error;}},// Get daily revenue statistics\ngetDailyRevenueStatistics:async(startDate,endDate)=>{try{// Kiểm tra kết nối API trước khi gọi\nconst isConnected=await checkApiConnection();if(!isConnected){console.error('API connection failed. Service might be down.');throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');}const formattedStartDate=formatDateToString(startDate);const formattedEndDate=formatDateToString(endDate);// Log chi tiết thông tin request\nconsole.log(\"Calling API: \".concat(BASE_URL,\"/revenue/daily?startDate=\").concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate));console.log('Request parameters:',{startDate:formattedStartDate,endDate:formattedEndDate});// Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\nlet result;try{// Tăng timeout để tránh lỗi timeout\nresult=await get(\"\".concat(BASE_URL,\"/revenue/daily?startDate=\").concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate),{timeout:30000,// 30 second timeout\nheaders:{'Accept':'application/json','Content-Type':'application/json','Cache-Control':'no-cache'}});}catch(gatewayError){console.error('Failed to get data through API Gateway:',gatewayError);console.log('Trying direct connection to service...');// Thử kết nối trực tiếp đến service\nconst directResponse=await axios.get(\"http://localhost:8085/api/customer-statistics/revenue/daily?startDate=\".concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate),{timeout:30000,headers:{'Accept':'application/json','Content-Type':'application/json'}});result=directResponse.data;}console.log('API result:',result);// Kiểm tra kết quả trả về\nif(!result){console.error('API returned null or undefined result');return[];}// Normalize the data using our helper function\nif(Array.isArray(result)){try{// Xử lý từng phần tử một cách an toàn\nconst normalizedData=result.map(item=>{// Đảm bảo item không null\nif(!item)return normalizeTimeBasedRevenue(null);return normalizeTimeBasedRevenue(item);}).filter(item=>item!==null)// Sắp xếp theo ngày tăng dần\n.sort((a,b)=>{const dateA=new Date(a.date);const dateB=new Date(b.date);return dateA.getTime()-dateB.getTime();});console.log('Normalized data:',normalizedData);return normalizedData;}catch(err){console.error('Error mapping daily revenue data:',err);return[];}}// Fallback if result is not an array\nconsole.error('API result is not an array:',result);return[];}catch(error){console.error('Error fetching daily revenue statistics:',error);throw error;}},// Get weekly revenue statistics\ngetWeeklyRevenueStatistics:async(startDate,endDate)=>{try{// Kiểm tra kết nối API trước khi gọi\nconst isConnected=await checkApiConnection();if(!isConnected){console.error('API connection failed. Service might be down.');throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');}const formattedStartDate=formatDateToString(startDate);const formattedEndDate=formatDateToString(endDate);// Log chi tiết thông tin request\nconsole.log(\"Calling API: \".concat(BASE_URL,\"/revenue/weekly?startDate=\").concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate));console.log('Request parameters:',{startDate:formattedStartDate,endDate:formattedEndDate});// Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\nlet result;try{// Tăng timeout để tránh lỗi timeout\nresult=await get(\"\".concat(BASE_URL,\"/revenue/weekly?startDate=\").concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate),{timeout:30000,// 30 second timeout\nheaders:{'Accept':'application/json','Content-Type':'application/json','Cache-Control':'no-cache'}});}catch(gatewayError){console.error('Failed to get data through API Gateway:',gatewayError);console.log('Trying direct connection to service...');// Thử kết nối trực tiếp đến service\nconst directResponse=await axios.get(\"http://localhost:8085/api/customer-statistics/revenue/weekly?startDate=\".concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate),{timeout:30000,headers:{'Accept':'application/json','Content-Type':'application/json'}});result=directResponse.data;}console.log('API result:',result);// Kiểm tra kết quả trả về\nif(!result){console.error('API returned null or undefined result');return[];}// Normalize the data using our helper function\nif(Array.isArray(result)){try{// Xử lý từng phần tử một cách an toàn\nconst normalizedData=result.map(item=>{// Đảm bảo item không null\nif(!item)return normalizeTimeBasedRevenue(null);return normalizeTimeBasedRevenue(item);}).filter(item=>item!==null);console.log('Normalized data:',normalizedData);return normalizedData;}catch(err){console.error('Error mapping weekly revenue data:',err);return[];}}// Fallback if result is not an array\nconsole.error('API result is not an array:',result);return[];}catch(error){console.error('Error fetching weekly revenue statistics:',error);throw error;}},// Get monthly revenue statistics\ngetMonthlyRevenueStatistics:async(startDate,endDate)=>{try{// Kiểm tra kết nối API trước khi gọi\nconst isConnected=await checkApiConnection();if(!isConnected){console.error('API connection failed. Service might be down.');throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');}const formattedStartDate=formatDateToString(startDate);const formattedEndDate=formatDateToString(endDate);// Log chi tiết thông tin request\nconsole.log(\"Calling API: \".concat(BASE_URL,\"/revenue/monthly?startDate=\").concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate));console.log('Request parameters:',{startDate:formattedStartDate,endDate:formattedEndDate});// Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\nlet result;try{// Tăng timeout để tránh lỗi timeout\nresult=await get(\"\".concat(BASE_URL,\"/revenue/monthly?startDate=\").concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate),{timeout:30000,// 30 second timeout\nheaders:{'Accept':'application/json','Content-Type':'application/json','Cache-Control':'no-cache'}});}catch(gatewayError){console.error('Failed to get data through API Gateway:',gatewayError);console.log('Trying direct connection to service...');// Thử kết nối trực tiếp đến service\nconst directResponse=await axios.get(\"http://localhost:8085/api/customer-statistics/revenue/monthly?startDate=\".concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate),{timeout:30000,headers:{'Accept':'application/json','Content-Type':'application/json'}});result=directResponse.data;}console.log('API result:',result);// Kiểm tra kết quả trả về\nif(!result){console.error('API returned null or undefined result');return[];}// Normalize the data using our helper function\nif(Array.isArray(result)){try{// Xử lý từng phần tử một cách an toàn\nconst normalizedData=result.map(item=>{// Đảm bảo item không null\nif(!item)return normalizeTimeBasedRevenue(null);return normalizeTimeBasedRevenue(item);}).filter(item=>item!==null)// Sắp xếp theo ngày tăng dần\n.sort((a,b)=>{const dateA=new Date(a.date);const dateB=new Date(b.date);return dateA.getTime()-dateB.getTime();});console.log('Normalized data:',normalizedData);return normalizedData;}catch(err){console.error('Error mapping monthly revenue data:',err);return[];}}// Fallback if result is not an array\nconsole.error('API result is not an array:',result);return[];}catch(error){console.error('Error fetching monthly revenue statistics:',error);throw error;}},// Get yearly revenue statistics\ngetYearlyRevenueStatistics:async(startDate,endDate)=>{try{// Kiểm tra kết nối API trước khi gọi\nconst isConnected=await checkApiConnection();if(!isConnected){console.error('API connection failed. Service might be down.');throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');}const formattedStartDate=formatDateToString(startDate);const formattedEndDate=formatDateToString(endDate);// Log chi tiết thông tin request\nconsole.log(\"Calling API: \".concat(BASE_URL,\"/revenue/yearly?startDate=\").concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate));console.log('Request parameters:',{startDate:formattedStartDate,endDate:formattedEndDate});// Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\nlet result;try{// Tăng timeout để tránh lỗi timeout\nresult=await get(\"\".concat(BASE_URL,\"/revenue/yearly?startDate=\").concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate),{timeout:30000,// 30 second timeout\nheaders:{'Accept':'application/json','Content-Type':'application/json','Cache-Control':'no-cache'}});}catch(gatewayError){console.error('Failed to get data through API Gateway:',gatewayError);console.log('Trying direct connection to service...');// Thử kết nối trực tiếp đến service\nconst directResponse=await axios.get(\"http://localhost:8085/api/customer-statistics/revenue/yearly?startDate=\".concat(formattedStartDate,\"&endDate=\").concat(formattedEndDate),{timeout:30000,headers:{'Accept':'application/json','Content-Type':'application/json'}});result=directResponse.data;}console.log('API result:',result);// Kiểm tra kết quả trả về\nif(!result){console.error('API returned null or undefined result');return[];}// Normalize the data using our helper function\nif(Array.isArray(result)){try{// Xử lý từng phần tử một cách an toàn\nconst normalizedData=result.map(item=>{// Đảm bảo item không null\nif(!item)return normalizeTimeBasedRevenue(null);return normalizeTimeBasedRevenue(item);}).filter(item=>item!==null)// Sắp xếp theo năm tăng dần\n.sort((a,b)=>{const dateA=new Date(a.date);const dateB=new Date(b.date);return dateA.getTime()-dateB.getTime();});console.log('Normalized data:',normalizedData);return normalizedData;}catch(err){console.error('Error mapping yearly revenue data:',err);return[];}}// Fallback if result is not an array\nconsole.error('API result is not an array:',result);return[];}catch(error){console.error('Error fetching yearly revenue statistics:',error);throw error;}}};// Helper function to format date to string in ISO format (yyyy-MM-dd)\nconst formatDateToString=date=>{try{// Ensure we have a valid date\nif(!(date instanceof Date)||isNaN(date.getTime())){console.error('Invalid date provided:',date);// Return today's date as fallback\nconst today=new Date();return formatDateForInput(today);}// Xử lý đặc biệt để tránh vấn đề múi giờ\n// Đặt giờ là 12:00:00 để tránh vấn đề chuyển đổi múi giờ\nconst adjustedDate=new Date(date.getFullYear(),date.getMonth(),date.getDate(),12,0,0);// Format to ISO date string using our utility function\nreturn formatDateForInput(adjustedDate);}catch(error){console.error('Error formatting date:',error);// Return today's date as fallback\nconst today=new Date();return formatDateForInput(today);}};", "map": {"version": 3, "names": ["normalizeCustomerRevenue", "normalizeTimeBasedRevenue", "get", "axios", "formatDateForInput", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "BASE_URL", "checkApiConnection", "console", "log", "concat", "response", "timeout", "headers", "status", "data", "error", "directResponse", "directError", "customerStatisticsService", "getCustomerRevenueStatistics", "startDate", "endDate", "isConnected", "Error", "formattedStartDate", "formatDateToString", "formattedEndDate", "result", "gatewayError", "Array", "isArray", "normalizedData", "map", "customer", "filter", "err", "warn", "singleItem", "isAxiosError", "_error$response", "_error$response2", "_error$config", "config", "url", "message", "includes", "getCustomerInvoices", "customerId", "invoice", "_objectSpread", "id", "paymentCode", "paymentAmount", "contractCode", "paymentDate", "Date", "_error$response3", "_error$response4", "_error$config2", "getDailyRevenueStatistics", "item", "sort", "a", "b", "dateA", "date", "dateB", "getTime", "getWeeklyRevenueStatistics", "getMonthlyRevenueStatistics", "getYearlyRevenueStatistics", "isNaN", "today", "adjustedDate", "getFullYear", "getMonth", "getDate"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/services/statistics/customerStatisticsService.ts"], "sourcesContent": ["import { CustomerRevenue, CustomerPayment, TimeBasedRevenue, normalizeCustomerRevenue, normalizeTimeBasedRevenue } from '../../models';\nimport { get } from '../api/apiClient';\nimport axios from 'axios';\nimport { formatDateForInput } from '../../utils/dateUtils';\n\n// Đảm bảo URL đúng, thêm kiểm tra môi trường\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080';\nconst BASE_URL = '/api/customer-statistics';\n\n// Hàm kiểm tra kết nối đến API\nconst checkApiConnection = async (): Promise<boolean> => {\n  try {\n    console.log(`Checking API connection to: ${API_BASE_URL}${BASE_URL}/health`);\n\n    // Thử kết nối trực tiếp đến API Gateway\n    const response = await axios.get(`${API_BASE_URL}${BASE_URL}/health`, {\n      timeout: 5000,\n      headers: {\n        'Accept': 'application/json',\n        'Content-Type': 'application/json',\n        'Cache-Control': 'no-cache'\n      }\n    });\n\n    console.log('API health check response:', response.status, response.data);\n    return response.status === 200;\n  } catch (error) {\n    console.error('API connection check failed:', error);\n\n    // Thử kết nối trực tiếp đến service\n    try {\n      console.log('Trying direct connection to service at http://localhost:8085/api/customer-statistics/health');\n      const directResponse = await axios.get('http://localhost:8085/api/customer-statistics/health', {\n        timeout: 5000,\n        headers: {\n          'Accept': 'application/json',\n          'Content-Type': 'application/json'\n        }\n      });\n      console.log('Direct API health check response:', directResponse.status, directResponse.data);\n      return directResponse.status === 200;\n    } catch (directError) {\n      console.error('Direct API connection check failed:', directError);\n      return false;\n    }\n  }\n};\n\nexport const customerStatisticsService = {\n  // Get customer revenue statistics\n  getCustomerRevenueStatistics: async (startDate: Date, endDate: Date): Promise<CustomerRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<CustomerRevenue[]>(\n          `${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(customer => {\n            // Đảm bảo customer không null\n            if (!customer) return normalizeCustomerRevenue(null);\n            return normalizeCustomerRevenue(customer);\n          }).filter(customer => customer !== null);\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping customer revenue data:', err);\n          return [];\n        }\n      }\n\n      // If result is not an array, try to convert it\n      if (typeof result === 'object') {\n        console.warn('Result is not an array, attempting to convert:', result);\n        try {\n          // Nếu là object, thử chuyển thành array\n          const singleItem = normalizeCustomerRevenue(result);\n          return [singleItem];\n        } catch (err) {\n          console.error('Failed to convert object to array:', err);\n        }\n      }\n\n      console.error('Invalid result format:', result);\n      return [];\n    } catch (error) {\n      console.error('Error in getCustomerRevenueStatistics:', error);\n      if (axios.isAxiosError(error)) {\n        console.error('Response data:', error.response?.data);\n        console.error('Response status:', error.response?.status);\n        console.error('Request URL:', error.config?.url);\n\n        // Kiểm tra lỗi CORS\n        if (error.message.includes('Network Error') || !error.response) {\n          console.error('Possible CORS or network issue');\n        }\n      }\n      throw error;\n    }\n  },\n\n  // Get customer invoices\n  getCustomerInvoices: async (customerId: number, startDate: Date, endDate: Date): Promise<CustomerPayment[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { customerId, startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        result = await get<CustomerPayment[]>(\n          `${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get invoices through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('Invoices result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result for invoices');\n        return [];\n      }\n\n      // Ensure we have a valid array\n      if (Array.isArray(result)) {\n        // Xử lý dữ liệu trả về để đảm bảo các trường cần thiết\n        return result.map(invoice => ({\n          ...invoice,\n          // Đảm bảo các trường quan trọng có giá trị mặc định nếu null\n          id: invoice.id || 0,\n          paymentCode: invoice.paymentCode || '',\n          paymentAmount: typeof invoice.paymentAmount === 'number' ? invoice.paymentAmount : 0,\n          contractCode: invoice.contractCode || 'Không xác định',\n          paymentDate: invoice.paymentDate ? new Date(invoice.paymentDate) : new Date()\n        })).filter(invoice => invoice !== null);\n      }\n\n      // If result is not an array but is an object, try to convert it\n      if (typeof result === 'object' && result !== null) {\n        console.warn('Invoices result is not an array, attempting to convert:', result);\n        try {\n          // Nếu là object, thử chuyển thành array\n          return [result as CustomerPayment];\n        } catch (err) {\n          console.error('Failed to convert invoice object to array:', err);\n        }\n      }\n\n      // If result is not an array, return empty array\n      console.error('Invalid invoices result format:', result);\n      return [];\n    } catch (error) {\n      console.error('Error in getCustomerInvoices:', error);\n      if (axios.isAxiosError(error)) {\n        console.error('Response data:', error.response?.data);\n        console.error('Response status:', error.response?.status);\n        console.error('Request URL:', error.config?.url);\n\n        // Kiểm tra lỗi CORS\n        if (error.message.includes('Network Error') || !error.response) {\n          console.error('Possible CORS or network issue in getCustomerInvoices');\n        }\n      }\n      throw error;\n    }\n  },\n\n  // Get daily revenue statistics\n  getDailyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          })\n          .filter(item => item !== null)\n          // Sắp xếp theo ngày tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping daily revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching daily revenue statistics:', error);\n      throw error;\n    }\n  },\n\n  // Get weekly revenue statistics\n  getWeeklyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null);\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping weekly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching weekly revenue statistics:', error);\n      throw error;\n    }\n  },\n\n  // Get monthly revenue statistics\n  getMonthlyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          })\n          .filter(item => item !== null)\n          // Sắp xếp theo ngày tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping monthly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching monthly revenue statistics:', error);\n      throw error;\n    }\n  },\n\n  // Get yearly revenue statistics\n  getYearlyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          })\n          .filter(item => item !== null)\n          // Sắp xếp theo năm tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping yearly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching yearly revenue statistics:', error);\n      throw error;\n    }\n  }\n};\n\n// Helper function to format date to string in ISO format (yyyy-MM-dd)\nconst formatDateToString = (date: Date): string => {\n  try {\n    // Ensure we have a valid date\n    if (!(date instanceof Date) || isNaN(date.getTime())) {\n      console.error('Invalid date provided:', date);\n      // Return today's date as fallback\n      const today = new Date();\n      return formatDateForInput(today);\n    }\n\n    // Xử lý đặc biệt để tránh vấn đề múi giờ\n    // Đặt giờ là 12:00:00 để tránh vấn đề chuyển đổi múi giờ\n    const adjustedDate = new Date(\n      date.getFullYear(),\n      date.getMonth(),\n      date.getDate(),\n      12, 0, 0\n    );\n\n    // Format to ISO date string using our utility function\n    return formatDateForInput(adjustedDate);\n  } catch (error) {\n    console.error('Error formatting date:', error);\n    // Return today's date as fallback\n    const today = new Date();\n    return formatDateForInput(today);\n  }\n};\n"], "mappings": "gKAAA,OAA6DA,wBAAwB,CAAEC,yBAAyB,KAAQ,cAAc,CACtI,OAASC,GAAG,KAAQ,kBAAkB,CACtC,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,kBAAkB,KAAQ,uBAAuB,CAE1D;AACA,KAAM,CAAAC,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CAC7E,KAAM,CAAAC,QAAQ,CAAG,0BAA0B,CAE3C;AACA,KAAM,CAAAC,kBAAkB,CAAG,KAAAA,CAAA,GAA8B,CACvD,GAAI,CACFC,OAAO,CAACC,GAAG,gCAAAC,MAAA,CAAgCR,YAAY,EAAAQ,MAAA,CAAGJ,QAAQ,WAAS,CAAC,CAE5E;AACA,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAX,KAAK,CAACD,GAAG,IAAAW,MAAA,CAAIR,YAAY,EAAAQ,MAAA,CAAGJ,QAAQ,YAAW,CACpEM,OAAO,CAAE,IAAI,CACbC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAAkB,CAClC,eAAe,CAAE,UACnB,CACF,CAAC,CAAC,CAEFL,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAEE,QAAQ,CAACG,MAAM,CAAEH,QAAQ,CAACI,IAAI,CAAC,CACzE,MAAO,CAAAJ,QAAQ,CAACG,MAAM,GAAK,GAAG,CAChC,CAAE,MAAOE,KAAK,CAAE,CACdR,OAAO,CAACQ,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CAEpD;AACA,GAAI,CACFR,OAAO,CAACC,GAAG,CAAC,6FAA6F,CAAC,CAC1G,KAAM,CAAAQ,cAAc,CAAG,KAAM,CAAAjB,KAAK,CAACD,GAAG,CAAC,sDAAsD,CAAE,CAC7Fa,OAAO,CAAE,IAAI,CACbC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CACFL,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAEQ,cAAc,CAACH,MAAM,CAAEG,cAAc,CAACF,IAAI,CAAC,CAC5F,MAAO,CAAAE,cAAc,CAACH,MAAM,GAAK,GAAG,CACtC,CAAE,MAAOI,WAAW,CAAE,CACpBV,OAAO,CAACQ,KAAK,CAAC,qCAAqC,CAAEE,WAAW,CAAC,CACjE,MAAO,MAAK,CACd,CACF,CACF,CAAC,CAED,MAAO,MAAM,CAAAC,yBAAyB,CAAG,CACvC;AACAC,4BAA4B,CAAE,KAAAA,CAAOC,SAAe,CAAEC,OAAa,GAAiC,CAClG,GAAI,CACF;AACA,KAAM,CAAAC,WAAW,CAAG,KAAM,CAAAhB,kBAAkB,CAAC,CAAC,CAC9C,GAAI,CAACgB,WAAW,CAAE,CAChBf,OAAO,CAACQ,KAAK,CAAC,+CAA+C,CAAC,CAC9D,KAAM,IAAI,CAAAQ,KAAK,CAAC,wEAAwE,CAAC,CAC3F,CAEA,KAAM,CAAAC,kBAAkB,CAAGC,kBAAkB,CAACL,SAAS,CAAC,CACxD,KAAM,CAAAM,gBAAgB,CAAGD,kBAAkB,CAACJ,OAAO,CAAC,CAEpD;AACAd,OAAO,CAACC,GAAG,iBAAAC,MAAA,CAAiBJ,QAAQ,wBAAAI,MAAA,CAAsBe,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,CAAE,CAAC,CAC3GnB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAE,CAAEY,SAAS,CAAEI,kBAAkB,CAAEH,OAAO,CAAEK,gBAAiB,CAAC,CAAC,CAEhG;AACA,GAAI,CAAAC,MAAM,CACV,GAAI,CACF;AACAA,MAAM,CAAG,KAAM,CAAA7B,GAAG,IAAAW,MAAA,CACbJ,QAAQ,wBAAAI,MAAA,CAAsBe,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,EAC/E,CACEf,OAAO,CAAE,KAAK,CAAE;AAChBC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAAkB,CAClC,eAAe,CAAE,UACnB,CACF,CACF,CAAC,CACH,CAAE,MAAOgB,YAAY,CAAE,CACrBrB,OAAO,CAACQ,KAAK,CAAC,yCAAyC,CAAEa,YAAY,CAAC,CACtErB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CAErD;AACA,KAAM,CAAAQ,cAAc,CAAG,KAAM,CAAAjB,KAAK,CAACD,GAAG,oEAAAW,MAAA,CAC+Be,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,EACjH,CACEf,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAClB,CACF,CACF,CAAC,CAEDe,MAAM,CAAGX,cAAc,CAACF,IAAI,CAC9B,CAEAP,OAAO,CAACC,GAAG,CAAC,aAAa,CAAEmB,MAAM,CAAC,CAElC;AACA,GAAI,CAACA,MAAM,CAAE,CACXpB,OAAO,CAACQ,KAAK,CAAC,uCAAuC,CAAC,CACtD,MAAO,EAAE,CACX,CAEA;AACA,GAAIc,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,CAAE,CACzB,GAAI,CACF;AACA,KAAM,CAAAI,cAAc,CAAGJ,MAAM,CAACK,GAAG,CAACC,QAAQ,EAAI,CAC5C;AACA,GAAI,CAACA,QAAQ,CAAE,MAAO,CAAArC,wBAAwB,CAAC,IAAI,CAAC,CACpD,MAAO,CAAAA,wBAAwB,CAACqC,QAAQ,CAAC,CAC3C,CAAC,CAAC,CAACC,MAAM,CAACD,QAAQ,EAAIA,QAAQ,GAAK,IAAI,CAAC,CAExC1B,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEuB,cAAc,CAAC,CAC/C,MAAO,CAAAA,cAAc,CACvB,CAAE,MAAOI,GAAG,CAAE,CACZ5B,OAAO,CAACQ,KAAK,CAAC,sCAAsC,CAAEoB,GAAG,CAAC,CAC1D,MAAO,EAAE,CACX,CACF,CAEA;AACA,GAAI,MAAO,CAAAR,MAAM,GAAK,QAAQ,CAAE,CAC9BpB,OAAO,CAAC6B,IAAI,CAAC,gDAAgD,CAAET,MAAM,CAAC,CACtE,GAAI,CACF;AACA,KAAM,CAAAU,UAAU,CAAGzC,wBAAwB,CAAC+B,MAAM,CAAC,CACnD,MAAO,CAACU,UAAU,CAAC,CACrB,CAAE,MAAOF,GAAG,CAAE,CACZ5B,OAAO,CAACQ,KAAK,CAAC,oCAAoC,CAAEoB,GAAG,CAAC,CAC1D,CACF,CAEA5B,OAAO,CAACQ,KAAK,CAAC,wBAAwB,CAAEY,MAAM,CAAC,CAC/C,MAAO,EAAE,CACX,CAAE,MAAOZ,KAAK,CAAE,CACdR,OAAO,CAACQ,KAAK,CAAC,wCAAwC,CAAEA,KAAK,CAAC,CAC9D,GAAIhB,KAAK,CAACuC,YAAY,CAACvB,KAAK,CAAC,CAAE,KAAAwB,eAAA,CAAAC,gBAAA,CAAAC,aAAA,CAC7BlC,OAAO,CAACQ,KAAK,CAAC,gBAAgB,EAAAwB,eAAA,CAAExB,KAAK,CAACL,QAAQ,UAAA6B,eAAA,iBAAdA,eAAA,CAAgBzB,IAAI,CAAC,CACrDP,OAAO,CAACQ,KAAK,CAAC,kBAAkB,EAAAyB,gBAAA,CAAEzB,KAAK,CAACL,QAAQ,UAAA8B,gBAAA,iBAAdA,gBAAA,CAAgB3B,MAAM,CAAC,CACzDN,OAAO,CAACQ,KAAK,CAAC,cAAc,EAAA0B,aAAA,CAAE1B,KAAK,CAAC2B,MAAM,UAAAD,aAAA,iBAAZA,aAAA,CAAcE,GAAG,CAAC,CAEhD;AACA,GAAI5B,KAAK,CAAC6B,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAI,CAAC9B,KAAK,CAACL,QAAQ,CAAE,CAC9DH,OAAO,CAACQ,KAAK,CAAC,gCAAgC,CAAC,CACjD,CACF,CACA,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA+B,mBAAmB,CAAE,KAAAA,CAAOC,UAAkB,CAAE3B,SAAe,CAAEC,OAAa,GAAiC,CAC7G,GAAI,CACF;AACA,KAAM,CAAAC,WAAW,CAAG,KAAM,CAAAhB,kBAAkB,CAAC,CAAC,CAC9C,GAAI,CAACgB,WAAW,CAAE,CAChBf,OAAO,CAACQ,KAAK,CAAC,+CAA+C,CAAC,CAC9D,KAAM,IAAI,CAAAQ,KAAK,CAAC,wEAAwE,CAAC,CAC3F,CAEA,KAAM,CAAAC,kBAAkB,CAAGC,kBAAkB,CAACL,SAAS,CAAC,CACxD,KAAM,CAAAM,gBAAgB,CAAGD,kBAAkB,CAACJ,OAAO,CAAC,CAEpD;AACAd,OAAO,CAACC,GAAG,iBAAAC,MAAA,CAAiBJ,QAAQ,eAAAI,MAAA,CAAasC,UAAU,yBAAAtC,MAAA,CAAuBe,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,CAAE,CAAC,CACnInB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAE,CAAEuC,UAAU,CAAE3B,SAAS,CAAEI,kBAAkB,CAAEH,OAAO,CAAEK,gBAAiB,CAAC,CAAC,CAE5G;AACA,GAAI,CAAAC,MAAM,CACV,GAAI,CACFA,MAAM,CAAG,KAAM,CAAA7B,GAAG,IAAAW,MAAA,CACbJ,QAAQ,eAAAI,MAAA,CAAasC,UAAU,yBAAAtC,MAAA,CAAuBe,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,EACvG,CACEf,OAAO,CAAE,KAAK,CAAE;AAChBC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAAkB,CAClC,eAAe,CAAE,UACnB,CACF,CACF,CAAC,CACH,CAAE,MAAOgB,YAAY,CAAE,CACrBrB,OAAO,CAACQ,KAAK,CAAC,6CAA6C,CAAEa,YAAY,CAAC,CAC1ErB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CAErD;AACA,KAAM,CAAAQ,cAAc,CAAG,KAAM,CAAAjB,KAAK,CAACD,GAAG,2DAAAW,MAAA,CACsBsC,UAAU,yBAAAtC,MAAA,CAAuBe,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,EACzI,CACEf,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAClB,CACF,CACF,CAAC,CAEDe,MAAM,CAAGX,cAAc,CAACF,IAAI,CAC9B,CAEAP,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEmB,MAAM,CAAC,CAEvC;AACA,GAAI,CAACA,MAAM,CAAE,CACXpB,OAAO,CAACQ,KAAK,CAAC,oDAAoD,CAAC,CACnE,MAAO,EAAE,CACX,CAEA;AACA,GAAIc,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,CAAE,CACzB;AACA,MAAO,CAAAA,MAAM,CAACK,GAAG,CAACgB,OAAO,EAAAC,aAAA,CAAAA,aAAA,IACpBD,OAAO,MACV;AACAE,EAAE,CAAEF,OAAO,CAACE,EAAE,EAAI,CAAC,CACnBC,WAAW,CAAEH,OAAO,CAACG,WAAW,EAAI,EAAE,CACtCC,aAAa,CAAE,MAAO,CAAAJ,OAAO,CAACI,aAAa,GAAK,QAAQ,CAAGJ,OAAO,CAACI,aAAa,CAAG,CAAC,CACpFC,YAAY,CAAEL,OAAO,CAACK,YAAY,EAAI,gBAAgB,CACtDC,WAAW,CAAEN,OAAO,CAACM,WAAW,CAAG,GAAI,CAAAC,IAAI,CAACP,OAAO,CAACM,WAAW,CAAC,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,EAC7E,CAAC,CAACrB,MAAM,CAACc,OAAO,EAAIA,OAAO,GAAK,IAAI,CAAC,CACzC,CAEA;AACA,GAAI,MAAO,CAAArB,MAAM,GAAK,QAAQ,EAAIA,MAAM,GAAK,IAAI,CAAE,CACjDpB,OAAO,CAAC6B,IAAI,CAAC,yDAAyD,CAAET,MAAM,CAAC,CAC/E,GAAI,CACF;AACA,MAAO,CAACA,MAAM,CAAoB,CACpC,CAAE,MAAOQ,GAAG,CAAE,CACZ5B,OAAO,CAACQ,KAAK,CAAC,4CAA4C,CAAEoB,GAAG,CAAC,CAClE,CACF,CAEA;AACA5B,OAAO,CAACQ,KAAK,CAAC,iCAAiC,CAAEY,MAAM,CAAC,CACxD,MAAO,EAAE,CACX,CAAE,MAAOZ,KAAK,CAAE,CACdR,OAAO,CAACQ,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,GAAIhB,KAAK,CAACuC,YAAY,CAACvB,KAAK,CAAC,CAAE,KAAAyC,gBAAA,CAAAC,gBAAA,CAAAC,cAAA,CAC7BnD,OAAO,CAACQ,KAAK,CAAC,gBAAgB,EAAAyC,gBAAA,CAAEzC,KAAK,CAACL,QAAQ,UAAA8C,gBAAA,iBAAdA,gBAAA,CAAgB1C,IAAI,CAAC,CACrDP,OAAO,CAACQ,KAAK,CAAC,kBAAkB,EAAA0C,gBAAA,CAAE1C,KAAK,CAACL,QAAQ,UAAA+C,gBAAA,iBAAdA,gBAAA,CAAgB5C,MAAM,CAAC,CACzDN,OAAO,CAACQ,KAAK,CAAC,cAAc,EAAA2C,cAAA,CAAE3C,KAAK,CAAC2B,MAAM,UAAAgB,cAAA,iBAAZA,cAAA,CAAcf,GAAG,CAAC,CAEhD;AACA,GAAI5B,KAAK,CAAC6B,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAI,CAAC9B,KAAK,CAACL,QAAQ,CAAE,CAC9DH,OAAO,CAACQ,KAAK,CAAC,uDAAuD,CAAC,CACxE,CACF,CACA,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA4C,yBAAyB,CAAE,KAAAA,CAAOvC,SAAe,CAAEC,OAAa,GAAkC,CAChG,GAAI,CACF;AACA,KAAM,CAAAC,WAAW,CAAG,KAAM,CAAAhB,kBAAkB,CAAC,CAAC,CAC9C,GAAI,CAACgB,WAAW,CAAE,CAChBf,OAAO,CAACQ,KAAK,CAAC,+CAA+C,CAAC,CAC9D,KAAM,IAAI,CAAAQ,KAAK,CAAC,wEAAwE,CAAC,CAC3F,CAEA,KAAM,CAAAC,kBAAkB,CAAGC,kBAAkB,CAACL,SAAS,CAAC,CACxD,KAAM,CAAAM,gBAAgB,CAAGD,kBAAkB,CAACJ,OAAO,CAAC,CAEpD;AACAd,OAAO,CAACC,GAAG,iBAAAC,MAAA,CAAiBJ,QAAQ,8BAAAI,MAAA,CAA4Be,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,CAAE,CAAC,CACjHnB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAE,CAAEY,SAAS,CAAEI,kBAAkB,CAAEH,OAAO,CAAEK,gBAAiB,CAAC,CAAC,CAEhG;AACA,GAAI,CAAAC,MAAM,CACV,GAAI,CACF;AACAA,MAAM,CAAG,KAAM,CAAA7B,GAAG,IAAAW,MAAA,CACbJ,QAAQ,8BAAAI,MAAA,CAA4Be,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,EACrF,CACEf,OAAO,CAAE,KAAK,CAAE;AAChBC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAAkB,CAClC,eAAe,CAAE,UACnB,CACF,CACF,CAAC,CACH,CAAE,MAAOgB,YAAY,CAAE,CACrBrB,OAAO,CAACQ,KAAK,CAAC,yCAAyC,CAAEa,YAAY,CAAC,CACtErB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CAErD;AACA,KAAM,CAAAQ,cAAc,CAAG,KAAM,CAAAjB,KAAK,CAACD,GAAG,0EAAAW,MAAA,CACqCe,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,EACvH,CACEf,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAClB,CACF,CACF,CAAC,CAEDe,MAAM,CAAGX,cAAc,CAACF,IAAI,CAC9B,CAEAP,OAAO,CAACC,GAAG,CAAC,aAAa,CAAEmB,MAAM,CAAC,CAElC;AACA,GAAI,CAACA,MAAM,CAAE,CACXpB,OAAO,CAACQ,KAAK,CAAC,uCAAuC,CAAC,CACtD,MAAO,EAAE,CACX,CAEA;AACA,GAAIc,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,CAAE,CACzB,GAAI,CACF;AACA,KAAM,CAAAI,cAAc,CAAGJ,MAAM,CAACK,GAAG,CAAC4B,IAAI,EAAI,CACxC;AACA,GAAI,CAACA,IAAI,CAAE,MAAO,CAAA/D,yBAAyB,CAAC,IAAI,CAAC,CACjD,MAAO,CAAAA,yBAAyB,CAAC+D,IAAI,CAAC,CACxC,CAAC,CAAC,CACD1B,MAAM,CAAC0B,IAAI,EAAIA,IAAI,GAAK,IAAI,CAC7B;AAAA,CACCC,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CACd,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAT,IAAI,CAACO,CAAC,CAACG,IAAI,CAAC,CAC9B,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAX,IAAI,CAACQ,CAAC,CAACE,IAAI,CAAC,CAC9B,MAAO,CAAAD,KAAK,CAACG,OAAO,CAAC,CAAC,CAAGD,KAAK,CAACC,OAAO,CAAC,CAAC,CAC1C,CAAC,CAAC,CAEF5D,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEuB,cAAc,CAAC,CAC/C,MAAO,CAAAA,cAAc,CACvB,CAAE,MAAOI,GAAG,CAAE,CACZ5B,OAAO,CAACQ,KAAK,CAAC,mCAAmC,CAAEoB,GAAG,CAAC,CACvD,MAAO,EAAE,CACX,CACF,CAEA;AACA5B,OAAO,CAACQ,KAAK,CAAC,6BAA6B,CAAEY,MAAM,CAAC,CACpD,MAAO,EAAE,CACX,CAAE,MAAOZ,KAAK,CAAE,CACdR,OAAO,CAACQ,KAAK,CAAC,0CAA0C,CAAEA,KAAK,CAAC,CAChE,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACAqD,0BAA0B,CAAE,KAAAA,CAAOhD,SAAe,CAAEC,OAAa,GAAkC,CACjG,GAAI,CACF;AACA,KAAM,CAAAC,WAAW,CAAG,KAAM,CAAAhB,kBAAkB,CAAC,CAAC,CAC9C,GAAI,CAACgB,WAAW,CAAE,CAChBf,OAAO,CAACQ,KAAK,CAAC,+CAA+C,CAAC,CAC9D,KAAM,IAAI,CAAAQ,KAAK,CAAC,wEAAwE,CAAC,CAC3F,CAEA,KAAM,CAAAC,kBAAkB,CAAGC,kBAAkB,CAACL,SAAS,CAAC,CACxD,KAAM,CAAAM,gBAAgB,CAAGD,kBAAkB,CAACJ,OAAO,CAAC,CAEpD;AACAd,OAAO,CAACC,GAAG,iBAAAC,MAAA,CAAiBJ,QAAQ,+BAAAI,MAAA,CAA6Be,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,CAAE,CAAC,CAClHnB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAE,CAAEY,SAAS,CAAEI,kBAAkB,CAAEH,OAAO,CAAEK,gBAAiB,CAAC,CAAC,CAEhG;AACA,GAAI,CAAAC,MAAM,CACV,GAAI,CACF;AACAA,MAAM,CAAG,KAAM,CAAA7B,GAAG,IAAAW,MAAA,CACbJ,QAAQ,+BAAAI,MAAA,CAA6Be,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,EACtF,CACEf,OAAO,CAAE,KAAK,CAAE;AAChBC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAAkB,CAClC,eAAe,CAAE,UACnB,CACF,CACF,CAAC,CACH,CAAE,MAAOgB,YAAY,CAAE,CACrBrB,OAAO,CAACQ,KAAK,CAAC,yCAAyC,CAAEa,YAAY,CAAC,CACtErB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CAErD;AACA,KAAM,CAAAQ,cAAc,CAAG,KAAM,CAAAjB,KAAK,CAACD,GAAG,2EAAAW,MAAA,CACsCe,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,EACxH,CACEf,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAClB,CACF,CACF,CAAC,CAEDe,MAAM,CAAGX,cAAc,CAACF,IAAI,CAC9B,CAEAP,OAAO,CAACC,GAAG,CAAC,aAAa,CAAEmB,MAAM,CAAC,CAElC;AACA,GAAI,CAACA,MAAM,CAAE,CACXpB,OAAO,CAACQ,KAAK,CAAC,uCAAuC,CAAC,CACtD,MAAO,EAAE,CACX,CAEA;AACA,GAAIc,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,CAAE,CACzB,GAAI,CACF;AACA,KAAM,CAAAI,cAAc,CAAGJ,MAAM,CAACK,GAAG,CAAC4B,IAAI,EAAI,CACxC;AACA,GAAI,CAACA,IAAI,CAAE,MAAO,CAAA/D,yBAAyB,CAAC,IAAI,CAAC,CACjD,MAAO,CAAAA,yBAAyB,CAAC+D,IAAI,CAAC,CACxC,CAAC,CAAC,CAAC1B,MAAM,CAAC0B,IAAI,EAAIA,IAAI,GAAK,IAAI,CAAC,CAEhCrD,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEuB,cAAc,CAAC,CAC/C,MAAO,CAAAA,cAAc,CACvB,CAAE,MAAOI,GAAG,CAAE,CACZ5B,OAAO,CAACQ,KAAK,CAAC,oCAAoC,CAAEoB,GAAG,CAAC,CACxD,MAAO,EAAE,CACX,CACF,CAEA;AACA5B,OAAO,CAACQ,KAAK,CAAC,6BAA6B,CAAEY,MAAM,CAAC,CACpD,MAAO,EAAE,CACX,CAAE,MAAOZ,KAAK,CAAE,CACdR,OAAO,CAACQ,KAAK,CAAC,2CAA2C,CAAEA,KAAK,CAAC,CACjE,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACAsD,2BAA2B,CAAE,KAAAA,CAAOjD,SAAe,CAAEC,OAAa,GAAkC,CAClG,GAAI,CACF;AACA,KAAM,CAAAC,WAAW,CAAG,KAAM,CAAAhB,kBAAkB,CAAC,CAAC,CAC9C,GAAI,CAACgB,WAAW,CAAE,CAChBf,OAAO,CAACQ,KAAK,CAAC,+CAA+C,CAAC,CAC9D,KAAM,IAAI,CAAAQ,KAAK,CAAC,wEAAwE,CAAC,CAC3F,CAEA,KAAM,CAAAC,kBAAkB,CAAGC,kBAAkB,CAACL,SAAS,CAAC,CACxD,KAAM,CAAAM,gBAAgB,CAAGD,kBAAkB,CAACJ,OAAO,CAAC,CAEpD;AACAd,OAAO,CAACC,GAAG,iBAAAC,MAAA,CAAiBJ,QAAQ,gCAAAI,MAAA,CAA8Be,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,CAAE,CAAC,CACnHnB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAE,CAAEY,SAAS,CAAEI,kBAAkB,CAAEH,OAAO,CAAEK,gBAAiB,CAAC,CAAC,CAEhG;AACA,GAAI,CAAAC,MAAM,CACV,GAAI,CACF;AACAA,MAAM,CAAG,KAAM,CAAA7B,GAAG,IAAAW,MAAA,CACbJ,QAAQ,gCAAAI,MAAA,CAA8Be,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,EACvF,CACEf,OAAO,CAAE,KAAK,CAAE;AAChBC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAAkB,CAClC,eAAe,CAAE,UACnB,CACF,CACF,CAAC,CACH,CAAE,MAAOgB,YAAY,CAAE,CACrBrB,OAAO,CAACQ,KAAK,CAAC,yCAAyC,CAAEa,YAAY,CAAC,CACtErB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CAErD;AACA,KAAM,CAAAQ,cAAc,CAAG,KAAM,CAAAjB,KAAK,CAACD,GAAG,4EAAAW,MAAA,CACuCe,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,EACzH,CACEf,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAClB,CACF,CACF,CAAC,CAEDe,MAAM,CAAGX,cAAc,CAACF,IAAI,CAC9B,CAEAP,OAAO,CAACC,GAAG,CAAC,aAAa,CAAEmB,MAAM,CAAC,CAElC;AACA,GAAI,CAACA,MAAM,CAAE,CACXpB,OAAO,CAACQ,KAAK,CAAC,uCAAuC,CAAC,CACtD,MAAO,EAAE,CACX,CAEA;AACA,GAAIc,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,CAAE,CACzB,GAAI,CACF;AACA,KAAM,CAAAI,cAAc,CAAGJ,MAAM,CAACK,GAAG,CAAC4B,IAAI,EAAI,CACxC;AACA,GAAI,CAACA,IAAI,CAAE,MAAO,CAAA/D,yBAAyB,CAAC,IAAI,CAAC,CACjD,MAAO,CAAAA,yBAAyB,CAAC+D,IAAI,CAAC,CACxC,CAAC,CAAC,CACD1B,MAAM,CAAC0B,IAAI,EAAIA,IAAI,GAAK,IAAI,CAC7B;AAAA,CACCC,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CACd,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAT,IAAI,CAACO,CAAC,CAACG,IAAI,CAAC,CAC9B,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAX,IAAI,CAACQ,CAAC,CAACE,IAAI,CAAC,CAC9B,MAAO,CAAAD,KAAK,CAACG,OAAO,CAAC,CAAC,CAAGD,KAAK,CAACC,OAAO,CAAC,CAAC,CAC1C,CAAC,CAAC,CAEF5D,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEuB,cAAc,CAAC,CAC/C,MAAO,CAAAA,cAAc,CACvB,CAAE,MAAOI,GAAG,CAAE,CACZ5B,OAAO,CAACQ,KAAK,CAAC,qCAAqC,CAAEoB,GAAG,CAAC,CACzD,MAAO,EAAE,CACX,CACF,CAEA;AACA5B,OAAO,CAACQ,KAAK,CAAC,6BAA6B,CAAEY,MAAM,CAAC,CACpD,MAAO,EAAE,CACX,CAAE,MAAOZ,KAAK,CAAE,CACdR,OAAO,CAACQ,KAAK,CAAC,4CAA4C,CAAEA,KAAK,CAAC,CAClE,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACAuD,0BAA0B,CAAE,KAAAA,CAAOlD,SAAe,CAAEC,OAAa,GAAkC,CACjG,GAAI,CACF;AACA,KAAM,CAAAC,WAAW,CAAG,KAAM,CAAAhB,kBAAkB,CAAC,CAAC,CAC9C,GAAI,CAACgB,WAAW,CAAE,CAChBf,OAAO,CAACQ,KAAK,CAAC,+CAA+C,CAAC,CAC9D,KAAM,IAAI,CAAAQ,KAAK,CAAC,wEAAwE,CAAC,CAC3F,CAEA,KAAM,CAAAC,kBAAkB,CAAGC,kBAAkB,CAACL,SAAS,CAAC,CACxD,KAAM,CAAAM,gBAAgB,CAAGD,kBAAkB,CAACJ,OAAO,CAAC,CAEpD;AACAd,OAAO,CAACC,GAAG,iBAAAC,MAAA,CAAiBJ,QAAQ,+BAAAI,MAAA,CAA6Be,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,CAAE,CAAC,CAClHnB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAE,CAAEY,SAAS,CAAEI,kBAAkB,CAAEH,OAAO,CAAEK,gBAAiB,CAAC,CAAC,CAEhG;AACA,GAAI,CAAAC,MAAM,CACV,GAAI,CACF;AACAA,MAAM,CAAG,KAAM,CAAA7B,GAAG,IAAAW,MAAA,CACbJ,QAAQ,+BAAAI,MAAA,CAA6Be,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,EACtF,CACEf,OAAO,CAAE,KAAK,CAAE;AAChBC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAAkB,CAClC,eAAe,CAAE,UACnB,CACF,CACF,CAAC,CACH,CAAE,MAAOgB,YAAY,CAAE,CACrBrB,OAAO,CAACQ,KAAK,CAAC,yCAAyC,CAAEa,YAAY,CAAC,CACtErB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CAErD;AACA,KAAM,CAAAQ,cAAc,CAAG,KAAM,CAAAjB,KAAK,CAACD,GAAG,2EAAAW,MAAA,CACsCe,kBAAkB,cAAAf,MAAA,CAAYiB,gBAAgB,EACxH,CACEf,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAClB,CACF,CACF,CAAC,CAEDe,MAAM,CAAGX,cAAc,CAACF,IAAI,CAC9B,CAEAP,OAAO,CAACC,GAAG,CAAC,aAAa,CAAEmB,MAAM,CAAC,CAElC;AACA,GAAI,CAACA,MAAM,CAAE,CACXpB,OAAO,CAACQ,KAAK,CAAC,uCAAuC,CAAC,CACtD,MAAO,EAAE,CACX,CAEA;AACA,GAAIc,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,CAAE,CACzB,GAAI,CACF;AACA,KAAM,CAAAI,cAAc,CAAGJ,MAAM,CAACK,GAAG,CAAC4B,IAAI,EAAI,CACxC;AACA,GAAI,CAACA,IAAI,CAAE,MAAO,CAAA/D,yBAAyB,CAAC,IAAI,CAAC,CACjD,MAAO,CAAAA,yBAAyB,CAAC+D,IAAI,CAAC,CACxC,CAAC,CAAC,CACD1B,MAAM,CAAC0B,IAAI,EAAIA,IAAI,GAAK,IAAI,CAC7B;AAAA,CACCC,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CACd,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAT,IAAI,CAACO,CAAC,CAACG,IAAI,CAAC,CAC9B,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAX,IAAI,CAACQ,CAAC,CAACE,IAAI,CAAC,CAC9B,MAAO,CAAAD,KAAK,CAACG,OAAO,CAAC,CAAC,CAAGD,KAAK,CAACC,OAAO,CAAC,CAAC,CAC1C,CAAC,CAAC,CAEF5D,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEuB,cAAc,CAAC,CAC/C,MAAO,CAAAA,cAAc,CACvB,CAAE,MAAOI,GAAG,CAAE,CACZ5B,OAAO,CAACQ,KAAK,CAAC,oCAAoC,CAAEoB,GAAG,CAAC,CACxD,MAAO,EAAE,CACX,CACF,CAEA;AACA5B,OAAO,CAACQ,KAAK,CAAC,6BAA6B,CAAEY,MAAM,CAAC,CACpD,MAAO,EAAE,CACX,CAAE,MAAOZ,KAAK,CAAE,CACdR,OAAO,CAACQ,KAAK,CAAC,2CAA2C,CAAEA,KAAK,CAAC,CACjE,KAAM,CAAAA,KAAK,CACb,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAU,kBAAkB,CAAIwC,IAAU,EAAa,CACjD,GAAI,CACF;AACA,GAAI,EAAEA,IAAI,WAAY,CAAAV,IAAI,CAAC,EAAIgB,KAAK,CAACN,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAAE,CACpD5D,OAAO,CAACQ,KAAK,CAAC,wBAAwB,CAAEkD,IAAI,CAAC,CAC7C;AACA,KAAM,CAAAO,KAAK,CAAG,GAAI,CAAAjB,IAAI,CAAC,CAAC,CACxB,MAAO,CAAAvD,kBAAkB,CAACwE,KAAK,CAAC,CAClC,CAEA;AACA;AACA,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAAlB,IAAI,CAC3BU,IAAI,CAACS,WAAW,CAAC,CAAC,CAClBT,IAAI,CAACU,QAAQ,CAAC,CAAC,CACfV,IAAI,CAACW,OAAO,CAAC,CAAC,CACd,EAAE,CAAE,CAAC,CAAE,CACT,CAAC,CAED;AACA,MAAO,CAAA5E,kBAAkB,CAACyE,YAAY,CAAC,CACzC,CAAE,MAAO1D,KAAK,CAAE,CACdR,OAAO,CAACQ,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C;AACA,KAAM,CAAAyD,KAAK,CAAG,GAAI,CAAAjB,IAAI,CAAC,CAAC,CACxB,MAAO,CAAAvD,kBAAkB,CAACwE,KAAK,CAAC,CAClC,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}