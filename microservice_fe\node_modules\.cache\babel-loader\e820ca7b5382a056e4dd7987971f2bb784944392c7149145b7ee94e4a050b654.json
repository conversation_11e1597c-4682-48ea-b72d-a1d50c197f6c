{"ast": null, "code": "import React from'react';import{CircularProgress,Box}from'@mui/material';import{jsx as _jsx}from\"react/jsx-runtime\";const LoadingSpinner=_ref=>{let{size=40,color='primary',fullScreen=false}=_ref;if(fullScreen){return/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',alignItems:'center',position:'fixed',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(255, 255, 255, 0.7)',zIndex:9999},children:/*#__PURE__*/_jsx(CircularProgress,{size:size,color:color})});}return/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',p:2},children:/*#__PURE__*/_jsx(CircularProgress,{size:size,color:color})});};export default LoadingSpinner;", "map": {"version": 3, "names": ["React", "CircularProgress", "Box", "jsx", "_jsx", "LoadingSpinner", "_ref", "size", "color", "fullScreen", "sx", "display", "justifyContent", "alignItems", "position", "top", "left", "right", "bottom", "backgroundColor", "zIndex", "children", "p"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/common/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\nimport { CircularProgress, Box } from '@mui/material';\n\ninterface LoadingSpinnerProps {\n  size?: number;\n  color?: 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' | 'inherit';\n  fullScreen?: boolean;\n}\n\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ \n  size = 40, \n  color = 'primary',\n  fullScreen = false \n}) => {\n  if (fullScreen) {\n    return (\n      <Box\n        sx={{\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(255, 255, 255, 0.7)',\n          zIndex: 9999,\n        }}\n      >\n        <CircularProgress size={size} color={color} />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>\n      <CircularProgress size={size} color={color} />\n    </Box>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,gBAAgB,CAAEC,GAAG,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAQtD,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAIhD,IAJiD,CACrDC,IAAI,CAAG,EAAE,CACTC,KAAK,CAAG,SAAS,CACjBC,UAAU,CAAG,KACf,CAAC,CAAAH,IAAA,CACC,GAAIG,UAAU,CAAE,CACd,mBACEL,IAAA,CAACF,GAAG,EACFQ,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,QAAQ,CACxBC,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,OAAO,CACjBC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTC,eAAe,CAAE,0BAA0B,CAC3CC,MAAM,CAAE,IACV,CAAE,CAAAC,QAAA,cAEFjB,IAAA,CAACH,gBAAgB,EAACM,IAAI,CAAEA,IAAK,CAACC,KAAK,CAAEA,KAAM,CAAE,CAAC,CAC3C,CAAC,CAEV,CAEA,mBACEJ,IAAA,CAACF,GAAG,EAACQ,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEU,CAAC,CAAE,CAAE,CAAE,CAAAD,QAAA,cAC3DjB,IAAA,CAACH,gBAAgB,EAACM,IAAI,CAAEA,IAAK,CAACC,KAAK,CAAEA,KAAM,CAAE,CAAC,CAC3C,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}