{"ast": null, "code": "export{};", "map": {"version": 3, "names": [], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/models/WorkShift.ts"], "sourcesContent": ["export interface WorkShift {\n  id?: number;\n  jobDetailId?: number;\n  startTime: string;\n  endTime: string;\n  numberOfWorkers: number;\n  salary: number;\n  workingDays: string; // Comma-separated string of day numbers (1-7)\n  isDeleted?: boolean;\n  createdAt?: string;\n  updatedAt?: string;\n}\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}