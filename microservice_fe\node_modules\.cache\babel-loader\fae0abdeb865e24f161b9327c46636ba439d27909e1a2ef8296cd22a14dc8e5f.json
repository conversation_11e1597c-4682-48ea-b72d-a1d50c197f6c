{"ast": null, "code": "export{};", "map": {"version": 3, "names": [], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/models/JobCategory.ts"], "sourcesContent": ["export interface JobCategory {\n  id?: number;\n  name: string;\n  description?: string;\n  isDeleted?: boolean;\n  createdAt?: string;\n  updatedAt?: string;\n}\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}