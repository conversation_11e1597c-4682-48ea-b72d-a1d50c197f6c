{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getISOWeekYear} function options.\n */\n\n/**\n * @name getISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the ISO week-numbering year of the given date.\n *\n * @description\n * Get the ISO week-numbering year of the given date,\n * which always starts 3 days before the year's first Thursday.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n *\n * @returns The ISO week-numbering year\n *\n * @example\n * // Which ISO-week numbering year is 2 January 2005?\n * const result = getISOWeekYear(new Date(2005, 0, 2))\n * //=> 2004\n */\nexport function getISOWeekYear(date, options) {\n  const _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);\n  const year = _date.getFullYear();\n  const fourthOfJanuaryOfNextYear = constructFrom(_date, 0);\n  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);\n  const startOfNextYear = startOfISOWeek(fourthOfJanuaryOfNextYear);\n  const fourthOfJanuaryOfThisYear = constructFrom(_date, 0);\n  fourthOfJanuaryOfThisYear.setFullYear(year, 0, 4);\n  fourthOfJanuaryOfThisYear.setHours(0, 0, 0, 0);\n  const startOfThisYear = startOfISOWeek(fourthOfJanuaryOfThisYear);\n  if (_date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (_date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}\n\n// Fallback for modularized imports:\nexport default getISOWeekYear;", "map": {"version": 3, "names": ["constructFrom", "startOfISOWeek", "toDate", "getISOWeekYear", "date", "options", "_date", "in", "year", "getFullYear", "fourthOfJanuaryOfNextYear", "setFullYear", "setHours", "startOfNextYear", "fourthOfJanuaryOfThisYear", "startOfThisYear", "getTime"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/date-fns/getISOWeekYear.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getISOWeekYear} function options.\n */\n\n/**\n * @name getISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the ISO week-numbering year of the given date.\n *\n * @description\n * Get the ISO week-numbering year of the given date,\n * which always starts 3 days before the year's first Thursday.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n *\n * @returns The ISO week-numbering year\n *\n * @example\n * // Which ISO-week numbering year is 2 January 2005?\n * const result = getISOWeekYear(new Date(2005, 0, 2))\n * //=> 2004\n */\nexport function getISOWeekYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n\n  const fourthOfJanuaryOfNextYear = constructFrom(_date, 0);\n  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);\n  const startOfNextYear = startOfISOWeek(fourthOfJanuaryOfNextYear);\n\n  const fourthOfJanuaryOfThisYear = constructFrom(_date, 0);\n  fourthOfJanuaryOfThisYear.setFullYear(year, 0, 4);\n  fourthOfJanuaryOfThisYear.setHours(0, 0, 0, 0);\n  const startOfThisYear = startOfISOWeek(fourthOfJanuaryOfThisYear);\n\n  if (_date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (_date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}\n\n// Fallback for modularized imports:\nexport default getISOWeekYear;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC5C,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,EAAE,CAAC;EACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;EAEhC,MAAMC,yBAAyB,GAAGV,aAAa,CAACM,KAAK,EAAE,CAAC,CAAC;EACzDI,yBAAyB,CAACC,WAAW,CAACH,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACrDE,yBAAyB,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9C,MAAMC,eAAe,GAAGZ,cAAc,CAACS,yBAAyB,CAAC;EAEjE,MAAMI,yBAAyB,GAAGd,aAAa,CAACM,KAAK,EAAE,CAAC,CAAC;EACzDQ,yBAAyB,CAACH,WAAW,CAACH,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;EACjDM,yBAAyB,CAACF,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9C,MAAMG,eAAe,GAAGd,cAAc,CAACa,yBAAyB,CAAC;EAEjE,IAAIR,KAAK,CAACU,OAAO,CAAC,CAAC,IAAIH,eAAe,CAACG,OAAO,CAAC,CAAC,EAAE;IAChD,OAAOR,IAAI,GAAG,CAAC;EACjB,CAAC,MAAM,IAAIF,KAAK,CAACU,OAAO,CAAC,CAAC,IAAID,eAAe,CAACC,OAAO,CAAC,CAAC,EAAE;IACvD,OAAOR,IAAI;EACb,CAAC,MAAM;IACL,OAAOA,IAAI,GAAG,CAAC;EACjB;AACF;;AAEA;AACA,eAAeL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}