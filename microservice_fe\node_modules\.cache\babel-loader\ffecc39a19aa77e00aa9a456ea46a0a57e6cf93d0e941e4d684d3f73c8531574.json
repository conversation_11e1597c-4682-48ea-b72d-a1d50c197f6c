{"ast": null, "code": "import{get,post,put,del}from'../api/apiClient';const BASE_URL='/api/job-category';export const jobCategoryService={// Get all job categories\ngetAllJobCategories:async()=>{return get(BASE_URL);},// Get job category by ID\ngetJobCategoryById:async id=>{return get(\"\".concat(BASE_URL,\"/\").concat(id));},// Create a new job category\ncreateJobCategory:async jobCategory=>{return post(BASE_URL,jobCategory);},// Update an existing job category\nupdateJobCategory:async jobCategory=>{return put(BASE_URL,jobCategory);},// Delete a job category\ndeleteJobCategory:async id=>{return del(\"\".concat(BASE_URL,\"/\").concat(id));}};", "map": {"version": 3, "names": ["get", "post", "put", "del", "BASE_URL", "jobCategoryService", "getAllJobCategories", "getJobCategoryById", "id", "concat", "createJobCategory", "jobCategory", "updateJobCategory", "deleteJobCategory"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/services/job/jobCategoryService.ts"], "sourcesContent": ["import { JobCategory } from '../../models';\nimport { get, post, put, del } from '../api/apiClient';\n\nconst BASE_URL = '/api/job-category';\n\nexport const jobCategoryService = {\n  // Get all job categories\n  getAllJobCategories: async (): Promise<JobCategory[]> => {\n    return get<JobCategory[]>(BASE_URL);\n  },\n\n  // Get job category by ID\n  getJobCategoryById: async (id: number): Promise<JobCategory> => {\n    return get<JobCategory>(`${BASE_URL}/${id}`);\n  },\n\n  // Create a new job category\n  createJobCategory: async (jobCategory: JobCategory): Promise<JobCategory> => {\n    return post<JobCategory>(BASE_URL, jobCategory);\n  },\n\n  // Update an existing job category\n  updateJobCategory: async (jobCategory: JobCategory): Promise<JobCategory> => {\n    return put<JobCategory>(BASE_URL, jobCategory);\n  },\n\n  // Delete a job category\n  deleteJobCategory: async (id: number): Promise<void> => {\n    return del<void>(`${BASE_URL}/${id}`);\n  }\n};\n"], "mappings": "AACA,OAASA,GAAG,CAAEC,IAAI,CAAEC,GAAG,CAAEC,GAAG,KAAQ,kBAAkB,CAEtD,KAAM,CAAAC,QAAQ,CAAG,mBAAmB,CAEpC,MAAO,MAAM,CAAAC,kBAAkB,CAAG,CAChC;AACAC,mBAAmB,CAAE,KAAAA,CAAA,GAAoC,CACvD,MAAO,CAAAN,GAAG,CAAgBI,QAAQ,CAAC,CACrC,CAAC,CAED;AACAG,kBAAkB,CAAE,KAAO,CAAAC,EAAU,EAA2B,CAC9D,MAAO,CAAAR,GAAG,IAAAS,MAAA,CAAiBL,QAAQ,MAAAK,MAAA,CAAID,EAAE,CAAE,CAAC,CAC9C,CAAC,CAED;AACAE,iBAAiB,CAAE,KAAO,CAAAC,WAAwB,EAA2B,CAC3E,MAAO,CAAAV,IAAI,CAAcG,QAAQ,CAAEO,WAAW,CAAC,CACjD,CAAC,CAED;AACAC,iBAAiB,CAAE,KAAO,CAAAD,WAAwB,EAA2B,CAC3E,MAAO,CAAAT,GAAG,CAAcE,QAAQ,CAAEO,WAAW,CAAC,CAChD,CAAC,CAED;AACAE,iBAAiB,CAAE,KAAO,CAAAL,EAAU,EAAoB,CACtD,MAAO,CAAAL,GAAG,IAAAM,MAAA,CAAUL,QAAQ,MAAAK,MAAA,CAAID,EAAE,CAAE,CAAC,CACvC,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}