import { formatDistance } from "./ug/_lib/formatDistance.js";
import { formatLong } from "./ug/_lib/formatLong.js";
import { formatRelative } from "./ug/_lib/formatRelative.js";
import { localize } from "./ug/_lib/localize.js";
import { match } from "./ug/_lib/match.js";

/**
 * @category Locales
 * @summary Uighur locale
 * @language Uighur
 * @iso-639-2 uig
 * <AUTHOR> [@abduwaly](https://github.com/abduwaly)
 */
export const ug = {
  code: "ug",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default ug;
