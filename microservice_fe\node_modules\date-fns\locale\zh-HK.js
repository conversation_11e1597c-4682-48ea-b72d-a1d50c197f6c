import { formatDistance } from "./zh-HK/_lib/formatDistance.js";
import { formatLong } from "./zh-HK/_lib/formatLong.js";
import { formatRelative } from "./zh-HK/_lib/formatRelative.js";
import { localize } from "./zh-HK/_lib/localize.js";
import { match } from "./zh-HK/_lib/match.js";

/**
 * @category Locales
 * @summary Chinese Traditional locale.
 * @language Chinese Traditional
 * @iso-639-2 zho
 * <AUTHOR> [@gaplo](https://github.com/gaplo)
 */
export const zhHK = {
  code: "zh-HK",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default zhHK;
