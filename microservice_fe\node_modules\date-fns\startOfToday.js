import { startOfDay } from "./startOfDay.js";

/**
 * The {@link startOfToday} function options.
 */

/**
 * @name startOfToday
 * @category Day Helpers
 * @summary Return the start of today.
 * @pure false
 *
 * @description
 * Return the start of today.
 *
 * @typeParam ContextDate - The `Date` type of the context function.
 *
 * @param options - An object with options
 *
 * @returns The start of today
 *
 * @example
 * // If today is 6 October 2014:
 * const result = startOfToday()
 * //=> Mon Oct 6 2014 00:00:00
 */
export function startOfToday(options) {
  return startOfDay(Date.now(), options);
}

// Fallback for modularized imports:
export default startOfToday;
