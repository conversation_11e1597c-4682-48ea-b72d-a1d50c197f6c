{"name": "postcss-media-minmax", "version": "5.0.0", "description": "Using more intuitive `>=`, `<=`, `>`, `<` instead of media queries min/max prefix.", "scripts": {"test": "tape test", "release": "npmpub"}, "repository": "https://github.com/postcss/postcss-media-minmax.git", "keywords": ["css", "css3", "postcss", "postcss-plugin", "media querie", "media queries"], "author": "yisi", "license": "MIT", "files": ["CHANGELOG.md", "README.md", "README-zh.md", "LICENSE", "index.js"], "engines": {"node": ">=10.0.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "devDependencies": {"npmpub": "^4.1.0", "postcss": "^8.1.0", "tape": "^4.9.1"}}