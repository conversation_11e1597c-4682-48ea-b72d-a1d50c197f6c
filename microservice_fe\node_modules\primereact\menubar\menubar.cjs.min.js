"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),n=require("primereact/api"),t=require("primereact/componentbase"),r=require("primereact/hooks"),i=require("primereact/icons/bars"),o=require("primereact/utils"),a=require("primereact/icons/angledown"),u=require("primereact/icons/angleright"),c=require("primereact/ripple");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function s(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}})),n.default=e,Object.freeze(n)}var m=s(e),f=l(n);function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function d(e,n){if("object"!=p(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=p(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}function b(e){var n=d(e,"string");return"symbol"==p(n)?n:n+""}function v(e,n,t){return(n=b(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function y(e){if(Array.isArray(e))return e}function g(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,i,o,a,u=[],c=!0,l=!1;try{if(o=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=o.call(t)).done)&&(u.push(r.value),u.length!==n);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=t.return&&(a=t.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}function x(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function I(e,n){if(e){if("string"==typeof e)return x(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?x(e,n):void 0}}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function E(e,n){return y(e)||g(e,n)||I(e,n)||h()}var O=t.ComponentBase.extend({defaultProps:{__TYPE:"Menubar",id:null,model:null,style:null,className:null,start:null,ariaLabel:null,ariaLabelledBy:null,onFocus:null,onBlur:null,submenuIcon:null,menuIcon:null,end:null,children:void 0},css:{classes:{start:"p-menubar-start",end:"p-menubar-end",button:"p-menubar-button",root:function(e){return o.classNames("p-menubar p-component",{"p-menubar-mobile-active":e.mobileActiveState})},separator:"p-menuitem-separator",icon:"p-menuitem-icon",label:"p-menuitem-text",submenuIcon:"p-submenu-icon",menuitem:function(e){return o.classNames("p-menuitem",{"p-menuitem-active p-highlight":e.active,"p-focus":e.focused,"p-disabled":e.disabled})},menu:"p-menubar-root-list",content:"p-menuitem-content",submenu:"p-submenu-list",action:function(e){return o.classNames("p-menuitem-link",{"p-disabled":e.disabled})}},styles:"\n@layer primereact {\n    .p-menubar {\n        display: flex;\n        align-items: center;\n    }\n\n    .p-menubar ul {\n        margin: 0;\n        padding: 0;\n        list-style: none;\n    }\n\n    .p-menubar .p-menuitem-link {\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        text-decoration: none;\n        overflow: hidden;\n        position: relative;\n    }\n\n    .p-menubar .p-menuitem-text {\n        line-height: 1;\n    }\n\n    .p-menubar .p-menuitem {\n        position: relative;\n    }\n\n    .p-menubar-root-list {\n        display: flex;\n        align-items: center;\n        flex-wrap: wrap;\n    }\n\n    .p-menubar-root-list > li ul {\n        display: none;\n        z-index: 1;\n    }\n\n    .p-menubar-root-list > .p-menuitem-active > .p-submenu-list {\n        display: block;\n    }\n\n    .p-menubar .p-submenu-list {\n        display: none;\n        position: absolute;\n        z-index: 5;\n    }\n\n    .p-menubar .p-submenu-list > .p-menuitem-active > .p-submenu-list {\n        display: block;\n        left: 100%;\n        top: 0;\n    }\n\n    .p-menubar .p-submenu-list .p-menuitem .p-menuitem-content .p-menuitem-link .p-submenu-icon {\n        margin-left: auto;\n    }\n\n    .p-menubar .p-menubar-end {\n        margin-left: auto;\n        align-self: center;\n    }\n\n    .p-menubar-button {\n        display: none;\n        cursor: pointer;\n        align-items: center;\n        justify-content: center;\n        text-decoration: none;\n    }\n}\n"}});function k(){return k=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},k.apply(null,arguments)}function j(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function P(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?j(Object(t),!0).forEach((function(n){v(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):j(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var N=m.memo(m.forwardRef((function(e,n){var t=r.useMergeProps(),i=e.ptm,l=e.cx,s=function(n,t,r){return i(t,{props:e,hostName:e.hostName,context:{item:n,index:r,active:x(n),focused:E(n),disabled:h(n),level:e.level}})},f=function(n,t){h(t)||e.mobileActive?n.preventDefault():e.onItemMouseEnter&&e.onItemMouseEnter({originalEvent:n,processedItem:t})},p=function(e,n){var t=n.item;h(n)?e.preventDefault():(t.command&&t.command({originalEvent:e,item:t}),d({originalEvent:e,processedItem:n,isFocus:!0}),t.url||(e.preventDefault(),e.stopPropagation()))},d=function(n){e.onLeafClick&&e.onLeafClick(n)},b=function(e){var n;return null===(n=e.item)||void 0===n?void 0:n.id},y=function(n){return"".concat(e.id,"_").concat(n.key)},g=function(e,n,t){return e&&e.item?o.ObjectUtils.getItemValue(e.item[n],t):void 0},x=function(n){return e.activeItemPath.some((function(e){return e.key===n.key}))},I=function(e){return!1!==g(e,"visible")},h=function(e){return g(e,"disabled")},E=function(n){return e.focusedItemId===y(n)},O=function(e){return o.ObjectUtils.isNotEmpty(e.items)},j=function(n){return n-e.model.slice(0,n).filter((function(e){return I(e)&&g(e,"separator")})).length+1},S=function(n,r){var o=e.id+"_separator_"+r+"_"+n.key,a=t({"data-id":o,className:l("separator"),role:"separator"},i("separator",{hostName:e.hostName}));return m.createElement("li",k({},a,{key:o}))},w=function(n){var t=n&&n.items;return t?m.createElement(N,{id:e.id,hostName:e.hostName,menuProps:e.menuProps,level:e.level+1,model:t,activeItemPath:e.activeItemPath,focusedItemId:e.focusedItemId,onLeafClick:d,onItemMouseEnter:e.onItemMouseEnter,submenuIcon:e.submenuIcon,ptm:i,style:{display:x(n)?"block":"none"},cx:l}):null},D=function(n,r){var i=n.item;if(!I(n))return null;var d=b(n),N=y(n),S=x(n),D=E(n),U=h(n)||!1,K=O(n),M=o.classNames("p-menuitem-link",{"p-disabled":U}),A=o.classNames("p-menuitem-icon",g(n,"icon")),C=t({className:l("icon")},s(n,"icon",r)),L=o.IconUtils.getJSXIcon(i.icon,P({},C),{props:e.menuProps}),R=t({className:l("label")},s(n,"label",r)),_=i.label&&m.createElement("span",R,i.label),H=g(n,"items"),T=t({className:l("submenuIcon")},s(n,"submenuIcon",r)),B=H&&o.IconUtils.getJSXIcon(e.root?e.submenuIcon||m.createElement(a.AngleDownIcon,T):e.submenuIcon||m.createElement(u.AngleRightIcon,T),P({},T),{props:P({menuProps:e.menuProps},e)}),q=w(n),F=t({href:i.url||"#",tabIndex:"-1",className:l("action",{disabled:U}),onFocus:function(e){return e.stopPropagation()},target:g(n,"target"),"aria-haspopup":null!=H},s(n,"action",r)),z=m.createElement("a",F,L,_,B,m.createElement(c.Ripple,null));i.template&&(z=o.ObjectUtils.getJSXElement(i.template,i,{className:M,labelClassName:"p-menuitem-text",iconClassName:A,submenuIconClassName:"p-submenu-icon",element:z,props:e}));var J=t({onClick:function(e){return p(e,n)},onMouseEnter:function(e){return f(e,n)},className:l("content")},s(n,"content",r)),X=g(n,"className"),Z=t(v({id:d,"data-id":N,role:"menuitem","aria-label":i.label,"aria-disabled":U,"aria-expanded":K?S:void 0,"aria-haspopup":K&&!i.url?"menu":void 0,"aria-setsize":e.model.filter((function(e){return I(e)&&!g(e,"separator")})).length,"aria-posinset":j(r),"data-p-highlight":S,"data-p-focused":D,"data-p-disabled":U,className:o.classNames(X,l("menuitem",{active:S,focused:D,disabled:U}))},"data-p-disabled",U||!1),s(n,"menuitem",r));return m.createElement("li",k({},Z,{key:"".concat(N)}),m.createElement("div",J,z),q)},U=e.root?"menubar":"menu",K=e.root?"menu":"submenu",M=e.root?"0":null,A=e.model?e.model.map((function(e,n){return!1===e.visible?null:g(e,"separator")?S(e,n):D(e,n)})):null,C=t({ref:n,className:l(K),level:e.level,onFocus:e.onFocus,onBlur:e.onBlur,onKeyDown:e.onKeyDown,"data-id":e.id,tabIndex:M,"aria-activedescendant":e.ariaActivedescendant,style:e.style,role:U},i(K));return m.createElement("ul",C,A)})));function S(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function w(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?S(Object(t),!0).forEach((function(n){v(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):S(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}N.displayName="MenubarSub";var D=m.memo(m.forwardRef((function(e,a){var u=r.useMergeProps(),c=m.useContext(n.PrimeReactContext),l=O.getProps(e,c),s=E(m.useState(l.id),2),p=s[0],d=s[1],b=E(m.useState(!1),2),y=b[0],g=b[1],x=E(m.useState(!1),2),I=x[0],h=x[1],k=E(m.useState({index:-1,level:0,parentKey:""}),2),j=k[0],P=k[1],S=E(m.useState(null),2),D=S[0],U=S[1],K=E(m.useState([]),2),M=K[0],A=K[1],C=E(m.useState([]),2),L=C[0],R=C[1],_=E(m.useState([]),2),H=_[0],T=_[1],B=E(m.useState(!1),2),q=B[0],F=B[1],z=E(m.useState(!1),2),J=z[0],X=z[1],Z=m.useRef(null),V=m.useRef(null),W=m.useRef(null),Y=m.useRef(""),$=m.useRef(null),G=m.useRef(!1),Q=O.setMetaData({props:l,state:{id:p,mobileActive:y}}),ee=Q.ptm,ne=Q.cx;t.useHandleStyle(O.css.styles,Q.isUnstyled,{name:"menubar"});var te=E(r.useEventListener({type:"click",listener:function(e){W.current&&!W.current.contains(e.target)&&se()},options:{capture:!0}}),2),re=te[0],ie=te[1],oe=E(r.useResizeListener({listener:function(e){o.DomHandler.isTouchDevice()||se(e)}}),2),ae=oe[0],ue=oe[1],ce=function(e){y?(g(!1),se()):(g(!0),setTimeout((function(){le()}),1)),e.preventDefault()},le=function(){P({index:Ce(),level:0,parentKey:""}),o.DomHandler.focus(V.current)},se=function(e){y&&(g(!1),setTimeout((function(){o.DomHandler.focus(W.current)}),0)),A([]),P({index:-1,level:0,parentKey:""}),e&&o.DomHandler.focus(V.current),X(!1)},me=function(e,n){return e?o.ObjectUtils.getItemValue(e[n]):void 0},fe=function(e){return me(e,"separator")},pe=function(e){return e?me(e.item,"label"):void 0},de=function(e){return e&&o.ObjectUtils.isNotEmpty(e.items)},be=function(e){var n=e.processedItem,t=e.isFocus;if(!o.ObjectUtils.isEmpty(n)){var r=n.index,i=n.key,a=n.level,u=n.parentKey,c=o.ObjectUtils.isNotEmpty(n.items),l=M.filter((function(e){return e.parentKey!==u&&e.parentKey!==i}));c&&l.push(n),P({index:r,level:a,parentKey:u}),A(l),c&&X(!0),t&&o.DomHandler.focus(V.current)}},ve=function(e){var n=L[j.index];if(n?o.ObjectUtils.isEmpty(n.parent):null){de(n)&&(be({originalEvent:e,processedItem:n}),P({index:-1,parentKey:n.key}),setTimeout((function(){return F(!0)}),0))}else{var t=-1!==j.index?Ke(j.index):Ce();_e(t)}e.preventDefault()},ye=function(e){var n=L[j.index];if(o.ObjectUtils.isEmpty(n.parent)){de(n)&&(be({originalEvent:e,processedItem:n}),P({index:-1,parentKey:n.key}),G.current=!0,setTimeout((function(){return F(!0)}),0))}else{var t=M.find((function(e){return e.key===n.parentKey}));if(0===j.index&&t&&""===t.parentKey)P({index:-1,parentKey:t?t.parentKey:""}),Y.current="",ge(e);else{var r=-1!==j.index?Me(j.index):Le();_e(r)}}e.preventDefault()},ge=function(e){var n=L[j.index],t=n?M.find((function(e){return e.key===n.parentKey})):null;if(t)be({originalEvent:e,processedItem:t}),A(M.filter((function(e){return e.key!==t.key})));else{var r=-1!==j.index?Me(j.index):Le();_e(r)}e.preventDefault()},xe=function(e){var n=L[j.index];if(n?M.find((function(e){return e.key===n.parentKey})):null){de(n)&&(be({originalEvent:e,processedItem:n}),P({index:-1,parentKey:n.key}),setTimeout((function(){return F(!0)}),0))}else{var t=-1!==j.index?Ke(j.index):Ce();_e(t)}e.preventDefault()},Ie=function(e){_e(De()),e.preventDefault()},he=function(e){_e(Ue()),e.preventDefault()},Ee=function(e){if(-1!==j.index){var n=o.DomHandler.findSingle(V.current,'li[data-id="'.concat("".concat(D),'"]')),t=n&&o.DomHandler.findSingle(n,'a[data-pc-section="action"]');t?t.click():n&&n.click()}e.preventDefault()},Oe=function(e){Ee(e)},ke=function(e){se(!0),P({focusedItemInfo:j,index:Ce()})},je=function(e){if(-1!==j.index){var n=L[j.index];!de(n)&&be({originalEvent:e,processedItem:n})}se()},Pe=function(e){return Ne(e)&&pe(e).toLocaleLowerCase().startsWith(Y.current.toLocaleLowerCase())},Ne=function(e){return!!e&&!me(e.item,"disabled")&&!fe(e.item)},Se=function(e){return Ne(e)&&we(e)},we=function(e){return M.some((function(n){return n.key===e.key}))},De=function(){return L.findIndex((function(e){return Ne(e)}))},Ue=function(){return o.ObjectUtils.findLastIndex(L,(function(e){return Ne(e)}))},Ke=function(e){var n=e<L.length-1?L.slice(e+1).findIndex((function(e){return Ne(e)})):-1;return n>-1?n+e+1:e},Me=function(e){var n=e>0?o.ObjectUtils.findLastIndex(L.slice(0,e),(function(e){return Ne(e)})):-1;return n>-1?n:e},Ae=function(){return L.findIndex((function(e){return Se(e)}))},Ce=function(){return Ae()},Le=function(){return Ae()},Re=function(e,n){Y.current=(Y.current||"")+n;var t=-1,r=!1;return-1!==(t=-1!==j.index?-1===(t=L.slice(j.index).findIndex((function(e){return Pe(e)})))?L.slice(0,j.index).findIndex((function(e){return Pe(e)})):t+j.index:L.findIndex((function(e){return Pe(e)})))&&(r=!0),-1===t&&-1===j.index&&(t=Ce()),-1!==t&&_e(t),$.current&&clearTimeout($.current),$.current=setTimeout((function(){Y.current="",$.current=null}),500),r},_e=function(e){j.index!==e&&(P(w(w({},j),{},{index:e})),He())},He=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1,n=-1!==e?"".concat(p,"_").concat(e):D,t=o.DomHandler.findSingle(V.current,'li[data-id="'.concat(n,'"]'));t&&t.scrollIntoView&&t.scrollIntoView({block:"nearest",inline:"start"})},Te=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=[];return e&&e.forEach((function(e,o){var a=(""!==r?r+"_":"")+o,u={item:e,index:o,level:n,key:a,parent:t,parentKey:r};u.items=Te(e.items,n+1,u,a),i.push(u)})),i};r.useMountEffect((function(){p||d(o.UniqueComponentId())})),r.useUpdateEffect((function(){y?(re(),ae(),o.ZIndexUtils.set("menu",V.current,c&&c.autoZIndex||f.default.autoZIndex,c&&c.zIndex.menu||f.default.zIndex.menu)):(ue(),ie(),o.ZIndexUtils.clear(V.current))}),[y]),m.useEffect((function(){var e=Te(l.model||[],0,null,"");T(e)}),[l.model]),r.useUpdateEffect((function(){var e=M.find((function(e){return e.key===j.parentKey}));R(e?e.items:H)}),[M,j,H]),r.useUpdateEffect((function(){o.ObjectUtils.isNotEmpty(M)?(re(),ae()):(ie(),ue())}),[M]),r.useUpdateEffect((function(){if(q){var e=-1!==j.index?Ke(j.index):G.current?Ue():Ce();_e(e),G.current=!1,F(!1)}}),[q]),r.useUpdateEffect((function(){U(-1!==j.index?"".concat(p).concat(o.ObjectUtils.isNotEmpty(j.parentKey)?"_"+j.parentKey:"","_").concat(j.index):null)}),[j]),r.useUnmountEffect((function(){o.ZIndexUtils.clear(V.current)})),m.useImperativeHandle(a,(function(){return{props:l,toggle:ce,getElement:function(){return Z.current},getRootMenu:function(){return V.current},getMenuButton:function(){return W.current}}}));var Be=function(){if(l.start){var e=o.ObjectUtils.getJSXElement(l.start,l),n=u({className:ne("start")},ee("start"));return m.createElement("div",n,e)}return null}(),qe=function(){if(l.end){var e=o.ObjectUtils.getJSXElement(l.end,l),n=u({className:ne("end")},ee("end"));return m.createElement("div",n,e)}return null}(),Fe=function(){if(l.model&&l.model.length<1)return null;var e=u(v(v(v(v({ref:W,href:"#",tabIndex:"0","aria-haspopup":!!(y&&l.model&&l.model.length>0),"aria-expanded":y,"aria-label":n.ariaLabel("navigation"),"aria-controls":p,role:"button"},"tabIndex",0),"className",ne("button")),"onKeyDown",(function(e){var n;("Enter"===(n=e).code||"NumpadEnter"===n.code||"Space"===n.code)&&ce(n)})),"onClick",(function(e){return ce(e)})),ee("button")),t=u(ee("popupIcon")),r=o.IconUtils.getJSXIcon(l.menuIcon||m.createElement(i.BarsIcon,t),w({},t),{props:l});return m.createElement("a",e,r)}(),ze=m.createElement(N,{hostName:"Menubar",ariaActivedescendant:I?D:void 0,level:0,id:p,ref:V,menuProps:l,model:H,onLeafClick:function(e){var n=e.originalEvent,t=e.processedItem,r=de(t),i=o.ObjectUtils.isEmpty(t.parent);if(we(t)){var a=t.index,u=t.key,c=t.level,l=t.parentKey;A(M.filter((function(e){return u!==e.key&&u.startsWith(e.key)}))),P({index:a,level:c,parentKey:l}),r||X(!i),setTimeout((function(){o.DomHandler.focus(V.current),r&&X(!0)}),0)}else if(r)o.DomHandler.focus(V.current),be({originalEvent:n,processedItem:t});else{var s=i?t:M.find((function(e){return""===e.parentKey})),m=s?s.index:-1;se(n),P({index:m,parentKey:s?s.parentKey:""}),g(!1)}},onItemMouseEnter:function(e){!y&&J&&be(e)},onFocus:function(e){h(!0),P(-1!==j.index?j:{index:Ce(),level:0,parentKey:""}),l.onFocus&&l.onFocus(e)},onBlur:function(e){h(!1),P({index:-1,level:0,parentKey:""}),Y.current="",X(!1),l.onBlur&&l.onBlur(e)},onKeyDown:function(e){var n=e.metaKey||e.ctrlKey;switch(e.code){case"ArrowDown":ve(e);break;case"ArrowUp":ye(e);break;case"ArrowLeft":ge(e);break;case"ArrowRight":xe(e);break;case"Home":Ie(e);break;case"End":he(e);break;case"Space":Oe(e);break;case"Enter":case"NumpadEnter":Ee(e);break;case"Escape":ke();break;case"Tab":je(e);break;case"PageDown":case"PageUp":case"Backspace":case"ShiftLeft":case"ShiftRight":break;default:!n&&o.ObjectUtils.isPrintableCharacter(e.key)&&Re(e,e.key)}},root:!0,activeItemPath:M,focusedItemId:I?D:void 0,submenuIcon:l.submenuIcon,ptm:ee,cx:ne}),Je=u({id:l.id,ref:Z,className:o.classNames(l.className,ne("root",{mobileActiveState:y})),style:l.style},O.getOtherProps(l),ee("root"));return m.createElement("div",Je,Be,Fe,ze,qe)})));D.displayName="Menubar",exports.Menubar=D;
