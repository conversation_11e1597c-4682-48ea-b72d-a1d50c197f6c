import*as e from"react";import n,{PrimeReactContext as t,a<PERSON><PERSON><PERSON><PERSON> as r}from"primereact/api";import{ComponentBase as i,useHandleStyle as o}from"primereact/componentbase";import{useMergeProps as a,useEventListener as u,useResizeListener as c,useMountEffect as l,useUpdateEffect as s,useUnmountEffect as m}from"primereact/hooks";import{BarsIcon as f}from"primereact/icons/bars";import{classNames as p,ObjectUtils as d,IconUtils as b,DomHandler as v,UniqueComponentId as y,ZIndexUtils as g}from"primereact/utils";import{AngleDownIcon as x}from"primereact/icons/angledown";import{AngleRightIcon as h}from"primereact/icons/angleright";import{Ripple as I}from"primereact/ripple";function E(e){return E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},E(e)}function k(e,n){if("object"!=E(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=E(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}function O(e){var n=k(e,"string");return"symbol"==E(n)?n:n+""}function S(e,n,t){return(n=O(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function w(e){if(Array.isArray(e))return e}function P(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,i,o,a,u=[],c=!0,l=!1;try{if(o=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=o.call(t)).done)&&(u.push(r.value),u.length!==n);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=t.return&&(a=t.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}function N(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function K(e,n){if(e){if("string"==typeof e)return N(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?N(e,n):void 0}}function j(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function D(e,n){return w(e)||P(e,n)||K(e,n)||j()}var A=i.extend({defaultProps:{__TYPE:"Menubar",id:null,model:null,style:null,className:null,start:null,ariaLabel:null,ariaLabelledBy:null,onFocus:null,onBlur:null,submenuIcon:null,menuIcon:null,end:null,children:void 0},css:{classes:{start:"p-menubar-start",end:"p-menubar-end",button:"p-menubar-button",root:function(e){return p("p-menubar p-component",{"p-menubar-mobile-active":e.mobileActiveState})},separator:"p-menuitem-separator",icon:"p-menuitem-icon",label:"p-menuitem-text",submenuIcon:"p-submenu-icon",menuitem:function(e){return p("p-menuitem",{"p-menuitem-active p-highlight":e.active,"p-focus":e.focused,"p-disabled":e.disabled})},menu:"p-menubar-root-list",content:"p-menuitem-content",submenu:"p-submenu-list",action:function(e){return p("p-menuitem-link",{"p-disabled":e.disabled})}},styles:"\n@layer primereact {\n    .p-menubar {\n        display: flex;\n        align-items: center;\n    }\n\n    .p-menubar ul {\n        margin: 0;\n        padding: 0;\n        list-style: none;\n    }\n\n    .p-menubar .p-menuitem-link {\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        text-decoration: none;\n        overflow: hidden;\n        position: relative;\n    }\n\n    .p-menubar .p-menuitem-text {\n        line-height: 1;\n    }\n\n    .p-menubar .p-menuitem {\n        position: relative;\n    }\n\n    .p-menubar-root-list {\n        display: flex;\n        align-items: center;\n        flex-wrap: wrap;\n    }\n\n    .p-menubar-root-list > li ul {\n        display: none;\n        z-index: 1;\n    }\n\n    .p-menubar-root-list > .p-menuitem-active > .p-submenu-list {\n        display: block;\n    }\n\n    .p-menubar .p-submenu-list {\n        display: none;\n        position: absolute;\n        z-index: 5;\n    }\n\n    .p-menubar .p-submenu-list > .p-menuitem-active > .p-submenu-list {\n        display: block;\n        left: 100%;\n        top: 0;\n    }\n\n    .p-menubar .p-submenu-list .p-menuitem .p-menuitem-content .p-menuitem-link .p-submenu-icon {\n        margin-left: auto;\n    }\n\n    .p-menubar .p-menubar-end {\n        margin-left: auto;\n        align-self: center;\n    }\n\n    .p-menubar-button {\n        display: none;\n        cursor: pointer;\n        align-items: center;\n        justify-content: center;\n        text-decoration: none;\n    }\n}\n"}});function C(){return C=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},C.apply(null,arguments)}function L(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function M(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?L(Object(t),!0).forEach((function(n){S(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):L(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var T=e.memo(e.forwardRef((function(n,t){var r=a(),i=n.ptm,o=n.cx,u=function(e,t,r){return i(t,{props:n,hostName:n.hostName,context:{item:e,index:r,active:y(e),focused:k(e),disabled:E(e),level:n.level}})},c=function(e,t){E(t)||n.mobileActive?e.preventDefault():n.onItemMouseEnter&&n.onItemMouseEnter({originalEvent:e,processedItem:t})},l=function(e,n){var t=n.item;E(n)?e.preventDefault():(t.command&&t.command({originalEvent:e,item:t}),s({originalEvent:e,processedItem:n,isFocus:!0}),t.url||(e.preventDefault(),e.stopPropagation()))},s=function(e){n.onLeafClick&&n.onLeafClick(e)},m=function(e){var n;return null===(n=e.item)||void 0===n?void 0:n.id},f=function(e){return"".concat(n.id,"_").concat(e.key)},v=function(e,n,t){return e&&e.item?d.getItemValue(e.item[n],t):void 0},y=function(e){return n.activeItemPath.some((function(n){return n.key===e.key}))},g=function(e){return!1!==v(e,"visible")},E=function(e){return v(e,"disabled")},k=function(e){return n.focusedItemId===f(e)},O=function(e){return d.isNotEmpty(e.items)},w=function(e){return e-n.model.slice(0,e).filter((function(e){return g(e)&&v(e,"separator")})).length+1},P=function(t,a){var u=n.id+"_separator_"+a+"_"+t.key,c=r({"data-id":u,className:o("separator"),role:"separator"},i("separator",{hostName:n.hostName}));return e.createElement("li",C({},c,{key:u}))},N=function(t){var r=t&&t.items;return r?e.createElement(T,{id:n.id,hostName:n.hostName,menuProps:n.menuProps,level:n.level+1,model:r,activeItemPath:n.activeItemPath,focusedItemId:n.focusedItemId,onLeafClick:s,onItemMouseEnter:n.onItemMouseEnter,submenuIcon:n.submenuIcon,ptm:i,style:{display:y(t)?"block":"none"},cx:o}):null},K=function(t,i){var a=t.item;if(!g(t))return null;var s=m(t),P=f(t),K=y(t),j=k(t),D=E(t)||!1,A=O(t),L=p("p-menuitem-link",{"p-disabled":D}),T=p("p-menuitem-icon",v(t,"icon")),R=r({className:o("icon")},u(t,"icon",i)),_=b.getJSXIcon(a.icon,M({},R),{props:n.menuProps}),B=r({className:o("label")},u(t,"label",i)),F=a.label&&e.createElement("span",B,a.label),J=v(t,"items"),X=r({className:o("submenuIcon")},u(t,"submenuIcon",i)),z=J&&b.getJSXIcon(n.root?n.submenuIcon||e.createElement(x,X):n.submenuIcon||e.createElement(h,X),M({},X),{props:M({menuProps:n.menuProps},n)}),U=N(t),V=r({href:a.url||"#",tabIndex:"-1",className:o("action",{disabled:D}),onFocus:function(e){return e.stopPropagation()},target:v(t,"target"),"aria-haspopup":null!=J},u(t,"action",i)),H=e.createElement("a",V,_,F,z,e.createElement(I,null));a.template&&(H=d.getJSXElement(a.template,a,{className:L,labelClassName:"p-menuitem-text",iconClassName:T,submenuIconClassName:"p-submenu-icon",element:H,props:n}));var W=r({onClick:function(e){return l(e,t)},onMouseEnter:function(e){return c(e,t)},className:o("content")},u(t,"content",i)),Z=v(t,"className"),Y=r(S({id:s,"data-id":P,role:"menuitem","aria-label":a.label,"aria-disabled":D,"aria-expanded":A?K:void 0,"aria-haspopup":A&&!a.url?"menu":void 0,"aria-setsize":n.model.filter((function(e){return g(e)&&!v(e,"separator")})).length,"aria-posinset":w(i),"data-p-highlight":K,"data-p-focused":j,"data-p-disabled":D,className:p(Z,o("menuitem",{active:K,focused:j,disabled:D}))},"data-p-disabled",D||!1),u(t,"menuitem",i));return e.createElement("li",C({},Y,{key:"".concat(P)}),e.createElement("div",W,H),U)},j=n.root?"menubar":"menu",D=n.root?"menu":"submenu",A=n.root?"0":null,L=n.model?n.model.map((function(e,n){return!1===e.visible?null:v(e,"separator")?P(e,n):K(e,n)})):null,R=r({ref:t,className:o(D),level:n.level,onFocus:n.onFocus,onBlur:n.onBlur,onKeyDown:n.onKeyDown,"data-id":n.id,tabIndex:A,"aria-activedescendant":n.ariaActivedescendant,style:n.style,role:j},i(D));return e.createElement("ul",R,L)})));function R(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function _(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?R(Object(t),!0).forEach((function(n){S(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):R(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}T.displayName="MenubarSub";var B=e.memo(e.forwardRef((function(i,x){var h=a(),I=e.useContext(t),E=A.getProps(i,I),k=D(e.useState(E.id),2),O=k[0],w=k[1],P=D(e.useState(!1),2),N=P[0],K=P[1],j=D(e.useState(!1),2),C=j[0],L=j[1],M=D(e.useState({index:-1,level:0,parentKey:""}),2),R=M[0],B=M[1],F=D(e.useState(null),2),J=F[0],X=F[1],z=D(e.useState([]),2),U=z[0],V=z[1],H=D(e.useState([]),2),W=H[0],Z=H[1],Y=D(e.useState([]),2),$=Y[0],q=Y[1],G=D(e.useState(!1),2),Q=G[0],ee=G[1],ne=D(e.useState(!1),2),te=ne[0],re=ne[1],ie=e.useRef(null),oe=e.useRef(null),ae=e.useRef(null),ue=e.useRef(""),ce=e.useRef(null),le=e.useRef(!1),se=A.setMetaData({props:E,state:{id:O,mobileActive:N}}),me=se.ptm,fe=se.cx;o(A.css.styles,se.isUnstyled,{name:"menubar"});var pe=D(u({type:"click",listener:function(e){ae.current&&!ae.current.contains(e.target)&&Ie()},options:{capture:!0}}),2),de=pe[0],be=pe[1],ve=D(c({listener:function(e){v.isTouchDevice()||Ie(e)}}),2),ye=ve[0],ge=ve[1],xe=function(e){N?(K(!1),Ie()):(K(!0),setTimeout((function(){he()}),1)),e.preventDefault()},he=function(){B({index:He(),level:0,parentKey:""}),v.focus(oe.current)},Ie=function(e){N&&(K(!1),setTimeout((function(){v.focus(ae.current)}),0)),V([]),B({index:-1,level:0,parentKey:""}),e&&v.focus(oe.current),re(!1)},Ee=function(e,n){return e?d.getItemValue(e[n]):void 0},ke=function(e){return Ee(e,"separator")},Oe=function(e){return e?Ee(e.item,"label"):void 0},Se=function(e){return e&&d.isNotEmpty(e.items)},we=function(e){var n=e.processedItem,t=e.isFocus;if(!d.isEmpty(n)){var r=n.index,i=n.key,o=n.level,a=n.parentKey,u=d.isNotEmpty(n.items),c=U.filter((function(e){return e.parentKey!==a&&e.parentKey!==i}));u&&c.push(n),B({index:r,level:o,parentKey:a}),V(c),u&&re(!0),t&&v.focus(oe.current)}},Pe=function(e){var n=W[R.index];if(n?d.isEmpty(n.parent):null){Se(n)&&(we({originalEvent:e,processedItem:n}),B({index:-1,parentKey:n.key}),setTimeout((function(){return ee(!0)}),0))}else{var t=-1!==R.index?ze(R.index):He();Ye(t)}e.preventDefault()},Ne=function(e){var n=W[R.index];if(d.isEmpty(n.parent)){Se(n)&&(we({originalEvent:e,processedItem:n}),B({index:-1,parentKey:n.key}),le.current=!0,setTimeout((function(){return ee(!0)}),0))}else{var t=U.find((function(e){return e.key===n.parentKey}));if(0===R.index&&t&&""===t.parentKey)B({index:-1,parentKey:t?t.parentKey:""}),ue.current="",Ke(e);else{var r=-1!==R.index?Ue(R.index):We();Ye(r)}}e.preventDefault()},Ke=function(e){var n=W[R.index],t=n?U.find((function(e){return e.key===n.parentKey})):null;if(t)we({originalEvent:e,processedItem:t}),V(U.filter((function(e){return e.key!==t.key})));else{var r=-1!==R.index?Ue(R.index):We();Ye(r)}e.preventDefault()},je=function(e){var n=W[R.index];if(n?U.find((function(e){return e.key===n.parentKey})):null){Se(n)&&(we({originalEvent:e,processedItem:n}),B({index:-1,parentKey:n.key}),setTimeout((function(){return ee(!0)}),0))}else{var t=-1!==R.index?ze(R.index):He();Ye(t)}e.preventDefault()},De=function(e){Ye(Je()),e.preventDefault()},Ae=function(e){Ye(Xe()),e.preventDefault()},Ce=function(e){if(-1!==R.index){var n=v.findSingle(oe.current,'li[data-id="'.concat("".concat(J),'"]')),t=n&&v.findSingle(n,'a[data-pc-section="action"]');t?t.click():n&&n.click()}e.preventDefault()},Le=function(e){Ce(e)},Me=function(e){Ie(!0),B({focusedItemInfo:R,index:He()})},Te=function(e){if(-1!==R.index){var n=W[R.index];!Se(n)&&we({originalEvent:e,processedItem:n})}Ie()},Re=function(e){return _e(e)&&Oe(e).toLocaleLowerCase().startsWith(ue.current.toLocaleLowerCase())},_e=function(e){return!!e&&!Ee(e.item,"disabled")&&!ke(e.item)},Be=function(e){return _e(e)&&Fe(e)},Fe=function(e){return U.some((function(n){return n.key===e.key}))},Je=function(){return W.findIndex((function(e){return _e(e)}))},Xe=function(){return d.findLastIndex(W,(function(e){return _e(e)}))},ze=function(e){var n=e<W.length-1?W.slice(e+1).findIndex((function(e){return _e(e)})):-1;return n>-1?n+e+1:e},Ue=function(e){var n=e>0?d.findLastIndex(W.slice(0,e),(function(e){return _e(e)})):-1;return n>-1?n:e},Ve=function(){return W.findIndex((function(e){return Be(e)}))},He=function(){return Ve()},We=function(){return Ve()},Ze=function(e,n){ue.current=(ue.current||"")+n;var t=-1,r=!1;return-1!==(t=-1!==R.index?-1===(t=W.slice(R.index).findIndex((function(e){return Re(e)})))?W.slice(0,R.index).findIndex((function(e){return Re(e)})):t+R.index:W.findIndex((function(e){return Re(e)})))&&(r=!0),-1===t&&-1===R.index&&(t=He()),-1!==t&&Ye(t),ce.current&&clearTimeout(ce.current),ce.current=setTimeout((function(){ue.current="",ce.current=null}),500),r},Ye=function(e){R.index!==e&&(B(_(_({},R),{},{index:e})),$e())},$e=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1,n=-1!==e?"".concat(O,"_").concat(e):J,t=v.findSingle(oe.current,'li[data-id="'.concat(n,'"]'));t&&t.scrollIntoView&&t.scrollIntoView({block:"nearest",inline:"start"})},qe=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=[];return e&&e.forEach((function(e,o){var a=(""!==r?r+"_":"")+o,u={item:e,index:o,level:n,key:a,parent:t,parentKey:r};u.items=qe(e.items,n+1,u,a),i.push(u)})),i};l((function(){O||w(y())})),s((function(){N?(de(),ye(),g.set("menu",oe.current,I&&I.autoZIndex||n.autoZIndex,I&&I.zIndex.menu||n.zIndex.menu)):(ge(),be(),g.clear(oe.current))}),[N]),e.useEffect((function(){var e=qe(E.model||[],0,null,"");q(e)}),[E.model]),s((function(){var e=U.find((function(e){return e.key===R.parentKey}));Z(e?e.items:$)}),[U,R,$]),s((function(){d.isNotEmpty(U)?(de(),ye()):(be(),ge())}),[U]),s((function(){if(Q){var e=-1!==R.index?ze(R.index):le.current?Xe():He();Ye(e),le.current=!1,ee(!1)}}),[Q]),s((function(){X(-1!==R.index?"".concat(O).concat(d.isNotEmpty(R.parentKey)?"_"+R.parentKey:"","_").concat(R.index):null)}),[R]),m((function(){g.clear(oe.current)})),e.useImperativeHandle(x,(function(){return{props:E,toggle:xe,getElement:function(){return ie.current},getRootMenu:function(){return oe.current},getMenuButton:function(){return ae.current}}}));var Ge=function(){if(E.start){var n=d.getJSXElement(E.start,E),t=h({className:fe("start")},me("start"));return e.createElement("div",t,n)}return null}(),Qe=function(){if(E.end){var n=d.getJSXElement(E.end,E),t=h({className:fe("end")},me("end"));return e.createElement("div",t,n)}return null}(),en=function(){if(E.model&&E.model.length<1)return null;var n=h(S(S(S(S({ref:ae,href:"#",tabIndex:"0","aria-haspopup":!!(N&&E.model&&E.model.length>0),"aria-expanded":N,"aria-label":r("navigation"),"aria-controls":O,role:"button"},"tabIndex",0),"className",fe("button")),"onKeyDown",(function(e){var n;("Enter"===(n=e).code||"NumpadEnter"===n.code||"Space"===n.code)&&xe(n)})),"onClick",(function(e){return xe(e)})),me("button")),t=h(me("popupIcon")),i=b.getJSXIcon(E.menuIcon||e.createElement(f,t),_({},t),{props:E});return e.createElement("a",n,i)}(),nn=e.createElement(T,{hostName:"Menubar",ariaActivedescendant:C?J:void 0,level:0,id:O,ref:oe,menuProps:E,model:$,onLeafClick:function(e){var n=e.originalEvent,t=e.processedItem,r=Se(t),i=d.isEmpty(t.parent);if(Fe(t)){var o=t.index,a=t.key,u=t.level,c=t.parentKey;V(U.filter((function(e){return a!==e.key&&a.startsWith(e.key)}))),B({index:o,level:u,parentKey:c}),r||re(!i),setTimeout((function(){v.focus(oe.current),r&&re(!0)}),0)}else if(r)v.focus(oe.current),we({originalEvent:n,processedItem:t});else{var l=i?t:U.find((function(e){return""===e.parentKey})),s=l?l.index:-1;Ie(n),B({index:s,parentKey:l?l.parentKey:""}),K(!1)}},onItemMouseEnter:function(e){!N&&te&&we(e)},onFocus:function(e){L(!0),B(-1!==R.index?R:{index:He(),level:0,parentKey:""}),E.onFocus&&E.onFocus(e)},onBlur:function(e){L(!1),B({index:-1,level:0,parentKey:""}),ue.current="",re(!1),E.onBlur&&E.onBlur(e)},onKeyDown:function(e){var n=e.metaKey||e.ctrlKey;switch(e.code){case"ArrowDown":Pe(e);break;case"ArrowUp":Ne(e);break;case"ArrowLeft":Ke(e);break;case"ArrowRight":je(e);break;case"Home":De(e);break;case"End":Ae(e);break;case"Space":Le(e);break;case"Enter":case"NumpadEnter":Ce(e);break;case"Escape":Me();break;case"Tab":Te(e);break;case"PageDown":case"PageUp":case"Backspace":case"ShiftLeft":case"ShiftRight":break;default:!n&&d.isPrintableCharacter(e.key)&&Ze(e,e.key)}},root:!0,activeItemPath:U,focusedItemId:C?J:void 0,submenuIcon:E.submenuIcon,ptm:me,cx:fe}),tn=h({id:E.id,ref:ie,className:p(E.className,fe("root",{mobileActiveState:N})),style:E.style},A.getOtherProps(E),me("root"));return e.createElement("div",tn,Ge,en,nn,Qe)})));B.displayName="Menubar";export{B as Menubar};
