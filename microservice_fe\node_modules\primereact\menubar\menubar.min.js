this.primereact=this.primereact||{},this.primereact.menubar=function(e,n,t,r,i,o,a,u,c,l){"use strict";function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function m(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}})),n.default=e,Object.freeze(n)}var f=m(n),p=s(t);function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function b(e,n){if("object"!=d(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=d(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}function v(e){var n=b(e,"string");return"symbol"==d(n)?n:n+""}function y(e,n,t){return(n=v(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function g(e){if(Array.isArray(e))return e}function x(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,i,o,a,u=[],c=!0,l=!1;try{if(o=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=o.call(t)).done)&&(u.push(r.value),u.length!==n);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=t.return&&(a=t.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}function I(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function h(e,n){if(e){if("string"==typeof e)return I(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?I(e,n):void 0}}function E(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function O(e,n){return g(e)||x(e,n)||h(e,n)||E()}var k=r.ComponentBase.extend({defaultProps:{__TYPE:"Menubar",id:null,model:null,style:null,className:null,start:null,ariaLabel:null,ariaLabelledBy:null,onFocus:null,onBlur:null,submenuIcon:null,menuIcon:null,end:null,children:void 0},css:{classes:{start:"p-menubar-start",end:"p-menubar-end",button:"p-menubar-button",root:function(e){return a.classNames("p-menubar p-component",{"p-menubar-mobile-active":e.mobileActiveState})},separator:"p-menuitem-separator",icon:"p-menuitem-icon",label:"p-menuitem-text",submenuIcon:"p-submenu-icon",menuitem:function(e){return a.classNames("p-menuitem",{"p-menuitem-active p-highlight":e.active,"p-focus":e.focused,"p-disabled":e.disabled})},menu:"p-menubar-root-list",content:"p-menuitem-content",submenu:"p-submenu-list",action:function(e){return a.classNames("p-menuitem-link",{"p-disabled":e.disabled})}},styles:"\n@layer primereact {\n    .p-menubar {\n        display: flex;\n        align-items: center;\n    }\n\n    .p-menubar ul {\n        margin: 0;\n        padding: 0;\n        list-style: none;\n    }\n\n    .p-menubar .p-menuitem-link {\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        text-decoration: none;\n        overflow: hidden;\n        position: relative;\n    }\n\n    .p-menubar .p-menuitem-text {\n        line-height: 1;\n    }\n\n    .p-menubar .p-menuitem {\n        position: relative;\n    }\n\n    .p-menubar-root-list {\n        display: flex;\n        align-items: center;\n        flex-wrap: wrap;\n    }\n\n    .p-menubar-root-list > li ul {\n        display: none;\n        z-index: 1;\n    }\n\n    .p-menubar-root-list > .p-menuitem-active > .p-submenu-list {\n        display: block;\n    }\n\n    .p-menubar .p-submenu-list {\n        display: none;\n        position: absolute;\n        z-index: 5;\n    }\n\n    .p-menubar .p-submenu-list > .p-menuitem-active > .p-submenu-list {\n        display: block;\n        left: 100%;\n        top: 0;\n    }\n\n    .p-menubar .p-submenu-list .p-menuitem .p-menuitem-content .p-menuitem-link .p-submenu-icon {\n        margin-left: auto;\n    }\n\n    .p-menubar .p-menubar-end {\n        margin-left: auto;\n        align-self: center;\n    }\n\n    .p-menubar-button {\n        display: none;\n        cursor: pointer;\n        align-items: center;\n        justify-content: center;\n        text-decoration: none;\n    }\n}\n"}});function j(){return j=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},j.apply(null,arguments)}function P(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function N(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?P(Object(t),!0).forEach((function(n){y(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):P(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var S=f.memo(f.forwardRef((function(e,n){var t=i.useMergeProps(),r=e.ptm,o=e.cx,s=function(n,t,i){return r(t,{props:e,hostName:e.hostName,context:{item:n,index:i,active:x(n),focused:E(n),disabled:h(n),level:e.level}})},m=function(n,t){h(t)||e.mobileActive?n.preventDefault():e.onItemMouseEnter&&e.onItemMouseEnter({originalEvent:n,processedItem:t})},p=function(e,n){var t=n.item;h(n)?e.preventDefault():(t.command&&t.command({originalEvent:e,item:t}),d({originalEvent:e,processedItem:n,isFocus:!0}),t.url||(e.preventDefault(),e.stopPropagation()))},d=function(n){e.onLeafClick&&e.onLeafClick(n)},b=function(e){var n;return null===(n=e.item)||void 0===n?void 0:n.id},v=function(n){return"".concat(e.id,"_").concat(n.key)},g=function(e,n,t){return e&&e.item?a.ObjectUtils.getItemValue(e.item[n],t):void 0},x=function(n){return e.activeItemPath.some((function(e){return e.key===n.key}))},I=function(e){return!1!==g(e,"visible")},h=function(e){return g(e,"disabled")},E=function(n){return e.focusedItemId===v(n)},O=function(e){return a.ObjectUtils.isNotEmpty(e.items)},k=function(n){return n-e.model.slice(0,n).filter((function(e){return I(e)&&g(e,"separator")})).length+1},P=function(n,i){var a=e.id+"_separator_"+i+"_"+n.key,u=t({"data-id":a,className:o("separator"),role:"separator"},r("separator",{hostName:e.hostName}));return f.createElement("li",j({},u,{key:a}))},w=function(n){var t=n&&n.items;return t?f.createElement(S,{id:e.id,hostName:e.hostName,menuProps:e.menuProps,level:e.level+1,model:t,activeItemPath:e.activeItemPath,focusedItemId:e.focusedItemId,onLeafClick:d,onItemMouseEnter:e.onItemMouseEnter,submenuIcon:e.submenuIcon,ptm:r,style:{display:x(n)?"block":"none"},cx:o}):null},D=function(n,r){var i=n.item;if(!I(n))return null;var d=b(n),P=v(n),S=x(n),D=E(n),U=h(n)||!1,K=O(n),M=a.classNames("p-menuitem-link",{"p-disabled":U}),A=a.classNames("p-menuitem-icon",g(n,"icon")),C=t({className:o("icon")},s(n,"icon",r)),L=a.IconUtils.getJSXIcon(i.icon,N({},C),{props:e.menuProps}),R=t({className:o("label")},s(n,"label",r)),_=i.label&&f.createElement("span",R,i.label),H=g(n,"items"),T=t({className:o("submenuIcon")},s(n,"submenuIcon",r)),B=H&&a.IconUtils.getJSXIcon(e.root?e.submenuIcon||f.createElement(u.AngleDownIcon,T):e.submenuIcon||f.createElement(c.AngleRightIcon,T),N({},T),{props:N({menuProps:e.menuProps},e)}),F=w(n),z=t({href:i.url||"#",tabIndex:"-1",className:o("action",{disabled:U}),onFocus:function(e){return e.stopPropagation()},target:g(n,"target"),"aria-haspopup":null!=H},s(n,"action",r)),J=f.createElement("a",z,L,_,B,f.createElement(l.Ripple,null));i.template&&(J=a.ObjectUtils.getJSXElement(i.template,i,{className:M,labelClassName:"p-menuitem-text",iconClassName:A,submenuIconClassName:"p-submenu-icon",element:J,props:e}));var X=t({onClick:function(e){return p(e,n)},onMouseEnter:function(e){return m(e,n)},className:o("content")},s(n,"content",r)),Z=g(n,"className"),V=t(y({id:d,"data-id":P,role:"menuitem","aria-label":i.label,"aria-disabled":U,"aria-expanded":K?S:void 0,"aria-haspopup":K&&!i.url?"menu":void 0,"aria-setsize":e.model.filter((function(e){return I(e)&&!g(e,"separator")})).length,"aria-posinset":k(r),"data-p-highlight":S,"data-p-focused":D,"data-p-disabled":U,className:a.classNames(Z,o("menuitem",{active:S,focused:D,disabled:U}))},"data-p-disabled",U||!1),s(n,"menuitem",r));return f.createElement("li",j({},V,{key:"".concat(P)}),f.createElement("div",X,J),F)},U=e.root?"menubar":"menu",K=e.root?"menu":"submenu",M=e.root?"0":null,A=e.model?e.model.map((function(e,n){return!1===e.visible?null:g(e,"separator")?P(e,n):D(e,n)})):null,C=t({ref:n,className:o(K),level:e.level,onFocus:e.onFocus,onBlur:e.onBlur,onKeyDown:e.onKeyDown,"data-id":e.id,tabIndex:M,"aria-activedescendant":e.ariaActivedescendant,style:e.style,role:U},r(K));return f.createElement("ul",C,A)})));function w(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function D(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?w(Object(t),!0).forEach((function(n){y(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):w(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}S.displayName="MenubarSub";var U=f.memo(f.forwardRef((function(e,n){var u=i.useMergeProps(),c=f.useContext(t.PrimeReactContext),l=k.getProps(e,c),s=O(f.useState(l.id),2),m=s[0],d=s[1],b=O(f.useState(!1),2),v=b[0],g=b[1],x=O(f.useState(!1),2),I=x[0],h=x[1],E=O(f.useState({index:-1,level:0,parentKey:""}),2),j=E[0],P=E[1],N=O(f.useState(null),2),w=N[0],U=N[1],K=O(f.useState([]),2),M=K[0],A=K[1],C=O(f.useState([]),2),L=C[0],R=C[1],_=O(f.useState([]),2),H=_[0],T=_[1],B=O(f.useState(!1),2),F=B[0],z=B[1],J=O(f.useState(!1),2),X=J[0],Z=J[1],V=f.useRef(null),W=f.useRef(null),q=f.useRef(null),Y=f.useRef(""),$=f.useRef(null),G=f.useRef(!1),Q=k.setMetaData({props:l,state:{id:m,mobileActive:v}}),ee=Q.ptm,ne=Q.cx;r.useHandleStyle(k.css.styles,Q.isUnstyled,{name:"menubar"});var te=O(i.useEventListener({type:"click",listener:function(e){q.current&&!q.current.contains(e.target)&&se()},options:{capture:!0}}),2),re=te[0],ie=te[1],oe=O(i.useResizeListener({listener:function(e){a.DomHandler.isTouchDevice()||se(e)}}),2),ae=oe[0],ue=oe[1],ce=function(e){v?(g(!1),se()):(g(!0),setTimeout((function(){le()}),1)),e.preventDefault()},le=function(){P({index:Ce(),level:0,parentKey:""}),a.DomHandler.focus(W.current)},se=function(e){v&&(g(!1),setTimeout((function(){a.DomHandler.focus(q.current)}),0)),A([]),P({index:-1,level:0,parentKey:""}),e&&a.DomHandler.focus(W.current),Z(!1)},me=function(e,n){return e?a.ObjectUtils.getItemValue(e[n]):void 0},fe=function(e){return me(e,"separator")},pe=function(e){return e?me(e.item,"label"):void 0},de=function(e){return e&&a.ObjectUtils.isNotEmpty(e.items)},be=function(e){var n=e.processedItem,t=e.isFocus;if(!a.ObjectUtils.isEmpty(n)){var r=n.index,i=n.key,o=n.level,u=n.parentKey,c=a.ObjectUtils.isNotEmpty(n.items),l=M.filter((function(e){return e.parentKey!==u&&e.parentKey!==i}));c&&l.push(n),P({index:r,level:o,parentKey:u}),A(l),c&&Z(!0),t&&a.DomHandler.focus(W.current)}},ve=function(e){var n=L[j.index];if(n?a.ObjectUtils.isEmpty(n.parent):null){de(n)&&(be({originalEvent:e,processedItem:n}),P({index:-1,parentKey:n.key}),setTimeout((function(){return z(!0)}),0))}else{var t=-1!==j.index?Ke(j.index):Ce();_e(t)}e.preventDefault()},ye=function(e){var n=L[j.index];if(a.ObjectUtils.isEmpty(n.parent)){de(n)&&(be({originalEvent:e,processedItem:n}),P({index:-1,parentKey:n.key}),G.current=!0,setTimeout((function(){return z(!0)}),0))}else{var t=M.find((function(e){return e.key===n.parentKey}));if(0===j.index&&t&&""===t.parentKey)P({index:-1,parentKey:t?t.parentKey:""}),Y.current="",ge(e);else{var r=-1!==j.index?Me(j.index):Le();_e(r)}}e.preventDefault()},ge=function(e){var n=L[j.index],t=n?M.find((function(e){return e.key===n.parentKey})):null;if(t)be({originalEvent:e,processedItem:t}),A(M.filter((function(e){return e.key!==t.key})));else{var r=-1!==j.index?Me(j.index):Le();_e(r)}e.preventDefault()},xe=function(e){var n=L[j.index];if(n?M.find((function(e){return e.key===n.parentKey})):null){de(n)&&(be({originalEvent:e,processedItem:n}),P({index:-1,parentKey:n.key}),setTimeout((function(){return z(!0)}),0))}else{var t=-1!==j.index?Ke(j.index):Ce();_e(t)}e.preventDefault()},Ie=function(e){_e(De()),e.preventDefault()},he=function(e){_e(Ue()),e.preventDefault()},Ee=function(e){if(-1!==j.index){var n=a.DomHandler.findSingle(W.current,'li[data-id="'.concat("".concat(w),'"]')),t=n&&a.DomHandler.findSingle(n,'a[data-pc-section="action"]');t?t.click():n&&n.click()}e.preventDefault()},Oe=function(e){Ee(e)},ke=function(e){se(!0),P({focusedItemInfo:j,index:Ce()})},je=function(e){if(-1!==j.index){var n=L[j.index];!de(n)&&be({originalEvent:e,processedItem:n})}se()},Pe=function(e){return Ne(e)&&pe(e).toLocaleLowerCase().startsWith(Y.current.toLocaleLowerCase())},Ne=function(e){return!!e&&!me(e.item,"disabled")&&!fe(e.item)},Se=function(e){return Ne(e)&&we(e)},we=function(e){return M.some((function(n){return n.key===e.key}))},De=function(){return L.findIndex((function(e){return Ne(e)}))},Ue=function(){return a.ObjectUtils.findLastIndex(L,(function(e){return Ne(e)}))},Ke=function(e){var n=e<L.length-1?L.slice(e+1).findIndex((function(e){return Ne(e)})):-1;return n>-1?n+e+1:e},Me=function(e){var n=e>0?a.ObjectUtils.findLastIndex(L.slice(0,e),(function(e){return Ne(e)})):-1;return n>-1?n:e},Ae=function(){return L.findIndex((function(e){return Se(e)}))},Ce=function(){return Ae()},Le=function(){return Ae()},Re=function(e,n){Y.current=(Y.current||"")+n;var t=-1,r=!1;return-1!==(t=-1!==j.index?-1===(t=L.slice(j.index).findIndex((function(e){return Pe(e)})))?L.slice(0,j.index).findIndex((function(e){return Pe(e)})):t+j.index:L.findIndex((function(e){return Pe(e)})))&&(r=!0),-1===t&&-1===j.index&&(t=Ce()),-1!==t&&_e(t),$.current&&clearTimeout($.current),$.current=setTimeout((function(){Y.current="",$.current=null}),500),r},_e=function(e){j.index!==e&&(P(D(D({},j),{},{index:e})),He())},He=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1,n=-1!==e?"".concat(m,"_").concat(e):w,t=a.DomHandler.findSingle(W.current,'li[data-id="'.concat(n,'"]'));t&&t.scrollIntoView&&t.scrollIntoView({block:"nearest",inline:"start"})},Te=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=[];return e&&e.forEach((function(e,o){var a=(""!==r?r+"_":"")+o,u={item:e,index:o,level:n,key:a,parent:t,parentKey:r};u.items=Te(e.items,n+1,u,a),i.push(u)})),i};i.useMountEffect((function(){m||d(a.UniqueComponentId())})),i.useUpdateEffect((function(){v?(re(),ae(),a.ZIndexUtils.set("menu",W.current,c&&c.autoZIndex||p.default.autoZIndex,c&&c.zIndex.menu||p.default.zIndex.menu)):(ue(),ie(),a.ZIndexUtils.clear(W.current))}),[v]),f.useEffect((function(){var e=Te(l.model||[],0,null,"");T(e)}),[l.model]),i.useUpdateEffect((function(){var e=M.find((function(e){return e.key===j.parentKey}));R(e?e.items:H)}),[M,j,H]),i.useUpdateEffect((function(){a.ObjectUtils.isNotEmpty(M)?(re(),ae()):(ie(),ue())}),[M]),i.useUpdateEffect((function(){if(F){var e=-1!==j.index?Ke(j.index):G.current?Ue():Ce();_e(e),G.current=!1,z(!1)}}),[F]),i.useUpdateEffect((function(){U(-1!==j.index?"".concat(m).concat(a.ObjectUtils.isNotEmpty(j.parentKey)?"_"+j.parentKey:"","_").concat(j.index):null)}),[j]),i.useUnmountEffect((function(){a.ZIndexUtils.clear(W.current)})),f.useImperativeHandle(n,(function(){return{props:l,toggle:ce,getElement:function(){return V.current},getRootMenu:function(){return W.current},getMenuButton:function(){return q.current}}}));var Be=function(){if(l.start){var e=a.ObjectUtils.getJSXElement(l.start,l),n=u({className:ne("start")},ee("start"));return f.createElement("div",n,e)}return null}(),Fe=function(){if(l.end){var e=a.ObjectUtils.getJSXElement(l.end,l),n=u({className:ne("end")},ee("end"));return f.createElement("div",n,e)}return null}(),ze=function(){if(l.model&&l.model.length<1)return null;var e=u(y(y(y(y({ref:q,href:"#",tabIndex:"0","aria-haspopup":!!(v&&l.model&&l.model.length>0),"aria-expanded":v,"aria-label":t.ariaLabel("navigation"),"aria-controls":m,role:"button"},"tabIndex",0),"className",ne("button")),"onKeyDown",(function(e){var n;("Enter"===(n=e).code||"NumpadEnter"===n.code||"Space"===n.code)&&ce(n)})),"onClick",(function(e){return ce(e)})),ee("button")),n=u(ee("popupIcon")),r=a.IconUtils.getJSXIcon(l.menuIcon||f.createElement(o.BarsIcon,n),D({},n),{props:l});return f.createElement("a",e,r)}(),Je=f.createElement(S,{hostName:"Menubar",ariaActivedescendant:I?w:void 0,level:0,id:m,ref:W,menuProps:l,model:H,onLeafClick:function(e){var n=e.originalEvent,t=e.processedItem,r=de(t),i=a.ObjectUtils.isEmpty(t.parent);if(we(t)){var o=t.index,u=t.key,c=t.level,l=t.parentKey;A(M.filter((function(e){return u!==e.key&&u.startsWith(e.key)}))),P({index:o,level:c,parentKey:l}),r||Z(!i),setTimeout((function(){a.DomHandler.focus(W.current),r&&Z(!0)}),0)}else if(r)a.DomHandler.focus(W.current),be({originalEvent:n,processedItem:t});else{var s=i?t:M.find((function(e){return""===e.parentKey})),m=s?s.index:-1;se(n),P({index:m,parentKey:s?s.parentKey:""}),g(!1)}},onItemMouseEnter:function(e){!v&&X&&be(e)},onFocus:function(e){h(!0),P(-1!==j.index?j:{index:Ce(),level:0,parentKey:""}),l.onFocus&&l.onFocus(e)},onBlur:function(e){h(!1),P({index:-1,level:0,parentKey:""}),Y.current="",Z(!1),l.onBlur&&l.onBlur(e)},onKeyDown:function(e){var n=e.metaKey||e.ctrlKey;switch(e.code){case"ArrowDown":ve(e);break;case"ArrowUp":ye(e);break;case"ArrowLeft":ge(e);break;case"ArrowRight":xe(e);break;case"Home":Ie(e);break;case"End":he(e);break;case"Space":Oe(e);break;case"Enter":case"NumpadEnter":Ee(e);break;case"Escape":ke();break;case"Tab":je(e);break;case"PageDown":case"PageUp":case"Backspace":case"ShiftLeft":case"ShiftRight":break;default:!n&&a.ObjectUtils.isPrintableCharacter(e.key)&&Re(e,e.key)}},root:!0,activeItemPath:M,focusedItemId:I?w:void 0,submenuIcon:l.submenuIcon,ptm:ee,cx:ne}),Xe=u({id:l.id,ref:V,className:a.classNames(l.className,ne("root",{mobileActiveState:v})),style:l.style},k.getOtherProps(l),ee("root"));return f.createElement("div",Xe,Be,ze,Je,Fe)})));return U.displayName="Menubar",e.Menubar=U,Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,primereact.api,primereact.componentbase,primereact.hooks,primereact.icons.bars,primereact.utils,primereact.icons.angledown,primereact.icons.angleright,primereact.ripple);
