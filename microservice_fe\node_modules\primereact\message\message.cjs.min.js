"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("primereact/api"),r=require("primereact/componentbase"),n=require("primereact/hooks"),i=require("primereact/icons/check"),o=require("primereact/icons/exclamationtriangle"),c=require("primereact/icons/infocircle"),s=require("primereact/icons/timescircle"),a=require("primereact/utils");function l(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var u=l(e);function p(){return p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},p.apply(null,arguments)}function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}function f(e,t){if("object"!=m(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=m(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function y(e){var t=f(e,"string");return"symbol"==m(t)?t:t+""}function b(e,t,r){return(t=y(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var g=r.ComponentBase.extend({defaultProps:{__TYPE:"Message",id:null,className:null,style:null,text:null,icon:null,severity:"info",content:null,children:void 0},css:{classes:{root:function(e){var t=e.props.severity;return a.classNames("p-inline-message p-component",b({},"p-inline-message-".concat(t),t))},icon:"p-inline-message-icon",text:"p-inline-message-text"},styles:"\n        @layer primereact {\n            .p-inline-message {\n                display: inline-flex;\n                align-items: center;\n                justify-content: center;\n                vertical-align: top;\n            }\n\n            .p-inline-message-icon {\n                flex-shrink: 0;\n            }\n            \n            .p-inline-message-icon-only .p-inline-message-text {\n                visibility: hidden;\n                width: 0;\n            }\n            \n            .p-fluid .p-inline-message {\n                display: flex;\n            }        \n        }\n        "}});function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach((function(t){b(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var O=u.memo(u.forwardRef((function(e,l){var m=n.useMergeProps(),f=u.useContext(t.PrimeReactContext),y=g.getProps(e,f),b=u.useRef(null),v=g.setMetaData({props:y}),O=v.ptm,j=v.cx;r.useHandleStyle(g.css.styles,v.isUnstyled,{name:"message"});u.useImperativeHandle(l,(function(){return{props:y,getElement:function(){return b.current}}}));var P=function(){if(y.content)return a.ObjectUtils.getJSXElement(y.content,y);var e=a.ObjectUtils.getJSXElement(y.text,y),t=m({className:j("icon")},O("icon")),r=y.icon;if(!r)switch(y.severity){case"info":r=u.createElement(c.InfoCircleIcon,t);break;case"warn":r=u.createElement(o.ExclamationTriangleIcon,t);break;case"error":r=u.createElement(s.TimesCircleIcon,t);break;case"success":r=u.createElement(i.CheckIcon,t)}var n=a.IconUtils.getJSXIcon(r,d({},t),{props:y}),l=m({className:j("text")},O("text"));return u.createElement(u.Fragment,null,n,u.createElement("span",l,e))}(),x=m({className:a.classNames(y.className,j("root")),style:y.style,role:"alert","aria-live":"polite","aria-atomic":"true"},g.getOtherProps(y),O("root"));return u.createElement("div",p({id:y.id,ref:b},x),P)})));O.displayName="Message",exports.Message=O;
