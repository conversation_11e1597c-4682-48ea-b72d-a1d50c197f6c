import*as e from"react";import{PrimeReactContext as t}from"primereact/api";import{ComponentBase as r,useHandleStyle as n}from"primereact/componentbase";import{useMergeProps as i}from"primereact/hooks";import{CheckIcon as o}from"primereact/icons/check";import{ExclamationTriangleIcon as a}from"primereact/icons/exclamationtriangle";import{InfoCircleIcon as c}from"primereact/icons/infocircle";import{TimesCircleIcon as s}from"primereact/icons/timescircle";import{classNames as l,ObjectUtils as m,IconUtils as p}from"primereact/utils";function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},u.apply(null,arguments)}function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function y(e,t){if("object"!=f(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function b(e){var t=y(e,"string");return"symbol"==f(t)?t:t+""}function g(e,t,r){return(t=b(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var v=r.extend({defaultProps:{__TYPE:"Message",id:null,className:null,style:null,text:null,icon:null,severity:"info",content:null,children:void 0},css:{classes:{root:function(e){var t=e.props.severity;return l("p-inline-message p-component",g({},"p-inline-message-".concat(t),t))},icon:"p-inline-message-icon",text:"p-inline-message-text"},styles:"\n        @layer primereact {\n            .p-inline-message {\n                display: inline-flex;\n                align-items: center;\n                justify-content: center;\n                vertical-align: top;\n            }\n\n            .p-inline-message-icon {\n                flex-shrink: 0;\n            }\n            \n            .p-inline-message-icon-only .p-inline-message-text {\n                visibility: hidden;\n                width: 0;\n            }\n            \n            .p-fluid .p-inline-message {\n                display: flex;\n            }        \n        }\n        "}});function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function O(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach((function(t){g(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var j=e.memo(e.forwardRef((function(r,f){var y=i(),b=e.useContext(t),g=v.getProps(r,b),d=e.useRef(null),j=v.setMetaData({props:g}),P=j.ptm,h=j.cx;n(v.css.styles,j.isUnstyled,{name:"message"});e.useImperativeHandle(f,(function(){return{props:g,getElement:function(){return d.current}}}));var x=function(){if(g.content)return m.getJSXElement(g.content,g);var t=m.getJSXElement(g.text,g),r=y({className:h("icon")},P("icon")),n=g.icon;if(!n)switch(g.severity){case"info":n=e.createElement(c,r);break;case"warn":n=e.createElement(a,r);break;case"error":n=e.createElement(s,r);break;case"success":n=e.createElement(o,r)}var i=p.getJSXIcon(n,O({},r),{props:g}),l=y({className:h("text")},P("text"));return e.createElement(e.Fragment,null,i,e.createElement("span",l,t))}(),w=y({className:l(g.className,h("root")),style:g.style,role:"alert","aria-live":"polite","aria-atomic":"true"},v.getOtherProps(g),P("root"));return e.createElement("div",u({id:g.id,ref:d},w),x)})));j.displayName="Message";export{j as Message};
