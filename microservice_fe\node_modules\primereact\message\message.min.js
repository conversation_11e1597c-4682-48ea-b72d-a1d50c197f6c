this.primereact=this.primereact||{},this.primereact.message=function(e,t,n,r,i,c,o,s,a,l){"use strict";function u(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var p=u(t);function m(){return m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},m.apply(null,arguments)}function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function y(e,t){if("object"!=f(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=f(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function b(e){var t=y(e,"string");return"symbol"==f(t)?t:t+""}function g(e,t,n){return(t=b(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var v=r.ComponentBase.extend({defaultProps:{__TYPE:"Message",id:null,className:null,style:null,text:null,icon:null,severity:"info",content:null,children:void 0},css:{classes:{root:function(e){var t=e.props.severity;return l.classNames("p-inline-message p-component",g({},"p-inline-message-".concat(t),t))},icon:"p-inline-message-icon",text:"p-inline-message-text"},styles:"\n        @layer primereact {\n            .p-inline-message {\n                display: inline-flex;\n                align-items: center;\n                justify-content: center;\n                vertical-align: top;\n            }\n\n            .p-inline-message-icon {\n                flex-shrink: 0;\n            }\n            \n            .p-inline-message-icon-only .p-inline-message-text {\n                visibility: hidden;\n                width: 0;\n            }\n            \n            .p-fluid .p-inline-message {\n                display: flex;\n            }        \n        }\n        "}});function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function O(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){g(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var j=p.memo(p.forwardRef((function(e,t){var u=i.useMergeProps(),f=p.useContext(n.PrimeReactContext),y=v.getProps(e,f),b=p.useRef(null),g=v.setMetaData({props:y}),d=g.ptm,j=g.cx;r.useHandleStyle(v.css.styles,g.isUnstyled,{name:"message"});p.useImperativeHandle(t,(function(){return{props:y,getElement:function(){return b.current}}}));var P=function(){if(y.content)return l.ObjectUtils.getJSXElement(y.content,y);var e=l.ObjectUtils.getJSXElement(y.text,y),t=u({className:j("icon")},d("icon")),n=y.icon;if(!n)switch(y.severity){case"info":n=p.createElement(s.InfoCircleIcon,t);break;case"warn":n=p.createElement(o.ExclamationTriangleIcon,t);break;case"error":n=p.createElement(a.TimesCircleIcon,t);break;case"success":n=p.createElement(c.CheckIcon,t)}var r=l.IconUtils.getJSXIcon(n,O({},t),{props:y}),i=u({className:j("text")},d("text"));return p.createElement(p.Fragment,null,r,p.createElement("span",i,e))}(),h=u({className:l.classNames(y.className,j("root")),style:y.style,role:"alert","aria-live":"polite","aria-atomic":"true"},v.getOtherProps(y),d("root"));return p.createElement("div",m({id:y.id,ref:b},h),P)})));return j.displayName="Message",e.Message=j,Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,primereact.api,primereact.componentbase,primereact.hooks,primereact.icons.check,primereact.icons.exclamationtriangle,primereact.icons.infocircle,primereact.icons.timescircle,primereact.utils);
