"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("react-transition-group"),r=require("primereact/api"),n=require("primereact/componentbase"),a=require("primereact/csstransition"),o=require("primereact/hooks"),s=require("primereact/utils"),i=require("primereact/icons/check"),c=require("primereact/icons/exclamationtriangle"),l=require("primereact/icons/infocircle"),u=require("primereact/icons/times"),m=require("primereact/icons/timescircle"),p=require("primereact/ripple");function f(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var y=f(e);function g(){return g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},g.apply(null,arguments)}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function d(e){if(Array.isArray(e))return b(e)}function v(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function h(e,t){if(e){if("string"==typeof e)return b(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(e,t):void 0}}function O(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function j(e){return d(e)||v(e)||h(e)||O()}function N(e){return N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},N(e)}function w(e,t){if("object"!=N(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=N(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function P(e){var t=w(e,"string");return"symbol"==N(t)?t:t+""}function E(e,t,r){return(t=P(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function S(e){if(Array.isArray(e))return e}function x(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,s,i=[],c=!0,l=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(i.push(n.value),i.length!==t);c=!0);}catch(e){l=!0,a=e}finally{try{if(!c&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(l)throw a}}return i}}function I(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function k(e,t){return S(e)||x(e,t)||h(e,t)||I()}var C=n.ComponentBase.extend({defaultProps:{__TYPE:"Messages",__parentMetadata:null,id:null,className:null,style:null,transitionOptions:null,onRemove:null,onClick:null,children:void 0},css:{classes:{uimessage:{root:function(e){var t=e.severity;return s.classNames("p-message p-component",E({},"p-message-".concat(t),t))},wrapper:"p-message-wrapper",detail:"p-message-detail",summary:"p-message-summary",icon:"p-message-icon",buttonicon:"p-message-close-icon",button:"p-message-close p-link",transition:"p-message"}},styles:"\n@layer primereact {\n    .p-message-wrapper {\n        display: flex;\n        align-items: center;\n    }\n\n    .p-message-icon {\n        flex-shrink: 0;\n    }\n    \n    .p-message-close {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n    }\n    \n    .p-message-close.p-link {\n        margin-left: auto;\n        overflow: hidden;\n        position: relative;\n    }\n    \n    .p-message-enter {\n        opacity: 0;\n    }\n    \n    .p-message-enter-active {\n        opacity: 1;\n        transition: opacity .3s;\n    }\n    \n    .p-message-exit {\n        opacity: 1;\n        max-height: 1000px;\n    }\n    \n    .p-message-exit-active {\n        opacity: 0;\n        max-height: 0;\n        margin: 0;\n        overflow: hidden;\n        transition: max-height .3s cubic-bezier(0, 1, 0, 1), opacity .3s, margin .3s;\n    }\n    \n    .p-message-exit-active .p-message-close {\n        display: none;\n    }\n}\n"}});function _(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function q(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_(Object(r),!0).forEach((function(t){E(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var D=y.memo(y.forwardRef((function(e,t){var n=o.useMergeProps(),a=e.ptCallbacks,f=a.ptm,g=a.ptmo,b=a.cx,d=e.message.message,v=d.severity,h=d.content,O=d.summary,j=d.detail,N=d.closable,w=d.life,P=d.sticky,E=d.className,S=d.style,x=d.contentClassName,I=d.contentStyle,C=d.icon,_=d.closeIcon,D=d.pt,M={index:e.index},A=q(q({},e.metaData),M),R=k(o.useTimeout((function(){U(null)}),w||3e3,!P),1)[0],T=function(t,r){return f(t,q({hostName:e.hostName},r))},U=function(t){R(),e.onClose&&e.onClose(e.message),t&&(t.preventDefault(),t.stopPropagation())},z=function(){if(!1!==N){var t=n({className:b("uimessage.buttonicon")},T("buttonicon",A),g(D,"buttonicon",q(q({},M),{},{hostName:e.hostName}))),a=s.IconUtils.getJSXIcon(_||y.createElement(u.TimesIcon,t),q({},t),{props:e}),o=n({type:"button",className:b("uimessage.button"),"aria-label":r.ariaLabel("close"),onClick:U},T("button",A),g(D,"button",q(q({},M),{},{hostName:e.hostName})));return y.createElement("button",o,a,y.createElement(p.Ripple,null))}return null}(),H=function(){if(e.message){var t=n({className:b("uimessage.icon")},T("icon",A),g(D,"icon",q(q({},M),{},{hostName:e.hostName}))),r=C;if(!C)switch(v){case"info":r=y.createElement(l.InfoCircleIcon,t);break;case"warn":r=y.createElement(c.ExclamationTriangleIcon,t);break;case"error":r=y.createElement(m.TimesCircleIcon,t);break;case"success":r=y.createElement(i.CheckIcon,t)}var a=s.IconUtils.getJSXIcon(r,q({},t),{props:e}),o=n({className:b("uimessage.summary")},T("summary",A),g(D,"summary",q(q({},M),{},{hostName:e.hostName}))),u=n({className:b("uimessage.detail")},T("detail",A),g(D,"detail",q(q({},M),{},{hostName:e.hostName})));return h||y.createElement(y.Fragment,null,a,y.createElement("span",o,O),y.createElement("span",u,j))}return null}(),J=n({className:s.classNames(x,b("uimessage.wrapper")),style:I},T("wrapper",A),g(D,"wrapper",q(q({},M),{},{hostName:e.hostName}))),X=n({ref:t,className:s.classNames(E,b("uimessage.root",{severity:v})),style:S,role:"alert","aria-live":"assertive","aria-atomic":"true",onClick:function(){e.onClick&&e.onClick(e.message)}},T("root",A),g(D,"root",q(q({},M),{},{hostName:e.hostName})));return y.createElement("div",X,y.createElement("div",J,H,z))})));function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach((function(t){E(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}D.displayName="UIMessage";var R=0,T=y.memo(y.forwardRef((function(e,i){var c=o.useMergeProps(),l=y.useContext(r.PrimeReactContext),u=C.getProps(e,l),m=k(y.useState([]),2),p=m[0],f=m[1],b=y.useRef(null),d=A(A({props:u},u.__parentMetadata),{},{state:{messages:p}}),v=C.setMetaData(d);n.useHandleStyle(C.css.styles,v.isUnstyled,{name:"messages"});var h=function(e){e&&f((function(t){return O(t,e,!0)}))},O=function(e,t,r){var n;if(Array.isArray(t)){var a=t.reduce((function(e,t){return e.push({_pId:R++,message:t}),e}),[]);n=r&&e?[].concat(j(e),j(a)):a}else{var o={_pId:R++,message:t};n=r&&e?[].concat(j(e),[o]):[o]}return n},N=function(){f([])},w=function(e){f((function(t){return O(t,e,!1)}))},P=function(e){var t=s.ObjectUtils.isNotEmpty(e._pId)?e._pId:e.message||e;f((function(r){return r.filter((function(r){return r._pId!==e._pId&&!s.ObjectUtils.deepEquals(r.message,t)}))})),u.onRemove&&u.onRemove(e.message||t)},E=function(e){P(e)};y.useImperativeHandle(i,(function(){return{props:u,show:h,replace:w,remove:P,clear:N,getElement:function(){return b.current}}}));var S=c({id:u.id,className:u.className,style:u.style},C.getOtherProps(u),v.ptm("root")),x=c({classNames:v.cx("uimessage.transition"),unmountOnExit:!0,timeout:{enter:300,exit:300},options:u.transitionOptions},v.ptm("transition"));return y.createElement("div",g({ref:b},S),y.createElement(t.TransitionGroup,null,p&&p.map((function(e,t){var r=y.createRef();return y.createElement(a.CSSTransition,g({nodeRef:r,key:e._pId},x),y.createElement(D,{hostName:"Messages",ref:r,message:e,onClick:u.onClick,onClose:E,ptCallbacks:v,metaData:d,index:t}))}))))})));T.displayName="Messages",exports.Messages=T;
