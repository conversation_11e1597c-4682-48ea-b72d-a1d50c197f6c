import*as e from"react";import{TransitionGroup as t}from"react-transition-group";import{ariaLabel as r,PrimeReactContext as n}from"primereact/api";import{ComponentBase as o,useHandleStyle as a}from"primereact/componentbase";import{CSSTransition as s}from"primereact/csstransition";import{useMergeProps as i,useTimeout as c}from"primereact/hooks";import{classNames as l,IconUtils as m,ObjectUtils as u}from"primereact/utils";import{CheckIcon as p}from"primereact/icons/check";import{ExclamationTriangleIcon as f}from"primereact/icons/exclamationtriangle";import{InfoCircleIcon as y}from"primereact/icons/infocircle";import{TimesIcon as g}from"primereact/icons/times";import{TimesCircleIcon as b}from"primereact/icons/timescircle";import{Ripple as d}from"primereact/ripple";function v(){return v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},v.apply(null,arguments)}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function O(e){if(Array.isArray(e))return h(e)}function w(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function N(e,t){if(e){if("string"==typeof e)return h(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(e,t):void 0}}function j(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function E(e){return O(e)||w(e)||N(e)||j()}function P(e){return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},P(e)}function S(e,t){if("object"!=P(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=P(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function x(e){var t=S(e,"string");return"symbol"==P(t)?t:t+""}function k(e,t,r){return(t=x(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function I(e){if(Array.isArray(e))return e}function C(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,s,i=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(i.push(n.value),i.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(l)throw o}}return i}}function _(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function D(e,t){return I(e)||C(e,t)||N(e,t)||_()}var A=o.extend({defaultProps:{__TYPE:"Messages",__parentMetadata:null,id:null,className:null,style:null,transitionOptions:null,onRemove:null,onClick:null,children:void 0},css:{classes:{uimessage:{root:function(e){var t=e.severity;return l("p-message p-component",k({},"p-message-".concat(t),t))},wrapper:"p-message-wrapper",detail:"p-message-detail",summary:"p-message-summary",icon:"p-message-icon",buttonicon:"p-message-close-icon",button:"p-message-close p-link",transition:"p-message"}},styles:"\n@layer primereact {\n    .p-message-wrapper {\n        display: flex;\n        align-items: center;\n    }\n\n    .p-message-icon {\n        flex-shrink: 0;\n    }\n    \n    .p-message-close {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n    }\n    \n    .p-message-close.p-link {\n        margin-left: auto;\n        overflow: hidden;\n        position: relative;\n    }\n    \n    .p-message-enter {\n        opacity: 0;\n    }\n    \n    .p-message-enter-active {\n        opacity: 1;\n        transition: opacity .3s;\n    }\n    \n    .p-message-exit {\n        opacity: 1;\n        max-height: 1000px;\n    }\n    \n    .p-message-exit-active {\n        opacity: 0;\n        max-height: 0;\n        margin: 0;\n        overflow: hidden;\n        transition: max-height .3s cubic-bezier(0, 1, 0, 1), opacity .3s, margin .3s;\n    }\n    \n    .p-message-exit-active .p-message-close {\n        display: none;\n    }\n}\n"}});function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach((function(t){k(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var T=e.memo(e.forwardRef((function(t,n){var o=i(),a=t.ptCallbacks,s=a.ptm,u=a.ptmo,v=a.cx,h=t.message.message,O=h.severity,w=h.content,N=h.summary,j=h.detail,E=h.closable,P=h.life,S=h.sticky,x=h.className,k=h.style,I=h.contentClassName,C=h.contentStyle,_=h.icon,A=h.closeIcon,M=h.pt,T={index:t.index},U=R(R({},t.metaData),T),J=D(c((function(){q(null)}),P||3e3,!S),1)[0],X=function(e,r){return s(e,R({hostName:t.hostName},r))},q=function(e){J(),t.onClose&&t.onClose(t.message),e&&(e.preventDefault(),e.stopPropagation())},z=function(){if(!1!==E){var n=o({className:v("uimessage.buttonicon")},X("buttonicon",U),u(M,"buttonicon",R(R({},T),{},{hostName:t.hostName}))),a=m.getJSXIcon(A||e.createElement(g,n),R({},n),{props:t}),s=o({type:"button",className:v("uimessage.button"),"aria-label":r("close"),onClick:q},X("button",U),u(M,"button",R(R({},T),{},{hostName:t.hostName})));return e.createElement("button",s,a,e.createElement(d,null))}return null}(),F=function(){if(t.message){var r=o({className:v("uimessage.icon")},X("icon",U),u(M,"icon",R(R({},T),{},{hostName:t.hostName}))),n=_;if(!_)switch(O){case"info":n=e.createElement(y,r);break;case"warn":n=e.createElement(f,r);break;case"error":n=e.createElement(b,r);break;case"success":n=e.createElement(p,r)}var a=m.getJSXIcon(n,R({},r),{props:t}),s=o({className:v("uimessage.summary")},X("summary",U),u(M,"summary",R(R({},T),{},{hostName:t.hostName}))),i=o({className:v("uimessage.detail")},X("detail",U),u(M,"detail",R(R({},T),{},{hostName:t.hostName})));return w||e.createElement(e.Fragment,null,a,e.createElement("span",s,N),e.createElement("span",i,j))}return null}(),H=o({className:l(I,v("uimessage.wrapper")),style:C},X("wrapper",U),u(M,"wrapper",R(R({},T),{},{hostName:t.hostName}))),Y=o({ref:n,className:l(x,v("uimessage.root",{severity:O})),style:k,role:"alert","aria-live":"assertive","aria-atomic":"true",onClick:function(){t.onClick&&t.onClick(t.message)}},X("root",U),u(M,"root",R(R({},T),{},{hostName:t.hostName})));return e.createElement("div",Y,e.createElement("div",H,F,z))})));function U(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function J(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?U(Object(r),!0).forEach((function(t){k(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}T.displayName="UIMessage";var X=0,q=e.memo(e.forwardRef((function(r,o){var c=i(),l=e.useContext(n),m=A.getProps(r,l),p=D(e.useState([]),2),f=p[0],y=p[1],g=e.useRef(null),b=J(J({props:m},m.__parentMetadata),{},{state:{messages:f}}),d=A.setMetaData(b);a(A.css.styles,d.isUnstyled,{name:"messages"});var h=function(e){e&&y((function(t){return O(t,e,!0)}))},O=function(e,t,r){var n;if(Array.isArray(t)){var o=t.reduce((function(e,t){return e.push({_pId:X++,message:t}),e}),[]);n=r&&e?[].concat(E(e),E(o)):o}else{var a={_pId:X++,message:t};n=r&&e?[].concat(E(e),[a]):[a]}return n},w=function(){y([])},N=function(e){y((function(t){return O(t,e,!1)}))},j=function(e){var t=u.isNotEmpty(e._pId)?e._pId:e.message||e;y((function(r){return r.filter((function(r){return r._pId!==e._pId&&!u.deepEquals(r.message,t)}))})),m.onRemove&&m.onRemove(e.message||t)},P=function(e){j(e)};e.useImperativeHandle(o,(function(){return{props:m,show:h,replace:N,remove:j,clear:w,getElement:function(){return g.current}}}));var S=c({id:m.id,className:m.className,style:m.style},A.getOtherProps(m),d.ptm("root")),x=c({classNames:d.cx("uimessage.transition"),unmountOnExit:!0,timeout:{enter:300,exit:300},options:m.transitionOptions},d.ptm("transition"));return e.createElement("div",v({ref:g},S),e.createElement(t,null,f&&f.map((function(t,r){var n=e.createRef();return e.createElement(s,v({nodeRef:n,key:t._pId},x),e.createElement(T,{hostName:"Messages",ref:n,message:t,onClick:m.onClick,onClose:P,ptCallbacks:d,metaData:b,index:r}))}))))})));q.displayName="Messages";export{q as Messages};
