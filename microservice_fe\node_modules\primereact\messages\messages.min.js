this.primereact=this.primereact||{},this.primereact.messages=function(e,t,n,r,a,o,s,i,c,l,u,m,p,f){"use strict";function y(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var g=y(t);function b(){return b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},b.apply(null,arguments)}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function v(e){if(Array.isArray(e))return d(e)}function h(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function O(e,t){if(e){if("string"==typeof e)return d(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(e,t):void 0}}function j(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function N(e){return v(e)||h(e)||O(e)||j()}function w(e){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},w(e)}function P(e,t){if("object"!=w(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=w(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function E(e){var t=P(e,"string");return"symbol"==w(t)?t:t+""}function S(e,t,n){return(t=E(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function I(e){if(Array.isArray(e))return e}function k(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,s,i=[],c=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);c=!0);}catch(e){l=!0,a=e}finally{try{if(!c&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(l)throw a}}return i}}function x(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function C(e,t){return I(e)||k(e,t)||O(e,t)||x()}var _=a.ComponentBase.extend({defaultProps:{__TYPE:"Messages",__parentMetadata:null,id:null,className:null,style:null,transitionOptions:null,onRemove:null,onClick:null,children:void 0},css:{classes:{uimessage:{root:function(e){var t=e.severity;return i.classNames("p-message p-component",S({},"p-message-".concat(t),t))},wrapper:"p-message-wrapper",detail:"p-message-detail",summary:"p-message-summary",icon:"p-message-icon",buttonicon:"p-message-close-icon",button:"p-message-close p-link",transition:"p-message"}},styles:"\n@layer primereact {\n    .p-message-wrapper {\n        display: flex;\n        align-items: center;\n    }\n\n    .p-message-icon {\n        flex-shrink: 0;\n    }\n    \n    .p-message-close {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n    }\n    \n    .p-message-close.p-link {\n        margin-left: auto;\n        overflow: hidden;\n        position: relative;\n    }\n    \n    .p-message-enter {\n        opacity: 0;\n    }\n    \n    .p-message-enter-active {\n        opacity: 1;\n        transition: opacity .3s;\n    }\n    \n    .p-message-exit {\n        opacity: 1;\n        max-height: 1000px;\n    }\n    \n    .p-message-exit-active {\n        opacity: 0;\n        max-height: 0;\n        margin: 0;\n        overflow: hidden;\n        transition: max-height .3s cubic-bezier(0, 1, 0, 1), opacity .3s, margin .3s;\n    }\n    \n    .p-message-exit-active .p-message-close {\n        display: none;\n    }\n}\n"}});function D(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function M(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?D(Object(n),!0).forEach((function(t){S(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):D(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var R=g.memo(g.forwardRef((function(e,t){var n=s.useMergeProps(),a=e.ptCallbacks,o=a.ptm,y=a.ptmo,b=a.cx,d=e.message.message,v=d.severity,h=d.content,O=d.summary,j=d.detail,N=d.closable,w=d.life,P=d.sticky,E=d.className,S=d.style,I=d.contentClassName,k=d.contentStyle,x=d.icon,_=d.closeIcon,D=d.pt,R={index:e.index},A=M(M({},e.metaData),R),T=C(s.useTimeout((function(){z(null)}),w||3e3,!P),1)[0],U=function(t,n){return o(t,M({hostName:e.hostName},n))},z=function(t){T(),e.onClose&&e.onClose(e.message),t&&(t.preventDefault(),t.stopPropagation())},G=function(){if(!1!==N){var t=n({className:b("uimessage.buttonicon")},U("buttonicon",A),y(D,"buttonicon",M(M({},R),{},{hostName:e.hostName}))),a=i.IconUtils.getJSXIcon(_||g.createElement(m.TimesIcon,t),M({},t),{props:e}),o=n({type:"button",className:b("uimessage.button"),"aria-label":r.ariaLabel("close"),onClick:z},U("button",A),y(D,"button",M(M({},R),{},{hostName:e.hostName})));return g.createElement("button",o,a,g.createElement(f.Ripple,null))}return null}(),H=function(){if(e.message){var t=n({className:b("uimessage.icon")},U("icon",A),y(D,"icon",M(M({},R),{},{hostName:e.hostName}))),r=x;if(!x)switch(v){case"info":r=g.createElement(u.InfoCircleIcon,t);break;case"warn":r=g.createElement(l.ExclamationTriangleIcon,t);break;case"error":r=g.createElement(p.TimesCircleIcon,t);break;case"success":r=g.createElement(c.CheckIcon,t)}var a=i.IconUtils.getJSXIcon(r,M({},t),{props:e}),o=n({className:b("uimessage.summary")},U("summary",A),y(D,"summary",M(M({},R),{},{hostName:e.hostName}))),s=n({className:b("uimessage.detail")},U("detail",A),y(D,"detail",M(M({},R),{},{hostName:e.hostName})));return h||g.createElement(g.Fragment,null,a,g.createElement("span",o,O),g.createElement("span",s,j))}return null}(),J=n({className:i.classNames(I,b("uimessage.wrapper")),style:k},U("wrapper",A),y(D,"wrapper",M(M({},R),{},{hostName:e.hostName}))),X=n({ref:t,className:i.classNames(E,b("uimessage.root",{severity:v})),style:S,role:"alert","aria-live":"assertive","aria-atomic":"true",onClick:function(){e.onClick&&e.onClick(e.message)}},U("root",A),y(D,"root",M(M({},R),{},{hostName:e.hostName})));return g.createElement("div",X,g.createElement("div",J,H,G))})));function A(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function T(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?A(Object(n),!0).forEach((function(t){S(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):A(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}R.displayName="UIMessage";var U=0,z=g.memo(g.forwardRef((function(e,t){var c=s.useMergeProps(),l=g.useContext(r.PrimeReactContext),u=_.getProps(e,l),m=C(g.useState([]),2),p=m[0],f=m[1],y=g.useRef(null),d=T(T({props:u},u.__parentMetadata),{},{state:{messages:p}}),v=_.setMetaData(d);a.useHandleStyle(_.css.styles,v.isUnstyled,{name:"messages"});var h=function(e){e&&f((function(t){return O(t,e,!0)}))},O=function(e,t,n){var r;if(Array.isArray(t)){var a=t.reduce((function(e,t){return e.push({_pId:U++,message:t}),e}),[]);r=n&&e?[].concat(N(e),N(a)):a}else{var o={_pId:U++,message:t};r=n&&e?[].concat(N(e),[o]):[o]}return r},j=function(){f([])},w=function(e){f((function(t){return O(t,e,!1)}))},P=function(e){var t=i.ObjectUtils.isNotEmpty(e._pId)?e._pId:e.message||e;f((function(n){return n.filter((function(n){return n._pId!==e._pId&&!i.ObjectUtils.deepEquals(n.message,t)}))})),u.onRemove&&u.onRemove(e.message||t)},E=function(e){P(e)};g.useImperativeHandle(t,(function(){return{props:u,show:h,replace:w,remove:P,clear:j,getElement:function(){return y.current}}}));var S=c({id:u.id,className:u.className,style:u.style},_.getOtherProps(u),v.ptm("root")),I=c({classNames:v.cx("uimessage.transition"),unmountOnExit:!0,timeout:{enter:300,exit:300},options:u.transitionOptions},v.ptm("transition"));return g.createElement("div",b({ref:y},S),g.createElement(n.TransitionGroup,null,p&&p.map((function(e,t){var n=g.createRef();return g.createElement(o.CSSTransition,b({nodeRef:n,key:e._pId},I),g.createElement(R,{hostName:"Messages",ref:n,message:e,onClick:u.onClick,onClose:E,ptCallbacks:v,metaData:d,index:t}))}))))})));return z.displayName="Messages",e.Messages=z,Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,ReactTransitionGroup,primereact.api,primereact.componentbase,primereact.csstransition,primereact.hooks,primereact.utils,primereact.icons.check,primereact.icons.exclamationtriangle,primereact.icons.infocircle,primereact.icons.times,primereact.icons.timescircle,primereact.ripple);
