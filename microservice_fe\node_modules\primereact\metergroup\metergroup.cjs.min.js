"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("primereact/api"),r=require("primereact/componentbase"),n=require("primereact/hooks"),l=require("primereact/utils");function a(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var o=a(e);function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},i.apply(null,arguments)}function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function c(e,t){if("object"!=s(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function p(e){var t=c(e,"string");return"symbol"==s(t)?t:t+""}function u(e,t,r){return(t=p(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var m=r.ComponentBase.extend({defaultProps:{__TYPE:"MeterGroup",__parentMetadata:null,children:void 0,className:null,values:null,min:0,max:100,orientation:"horizontal",labelPosition:"end",labelOrientation:"horizontal",start:null,end:null,meter:null,labelList:null},css:{classes:{root:function(e){var t=e.props;return[l.classNames("p-metergroup p-component",{"p-metergroup-horizontal":"horizontal"===t.orientation,"p-metergroup-vertical":"vertical"===t.orientation})]},metercontainer:"p-metergroup-meter-container",meter:"p-metergroup-meter",labellist:function(e){var t=e.props;return l.classNames("p-metergroup-label-list",{"p-metergroup-label-list-start":"start"===t.labelPosition,"p-metergroup-label-list-end":"end"===t.labelPosition,"p-metergroup-label-list-vertical":"vertical"===t.labelOrientation,"p-metergroup-label-list-horizontal":"horizontal"===t.labelOrientation})},labellistitem:"p-metergroup-label-list-item",labelicon:"p-metergroup-label-icon",labellisttype:"p-metergroup-label-type",label:"p-metergroup-label"},styles:"\n@layer primereact {\n    .p-metergroup {\n        position: relative;\n        overflow: hidden;\n    }\n\n    .p-metergroup-vertical.p-metergroup {\n        display: flex;\n    }\n\n    .p-metergroup-vertical .p-metergroup-meter-container {\n        flex-direction: column;\n    }\n\n    .p-metergroup-meter-container {\n        display: flex;\n    }\n\n    .p-metergroup-label-list {\n        display: flex;\n        margin: 0;\n        padding: 0;\n        list-style-type: none;\n    }\n\n    .p-metergroup-vertical .p-metergroup-label-list {\n        align-items: start;\n    }\n\n    .p-metergroup-label-list-vertical {\n        flex-direction: column;\n    }\n\n    .p-metergroup-label-list-horizontal {\n        flex-direction: row;\n    }\n\n    .p-metergroup-label-list-item {\n        display: inline-flex;\n        align-items: center;\n    }\n\n    .p-metergroup-label-type {\n        display: inline-block;\n    }\n}\n"}});function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach((function(t){u(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}exports.MeterGroup=function(e){var a=o.useContext(t.PrimeReactContext),s=m.getProps(e,a),c=s.values,p=s.min,u=s.max,b=s.orientation,g=s.labelPosition,v=s.start,y=s.end,d=s.meter,O=s.labelList,h=n.useMergeProps(),P=m.setMetaData(f(f({props:s},s.__parentMetadata),{},{context:{disabled:s.disabled}})),j=P.ptm,x=P.cx;r.useHandleStyle(m.css.styles,P.isUnstyled,{name:"metergroup"});var E=0,N=[];c.map((function(e){E+=e.value,N.push(Math.round(e.value/E*100))}));var w,S,M,_,z,k=function(){return Math.round(Math.max(0,Math.min(100,((arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)-p)/(u-p)*100)))},D=h({className:l.classNames(s.className,x("root",{orientation:b}))},m.getOtherProps(s),j("root")),q={totalPercent:E,percentages:N,values:c},C=O||(w=h({className:x("labellist")},j("labellist")),S=h({className:x("labellistitem")},j("labellistitem")),M=h({className:x("label")},j("label")),o.createElement("ol",w,c.map((function(e,t){var r=h({className:l.classNames(x("labelicon"),e.icon),style:{color:e.color}},j("labelicon")),n=h({className:x("labellisttype"),style:{backgroundColor:e.color}},j("labellisttype")),a=e.icon?o.createElement("i",r):o.createElement("span",n),s=k(e.value);return o.createElement("li",i({key:t},S),a,o.createElement("span",M,null==e?void 0:e.label," ","(".concat(s,"%)")))})))),U=l.ObjectUtils.getJSXElement(C,{values:c,totalPercent:E});return o.createElement("div",i({},D,{role:"meter","aria-valuemin":p,"aria-valuemax":u,"aria-valuenow":E}),"start"===g&&U,v&&l.ObjectUtils.getJSXElement(v,q),(_=c.map((function(e,t){var r=k(e.value),n={backgroundColor:e.color,width:"horizontal"===b?r+"%":"auto",height:"vertical"===b?r+"%":"auto"},a=h({className:x("meter"),style:n},j("meter"));if(d||e.meterTemplate){var s=h({className:x("meter")},j("meter"));return l.ObjectUtils.getJSXElement(e.meterTemplate||d,f(f({},e),{},{percentage:r,index:t}),s)}return o.createElement("span",i({key:t},a))})),z=h({className:x("metercontainer")},j("metercontainer")),o.createElement("div",z,_)),y&&l.ObjectUtils.getJSXElement(y,q),"end"===g&&U)};
