import*as e from"react";import{PrimeReactContext as t}from"primereact/api";import{ComponentBase as r,useHandleStyle as n}from"primereact/componentbase";import{useMergeProps as l}from"primereact/hooks";import{classNames as a,ObjectUtils as o}from"primereact/utils";function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},i.apply(null,arguments)}function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function m(e,t){if("object"!=p(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=p(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function s(e){var t=m(e,"string");return"symbol"==p(t)?t:t+""}function c(e,t,r){return(t=s(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var u=r.extend({defaultProps:{__TYPE:"MeterGroup",__parentMetadata:null,children:void 0,className:null,values:null,min:0,max:100,orientation:"horizontal",labelPosition:"end",labelOrientation:"horizontal",start:null,end:null,meter:null,labelList:null},css:{classes:{root:function(e){var t=e.props;return[a("p-metergroup p-component",{"p-metergroup-horizontal":"horizontal"===t.orientation,"p-metergroup-vertical":"vertical"===t.orientation})]},metercontainer:"p-metergroup-meter-container",meter:"p-metergroup-meter",labellist:function(e){var t=e.props;return a("p-metergroup-label-list",{"p-metergroup-label-list-start":"start"===t.labelPosition,"p-metergroup-label-list-end":"end"===t.labelPosition,"p-metergroup-label-list-vertical":"vertical"===t.labelOrientation,"p-metergroup-label-list-horizontal":"horizontal"===t.labelOrientation})},labellistitem:"p-metergroup-label-list-item",labelicon:"p-metergroup-label-icon",labellisttype:"p-metergroup-label-type",label:"p-metergroup-label"},styles:"\n@layer primereact {\n    .p-metergroup {\n        position: relative;\n        overflow: hidden;\n    }\n\n    .p-metergroup-vertical.p-metergroup {\n        display: flex;\n    }\n\n    .p-metergroup-vertical .p-metergroup-meter-container {\n        flex-direction: column;\n    }\n\n    .p-metergroup-meter-container {\n        display: flex;\n    }\n\n    .p-metergroup-label-list {\n        display: flex;\n        margin: 0;\n        padding: 0;\n        list-style-type: none;\n    }\n\n    .p-metergroup-vertical .p-metergroup-label-list {\n        align-items: start;\n    }\n\n    .p-metergroup-label-list-vertical {\n        flex-direction: column;\n    }\n\n    .p-metergroup-label-list-horizontal {\n        flex-direction: row;\n    }\n\n    .p-metergroup-label-list-item {\n        display: inline-flex;\n        align-items: center;\n    }\n\n    .p-metergroup-label-type {\n        display: inline-block;\n    }\n}\n"}});function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach((function(t){c(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var f=function(r){var p=e.useContext(t),m=u.getProps(r,p),s=m.values,c=m.min,b=m.max,f=m.orientation,v=m.labelPosition,y=m.start,d=m.end,h=m.meter,O=m.labelList,P=l(),x=u.setMetaData(g(g({props:m},m.__parentMetadata),{},{context:{disabled:m.disabled}})),j=x.ptm,E=x.cx;n(u.css.styles,x.isUnstyled,{name:"metergroup"});var w=0,S=[];s.map((function(e){w+=e.value,S.push(Math.round(e.value/w*100))}));var N,z,M,k,_,D=function(){return Math.round(Math.max(0,Math.min(100,((arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)-c)/(b-c)*100)))},J=P({className:a(m.className,E("root",{orientation:f}))},u.getOtherProps(m),j("root")),T={totalPercent:w,percentages:S,values:s},X=O||(N=P({className:E("labellist")},j("labellist")),z=P({className:E("labellistitem")},j("labellistitem")),M=P({className:E("label")},j("label")),e.createElement("ol",N,s.map((function(t,r){var n=P({className:a(E("labelicon"),t.icon),style:{color:t.color}},j("labelicon")),l=P({className:E("labellisttype"),style:{backgroundColor:t.color}},j("labellisttype")),o=t.icon?e.createElement("i",n):e.createElement("span",l),p=D(t.value);return e.createElement("li",i({key:r},z),o,e.createElement("span",M,null==t?void 0:t.label," ","(".concat(p,"%)")))})))),C=o.getJSXElement(X,{values:s,totalPercent:w});return e.createElement("div",i({},J,{role:"meter","aria-valuemin":c,"aria-valuemax":b,"aria-valuenow":w}),"start"===v&&C,y&&o.getJSXElement(y,T),(k=s.map((function(t,r){var n=D(t.value),l={backgroundColor:t.color,width:"horizontal"===f?n+"%":"auto",height:"vertical"===f?n+"%":"auto"},a=P({className:E("meter"),style:l},j("meter"));if(h||t.meterTemplate){var p=P({className:E("meter")},j("meter"));return o.getJSXElement(t.meterTemplate||h,g(g({},t),{},{percentage:n,index:r}),p)}return e.createElement("span",i({key:r},a))})),_=P({className:E("metercontainer")},j("metercontainer")),e.createElement("div",_,k)),d&&o.getJSXElement(d,T),"end"===v&&C)};export{f as MeterGroup};
