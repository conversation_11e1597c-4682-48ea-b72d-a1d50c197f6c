this.primereact=this.primereact||{},this.primereact.metergroup=function(e,t,r,n,l,a){"use strict";function o(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=o(t);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},s.apply(null,arguments)}function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function p(e,t){if("object"!=c(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function u(e){var t=p(e,"string");return"symbol"==c(t)?t:t+""}function m(e,t,r){return(t=u(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var b=n.ComponentBase.extend({defaultProps:{__TYPE:"MeterGroup",__parentMetadata:null,children:void 0,className:null,values:null,min:0,max:100,orientation:"horizontal",labelPosition:"end",labelOrientation:"horizontal",start:null,end:null,meter:null,labelList:null},css:{classes:{root:function(e){var t=e.props;return[a.classNames("p-metergroup p-component",{"p-metergroup-horizontal":"horizontal"===t.orientation,"p-metergroup-vertical":"vertical"===t.orientation})]},metercontainer:"p-metergroup-meter-container",meter:"p-metergroup-meter",labellist:function(e){var t=e.props;return a.classNames("p-metergroup-label-list",{"p-metergroup-label-list-start":"start"===t.labelPosition,"p-metergroup-label-list-end":"end"===t.labelPosition,"p-metergroup-label-list-vertical":"vertical"===t.labelOrientation,"p-metergroup-label-list-horizontal":"horizontal"===t.labelOrientation})},labellistitem:"p-metergroup-label-list-item",labelicon:"p-metergroup-label-icon",labellisttype:"p-metergroup-label-type",label:"p-metergroup-label"},styles:"\n@layer primereact {\n    .p-metergroup {\n        position: relative;\n        overflow: hidden;\n    }\n\n    .p-metergroup-vertical.p-metergroup {\n        display: flex;\n    }\n\n    .p-metergroup-vertical .p-metergroup-meter-container {\n        flex-direction: column;\n    }\n\n    .p-metergroup-meter-container {\n        display: flex;\n    }\n\n    .p-metergroup-label-list {\n        display: flex;\n        margin: 0;\n        padding: 0;\n        list-style-type: none;\n    }\n\n    .p-metergroup-vertical .p-metergroup-label-list {\n        align-items: start;\n    }\n\n    .p-metergroup-label-list-vertical {\n        flex-direction: column;\n    }\n\n    .p-metergroup-label-list-horizontal {\n        flex-direction: row;\n    }\n\n    .p-metergroup-label-list-item {\n        display: inline-flex;\n        align-items: center;\n    }\n\n    .p-metergroup-label-type {\n        display: inline-block;\n    }\n}\n"}});function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){m(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}return e.MeterGroup=function(e){var t=i.useContext(r.PrimeReactContext),o=b.getProps(e,t),c=o.values,p=o.min,u=o.max,m=o.orientation,f=o.labelPosition,v=o.start,y=o.end,d=o.meter,O=o.labelList,h=l.useMergeProps(),P=b.setMetaData(g(g({props:o},o.__parentMetadata),{},{context:{disabled:o.disabled}})),j=P.ptm,x=P.cx;n.useHandleStyle(b.css.styles,P.isUnstyled,{name:"metergroup"});var E=0,N=[];c.map((function(e){E+=e.value,N.push(Math.round(e.value/E*100))}));var w,S,M,_,z,k=function(){return Math.round(Math.max(0,Math.min(100,((arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)-p)/(u-p)*100)))},D=h({className:a.classNames(o.className,x("root",{orientation:m}))},b.getOtherProps(o),j("root")),C={totalPercent:E,percentages:N,values:c},U=O||(w=h({className:x("labellist")},j("labellist")),S=h({className:x("labellistitem")},j("labellistitem")),M=h({className:x("label")},j("label")),i.createElement("ol",w,c.map((function(e,t){var r=h({className:a.classNames(x("labelicon"),e.icon),style:{color:e.color}},j("labelicon")),n=h({className:x("labellisttype"),style:{backgroundColor:e.color}},j("labellisttype")),l=e.icon?i.createElement("i",r):i.createElement("span",n),o=k(e.value);return i.createElement("li",s({key:t},S),l,i.createElement("span",M,null==e?void 0:e.label," ","(".concat(o,"%)")))})))),J=a.ObjectUtils.getJSXElement(U,{values:c,totalPercent:E});return i.createElement("div",s({},D,{role:"meter","aria-valuemin":p,"aria-valuemax":u,"aria-valuenow":E}),"start"===f&&J,v&&a.ObjectUtils.getJSXElement(v,C),(_=c.map((function(e,t){var r=k(e.value),n={backgroundColor:e.color,width:"horizontal"===m?r+"%":"auto",height:"vertical"===m?r+"%":"auto"},l=h({className:x("meter"),style:n},j("meter"));if(d||e.meterTemplate){var o=h({className:x("meter")},j("meter"));return a.ObjectUtils.getJSXElement(e.meterTemplate||d,g(g({},e),{},{percentage:r,index:t}),o)}return i.createElement("span",s({key:t},l))})),z=h({className:x("metercontainer")},j("metercontainer")),i.createElement("div",z,_)),y&&a.ObjectUtils.getJSXElement(y,C),"end"===f&&J)},Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,primereact.api,primereact.componentbase,primereact.hooks,primereact.utils);
