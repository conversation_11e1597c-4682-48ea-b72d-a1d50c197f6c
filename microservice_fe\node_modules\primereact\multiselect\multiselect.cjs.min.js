"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("primereact/api"),n=require("primereact/componentbase"),l=require("primereact/hooks"),r=require("primereact/icons/chevrondown"),i=require("primereact/icons/spinner"),o=require("primereact/icons/times"),a=require("primereact/icons/timescircle"),c=require("primereact/overlayservice"),u=require("primereact/tooltip"),s=require("primereact/utils"),p=require("primereact/csstransition"),d=require("primereact/portal"),f=require("primereact/virtualscroller"),m=require("primereact/icons/check"),b=require("primereact/icons/search"),v=require("primereact/inputtext"),h=require("primereact/ripple");function y(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function g(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var l=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,l.get?l:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var O=g(e),x=y(t);function I(){return I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},I.apply(null,arguments)}function S(e){return S="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},S(e)}function E(e,t){if("object"!=S(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var l=n.call(e,t||"default");if("object"!=S(l))return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function k(e){var t=E(e,"string");return"symbol"==S(t)?t:t+""}function j(e,t,n){return(t=k(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function w(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,l=Array(t);n<t;n++)l[n]=e[n];return l}function C(e){if(Array.isArray(e))return w(e)}function D(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function N(e,t){if(e){if("string"==typeof e)return w(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?w(e,t):void 0}}function P(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function F(e){throw new TypeError('"'+e+'" is read-only')}function U(e){if(Array.isArray(e))return e}function L(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var l,r,i,o,a=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(l=i.call(n)).done)&&(a.push(l.value),a.length!==t);c=!0);}catch(e){u=!0,r=e}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw r}}return a}}function M(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function A(e,t){return U(e)||L(e,t)||N(e,t)||M()}var T=n.ComponentBase.extend({defaultProps:{__TYPE:"MultiSelect",appendTo:null,ariaLabelledBy:null,checkboxIcon:null,className:null,clearIcon:null,closeIcon:null,dataKey:null,disabled:!1,display:"comma",dropdownIcon:null,emptyFilterMessage:null,emptyMessage:null,filter:!1,filterBy:null,filterDelay:300,filterInputAutoFocus:!0,filterLocale:void 0,selectOnFocus:!1,focusOnHover:!0,autoOptionFocus:!1,filterMatchMode:"contains",filterPlaceholder:null,filterTemplate:null,fixedPlaceholder:!1,flex:!1,id:null,inline:!1,inputId:null,inputRef:null,invalid:!1,variant:null,itemCheckboxIcon:null,itemClassName:null,itemTemplate:null,loading:!1,loadingIcon:null,maxSelectedLabels:null,name:null,onBlur:null,onChange:null,onClick:null,onFilter:null,onFocus:null,onHide:null,onRemove:null,onSelectAll:null,onShow:null,optionDisabled:null,optionGroupChildren:null,optionGroupLabel:null,optionGroupTemplate:null,optionLabel:null,optionValue:null,options:null,overlayVisible:!1,panelClassName:null,panelFooterTemplate:null,panelHeaderTemplate:null,panelStyle:null,placeholder:null,removeIcon:null,resetFilterOnHide:!1,scrollHeight:"200px",selectAll:!1,selectAllLabel:null,selectedItemTemplate:null,selectedItemsLabel:void 0,selectionLimit:null,showClear:!1,showSelectAll:!0,style:null,tabIndex:0,tooltip:null,tooltipOptions:null,transitionOptions:null,useOptionAsValue:!1,value:null,virtualScrollerOptions:null,children:void 0},css:{classes:{root:function(e){var t,n=e.props,l=e.context,r=e.focusedState,i=e.overlayVisibleState;return s.classNames("p-multiselect p-component p-inputwrapper",{"p-multiselect-chip":"chip"===n.display&&(null==n.maxSelectedLabels||(null===(t=n.value)||void 0===t?void 0:t.length)<=n.maxSelectedLabels),"p-disabled":n.disabled,"p-invalid":n.invalid,"p-variant-filled":n.variant?"filled"===n.variant:l&&"filled"===l.inputStyle,"p-multiselect-clearable":n.showClear&&!n.disabled,"p-focus":r,"p-inputwrapper-filled":s.ObjectUtils.isNotEmpty(n.value),"p-inputwrapper-focus":r||i})},label:function(e){var t,n=e.props,l=e.empty;return s.classNames("p-multiselect-label",{"p-placeholder":l&&n.placeholder,"p-multiselect-label-empty":l&&!n.placeholder&&!n.selectedItemTemplate,"p-multiselect-items-label":!l&&"chip"!==n.display&&(null===(t=n.value)||void 0===t?void 0:t.length)>n.maxSelectedLabels})},panel:function(e){var t=e.panelProps,n=e.context;return s.classNames("p-multiselect-panel p-component",{"p-multiselect-inline":t.inline,"p-multiselect-flex":t.flex,"p-multiselect-limited":!e.allowOptionSelect,"p-input-filled":n&&"filled"===n.inputStyle||"filled"===x.default.inputStyle,"p-ripple-disabled":n&&!1===n.ripple||!1===x.default.ripple})},list:function(e){return"p-multiselect-items p-component"},labelContainer:"p-multiselect-label-container",triggerIcon:"p-multiselect-trigger-icon p-c",trigger:"p-multiselect-trigger",clearIcon:"p-multiselect-clear-icon",tokenLabel:"p-multiselect-token-label",token:"p-multiselect-token",removeTokenIcon:"p-multiselect-token-icon",wrapper:"p-multiselect-items-wrapper",emptyMessage:"p-multiselect-empty-message",itemGroup:"p-multiselect-item-group",closeButton:"p-multiselect-close p-link",header:"p-multiselect-header",closeIcon:"p-multiselect-close-icon",headerCheckboxContainer:"p-multiselect-select-all",headerCheckboxIcon:"p-multiselect-select-all p-checkbox-icon p-c",headerSelectAllLabel:"p-multiselect-select-all-label",filterContainer:"p-multiselect-filter-container",filterIcon:"p-multiselect-filter-icon",item:function(e){var t=e.itemProps;return s.classNames("p-multiselect-item",{"p-highlight":t.selected,"p-disabled":t.disabled,"p-focus":t.focusedOptionIndex===t.index})},checkboxContainer:"p-multiselect-checkbox",checkboxIcon:"p-checkbox-icon p-c",transition:"p-connected-overlay"},styles:"\n@layer primereact {\n    .p-multiselect {\n        display: inline-flex;\n        user-select: none;\n        cursor: pointer;\n    }\n    \n    .p-multiselect-trigger {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-shrink: 0;\n        cursor: pointer;\n    }\n    \n    .p-multiselect-label-container {\n        overflow: hidden;\n        flex: 1 1 auto;\n        cursor: pointer;\n    }\n    \n    .p-multiselect-label  {\n        display: block;\n        white-space: nowrap;\n        cursor: pointer;\n        overflow: hidden;\n        text-overflow: ellipsis;\n    }\n    \n    .p-multiselect-label-empty {\n        overflow: hidden;\n        visibility: hidden;\n    }\n    \n    .p-multiselect-token {\n        cursor: default;\n        display: inline-flex;\n        align-items: center;\n        flex: 0 0 auto;\n    }\n    \n    .p-multiselect-token-icon {\n        cursor: pointer;\n    }\n    \n    .p-multiselect .p-multiselect-panel {\n        min-width: 100%;\n    }\n    \n    .p-multiselect-inline.p-multiselect-panel {\n        border: none;\n        position: initial;\n        background: none;\n        box-shadow: none;\n    }\n    \n    .p-multiselect-inline.p-multiselect-panel .p-multiselect-items {\n        padding: 0;\n    }\n    \n    .p-multiselect-flex.p-multiselect-panel .p-multiselect-items {\n        display: flex;\n        flex-wrap: wrap;\n    }\n    \n    .p-multiselect-items-wrapper {\n        overflow: auto;\n    }\n    \n    .p-multiselect-items {\n        margin: 0;\n        padding: 0;\n        list-style-type: none;\n    }\n    \n    .p-multiselect-item {\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        font-weight: normal;\n        white-space: nowrap;\n        position: relative;\n        overflow: hidden;\n        outline: none;\n    }\n    \n    .p-multiselect-header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n    }\n    \n    .p-multiselect-select-all-label {\n        margin-left: 0.5rem;\n    }\n    \n    .p-multiselect-filter-container {\n        position: relative;\n        flex: 1 1 auto;\n    }\n    \n    .p-multiselect-filter-icon {\n        position: absolute;\n        top: 50%;\n        margin-top: -.5rem;\n    }\n    \n    .p-multiselect-filter-container .p-inputtext {\n        width: 100%;\n    }\n    \n    .p-multiselect-close {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-shrink: 0;\n        overflow: hidden;\n        position: relative;\n        margin-left: auto;\n    }\n    \n    .p-multiselect-clear-icon {\n        position: absolute;\n        top: 50%;\n        margin-top: -.5rem;\n        right: 3rem;\n    }\n    \n    .p-fluid .p-multiselect {\n        display: flex;\n    }\n}\n",inlineStyles:{root:function(e){var t=e.props;return t.showClear&&!t.disabled&&{position:"relative"}},itemGroup:function(e){var t=e.scrollerOptions;return{height:t.props?t.props.itemSize:void 0}}}}}),R=n.ComponentBase.extend({defaultProps:{__TYPE:"Checkbox",autoFocus:!1,checked:!1,className:null,disabled:!1,falseValue:!1,icon:null,id:null,inputId:null,inputRef:null,invalid:!1,variant:null,name:null,onChange:null,onContextMenu:null,onMouseDown:null,readOnly:!1,required:!1,style:null,tabIndex:null,tooltip:null,tooltipOptions:null,trueValue:!0,value:null,children:void 0},css:{classes:{box:"p-checkbox-box",input:"p-checkbox-input",icon:"p-checkbox-icon",root:function(e){var t=e.props,n=e.context;return s.classNames("p-checkbox p-component",{"p-highlight":e.checked,"p-disabled":t.disabled,"p-invalid":t.invalid,"p-variant-filled":t.variant?"filled"===t.variant:n&&"filled"===n.inputStyle})}}}});function H(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function V(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?H(Object(n),!0).forEach((function(t){j(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):H(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var q=O.memo(O.forwardRef((function(e,r){var i=l.useMergeProps(),o=O.useContext(t.PrimeReactContext),a=R.getProps(e,o),c=A(O.useState(!1),2),p=c[1],d=R.setMetaData({props:a,state:{focused:c[0]},context:{checked:a.checked===a.trueValue,disabled:a.disabled}}),f=d.ptm,b=d.cx;n.useHandleStyle(R.css.styles,d.isUnstyled,{name:"checkbox"});var v=O.useRef(null),h=O.useRef(a.inputRef),y=function(){return a.checked===a.trueValue},g=function(e){if(!a.disabled&&!a.readOnly&&a.onChange){var t,n=y()?a.falseValue:a.trueValue;if(null==a||null===(t=a.onChange)||void 0===t||t.call(a,{originalEvent:e,value:a.value,checked:n,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{type:"checkbox",name:a.name,id:a.id,value:a.value,checked:n}}),e.defaultPrevented)return;s.DomHandler.focus(h.current)}};O.useImperativeHandle(r,(function(){return{props:a,focus:function(){return s.DomHandler.focus(h.current)},getElement:function(){return v.current},getInput:function(){return h.current}}})),O.useEffect((function(){s.ObjectUtils.combinedRefs(h,a.inputRef)}),[h,a.inputRef]),l.useUpdateEffect((function(){h.current.checked=y()}),[a.checked,a.trueValue]),l.useMountEffect((function(){a.autoFocus&&s.DomHandler.focus(h.current,a.autoFocus)}));var x,S,E,k,j,w=y(),C=s.ObjectUtils.isNotEmpty(a.tooltip),D=R.getOtherProps(a),N=i({id:a.id,className:s.classNames(a.className,b("root",{checked:w,context:o})),style:a.style,"data-p-highlight":w,"data-p-disabled":a.disabled,onContextMenu:a.onContextMenu,onMouseDown:a.onMouseDown},D,f("root"));return O.createElement(O.Fragment,null,O.createElement("div",I({ref:v},N),(k=s.ObjectUtils.reduceKeys(D,s.DomHandler.ARIA_PROPS),j=i(V({id:a.inputId,type:"checkbox",className:b("input"),name:a.name,tabIndex:a.tabIndex,onFocus:function(e){return t=e,p(!0),void(null==a||null===(n=a.onFocus)||void 0===n||n.call(a,t));var t,n},onBlur:function(e){return t=e,p(!1),void(null==a||null===(n=a.onBlur)||void 0===n||n.call(a,t));var t,n},onChange:function(e){return g(e)},disabled:a.disabled,readOnly:a.readOnly,required:a.required,"aria-invalid":a.invalid,checked:w},k),f("input")),O.createElement("input",I({ref:h},j))),(x=i({className:b("icon")},f("icon")),S=i({className:b("box",{checked:w}),"data-p-highlight":w,"data-p-disabled":a.disabled},f("box")),E=s.IconUtils.getJSXIcon(w?a.icon||O.createElement(m.CheckIcon,x):null,V({},x),{props:a,checked:w}),O.createElement("div",S,E))),C&&O.createElement(u.Tooltip,I({target:v,content:a.tooltip,pt:f("tooltip")},a.tooltipOptions)))})));function K(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function G(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?K(Object(n),!0).forEach((function(t){j(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):K(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}q.displayName="Checkbox";var _=O.memo((function(e){var n=l.useMergeProps(),r=e.ptm,i=e.cx,a=e.isUnstyled,c={filter:function(e){return p(e)},reset:function(){return e.resetFilter()}},u=function(t,n){return r(t,G({hostName:e.hostName},n))},p=function(t){e.onFilter&&e.onFilter({originalEvent:t,query:t.target.value})},d=function(t){if(e.onSelectAll)e.onSelectAll({originalEvent:t,checked:e.selectAll});else{var n=e.isAllSelected()?[]:e.visibleOptions.filter((function(t){return e.isValidOption(t)})).map((function(t){return e.getOptionValue(t)}));e.updateModel(t,n,n)}},f=function(){var t=n({className:i("filterIcon")},u("filterIcon")),l=s.IconUtils.getJSXIcon(e.filterIcon||O.createElement(b.SearchIcon,t),G({},t),{props:e});if(e.filter){var o=n({className:i("filterContainer")},u("filterContainer")),a=O.createElement("div",o,O.createElement(v.InputText,{ref:e.filterRef,type:"text",role:"searchbox",value:e.filterValue,onChange:p,className:"p-multiselect-filter",placeholder:e.filterPlaceholder,pt:r("filterInput"),unstyled:e.unstyled,__parentMetadata:{parent:e.metaData}}),l);if(e.filterTemplate)a=s.ObjectUtils.getJSXElement(e.filterTemplate,{className:o.className,element:a,filterOptions:c,onFilter:p,filterIconClassName:e.filterIconClassName,props:e});return O.createElement(O.Fragment,null,a)}return null}(),y=e.id?e.id+"_selectall":s.UniqueComponentId(),g=n({htmlFor:y,className:i("headerSelectAllLabel")},u("headerSelectAllLabel")),x=n({className:i("headerCheckboxIcon")},u("headerCheckbox.icon")),I=n({className:i("headerCheckboxContainer")},u("headerCheckboxContainer")),S=s.IconUtils.getJSXIcon(e.itemCheckboxIcon||O.createElement(m.CheckIcon,x),G({},x),{selected:e.selected}),E=e.showSelectAll&&O.createElement("div",I,O.createElement(q,{id:y,checked:e.selectAll,onChange:d,role:"checkbox","aria-checked":e.selectAll,icon:S,pt:r("headerCheckbox"),unstyled:a()}),!e.filter&&O.createElement("label",g,e.selectAllLabel)),k=n({className:i("closeIcon"),"aria-hidden":!0},u("closeIcon")),j=s.IconUtils.getJSXIcon(e.closeIcon||O.createElement(o.TimesIcon,k),G({},k),{props:e}),w=n({className:i("header")},u("header")),C=n({type:"button",className:i("closeButton"),"aria-label":t.ariaLabel("close"),onClick:e.onClose},u("closeButton")),D=O.createElement("button",C,j,O.createElement(h.Ripple,null)),N=O.createElement("div",w,E,f,D);return e.template?s.ObjectUtils.getJSXElement(e.template,{className:"p-multiselect-header",checkboxElement:E,checked:e.selectAll,onChange:d,filterElement:f,closeElement:D,closeElementClassName:"p-multiselect-close p-link",closeIconClassName:"p-multiselect-close-icon",onCloseClick:e.onClose,element:N,itemCheckboxIcon:S,props:e}):N}));function B(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function J(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?B(Object(n),!0).forEach((function(t){j(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):B(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}_.displayName="MultiSelectHeader";var X=O.memo((function(e){var t=A(O.useState(!1),2),n=t[0],r=t[1],i=O.useRef(null),o=l.useMergeProps(),a=e.ptm,c=e.cx,u=e.isUnstyled,p=function(t){return a(t,{hostName:e.hostName,context:{selected:e.selected,disabled:e.disabled,focused:n,focusedIndex:e.focusedIndex,index:e.index}})},d=o({className:c("checkboxIcon")},p("checkbox.icon")),f=e.selected?s.IconUtils.getJSXIcon(e.checkboxIcon||O.createElement(m.CheckIcon,d),J({},d),{selected:e.selected}):null,b=e.template?s.ObjectUtils.getJSXElement(e.template,e.option):e.label,v=o({className:c("checkboxContainer")},p("checkboxContainer")),y=o({className:s.classNames(e.className,e.option.className,c("item",{itemProps:e})),style:e.style,onClick:function(t){e.onClick&&e.onClick(t,e.option),t.preventDefault(),t.stopPropagation()},onFocus:function(e){var t;r(!0),null==i||null===(t=i.current)||void 0===t||t.getInput().focus()},onBlur:function(e){r(!1)},onMouseMove:function(t){return null==e?void 0:e.onMouseMove(t,e.index)},role:"option","aria-selected":e.selected,"data-p-highlight":e.selected,"data-p-disabled":e.disabled},p("item"));return O.createElement("li",I({},y,{key:e.index+"_multiselectitem"}),O.createElement("div",v,O.createElement(q,{ref:i,checked:e.selected,icon:f,pt:a("checkbox"),unstyled:u(),tabIndex:-1})),O.createElement("span",null,b),O.createElement(h.Ripple,null))}));function z(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function W(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?z(Object(n),!0).forEach((function(t){j(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):z(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}X.displayName="MultiSelectItem";var Z=O.memo(O.forwardRef((function(e,n){var r=O.useRef(null),i=O.useRef(null),o=l.useMergeProps(),a=O.useContext(t.PrimeReactContext),c=e.ptm,u=e.cx,m=e.sx,b=e.isUnstyled,v=function(t,n){return c(t,W({hostName:e.hostName},n))},h=function(){e.onEnter((function(){if(r.current){var t=e.getSelectedOptionIndex();-1!==t&&setTimeout((function(){return r.current.scrollToIndex(t)}),0)}}))},y=function(){e.onEntered((function(){e.filter&&e.filterInputAutoFocus&&i.current&&s.DomHandler.focus(i.current,!1)}))},g=function(t){r.current&&r.current.scrollToIndex(0),e.onFilterInputChange&&e.onFilterInputChange(t)},x=function(){if(e.panelFooterTemplate){var t=s.ObjectUtils.getJSXElement(e.panelFooterTemplate,e,e.onOverlayHide);return O.createElement("div",{className:"p-multiselect-footer"},t)}return null},S=function(t,n){var l;e.focusOnHover&&(null==e||null===(l=e.changeFocusedOptionIndex)||void 0===l||l.call(e,t,n))},E=function(){var n=s.ObjectUtils.getJSXElement(e.emptyFilterMessage,e)||t.localeOption("emptyFilterMessage"),l=o({className:u("emptyMessage")},v("emptyMessage"));return O.createElement("li",I({},l,{key:"emptyFilterMessage"}),n)},k=function(t,n){var l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r={height:l.props?l.props.itemSize:void 0};if(!0===t.group&&e.optionGroupLabel){var i=e.optionGroupTemplate?s.ObjectUtils.getJSXElement(e.optionGroupTemplate,t,n):e.getOptionGroupLabel(t),a=n+"_"+e.getOptionGroupRenderKey(t),p=o({className:u("itemGroup"),style:m("itemGroup",{scrollerOptions:l})},v("itemGroup"));return O.createElement("li",I({key:a},p),i)}var d=e.getOptionLabel(t),f=n+"_"+e.getOptionRenderKey(t),h=e.isOptionDisabled(t),y=e.isSelected(t);return O.createElement(X,{hostName:e.hostName,key:f,focusedOptionIndex:e.focusedOptionIndex,label:d,option:t,style:r,index:n,template:e.itemTemplate,selected:y,onClick:e.onOptionSelect,onMouseMove:S,disabled:h,className:e.itemClassName,checkboxIcon:e.checkboxIcon,isUnstyled:b,ptm:c,cx:u})},j=function(){if(e.virtualScrollerOptions){var n=W(W({},e.virtualScrollerOptions),{style:W(W({},e.virtualScrollerOptions.style),{height:e.scrollHeight}),className:s.classNames("p-multiselect-items-wrapper",e.virtualScrollerOptions.className),items:e.visibleOptions,autoSize:!0,onLazyLoad:function(t){return e.virtualScrollerOptions.onLazyLoad(W(W({},t),{filter:e.filterValue}))},itemTemplate:function(e,t){return e&&k(e,t.index,t)},contentTemplate:function(t){var n=e.visibleOptions&&e.visibleOptions.length||!e.hasFilter?t.children:E(),l=o({ref:t.contentRef,style:t.style,className:s.classNames(t.className,u("list",{virtualScrollerProps:e.virtualScrollerOptions})),role:"listbox","aria-multiselectable":!0},v("list"));return O.createElement("ul",l,n)}});return O.createElement(f.VirtualScroller,I({ref:r},n,{pt:c("virtualScroller"),__parentMetadata:{parent:e.metaData}}))}var l,i,a=s.ObjectUtils.isNotEmpty(e.visibleOptions)?e.visibleOptions.map(k):e.hasFilter?E():(l=s.ObjectUtils.getJSXElement(e.emptyMessage,e)||t.localeOption("emptyMessage"),i=o({className:u("emptyMessage")},v("emptyMessage")),O.createElement("li",I({},i,{key:"emptyMessage"}),l)),p=o({className:u("wrapper"),style:{maxHeight:e.scrollHeight}},v("wrapper")),d=o({className:u("list"),role:"listbox","aria-multiselectable":!0},v("list"));return O.createElement("div",p,O.createElement("ul",d,a))},w=function(){var t=e.allowOptionSelect(),l=O.createElement(_,{hostName:e.hostName,id:e.id,filter:e.filter,filterRef:i,filterValue:e.filterValue,filterTemplate:e.filterTemplate,visibleOptions:e.visibleOptions,isValidOption:e.isValidOption,getOptionValue:e.getOptionValue,updateModel:e.updateModel,onFilter:g,filterPlaceholder:e.filterPlaceholder,onClose:e.onCloseClick,showSelectAll:e.showSelectAll,selectAll:e.isAllSelected(),selectAllLabel:e.selectAllLabel,onSelectAll:e.onSelectAll,template:e.panelHeaderTemplate,resetFilter:e.resetFilter,closeIcon:e.closeIcon,filterIcon:e.filterIcon,itemCheckboxIcon:e.itemCheckboxIcon,ptm:c,cx:u,isUnstyled:b,metaData:e.metaData}),r=j(),d=x(),f=o({className:s.classNames(e.panelClassName,u("panel",{panelProps:e,context:a,allowOptionSelect:t})),style:e.panelStyle,onClick:e.onClick},v("panel"));if(e.inline)return O.createElement("div",I({ref:n},f),r,d);var m=o({classNames:u("transition"),in:e.in,timeout:{enter:120,exit:100},options:e.transitionOptions,appear:!0,unmountOnExit:!0,onEnter:h,onEntered:y,onExit:e.onExit,onExited:e.onExited},v("transition")),S=o({ref:e.firstHiddenFocusableElementOnOverlay,role:"presentation",className:"p-hidden-accessible p-hidden-focusable",tabIndex:"0",onFocus:e.onFirstHiddenFocus,"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0},c("hiddenFirstFocusableEl")),E=o({ref:e.lastHiddenFocusableElementOnOverlay,role:"presentation",className:"p-hidden-accessible p-hidden-focusable",tabIndex:"0",onFocus:e.onLastHiddenFocus,"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0},c("hiddenLastFocusableEl"));return O.createElement(p.CSSTransition,I({nodeRef:n},m),O.createElement("div",I({ref:n},f),O.createElement("span",S),l,r,d,O.createElement("span",E)))}();return e.inline?w:O.createElement(d.Portal,{element:w,appendTo:e.appendTo})})));function Y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function $(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(n),!0).forEach((function(t){j(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Q(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=ee(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var l=0,r=function(){};return{s:r,n:function(){return l>=e.length?{done:!0}:{done:!1,value:e[l++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){a=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(a)throw i}}}}function ee(e,t){if(e){if("string"==typeof e)return te(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?te(e,t):void 0}}function te(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,l=Array(t);n<t;n++)l[n]=e[n];return l}Z.displayName="MultiSelectPanel";var ne=O.memo(O.forwardRef((function(e,p){var d=l.useMergeProps(),f=O.useContext(t.PrimeReactContext),m=T.getProps(e,f),b=A(O.useState(null),2),v=b[0],h=b[1],y=A(O.useState(!1),2),g=y[0],S=y[1],E=A(l.useDebounce("",m.filterDelay||0),3),k=E[0],w=E[1],U=E[2],L=A(O.useState(-1),2),M=L[0],R=L[1],H=A(O.useState(!1),2),V=H[0],q=H[1],K=A(O.useState(m.inline),2),G=K[0],_=K[1],B=O.useRef(null),J=O.useRef(null),X=O.useRef(null),z=O.useRef(null),W=O.useRef(null),Y=O.useRef(m.inputRef),ee=O.useRef(null),te=O.useRef(null),ne=O.useRef(null),le=w&&w.trim().length>0,re=s.ObjectUtils.isEmpty(m.value),ie=m.optionValue?null:m.dataKey,oe={props:m,state:{filterState:w,focused:V,overlayVisible:G}},ae=T.setMetaData(oe),ce=ae.ptm,ue=ae.cx,se=ae.sx,pe=ae.isUnstyled;n.useHandleStyle(T.css.styles,pe,{name:"multiselect"});var de=A(l.useOverlayListener({target:B,overlay:te,listener:function(e,t){t.valid&&("outside"===t.type?Me(e)||Ae(e)||Ue():f.hideOverlaysOnDocumentScrolling?Ue():s.DomHandler.isDocument(e.target)||Le())},when:G}),2),fe=de[0],me=de[1],be=function(){return!m.selectionLimit||!m.value||m.value&&m.value.length<m.selectionLimit},ve=function(e){var t=ze()&&e<gt.length-1?gt.slice(e+1).findIndex((function(e){return $e(e)})):-1;return t>-1?t+e+1:-1},he=function(e){var t=ze()&&e>0?s.ObjectUtils.findLastIndex(gt.slice(0,e),(function(e){return $e(e)})):-1;return t>-1?t:-1},ye=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=-1;return ze()&&(n=t?-1===(n=he(e))?ve(e):n:-1===(n=ve(e))?he(e):n),n>-1?n:e},ge=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;if(-1===t&&(t=ye(n,!0)),-1===n&&(n=ye(t)),-1!==t&&-1!==n){var l=Math.min(t,n),r=Math.max(t,n),i=gt.slice(l,r+1).filter((function(e){return Ye(e)})).map((function(e){return Ge(e)}));De(e,i,i)}},Oe=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;if(!m.disabled&&!Be(t)){var l,r=null;r=He(t)?m.value.filter((function(e){return!s.ObjectUtils.equals(e,Ge(t),ie)})):[].concat(C(l=m.value||[])||D(l)||N(l)||P(),[Ge(t)]),De(e,r,t),-1!==n&&h(n)}},xe=function(e){if(G){var t=-1!==v?rt(v):g?nt():et();e.shiftKey&&ge(e,M,t),at(e,t)}else Fe(),m.editable&&at(e,Qe());e.preventDefault()},Ie=function(e){if(e.altKey&&!(arguments.length>1&&void 0!==arguments[1]&&arguments[1]))-1!==v&&Oe(e,gt[v]),G&&Ue(),e.preventDefault();else{var t=-1!==v?it(v):g?lt():tt();at(e,t),!G&&Fe(),e.preventDefault()}},Se=function(e){G?-1!==v&&(e.shiftKey?ge(e,v):Oe(e,gt[v])):(h(-1),xe(e)),e.preventDefault()},Ee=function(e){var t=e.currentTarget;if(arguments.length>1&&void 0!==arguments[1]&&arguments[1]){t.setSelectionRange(0,e.shiftKey?t.value.length:0),h(-1)}else{var n=e.metaKey||e.ctrlKey,l=nt();e.shiftKey&&n&&ge(e,l,M),at(e,l),!G&&Fe()}e.preventDefault()},ke=function(e){var t=e.currentTarget;if(arguments.length>1&&void 0!==arguments[1]&&arguments[1]){var n=t.value.length;t.setSelectionRange(e.shiftKey?0:n,n),F("focusedOptionIndex")}else{var l=e.metaKey||e.ctrlKey,r=lt();e.shiftKey&&l&&ge(e,M,r),at(e,r),!G&&Fe()}e.preventDefault()},je=function(e){e.preventDefault()},we=function(e){e.preventDefault()},Ce=function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1]||(G&&We()?(s.DomHandler.focus(e.shiftKey?W.current:z.current),e.preventDefault()):(-1!==v&&Oe(e,gt[v]),G&&Ue(filter)))},De=function(e,t,n){m.onChange&&(m.onChange({originalEvent:e,value:t,selectedOption:n,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{name:m.name,id:m.id,value:t}}),s.DomHandler.focus(Y.current))},Ne=function(){U(""),m.onFilter&&m.onFilter({filter:""})},Pe=function(e){var t;G&&((t=e?e.currentTarget:s.DomHandler.findSingle(te.current,'li[data-p-highlight="true"]'))&&t.scrollIntoView&&t.scrollIntoView({block:"nearest",inline:"nearest"}))},Fe=function(){_(!0),h(-1!==v?v:m.autoOptionFocus?et():Qe()),s.DomHandler.focus(Y.current)},Ue=function(){h(-1),_(!1),S(!1)},Le=function(){!m.inline&&s.DomHandler.alignOverlay(te.current,ee.current.parentElement,m.appendTo||f&&f.appendTo||x.default.appendTo)},Me=function(e){return"clearicon"===s.DomHandler.getAttribute(e.target,"data-pc-section")},Ae=function(e){return"headercheckboxcontainer"===s.DomHandler.getAttribute(e.target,"data-pc-section")},Te=function(e){return te.current&&te.current.contains(e.target)},Re=function(e,t){return t.findIndex((function(t){return e.some((function(e){return s.ObjectUtils.equals(e,Ge(t),ie)}))}))},He=function(e){if(m.value){var t=Ge(e),n=Je(e);return m.value.some((function(e){return s.ObjectUtils.equals(n?e:Ge(e),t,ie)}))}return!1},Ve=function(e){var t;if(m.options)if(m.optionGroupLabel){var n,l=Q(m.options);try{for(l.s();!(n=l.n()).done;){if(t=qe(e,_e(n.value)))break}}catch(e){l.e(e)}finally{l.f()}}else t=qe(e,m.options),s.ObjectUtils.isEmpty(t)&&(t=qe(e,m.value));return t?Ke(t):null},qe=function(e,t){return t.find((function(t){return s.ObjectUtils.equals(Ge(t),e,ie)}))},Ke=function(e){return m.optionLabel?s.ObjectUtils.resolveFieldData(e,m.optionLabel):e&&void 0!==e.label?e.label:e},Ge=function(e){return m.useOptionAsValue?e:m.optionValue?s.ObjectUtils.resolveFieldData(e,m.optionValue):e&&void 0!==e.value?e.value:e},_e=function(e){return s.ObjectUtils.resolveFieldData(e,m.optionGroupChildren)},Be=function(e){var t;if(!be()&&!He(e))return!0;var n=m.optionDisabled;return n?s.ObjectUtils.isFunction(n)?n(e):s.ObjectUtils.resolveFieldData(e,n):e&&null!==(t=e.disabled)&&void 0!==t&&t},Je=function(e){return!m.useOptionAsValue&&m.optionValue||e&&void 0!==e.value},Xe=function(e){return m.optionGroupLabel&&e.group},ze=function(){return s.ObjectUtils.isNotEmpty(m.value)},We=function(){return s.DomHandler.getFocusableElements(te.current,':not([data-p-hidden-focusable="true"])').length>0},Ze=function(e){var t;return Ye(e)&&(null===(t=Ke(e))||void 0===t?void 0:t.toLocaleLowerCase(m.filterLocale).startsWith(J.current.toLocaleLowerCase(m.filterLocale)))},Ye=function(e){return s.ObjectUtils.isNotEmpty(e)&&!(Be(e)||Xe(e))},$e=function(e){return Ye(e)&&He(e)},Qe=function(){if(ze())for(var e,t=function(){var e=m.value[n],t=gt.findIndex((function(t){return $e(t)&&(n=e,l=Ge(t),s.ObjectUtils.equals(n,l,ie));var n,l}));if(t>-1)return{v:t}},n=m.value.length-1;n>=0;n--)if(e=t())return e.v;return-1},et=function(){var e=Qe();return e<0?nt():e},tt=function(){var e=Qe();return e<0?lt():e},nt=function(){return gt.findIndex((function(e){return Ye(e)}))},lt=function(){return s.ObjectUtils.findLastIndex(gt,(function(e){return Ye(e)}))},rt=function(e){var t=e<gt.length-1?gt.slice(e+1).findIndex((function(e){return Ye(e)})):-1;return t>-1?t+e+1:e},it=function(e){var t=e>0?s.ObjectUtils.findLastIndex(gt.slice(0,e),(function(e){return Ye(e)})):-1;return t>-1?t:e},ot=function(e){J.current=(J.current||"")+e.key;var t=-1;s.ObjectUtils.isNotEmpty(J.current)&&(-1===(t=-1!==v?-1===(t=gt.slice(v).findIndex((function(e){return Ze(e)})))?gt.slice(0,v).findIndex((function(e){return Ze(e)})):t+v:gt.findIndex((function(e){return Ze(e)})))&&-1===v&&(t=et()),-1!==t&&at(e,t)),X.current&&clearTimeout(X.current),X.current=setTimeout((function(){J.current="",X.current=null}),500)},at=function(e,t){v!==t&&(h(t),Pe(e),m.selectOnFocus&&Oe(e,gt[t],!1))},ct=function(e,t){if(e.stopPropagation(),ut(e.currentTarget)){var n=m.value.filter((function(e){return!s.ObjectUtils.equals(e,t,ie)}));m.onRemove&&m.onRemove({originalEvent:e,value:n}),De(e,n,t)}},ut=function(e){var t=ne.current;if(!(t.clientWidth<t.scrollWidth))return!0;var n=e.closest('[data-pc-section="token"]'),l=window.getComputedStyle(t),r=window.getComputedStyle(n),i=t.clientWidth-parseFloat(l.paddingLeft)-parseFloat(l.paddingRight);return n.getBoundingClientRect().right+parseFloat(r.marginRight)-t.getBoundingClientRect().left<=i},st=function(){var e=/{(.*?)}/,n=m.selectedItemsLabel||t.localeOption("selectionMessage"),l=m.value?m.value.length:0;return e.test(n)?n.replace(n.match(e)[0],l+""):n},pt=function(){var e;if(!re&&!m.fixedPlaceholder)return s.ObjectUtils.isNotEmpty(m.maxSelectedLabels)&&(null===(e=m.value)||void 0===e?void 0:e.length)>m.maxSelectedLabels?st():s.ObjectUtils.isArray(m.value)?m.value.reduce((function(e,t,n){return e+(0!==n?", ":"")+Ve(t)}),""):""},dt=function(e){return(e||[]).reduce((function(e,t,n){e.push($($({},t),{},{group:!0,index:n}));var l=_e(t);return l&&l.forEach((function(t){return e.push(t)})),e}),[])},ft=function(e){switch(e.code){case"Space":case"NumpadEnter":case"Enter":if(m.inline)break;De(e,[],[]),e.preventDefault(),e.stopPropagation()}},mt=function(e,t){switch(e.code){case"Space":case"NumpadEnter":case"Enter":if(m.inline)break;ct(e,t),e.preventDefault(),e.stopPropagation()}};O.useImperativeHandle(p,(function(){return{props:m,show:Fe,hide:Ue,focus:function(){return s.DomHandler.focus(Y.current)},getElement:function(){return B.current},getOverlay:function(){return te.current},getInput:function(){return Y.current}}})),l.useMountEffect((function(){Le()})),O.useEffect((function(){s.ObjectUtils.combinedRefs(Y,m.inputRef)}),[Y,m.inputRef]),O.useEffect((function(){!0===m.overlayVisible?Fe():!1===m.overlayVisible&&Ue()}),[m.overlayVisible]),l.useUpdateEffect((function(){G&&w&&le&&Le()}),[G,w,le]),l.useUnmountEffect((function(){s.ZIndexUtils.clear(te.current)}));var bt,vt,ht,yt,gt=function(){var e=m.optionGroupLabel?dt(m.options):m.options;if(le){var n=w.trim().toLocaleLowerCase(m.filterLocale),l=m.filterBy?m.filterBy.split(","):[m.optionLabel||"label"];if(m.optionGroupLabel){var r,i=[],o=Q(m.options);try{for(o.s();!(r=o.n()).done;){var a=r.value,c=t.FilterService.filter(_e(a),l,n,m.filterMatchMode,m.filterLocale);c&&c.length&&i.push($($({},a),j({},m.optionGroupChildren,c)))}}catch(e){o.e(e)}finally{o.f()}return dt(i)}return t.FilterService.filter(e,l,n,m.filterMatchMode,m.filterLocale)}return e}(),Ot=s.ObjectUtils.isNotEmpty(m.tooltip),xt=T.getOtherProps(m),It=s.ObjectUtils.reduceKeys(xt,s.DomHandler.ARIA_PROPS),St=d({className:ue("triggerIcon")},ce("triggerIcon")),Et=d({className:ue("trigger")},ce("trigger")),kt=m.loadingIcon?s.IconUtils.getJSXIcon(m.loadingIcon,$({},St),{props:m}):O.createElement(i.SpinnerIcon,I({spin:!0},St)),jt=m.dropdownIcon?s.IconUtils.getJSXIcon(m.dropdownIcon,$({},St),{props:m}):O.createElement(r.ChevronDownIcon,St),wt=O.createElement("div",Et,m.loading?kt:jt),Ct=!m.inline&&(bt=m.value?m.value.length:0,vt=s.ObjectUtils.isNotEmpty(m.maxSelectedLabels)&&bt>m.maxSelectedLabels?st():m.selectedItemTemplate?re?s.ObjectUtils.getJSXElement(m.selectedItemTemplate):m.value.map((function(e,t){var n=s.ObjectUtils.getJSXElement(m.selectedItemTemplate,e);return O.createElement(O.Fragment,{key:t},n)})):"chip"!==m.display||re?pt():m.value.slice(0,m.maxSelectedLabels||bt).map((function(e,n){var l={context:{value:e,index:n}},r=Ve(e),i=r+"_"+n,o=d({"aria-label":t.localeOption("removeTokenIcon"),className:ue("removeTokenIcon"),onClick:function(t){return ct(t,e)},onKeyDown:function(t){return mt(t,e)},tabIndex:m.tabIndex||"0"},ce("removeTokenIcon",l)),c=!m.disabled&&(m.removeIcon?s.IconUtils.getJSXIcon(m.removeIcon,$({},o),{props:m}):O.createElement(a.TimesCircleIcon,o)),u=d({className:ue("token")},ce("token",l)),p=d({className:ue("tokenLabel")},ce("tokenLabel",l));return O.createElement("div",I({},u,{key:i}),O.createElement("span",p,r),c)})),ht=d({ref:ee,className:ue("labelContainer")},ce("labelContainer")),yt=d({ref:ne,className:ue("label",{empty:re})},ce("label")),O.createElement("div",ht,O.createElement("div",yt,vt||m.placeholder||m.emptyMessage||"empty"))),Dt=!m.inline&&function(){var e=d({className:ue("clearIcon"),"aria-label":t.localeOption("clear"),onClick:function(e){return De(e,[],[])},onKeyDown:function(e){return ft(e)},tabIndex:m.tabIndex||"0"},ce("clearIcon")),n=s.IconUtils.getJSXIcon(m.clearIcon||O.createElement(o.TimesIcon,e),$({},e),{props:m});return re||!m.showClear||m.disabled?null:n}(),Nt=d($($({ref:B,id:m.id,style:$($({},m.style),se("root")),className:s.classNames(m.className,ue("root",{focusedState:V,context:f,overlayVisibleState:G}))},xt),{},{onClick:function(e){m.inline||m.disabled||m.loading||Te(e)||Me(e)||(G?Ue():Fe(),s.DomHandler.focus(Y.current),e.preventDefault()),S(!0)}}),T.getOtherProps(m),ce("root")),Pt=d({className:"p-hidden-accessible","data-p-hidden-accessible":!0},ce("hiddenInputWrapper")),Ft=d($({ref:Y,id:m.inputId,name:m.name,type:"text",onFocus:function(e){q(!0),m.onFocus&&m.onFocus(e)},onBlur:function(e){q(!1),m.onBlur&&m.onBlur(e)},onKeyDown:function(e){var t=e.metaKey||e.ctrlKey;switch(e.code){case"ArrowUp":if(m.inline)break;Ie(e);break;case"ArrowDown":if(m.inline)break;xe(e);break;case"Space":case"NumpadEnter":case"Enter":if(m.inline)break;Se(e);break;case"Home":if(m.inline)break;Ee(e),e.preventDefault();break;case"End":if(m.inline)break;ke(e),e.preventDefault();break;case"PageDown":we(e);break;case"PageUp":je(e);break;case"Escape":if(m.inline)break;Ue();break;case"Tab":Ce(e);break;case"ShiftLeft":case"ShiftRight":R(v);break;default:if("a"===e.key&&t){var n=gt.filter((function(e){return Ye(e)})).map((function(e){return Ge(e)}));De(e,n,n),e.preventDefault();break}!t&&s.ObjectUtils.isPrintableCharacter(e.key)&&(!G&&Fe(),ot(e),e.preventDefault())}S(!1)},role:"combobox","aria-expanded":G,disabled:m.disabled,tabIndex:m.disabled?-1:m.tabIndex,value:pt()},It),ce("input"));return O.createElement(O.Fragment,null,O.createElement("div",Nt,O.createElement("div",Pt,O.createElement("input",I({},Ft,{readOnly:!0}))),!m.inline&&O.createElement(O.Fragment,null,Ct,Dt,wt),O.createElement(Z,I({hostName:"MultiSelect",ref:te,visibleOptions:gt},m,{onClick:function(e){c.OverlayService.emit("overlay-click",{originalEvent:e,target:B.current})},onOverlayHide:Ue,filterValue:k,focusedOptionIndex:v,onFirstHiddenFocus:function(e){var t=e.relatedTarget===Y.current?s.DomHandler.getFirstFocusableElement(te.current,':not([data-p-hidden-focusable="true"])'):Y.current;s.DomHandler.focus(t)},onLastHiddenFocus:function(e){var t=e.relatedTarget===Y.current?s.DomHandler.getLastFocusableElement(te.current,':not([data-p-hidden-focusable="true"])'):Y.current;s.DomHandler.focus(t)},firstHiddenFocusableElementOnOverlay:z,lastHiddenFocusableElementOnOverlay:W,setFocusedOptionIndex:h,hasFilter:le,isValidOption:Ye,getOptionValue:Ge,updateModel:De,onFilterInputChange:function(e){var t=e.query;U(t),m.onFilter&&m.onFilter({originalEvent:e,filter:t})},resetFilter:Ne,onCloseClick:function(e){Ue(),s.DomHandler.focus(Y.current),e.preventDefault(),e.stopPropagation()},onSelectAll:function(e){if(m.onSelectAll)m.onSelectAll(e);else{var t=null;if(e.checked)t=[];else{var n=gt.filter((function(e){return Ye(e)&&!Be(e)}));n&&(t=n.map((function(e){return Ge(e)})))}m.selectionLimit&&t&&t.length&&(t=t.slice(0,m.selectionLimit)),De(e.originalEvent,t,t)}},getOptionLabel:Ke,getOptionRenderKey:function(e){return m.dataKey?s.ObjectUtils.resolveFieldData(e,m.dataKey):Ke(e)},isOptionDisabled:Be,getOptionGroupChildren:_e,getOptionGroupLabel:function(e){return s.ObjectUtils.resolveFieldData(e,m.optionGroupLabel)},getOptionGroupRenderKey:function(e){return s.ObjectUtils.resolveFieldData(e,m.optionGroupLabel)},isSelected:He,getSelectedOptionIndex:function(){if(null!=m.value&&m.options){if(m.optionGroupLabel){var e=0,t=m.options.findIndex((function(t,n){return(e=n)&&-1!==Re(m.value,_e(t))}));return-1!==t?{group:e,option:t}:-1}return Re(m.value,m.options)}return-1},isAllSelected:function(){return m.onSelectAll?m.selectAll:!s.ObjectUtils.isEmpty(gt)&&!gt.filter((function(e){return!Be(e)&&Ye(e)})).some((function(e){return!He(e)}))},onOptionSelect:Oe,allowOptionSelect:be,in:G,onEnter:function(e){s.ZIndexUtils.set("overlay",te.current,f&&f.autoZIndex||x.default.autoZIndex,f&&f.zIndex.overlay||x.default.zIndex.overlay),s.DomHandler.addStyles(te.current,{position:"absolute",top:"0",left:"0"}),Le(),Pe(),e&&e()},onEntered:function(e){e&&e(),fe(),m.onShow&&m.onShow()},onExit:function(){me()},onExited:function(){m.filter&&m.resetFilterOnHide&&Ne(),s.ZIndexUtils.clear(te.current),m.onHide&&m.onHide()},ptm:ce,cx:ue,sx:se,isUnstyled:pe,metaData:oe,changeFocusedOptionIndex:at}))),Ot&&O.createElement(u.Tooltip,I({target:B,content:m.tooltip,pt:ce("tooltip")},m.tooltipOptions)))})));ne.displayName="MultiSelect",exports.MultiSelect=ne;
