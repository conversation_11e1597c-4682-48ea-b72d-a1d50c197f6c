import*as e from"react";import t,{PrimeReactContext as n,aria<PERSON><PERSON><PERSON> as l,localeOption as r,FilterService as o}from"primereact/api";import{ComponentBase as i,useHandleStyle as a}from"primereact/componentbase";import{useMergeProps as c,useUpdateEffect as u,useMountEffect as s,useDebounce as p,useOverlayListener as f,useUnmountEffect as d}from"primereact/hooks";import{ChevronDownIcon as m}from"primereact/icons/chevrondown";import{SpinnerIcon as v}from"primereact/icons/spinner";import{TimesIcon as b}from"primereact/icons/times";import{TimesCircleIcon as h}from"primereact/icons/timescircle";import{OverlayService as y}from"primereact/overlayservice";import{Tooltip as g}from"primereact/tooltip";import{classNames as O,ObjectUtils as x,<PERSON><PERSON><PERSON><PERSON> as E,IconUtils as S,UniqueComponentId as I,ZIndexUtils as k}from"primereact/utils";import{CSSTransition as w}from"primereact/csstransition";import{Portal as C}from"primereact/portal";import{VirtualScroller as N}from"primereact/virtualscroller";import{CheckIcon as F}from"primereact/icons/check";import{SearchIcon as P}from"primereact/icons/search";import{InputText as D}from"primereact/inputtext";import{Ripple as j}from"primereact/ripple";function L(){return L=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},L.apply(null,arguments)}function A(e){return A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},A(e)}function M(e,t){if("object"!=A(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var l=n.call(e,t||"default");if("object"!=A(l))return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function T(e){var t=M(e,"string");return"symbol"==A(t)?t:t+""}function R(e,t,n){return(t=T(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function V(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,l=Array(t);n<t;n++)l[n]=e[n];return l}function H(e){if(Array.isArray(e))return V(e)}function K(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function G(e,t){if(e){if("string"==typeof e)return V(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?V(e,t):void 0}}function J(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function X(e){throw new TypeError('"'+e+'" is read-only')}function B(e){if(Array.isArray(e))return e}function _(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var l,r,o,i,a=[],c=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(l=o.call(n)).done)&&(a.push(l.value),a.length!==t);c=!0);}catch(e){u=!0,r=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw r}}return a}}function U(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function q(e,t){return B(e)||_(e,t)||G(e,t)||U()}var z=i.extend({defaultProps:{__TYPE:"MultiSelect",appendTo:null,ariaLabelledBy:null,checkboxIcon:null,className:null,clearIcon:null,closeIcon:null,dataKey:null,disabled:!1,display:"comma",dropdownIcon:null,emptyFilterMessage:null,emptyMessage:null,filter:!1,filterBy:null,filterDelay:300,filterInputAutoFocus:!0,filterLocale:void 0,selectOnFocus:!1,focusOnHover:!0,autoOptionFocus:!1,filterMatchMode:"contains",filterPlaceholder:null,filterTemplate:null,fixedPlaceholder:!1,flex:!1,id:null,inline:!1,inputId:null,inputRef:null,invalid:!1,variant:null,itemCheckboxIcon:null,itemClassName:null,itemTemplate:null,loading:!1,loadingIcon:null,maxSelectedLabels:null,name:null,onBlur:null,onChange:null,onClick:null,onFilter:null,onFocus:null,onHide:null,onRemove:null,onSelectAll:null,onShow:null,optionDisabled:null,optionGroupChildren:null,optionGroupLabel:null,optionGroupTemplate:null,optionLabel:null,optionValue:null,options:null,overlayVisible:!1,panelClassName:null,panelFooterTemplate:null,panelHeaderTemplate:null,panelStyle:null,placeholder:null,removeIcon:null,resetFilterOnHide:!1,scrollHeight:"200px",selectAll:!1,selectAllLabel:null,selectedItemTemplate:null,selectedItemsLabel:void 0,selectionLimit:null,showClear:!1,showSelectAll:!0,style:null,tabIndex:0,tooltip:null,tooltipOptions:null,transitionOptions:null,useOptionAsValue:!1,value:null,virtualScrollerOptions:null,children:void 0},css:{classes:{root:function(e){var t,n=e.props,l=e.context,r=e.focusedState,o=e.overlayVisibleState;return O("p-multiselect p-component p-inputwrapper",{"p-multiselect-chip":"chip"===n.display&&(null==n.maxSelectedLabels||(null===(t=n.value)||void 0===t?void 0:t.length)<=n.maxSelectedLabels),"p-disabled":n.disabled,"p-invalid":n.invalid,"p-variant-filled":n.variant?"filled"===n.variant:l&&"filled"===l.inputStyle,"p-multiselect-clearable":n.showClear&&!n.disabled,"p-focus":r,"p-inputwrapper-filled":x.isNotEmpty(n.value),"p-inputwrapper-focus":r||o})},label:function(e){var t,n=e.props,l=e.empty;return O("p-multiselect-label",{"p-placeholder":l&&n.placeholder,"p-multiselect-label-empty":l&&!n.placeholder&&!n.selectedItemTemplate,"p-multiselect-items-label":!l&&"chip"!==n.display&&(null===(t=n.value)||void 0===t?void 0:t.length)>n.maxSelectedLabels})},panel:function(e){var n=e.panelProps,l=e.context;return O("p-multiselect-panel p-component",{"p-multiselect-inline":n.inline,"p-multiselect-flex":n.flex,"p-multiselect-limited":!e.allowOptionSelect,"p-input-filled":l&&"filled"===l.inputStyle||"filled"===t.inputStyle,"p-ripple-disabled":l&&!1===l.ripple||!1===t.ripple})},list:function(e){return"p-multiselect-items p-component"},labelContainer:"p-multiselect-label-container",triggerIcon:"p-multiselect-trigger-icon p-c",trigger:"p-multiselect-trigger",clearIcon:"p-multiselect-clear-icon",tokenLabel:"p-multiselect-token-label",token:"p-multiselect-token",removeTokenIcon:"p-multiselect-token-icon",wrapper:"p-multiselect-items-wrapper",emptyMessage:"p-multiselect-empty-message",itemGroup:"p-multiselect-item-group",closeButton:"p-multiselect-close p-link",header:"p-multiselect-header",closeIcon:"p-multiselect-close-icon",headerCheckboxContainer:"p-multiselect-select-all",headerCheckboxIcon:"p-multiselect-select-all p-checkbox-icon p-c",headerSelectAllLabel:"p-multiselect-select-all-label",filterContainer:"p-multiselect-filter-container",filterIcon:"p-multiselect-filter-icon",item:function(e){var t=e.itemProps;return O("p-multiselect-item",{"p-highlight":t.selected,"p-disabled":t.disabled,"p-focus":t.focusedOptionIndex===t.index})},checkboxContainer:"p-multiselect-checkbox",checkboxIcon:"p-checkbox-icon p-c",transition:"p-connected-overlay"},styles:"\n@layer primereact {\n    .p-multiselect {\n        display: inline-flex;\n        user-select: none;\n        cursor: pointer;\n    }\n    \n    .p-multiselect-trigger {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-shrink: 0;\n        cursor: pointer;\n    }\n    \n    .p-multiselect-label-container {\n        overflow: hidden;\n        flex: 1 1 auto;\n        cursor: pointer;\n    }\n    \n    .p-multiselect-label  {\n        display: block;\n        white-space: nowrap;\n        cursor: pointer;\n        overflow: hidden;\n        text-overflow: ellipsis;\n    }\n    \n    .p-multiselect-label-empty {\n        overflow: hidden;\n        visibility: hidden;\n    }\n    \n    .p-multiselect-token {\n        cursor: default;\n        display: inline-flex;\n        align-items: center;\n        flex: 0 0 auto;\n    }\n    \n    .p-multiselect-token-icon {\n        cursor: pointer;\n    }\n    \n    .p-multiselect .p-multiselect-panel {\n        min-width: 100%;\n    }\n    \n    .p-multiselect-inline.p-multiselect-panel {\n        border: none;\n        position: initial;\n        background: none;\n        box-shadow: none;\n    }\n    \n    .p-multiselect-inline.p-multiselect-panel .p-multiselect-items {\n        padding: 0;\n    }\n    \n    .p-multiselect-flex.p-multiselect-panel .p-multiselect-items {\n        display: flex;\n        flex-wrap: wrap;\n    }\n    \n    .p-multiselect-items-wrapper {\n        overflow: auto;\n    }\n    \n    .p-multiselect-items {\n        margin: 0;\n        padding: 0;\n        list-style-type: none;\n    }\n    \n    .p-multiselect-item {\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        font-weight: normal;\n        white-space: nowrap;\n        position: relative;\n        overflow: hidden;\n        outline: none;\n    }\n    \n    .p-multiselect-header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n    }\n    \n    .p-multiselect-select-all-label {\n        margin-left: 0.5rem;\n    }\n    \n    .p-multiselect-filter-container {\n        position: relative;\n        flex: 1 1 auto;\n    }\n    \n    .p-multiselect-filter-icon {\n        position: absolute;\n        top: 50%;\n        margin-top: -.5rem;\n    }\n    \n    .p-multiselect-filter-container .p-inputtext {\n        width: 100%;\n    }\n    \n    .p-multiselect-close {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-shrink: 0;\n        overflow: hidden;\n        position: relative;\n        margin-left: auto;\n    }\n    \n    .p-multiselect-clear-icon {\n        position: absolute;\n        top: 50%;\n        margin-top: -.5rem;\n        right: 3rem;\n    }\n    \n    .p-fluid .p-multiselect {\n        display: flex;\n    }\n}\n",inlineStyles:{root:function(e){var t=e.props;return t.showClear&&!t.disabled&&{position:"relative"}},itemGroup:function(e){var t=e.scrollerOptions;return{height:t.props?t.props.itemSize:void 0}}}}}),W=i.extend({defaultProps:{__TYPE:"Checkbox",autoFocus:!1,checked:!1,className:null,disabled:!1,falseValue:!1,icon:null,id:null,inputId:null,inputRef:null,invalid:!1,variant:null,name:null,onChange:null,onContextMenu:null,onMouseDown:null,readOnly:!1,required:!1,style:null,tabIndex:null,tooltip:null,tooltipOptions:null,trueValue:!0,value:null,children:void 0},css:{classes:{box:"p-checkbox-box",input:"p-checkbox-input",icon:"p-checkbox-icon",root:function(e){var t=e.props,n=e.context;return O("p-checkbox p-component",{"p-highlight":e.checked,"p-disabled":t.disabled,"p-invalid":t.invalid,"p-variant-filled":t.variant?"filled"===t.variant:n&&"filled"===n.inputStyle})}}}});function Y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function Z(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(n),!0).forEach((function(t){R(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var $=e.memo(e.forwardRef((function(t,l){var r=c(),o=e.useContext(n),i=W.getProps(t,o),p=q(e.useState(!1),2),f=p[1],d=W.setMetaData({props:i,state:{focused:p[0]},context:{checked:i.checked===i.trueValue,disabled:i.disabled}}),m=d.ptm,v=d.cx;a(W.css.styles,d.isUnstyled,{name:"checkbox"});var b=e.useRef(null),h=e.useRef(i.inputRef),y=function(){return i.checked===i.trueValue},I=function(e){if(!i.disabled&&!i.readOnly&&i.onChange){var t,n=y()?i.falseValue:i.trueValue;if(null==i||null===(t=i.onChange)||void 0===t||t.call(i,{originalEvent:e,value:i.value,checked:n,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{type:"checkbox",name:i.name,id:i.id,value:i.value,checked:n}}),e.defaultPrevented)return;E.focus(h.current)}};e.useImperativeHandle(l,(function(){return{props:i,focus:function(){return E.focus(h.current)},getElement:function(){return b.current},getInput:function(){return h.current}}})),e.useEffect((function(){x.combinedRefs(h,i.inputRef)}),[h,i.inputRef]),u((function(){h.current.checked=y()}),[i.checked,i.trueValue]),s((function(){i.autoFocus&&E.focus(h.current,i.autoFocus)}));var k,w,C,N,P,D=y(),j=x.isNotEmpty(i.tooltip),A=W.getOtherProps(i),M=r({id:i.id,className:O(i.className,v("root",{checked:D,context:o})),style:i.style,"data-p-highlight":D,"data-p-disabled":i.disabled,onContextMenu:i.onContextMenu,onMouseDown:i.onMouseDown},A,m("root"));return e.createElement(e.Fragment,null,e.createElement("div",L({ref:b},M),(N=x.reduceKeys(A,E.ARIA_PROPS),P=r(Z({id:i.inputId,type:"checkbox",className:v("input"),name:i.name,tabIndex:i.tabIndex,onFocus:function(e){return t=e,f(!0),void(null==i||null===(n=i.onFocus)||void 0===n||n.call(i,t));var t,n},onBlur:function(e){return t=e,f(!1),void(null==i||null===(n=i.onBlur)||void 0===n||n.call(i,t));var t,n},onChange:function(e){return I(e)},disabled:i.disabled,readOnly:i.readOnly,required:i.required,"aria-invalid":i.invalid,checked:D},N),m("input")),e.createElement("input",L({ref:h},P))),(k=r({className:v("icon")},m("icon")),w=r({className:v("box",{checked:D}),"data-p-highlight":D,"data-p-disabled":i.disabled},m("box")),C=S.getJSXIcon(D?i.icon||e.createElement(F,k):null,Z({},k),{props:i,checked:D}),e.createElement("div",w,C))),j&&e.createElement(g,L({target:b,content:i.tooltip,pt:m("tooltip")},i.tooltipOptions)))})));function Q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function ee(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Q(Object(n),!0).forEach((function(t){R(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Q(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}$.displayName="Checkbox";var te=e.memo((function(t){var n=c(),r=t.ptm,o=t.cx,i=t.isUnstyled,a={filter:function(e){return s(e)},reset:function(){return t.resetFilter()}},u=function(e,n){return r(e,ee({hostName:t.hostName},n))},s=function(e){t.onFilter&&t.onFilter({originalEvent:e,query:e.target.value})},p=function(e){if(t.onSelectAll)t.onSelectAll({originalEvent:e,checked:t.selectAll});else{var n=t.isAllSelected()?[]:t.visibleOptions.filter((function(e){return t.isValidOption(e)})).map((function(e){return t.getOptionValue(e)}));t.updateModel(e,n,n)}},f=function(){var l=n({className:o("filterIcon")},u("filterIcon")),i=S.getJSXIcon(t.filterIcon||e.createElement(P,l),ee({},l),{props:t});if(t.filter){var c=n({className:o("filterContainer")},u("filterContainer")),p=e.createElement("div",c,e.createElement(D,{ref:t.filterRef,type:"text",role:"searchbox",value:t.filterValue,onChange:s,className:"p-multiselect-filter",placeholder:t.filterPlaceholder,pt:r("filterInput"),unstyled:t.unstyled,__parentMetadata:{parent:t.metaData}}),i);if(t.filterTemplate)p=x.getJSXElement(t.filterTemplate,{className:c.className,element:p,filterOptions:a,onFilter:s,filterIconClassName:t.filterIconClassName,props:t});return e.createElement(e.Fragment,null,p)}return null}(),d=t.id?t.id+"_selectall":I(),m=n({htmlFor:d,className:o("headerSelectAllLabel")},u("headerSelectAllLabel")),v=n({className:o("headerCheckboxIcon")},u("headerCheckbox.icon")),h=n({className:o("headerCheckboxContainer")},u("headerCheckboxContainer")),y=S.getJSXIcon(t.itemCheckboxIcon||e.createElement(F,v),ee({},v),{selected:t.selected}),g=t.showSelectAll&&e.createElement("div",h,e.createElement($,{id:d,checked:t.selectAll,onChange:p,role:"checkbox","aria-checked":t.selectAll,icon:y,pt:r("headerCheckbox"),unstyled:i()}),!t.filter&&e.createElement("label",m,t.selectAllLabel)),O=n({className:o("closeIcon"),"aria-hidden":!0},u("closeIcon")),E=S.getJSXIcon(t.closeIcon||e.createElement(b,O),ee({},O),{props:t}),k=n({className:o("header")},u("header")),w=n({type:"button",className:o("closeButton"),"aria-label":l("close"),onClick:t.onClose},u("closeButton")),C=e.createElement("button",w,E,e.createElement(j,null)),N=e.createElement("div",k,g,f,C);return t.template?x.getJSXElement(t.template,{className:"p-multiselect-header",checkboxElement:g,checked:t.selectAll,onChange:p,filterElement:f,closeElement:C,closeElementClassName:"p-multiselect-close p-link",closeIconClassName:"p-multiselect-close-icon",onCloseClick:t.onClose,element:N,itemCheckboxIcon:y,props:t}):N}));function ne(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function le(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ne(Object(n),!0).forEach((function(t){R(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ne(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}te.displayName="MultiSelectHeader";var re=e.memo((function(t){var n=q(e.useState(!1),2),l=n[0],r=n[1],o=e.useRef(null),i=c(),a=t.ptm,u=t.cx,s=t.isUnstyled,p=function(e){return a(e,{hostName:t.hostName,context:{selected:t.selected,disabled:t.disabled,focused:l,focusedIndex:t.focusedIndex,index:t.index}})},f=i({className:u("checkboxIcon")},p("checkbox.icon")),d=t.selected?S.getJSXIcon(t.checkboxIcon||e.createElement(F,f),le({},f),{selected:t.selected}):null,m=t.template?x.getJSXElement(t.template,t.option):t.label,v=i({className:u("checkboxContainer")},p("checkboxContainer")),b=i({className:O(t.className,t.option.className,u("item",{itemProps:t})),style:t.style,onClick:function(e){t.onClick&&t.onClick(e,t.option),e.preventDefault(),e.stopPropagation()},onFocus:function(e){var t;r(!0),null==o||null===(t=o.current)||void 0===t||t.getInput().focus()},onBlur:function(e){r(!1)},onMouseMove:function(e){return null==t?void 0:t.onMouseMove(e,t.index)},role:"option","aria-selected":t.selected,"data-p-highlight":t.selected,"data-p-disabled":t.disabled},p("item"));return e.createElement("li",L({},b,{key:t.index+"_multiselectitem"}),e.createElement("div",v,e.createElement($,{ref:o,checked:t.selected,icon:d,pt:a("checkbox"),unstyled:s(),tabIndex:-1})),e.createElement("span",null,m),e.createElement(j,null))}));function oe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function ie(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?oe(Object(n),!0).forEach((function(t){R(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):oe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}re.displayName="MultiSelectItem";var ae=e.memo(e.forwardRef((function(t,l){var o=e.useRef(null),i=e.useRef(null),a=c(),u=e.useContext(n),s=t.ptm,p=t.cx,f=t.sx,d=t.isUnstyled,m=function(e,n){return s(e,ie({hostName:t.hostName},n))},v=function(){t.onEnter((function(){if(o.current){var e=t.getSelectedOptionIndex();-1!==e&&setTimeout((function(){return o.current.scrollToIndex(e)}),0)}}))},b=function(){t.onEntered((function(){t.filter&&t.filterInputAutoFocus&&i.current&&E.focus(i.current,!1)}))},h=function(e){o.current&&o.current.scrollToIndex(0),t.onFilterInputChange&&t.onFilterInputChange(e)},y=function(){if(t.panelFooterTemplate){var n=x.getJSXElement(t.panelFooterTemplate,t,t.onOverlayHide);return e.createElement("div",{className:"p-multiselect-footer"},n)}return null},g=function(e,n){var l;t.focusOnHover&&(null==t||null===(l=t.changeFocusedOptionIndex)||void 0===l||l.call(t,e,n))},S=function(){var n=x.getJSXElement(t.emptyFilterMessage,t)||r("emptyFilterMessage"),l=a({className:p("emptyMessage")},m("emptyMessage"));return e.createElement("li",L({},l,{key:"emptyFilterMessage"}),n)},I=function(n,l){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o={height:r.props?r.props.itemSize:void 0};if(!0===n.group&&t.optionGroupLabel){var i=t.optionGroupTemplate?x.getJSXElement(t.optionGroupTemplate,n,l):t.getOptionGroupLabel(n),c=l+"_"+t.getOptionGroupRenderKey(n),u=a({className:p("itemGroup"),style:f("itemGroup",{scrollerOptions:r})},m("itemGroup"));return e.createElement("li",L({key:c},u),i)}var v=t.getOptionLabel(n),b=l+"_"+t.getOptionRenderKey(n),h=t.isOptionDisabled(n),y=t.isSelected(n);return e.createElement(re,{hostName:t.hostName,key:b,focusedOptionIndex:t.focusedOptionIndex,label:v,option:n,style:o,index:l,template:t.itemTemplate,selected:y,onClick:t.onOptionSelect,onMouseMove:g,disabled:h,className:t.itemClassName,checkboxIcon:t.checkboxIcon,isUnstyled:d,ptm:s,cx:p})},k=function(){if(t.virtualScrollerOptions){var n=ie(ie({},t.virtualScrollerOptions),{style:ie(ie({},t.virtualScrollerOptions.style),{height:t.scrollHeight}),className:O("p-multiselect-items-wrapper",t.virtualScrollerOptions.className),items:t.visibleOptions,autoSize:!0,onLazyLoad:function(e){return t.virtualScrollerOptions.onLazyLoad(ie(ie({},e),{filter:t.filterValue}))},itemTemplate:function(e,t){return e&&I(e,t.index,t)},contentTemplate:function(n){var l=t.visibleOptions&&t.visibleOptions.length||!t.hasFilter?n.children:S(),r=a({ref:n.contentRef,style:n.style,className:O(n.className,p("list",{virtualScrollerProps:t.virtualScrollerOptions})),role:"listbox","aria-multiselectable":!0},m("list"));return e.createElement("ul",r,l)}});return e.createElement(N,L({ref:o},n,{pt:s("virtualScroller"),__parentMetadata:{parent:t.metaData}}))}var l,i,c=x.isNotEmpty(t.visibleOptions)?t.visibleOptions.map(I):t.hasFilter?S():(l=x.getJSXElement(t.emptyMessage,t)||r("emptyMessage"),i=a({className:p("emptyMessage")},m("emptyMessage")),e.createElement("li",L({},i,{key:"emptyMessage"}),l)),u=a({className:p("wrapper"),style:{maxHeight:t.scrollHeight}},m("wrapper")),f=a({className:p("list"),role:"listbox","aria-multiselectable":!0},m("list"));return e.createElement("div",u,e.createElement("ul",f,c))},F=function(){var n=t.allowOptionSelect(),r=e.createElement(te,{hostName:t.hostName,id:t.id,filter:t.filter,filterRef:i,filterValue:t.filterValue,filterTemplate:t.filterTemplate,visibleOptions:t.visibleOptions,isValidOption:t.isValidOption,getOptionValue:t.getOptionValue,updateModel:t.updateModel,onFilter:h,filterPlaceholder:t.filterPlaceholder,onClose:t.onCloseClick,showSelectAll:t.showSelectAll,selectAll:t.isAllSelected(),selectAllLabel:t.selectAllLabel,onSelectAll:t.onSelectAll,template:t.panelHeaderTemplate,resetFilter:t.resetFilter,closeIcon:t.closeIcon,filterIcon:t.filterIcon,itemCheckboxIcon:t.itemCheckboxIcon,ptm:s,cx:p,isUnstyled:d,metaData:t.metaData}),o=k(),c=y(),f=a({className:O(t.panelClassName,p("panel",{panelProps:t,context:u,allowOptionSelect:n})),style:t.panelStyle,onClick:t.onClick},m("panel"));if(t.inline)return e.createElement("div",L({ref:l},f),o,c);var g=a({classNames:p("transition"),in:t.in,timeout:{enter:120,exit:100},options:t.transitionOptions,appear:!0,unmountOnExit:!0,onEnter:v,onEntered:b,onExit:t.onExit,onExited:t.onExited},m("transition")),x=a({ref:t.firstHiddenFocusableElementOnOverlay,role:"presentation",className:"p-hidden-accessible p-hidden-focusable",tabIndex:"0",onFocus:t.onFirstHiddenFocus,"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0},s("hiddenFirstFocusableEl")),E=a({ref:t.lastHiddenFocusableElementOnOverlay,role:"presentation",className:"p-hidden-accessible p-hidden-focusable",tabIndex:"0",onFocus:t.onLastHiddenFocus,"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0},s("hiddenLastFocusableEl"));return e.createElement(w,L({nodeRef:l},g),e.createElement("div",L({ref:l},f),e.createElement("span",x),r,o,c,e.createElement("span",E)))}();return t.inline?F:e.createElement(C,{element:F,appendTo:t.appendTo})})));function ce(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function ue(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ce(Object(n),!0).forEach((function(t){R(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ce(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function se(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=pe(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var l=0,r=function(){};return{s:r,n:function(){return l>=e.length?{done:!0}:{done:!1,value:e[l++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==n.return||n.return()}finally{if(a)throw o}}}}function pe(e,t){if(e){if("string"==typeof e)return fe(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?fe(e,t):void 0}}function fe(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,l=Array(t);n<t;n++)l[n]=e[n];return l}ae.displayName="MultiSelectPanel";var de=e.memo(e.forwardRef((function(l,i){var I=c(),w=e.useContext(n),C=z.getProps(l,w),N=q(e.useState(null),2),F=N[0],P=N[1],D=q(e.useState(!1),2),j=D[0],A=D[1],M=q(p("",C.filterDelay||0),3),T=M[0],V=M[1],B=M[2],_=q(e.useState(-1),2),U=_[0],W=_[1],Y=q(e.useState(!1),2),Z=Y[0],$=Y[1],Q=q(e.useState(C.inline),2),ee=Q[0],te=Q[1],ne=e.useRef(null),le=e.useRef(null),re=e.useRef(null),oe=e.useRef(null),ie=e.useRef(null),ce=e.useRef(C.inputRef),pe=e.useRef(null),fe=e.useRef(null),de=e.useRef(null),me=V&&V.trim().length>0,ve=x.isEmpty(C.value),be=C.optionValue?null:C.dataKey,he={props:C,state:{filterState:V,focused:Z,overlayVisible:ee}},ye=z.setMetaData(he),ge=ye.ptm,Oe=ye.cx,xe=ye.sx,Ee=ye.isUnstyled;a(z.css.styles,Ee,{name:"multiselect"});var Se=q(f({target:ne,overlay:fe,listener:function(e,t){t.valid&&("outside"===t.type?Ue(e)||qe(e)||Be():w.hideOverlaysOnDocumentScrolling?Be():E.isDocument(e.target)||_e())},when:ee}),2),Ie=Se[0],ke=Se[1],we=function(){return!C.selectionLimit||!C.value||C.value&&C.value.length<C.selectionLimit},Ce=function(e){var t=ot()&&e<Pt.length-1?Pt.slice(e+1).findIndex((function(e){return ut(e)})):-1;return t>-1?t+e+1:-1},Ne=function(e){var t=ot()&&e>0?x.findLastIndex(Pt.slice(0,e),(function(e){return ut(e)})):-1;return t>-1?t:-1},Fe=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=-1;return ot()&&(n=t?-1===(n=Ne(e))?Ce(e):n:-1===(n=Ce(e))?Ne(e):n),n>-1?n:e},Pe=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;if(-1===t&&(t=Fe(n,!0)),-1===n&&(n=Fe(t)),-1!==t&&-1!==n){var l=Math.min(t,n),r=Math.max(t,n),o=Pt.slice(l,r+1).filter((function(e){return ct(e)})).map((function(e){return et(e)}));Ke(e,o,o)}},De=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;if(!C.disabled&&!nt(t)){var l,r=null;r=Ye(t)?C.value.filter((function(e){return!x.equals(e,et(t),be)})):[].concat(H(l=C.value||[])||K(l)||G(l)||J(),[et(t)]),Ke(e,r,t),-1!==n&&P(n)}},je=function(e){if(ee){var t=-1!==F?vt(F):j?dt():pt();e.shiftKey&&Pe(e,U,t),yt(e,t)}else Xe(),C.editable&&yt(e,st());e.preventDefault()},Le=function(e){if(e.altKey&&!(arguments.length>1&&void 0!==arguments[1]&&arguments[1]))-1!==F&&De(e,Pt[F]),ee&&Be(),e.preventDefault();else{var t=-1!==F?bt(F):j?mt():ft();yt(e,t),!ee&&Xe(),e.preventDefault()}},Ae=function(e){ee?-1!==F&&(e.shiftKey?Pe(e,F):De(e,Pt[F])):(P(-1),je(e)),e.preventDefault()},Me=function(e){var t=e.currentTarget;if(arguments.length>1&&void 0!==arguments[1]&&arguments[1]){t.setSelectionRange(0,e.shiftKey?t.value.length:0),P(-1)}else{var n=e.metaKey||e.ctrlKey,l=dt();e.shiftKey&&n&&Pe(e,l,U),yt(e,l),!ee&&Xe()}e.preventDefault()},Te=function(e){var t=e.currentTarget;if(arguments.length>1&&void 0!==arguments[1]&&arguments[1]){var n=t.value.length;t.setSelectionRange(e.shiftKey?0:n,n),X("focusedOptionIndex")}else{var l=e.metaKey||e.ctrlKey,r=mt();e.shiftKey&&l&&Pe(e,U,r),yt(e,r),!ee&&Xe()}e.preventDefault()},Re=function(e){e.preventDefault()},Ve=function(e){e.preventDefault()},He=function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1]||(ee&&it()?(E.focus(e.shiftKey?ie.current:oe.current),e.preventDefault()):(-1!==F&&De(e,Pt[F]),ee&&Be(filter)))},Ke=function(e,t,n){C.onChange&&(C.onChange({originalEvent:e,value:t,selectedOption:n,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{name:C.name,id:C.id,value:t}}),E.focus(ce.current))},Ge=function(){B(""),C.onFilter&&C.onFilter({filter:""})},Je=function(e){var t;ee&&((t=e?e.currentTarget:E.findSingle(fe.current,'li[data-p-highlight="true"]'))&&t.scrollIntoView&&t.scrollIntoView({block:"nearest",inline:"nearest"}))},Xe=function(){te(!0),P(-1!==F?F:C.autoOptionFocus?pt():st()),E.focus(ce.current)},Be=function(){P(-1),te(!1),A(!1)},_e=function(){!C.inline&&E.alignOverlay(fe.current,pe.current.parentElement,C.appendTo||w&&w.appendTo||t.appendTo)},Ue=function(e){return"clearicon"===E.getAttribute(e.target,"data-pc-section")},qe=function(e){return"headercheckboxcontainer"===E.getAttribute(e.target,"data-pc-section")},ze=function(e){return fe.current&&fe.current.contains(e.target)},We=function(e,t){return t.findIndex((function(t){return e.some((function(e){return x.equals(e,et(t),be)}))}))},Ye=function(e){if(C.value){var t=et(e),n=lt(e);return C.value.some((function(e){return x.equals(n?e:et(e),t,be)}))}return!1},Ze=function(e){var t;if(C.options)if(C.optionGroupLabel){var n,l=se(C.options);try{for(l.s();!(n=l.n()).done;){if(t=$e(e,tt(n.value)))break}}catch(e){l.e(e)}finally{l.f()}}else t=$e(e,C.options),x.isEmpty(t)&&(t=$e(e,C.value));return t?Qe(t):null},$e=function(e,t){return t.find((function(t){return x.equals(et(t),e,be)}))},Qe=function(e){return C.optionLabel?x.resolveFieldData(e,C.optionLabel):e&&void 0!==e.label?e.label:e},et=function(e){return C.useOptionAsValue?e:C.optionValue?x.resolveFieldData(e,C.optionValue):e&&void 0!==e.value?e.value:e},tt=function(e){return x.resolveFieldData(e,C.optionGroupChildren)},nt=function(e){var t;if(!we()&&!Ye(e))return!0;var n=C.optionDisabled;return n?x.isFunction(n)?n(e):x.resolveFieldData(e,n):e&&null!==(t=e.disabled)&&void 0!==t&&t},lt=function(e){return!C.useOptionAsValue&&C.optionValue||e&&void 0!==e.value},rt=function(e){return C.optionGroupLabel&&e.group},ot=function(){return x.isNotEmpty(C.value)},it=function(){return E.getFocusableElements(fe.current,':not([data-p-hidden-focusable="true"])').length>0},at=function(e){var t;return ct(e)&&(null===(t=Qe(e))||void 0===t?void 0:t.toLocaleLowerCase(C.filterLocale).startsWith(le.current.toLocaleLowerCase(C.filterLocale)))},ct=function(e){return x.isNotEmpty(e)&&!(nt(e)||rt(e))},ut=function(e){return ct(e)&&Ye(e)},st=function(){if(ot())for(var e,t=function(){var e=C.value[n],t=Pt.findIndex((function(t){return ut(t)&&(n=e,l=et(t),x.equals(n,l,be));var n,l}));if(t>-1)return{v:t}},n=C.value.length-1;n>=0;n--)if(e=t())return e.v;return-1},pt=function(){var e=st();return e<0?dt():e},ft=function(){var e=st();return e<0?mt():e},dt=function(){return Pt.findIndex((function(e){return ct(e)}))},mt=function(){return x.findLastIndex(Pt,(function(e){return ct(e)}))},vt=function(e){var t=e<Pt.length-1?Pt.slice(e+1).findIndex((function(e){return ct(e)})):-1;return t>-1?t+e+1:e},bt=function(e){var t=e>0?x.findLastIndex(Pt.slice(0,e),(function(e){return ct(e)})):-1;return t>-1?t:e},ht=function(e){le.current=(le.current||"")+e.key;var t=-1;x.isNotEmpty(le.current)&&(-1===(t=-1!==F?-1===(t=Pt.slice(F).findIndex((function(e){return at(e)})))?Pt.slice(0,F).findIndex((function(e){return at(e)})):t+F:Pt.findIndex((function(e){return at(e)})))&&-1===F&&(t=pt()),-1!==t&&yt(e,t)),re.current&&clearTimeout(re.current),re.current=setTimeout((function(){le.current="",re.current=null}),500)},yt=function(e,t){F!==t&&(P(t),Je(e),C.selectOnFocus&&De(e,Pt[t],!1))},gt=function(e,t){if(e.stopPropagation(),Ot(e.currentTarget)){var n=C.value.filter((function(e){return!x.equals(e,t,be)}));C.onRemove&&C.onRemove({originalEvent:e,value:n}),Ke(e,n,t)}},Ot=function(e){var t=de.current;if(!(t.clientWidth<t.scrollWidth))return!0;var n=e.closest('[data-pc-section="token"]'),l=window.getComputedStyle(t),r=window.getComputedStyle(n),o=t.clientWidth-parseFloat(l.paddingLeft)-parseFloat(l.paddingRight);return n.getBoundingClientRect().right+parseFloat(r.marginRight)-t.getBoundingClientRect().left<=o},xt=function(){var e=/{(.*?)}/,t=C.selectedItemsLabel||r("selectionMessage"),n=C.value?C.value.length:0;return e.test(t)?t.replace(t.match(e)[0],n+""):t},Et=function(){var e;if(!ve&&!C.fixedPlaceholder)return x.isNotEmpty(C.maxSelectedLabels)&&(null===(e=C.value)||void 0===e?void 0:e.length)>C.maxSelectedLabels?xt():x.isArray(C.value)?C.value.reduce((function(e,t,n){return e+(0!==n?", ":"")+Ze(t)}),""):""},St=function(e){return(e||[]).reduce((function(e,t,n){e.push(ue(ue({},t),{},{group:!0,index:n}));var l=tt(t);return l&&l.forEach((function(t){return e.push(t)})),e}),[])},It=function(e){switch(e.code){case"Space":case"NumpadEnter":case"Enter":if(C.inline)break;Ke(e,[],[]),e.preventDefault(),e.stopPropagation()}},kt=function(e,t){switch(e.code){case"Space":case"NumpadEnter":case"Enter":if(C.inline)break;gt(e,t),e.preventDefault(),e.stopPropagation()}};e.useImperativeHandle(i,(function(){return{props:C,show:Xe,hide:Be,focus:function(){return E.focus(ce.current)},getElement:function(){return ne.current},getOverlay:function(){return fe.current},getInput:function(){return ce.current}}})),s((function(){_e()})),e.useEffect((function(){x.combinedRefs(ce,C.inputRef)}),[ce,C.inputRef]),e.useEffect((function(){!0===C.overlayVisible?Xe():!1===C.overlayVisible&&Be()}),[C.overlayVisible]),u((function(){ee&&V&&me&&_e()}),[ee,V,me]),d((function(){k.clear(fe.current)}));var wt,Ct,Nt,Ft,Pt=function(){var e=C.optionGroupLabel?St(C.options):C.options;if(me){var t=V.trim().toLocaleLowerCase(C.filterLocale),n=C.filterBy?C.filterBy.split(","):[C.optionLabel||"label"];if(C.optionGroupLabel){var l,r=[],i=se(C.options);try{for(i.s();!(l=i.n()).done;){var a=l.value,c=o.filter(tt(a),n,t,C.filterMatchMode,C.filterLocale);c&&c.length&&r.push(ue(ue({},a),R({},C.optionGroupChildren,c)))}}catch(e){i.e(e)}finally{i.f()}return St(r)}return o.filter(e,n,t,C.filterMatchMode,C.filterLocale)}return e}(),Dt=x.isNotEmpty(C.tooltip),jt=z.getOtherProps(C),Lt=x.reduceKeys(jt,E.ARIA_PROPS),At=I({className:Oe("triggerIcon")},ge("triggerIcon")),Mt=I({className:Oe("trigger")},ge("trigger")),Tt=C.loadingIcon?S.getJSXIcon(C.loadingIcon,ue({},At),{props:C}):e.createElement(v,L({spin:!0},At)),Rt=C.dropdownIcon?S.getJSXIcon(C.dropdownIcon,ue({},At),{props:C}):e.createElement(m,At),Vt=e.createElement("div",Mt,C.loading?Tt:Rt),Ht=!C.inline&&(wt=C.value?C.value.length:0,Ct=x.isNotEmpty(C.maxSelectedLabels)&&wt>C.maxSelectedLabels?xt():C.selectedItemTemplate?ve?x.getJSXElement(C.selectedItemTemplate):C.value.map((function(t,n){var l=x.getJSXElement(C.selectedItemTemplate,t);return e.createElement(e.Fragment,{key:n},l)})):"chip"!==C.display||ve?Et():C.value.slice(0,C.maxSelectedLabels||wt).map((function(t,n){var l={context:{value:t,index:n}},o=Ze(t),i=o+"_"+n,a=I({"aria-label":r("removeTokenIcon"),className:Oe("removeTokenIcon"),onClick:function(e){return gt(e,t)},onKeyDown:function(e){return kt(e,t)},tabIndex:C.tabIndex||"0"},ge("removeTokenIcon",l)),c=!C.disabled&&(C.removeIcon?S.getJSXIcon(C.removeIcon,ue({},a),{props:C}):e.createElement(h,a)),u=I({className:Oe("token")},ge("token",l)),s=I({className:Oe("tokenLabel")},ge("tokenLabel",l));return e.createElement("div",L({},u,{key:i}),e.createElement("span",s,o),c)})),Nt=I({ref:pe,className:Oe("labelContainer")},ge("labelContainer")),Ft=I({ref:de,className:Oe("label",{empty:ve})},ge("label")),e.createElement("div",Nt,e.createElement("div",Ft,Ct||C.placeholder||C.emptyMessage||"empty"))),Kt=!C.inline&&function(){var t=I({className:Oe("clearIcon"),"aria-label":r("clear"),onClick:function(e){return Ke(e,[],[])},onKeyDown:function(e){return It(e)},tabIndex:C.tabIndex||"0"},ge("clearIcon")),n=S.getJSXIcon(C.clearIcon||e.createElement(b,t),ue({},t),{props:C});return ve||!C.showClear||C.disabled?null:n}(),Gt=I(ue(ue({ref:ne,id:C.id,style:ue(ue({},C.style),xe("root")),className:O(C.className,Oe("root",{focusedState:Z,context:w,overlayVisibleState:ee}))},jt),{},{onClick:function(e){C.inline||C.disabled||C.loading||ze(e)||Ue(e)||(ee?Be():Xe(),E.focus(ce.current),e.preventDefault()),A(!0)}}),z.getOtherProps(C),ge("root")),Jt=I({className:"p-hidden-accessible","data-p-hidden-accessible":!0},ge("hiddenInputWrapper")),Xt=I(ue({ref:ce,id:C.inputId,name:C.name,type:"text",onFocus:function(e){$(!0),C.onFocus&&C.onFocus(e)},onBlur:function(e){$(!1),C.onBlur&&C.onBlur(e)},onKeyDown:function(e){var t=e.metaKey||e.ctrlKey;switch(e.code){case"ArrowUp":if(C.inline)break;Le(e);break;case"ArrowDown":if(C.inline)break;je(e);break;case"Space":case"NumpadEnter":case"Enter":if(C.inline)break;Ae(e);break;case"Home":if(C.inline)break;Me(e),e.preventDefault();break;case"End":if(C.inline)break;Te(e),e.preventDefault();break;case"PageDown":Ve(e);break;case"PageUp":Re(e);break;case"Escape":if(C.inline)break;Be();break;case"Tab":He(e);break;case"ShiftLeft":case"ShiftRight":W(F);break;default:if("a"===e.key&&t){var n=Pt.filter((function(e){return ct(e)})).map((function(e){return et(e)}));Ke(e,n,n),e.preventDefault();break}!t&&x.isPrintableCharacter(e.key)&&(!ee&&Xe(),ht(e),e.preventDefault())}A(!1)},role:"combobox","aria-expanded":ee,disabled:C.disabled,tabIndex:C.disabled?-1:C.tabIndex,value:Et()},Lt),ge("input"));return e.createElement(e.Fragment,null,e.createElement("div",Gt,e.createElement("div",Jt,e.createElement("input",L({},Xt,{readOnly:!0}))),!C.inline&&e.createElement(e.Fragment,null,Ht,Kt,Vt),e.createElement(ae,L({hostName:"MultiSelect",ref:fe,visibleOptions:Pt},C,{onClick:function(e){y.emit("overlay-click",{originalEvent:e,target:ne.current})},onOverlayHide:Be,filterValue:T,focusedOptionIndex:F,onFirstHiddenFocus:function(e){var t=e.relatedTarget===ce.current?E.getFirstFocusableElement(fe.current,':not([data-p-hidden-focusable="true"])'):ce.current;E.focus(t)},onLastHiddenFocus:function(e){var t=e.relatedTarget===ce.current?E.getLastFocusableElement(fe.current,':not([data-p-hidden-focusable="true"])'):ce.current;E.focus(t)},firstHiddenFocusableElementOnOverlay:oe,lastHiddenFocusableElementOnOverlay:ie,setFocusedOptionIndex:P,hasFilter:me,isValidOption:ct,getOptionValue:et,updateModel:Ke,onFilterInputChange:function(e){var t=e.query;B(t),C.onFilter&&C.onFilter({originalEvent:e,filter:t})},resetFilter:Ge,onCloseClick:function(e){Be(),E.focus(ce.current),e.preventDefault(),e.stopPropagation()},onSelectAll:function(e){if(C.onSelectAll)C.onSelectAll(e);else{var t=null;if(e.checked)t=[];else{var n=Pt.filter((function(e){return ct(e)&&!nt(e)}));n&&(t=n.map((function(e){return et(e)})))}C.selectionLimit&&t&&t.length&&(t=t.slice(0,C.selectionLimit)),Ke(e.originalEvent,t,t)}},getOptionLabel:Qe,getOptionRenderKey:function(e){return C.dataKey?x.resolveFieldData(e,C.dataKey):Qe(e)},isOptionDisabled:nt,getOptionGroupChildren:tt,getOptionGroupLabel:function(e){return x.resolveFieldData(e,C.optionGroupLabel)},getOptionGroupRenderKey:function(e){return x.resolveFieldData(e,C.optionGroupLabel)},isSelected:Ye,getSelectedOptionIndex:function(){if(null!=C.value&&C.options){if(C.optionGroupLabel){var e=0,t=C.options.findIndex((function(t,n){return(e=n)&&-1!==We(C.value,tt(t))}));return-1!==t?{group:e,option:t}:-1}return We(C.value,C.options)}return-1},isAllSelected:function(){return C.onSelectAll?C.selectAll:!x.isEmpty(Pt)&&!Pt.filter((function(e){return!nt(e)&&ct(e)})).some((function(e){return!Ye(e)}))},onOptionSelect:De,allowOptionSelect:we,in:ee,onEnter:function(e){k.set("overlay",fe.current,w&&w.autoZIndex||t.autoZIndex,w&&w.zIndex.overlay||t.zIndex.overlay),E.addStyles(fe.current,{position:"absolute",top:"0",left:"0"}),_e(),Je(),e&&e()},onEntered:function(e){e&&e(),Ie(),C.onShow&&C.onShow()},onExit:function(){ke()},onExited:function(){C.filter&&C.resetFilterOnHide&&Ge(),k.clear(fe.current),C.onHide&&C.onHide()},ptm:ge,cx:Oe,sx:xe,isUnstyled:Ee,metaData:he,changeFocusedOptionIndex:yt}))),Dt&&e.createElement(g,L({target:ne,content:C.tooltip,pt:ge("tooltip")},C.tooltipOptions)))})));de.displayName="MultiSelect";export{de as MultiSelect};
