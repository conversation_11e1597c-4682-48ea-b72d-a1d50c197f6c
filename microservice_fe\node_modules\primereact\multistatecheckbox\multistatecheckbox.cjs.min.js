"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("primereact/api"),n=require("primereact/componentbase"),r=require("primereact/hooks"),o=require("primereact/icons/check"),l=require("primereact/tooltip"),a=require("primereact/utils");function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var c=i(e);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(null,arguments)}function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function p(e,t){if("object"!=s(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function d(e){var t=p(e,"string");return"symbol"==s(t)?t:t+""}function f(e,t,n){return(t=d(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function b(e){if(Array.isArray(e))return e}function m(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,l,a,i=[],c=!0,u=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=l.call(n)).done)&&(i.push(r.value),i.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return i}}function y(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function v(e,t){if(e){if("string"==typeof e)return y(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?y(e,t):void 0}}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function O(e,t){return b(e)||m(e,t)||v(e,t)||h()}var g=n.ComponentBase.extend({defaultProps:{__TYPE:"Checkbox",autoFocus:!1,checked:!1,className:null,disabled:!1,falseValue:!1,icon:null,id:null,inputId:null,inputRef:null,invalid:!1,variant:null,name:null,onChange:null,onContextMenu:null,onMouseDown:null,readOnly:!1,required:!1,style:null,tabIndex:null,tooltip:null,tooltipOptions:null,trueValue:!0,value:null,children:void 0},css:{classes:{box:"p-checkbox-box",input:"p-checkbox-input",icon:"p-checkbox-icon",root:function(e){var t=e.props,n=e.context;return a.classNames("p-checkbox p-component",{"p-highlight":e.checked,"p-disabled":t.disabled,"p-invalid":t.invalid,"p-variant-filled":t.variant?"filled"===t.variant:n&&"filled"===n.inputStyle})}}}});function x(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function j(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?x(Object(n),!0).forEach((function(t){f(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):x(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var P=c.memo(c.forwardRef((function(e,i){var s=r.useMergeProps(),p=c.useContext(t.PrimeReactContext),d=g.getProps(e,p),f=O(c.useState(!1),2),b=f[1],m=g.setMetaData({props:d,state:{focused:f[0]},context:{checked:d.checked===d.trueValue,disabled:d.disabled}}),y=m.ptm,v=m.cx;n.useHandleStyle(g.css.styles,m.isUnstyled,{name:"checkbox"});var h=c.useRef(null),x=c.useRef(d.inputRef),P=function(){return d.checked===d.trueValue},k=function(e){if(!d.disabled&&!d.readOnly&&d.onChange){var t,n=P()?d.falseValue:d.trueValue;if(null==d||null===(t=d.onChange)||void 0===t||t.call(d,{originalEvent:e,value:d.value,checked:n,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{type:"checkbox",name:d.name,id:d.id,value:d.value,checked:n}}),e.defaultPrevented)return;a.DomHandler.focus(x.current)}};c.useImperativeHandle(i,(function(){return{props:d,focus:function(){return a.DomHandler.focus(x.current)},getElement:function(){return h.current},getInput:function(){return x.current}}})),c.useEffect((function(){a.ObjectUtils.combinedRefs(x,d.inputRef)}),[x,d.inputRef]),r.useUpdateEffect((function(){x.current.checked=P()}),[d.checked,d.trueValue]),r.useMountEffect((function(){d.autoFocus&&a.DomHandler.focus(x.current,d.autoFocus)}));var E,D,S,N,w,C=P(),I=a.ObjectUtils.isNotEmpty(d.tooltip),M=g.getOtherProps(d),R=s({id:d.id,className:a.classNames(d.className,v("root",{checked:C,context:p})),style:d.style,"data-p-highlight":C,"data-p-disabled":d.disabled,onContextMenu:d.onContextMenu,onMouseDown:d.onMouseDown},M,y("root"));return c.createElement(c.Fragment,null,c.createElement("div",u({ref:h},R),(N=a.ObjectUtils.reduceKeys(M,a.DomHandler.ARIA_PROPS),w=s(j({id:d.inputId,type:"checkbox",className:v("input"),name:d.name,tabIndex:d.tabIndex,onFocus:function(e){return t=e,b(!0),void(null==d||null===(n=d.onFocus)||void 0===n||n.call(d,t));var t,n},onBlur:function(e){return t=e,b(!1),void(null==d||null===(n=d.onBlur)||void 0===n||n.call(d,t));var t,n},onChange:function(e){return k(e)},disabled:d.disabled,readOnly:d.readOnly,required:d.required,"aria-invalid":d.invalid,checked:C},N),y("input")),c.createElement("input",u({ref:x},w))),(E=s({className:v("icon")},y("icon")),D=s({className:v("box",{checked:C}),"data-p-highlight":C,"data-p-disabled":d.disabled},y("box")),S=a.IconUtils.getJSXIcon(C?d.icon||c.createElement(o.CheckIcon,E):null,j({},E),{props:d,checked:C}),c.createElement("div",D,S))),I&&c.createElement(l.Tooltip,u({target:h,content:d.tooltip,pt:y("tooltip")},d.tooltipOptions)))})));P.displayName="Checkbox";var k=n.ComponentBase.extend({defaultProps:{__TYPE:"MultiStateCheckbox",autoFocus:!1,className:null,dataKey:null,disabled:!1,empty:!0,iconTemplate:null,id:null,onChange:null,optionIcon:null,optionLabel:null,optionValue:null,options:null,readOnly:!1,style:null,tabIndex:"0",tooltip:null,tooltipOptions:null,value:null,children:void 0},css:{classes:{icon:function(e){return a.classNames("p-checkbox-icon p-c",f({},"".concat(e.icon),!0))},root:function(e){return a.classNames("p-multistatecheckbox p-checkbox p-component",e.props.classNames)}},inlineStyles:{checkbox:function(e){var t=e.selectedOption;return t&&t.style}}}});function E(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function D(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?E(Object(n),!0).forEach((function(t){f(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):E(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var S=c.memo(c.forwardRef((function(e,o){var i=r.useMergeProps(),s=c.useContext(t.PrimeReactContext),p=k.getProps(e,s),d=O(c.useState(!1),2),b=d[0],m=d[1],y=c.useRef(null),v=p.optionValue?null:p.dataKey,h=k.setMetaData({props:p,state:{focused:b}}),g=h.ptm,x=h.cx,j=h.sx;n.useHandleStyle(k.css.styles,h.isUnstyled,{name:"multistatecheckbox"});var E=function(e){p.disabled||p.readOnly||N(e)},S=function(e){return p.optionValue?a.ObjectUtils.resolveFieldData(e,p.optionValue):e},N=function(e){if(p.onChange){var t=S(p.options?R===p.options.length-1?p.empty?null:p.options[0]:p.options[R+1]:null);p.onChange({originalEvent:e,value:t,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{name:p.name,id:p.id,value:t}})}};c.useImperativeHandle(o,(function(){return{props:p,focus:function(){return a.DomHandler.focusFirstElement(y.current)},getElement:function(){return y.current}}})),r.useMountEffect((function(){p.empty||null!==p.value||N(),p.autoFocus&&a.DomHandler.focusFirstElement(y.current)}));var w,C,I=(p.options&&(C=p.options.findIndex((function(e){return a.ObjectUtils.equals(p.value,S(e),v)})),w=p.options[C]),{option:w,index:C}),M=I.option,R=I.index,U=a.ObjectUtils.isNotEmpty(p.tooltip),F=k.getOtherProps(p),V=a.ObjectUtils.reduceKeys(F,a.DomHandler.ARIA_PROPS),q=function(){var e=M&&a.ObjectUtils.resolveFieldData(M,p.optionIcon||"icon")||"",t=a.classNames("p-checkbox-icon p-c",f({},"".concat(e),!0)),n=i({className:x("icon",{icon:e})},g("icon")),r=a.IconUtils.getJSXIcon(e,D({},n),{props:p});return p.iconTemplate?a.ObjectUtils.getJSXElement(p.iconTemplate,{option:M,className:t,element:r,props:p}):r}(),A=M?function(e){var t=p.optionLabel||p.optionValue;return t?a.ObjectUtils.resolveFieldData(e,t):e}(M):t.ariaLabel("nullLabel"),H=M?"true":"false",_=i({ref:y,id:p.id,className:a.classNames(p.className,x("root")),style:p.style,onClick:E},k.getOtherProps(p),g("root")),T=i(D({className:a.classNames(p.className),style:j("checkbox",{selectedOption:M}),tabIndex:p.tabIndex,onFocus:function(){m(!0)},onBlur:function(){m(!1)},onKeyDown:function(e){32===e.keyCode&&(N(e),e.preventDefault())},role:"checkbox","aria-checked":H,onChange:E,checked:!!M,disabled:null==p?void 0:p.disabled,icon:q},V),g("checkbox")),B=i({className:"p-hidden-accessible","aria-live":"polite"},g("srOnlyAria"));return c.createElement(c.Fragment,null,c.createElement("div",_,c.createElement(P,T),b&&c.createElement("span",B,A)),U&&c.createElement(l.Tooltip,u({target:y,content:p.tooltip,pt:g("tooltip")},p.tooltipOptions)))})));S.displayName="MultiStateCheckbox",exports.MultiStateCheckbox=S;
