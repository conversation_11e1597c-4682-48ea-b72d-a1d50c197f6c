import*as e from"react";import{PrimeReactContext as t,a<PERSON><PERSON><PERSON><PERSON> as n}from"primereact/api";import{ComponentBase as o,useHandleStyle as r}from"primereact/componentbase";import{useMergeProps as l,useUpdateEffect as i,useMountEffect as a}from"primereact/hooks";import{CheckIcon as c}from"primereact/icons/check";import{Tooltip as u}from"primereact/tooltip";import{classNames as s,<PERSON><PERSON><PERSON><PERSON> as p,ObjectUtils as f,IconUtils as d}from"primereact/utils";function m(){return m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},m.apply(null,arguments)}function b(e){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b(e)}function y(e,t){if("object"!=b(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=b(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function v(e){var t=y(e,"string");return"symbol"==b(t)?t:t+""}function h(e,t,n){return(t=v(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function g(e){if(Array.isArray(e))return e}function O(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,l,i,a=[],c=!0,u=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=l.call(n)).done)&&(a.push(o.value),a.length!==t);c=!0);}catch(e){u=!0,r=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw r}}return a}}function x(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function k(e,t){if(e){if("string"==typeof e)return x(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?x(e,t):void 0}}function P(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function j(e,t){return g(e)||O(e,t)||k(e,t)||P()}var E=o.extend({defaultProps:{__TYPE:"Checkbox",autoFocus:!1,checked:!1,className:null,disabled:!1,falseValue:!1,icon:null,id:null,inputId:null,inputRef:null,invalid:!1,variant:null,name:null,onChange:null,onContextMenu:null,onMouseDown:null,readOnly:!1,required:!1,style:null,tabIndex:null,tooltip:null,tooltipOptions:null,trueValue:!0,value:null,children:void 0},css:{classes:{box:"p-checkbox-box",input:"p-checkbox-input",icon:"p-checkbox-icon",root:function(e){var t=e.props,n=e.context;return s("p-checkbox p-component",{"p-highlight":e.checked,"p-disabled":t.disabled,"p-invalid":t.invalid,"p-variant-filled":t.variant?"filled"===t.variant:n&&"filled"===n.inputStyle})}}}});function S(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?S(Object(n),!0).forEach((function(t){h(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):S(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var D=e.memo(e.forwardRef((function(n,o){var b=l(),y=e.useContext(t),v=E.getProps(n,y),h=j(e.useState(!1),2),g=h[1],O=E.setMetaData({props:v,state:{focused:h[0]},context:{checked:v.checked===v.trueValue,disabled:v.disabled}}),x=O.ptm,k=O.cx;r(E.css.styles,O.isUnstyled,{name:"checkbox"});var P=e.useRef(null),S=e.useRef(v.inputRef),D=function(){return v.checked===v.trueValue},I=function(e){if(!v.disabled&&!v.readOnly&&v.onChange){var t,n=D()?v.falseValue:v.trueValue;if(null==v||null===(t=v.onChange)||void 0===t||t.call(v,{originalEvent:e,value:v.value,checked:n,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{type:"checkbox",name:v.name,id:v.id,value:v.value,checked:n}}),e.defaultPrevented)return;p.focus(S.current)}};e.useImperativeHandle(o,(function(){return{props:v,focus:function(){return p.focus(S.current)},getElement:function(){return P.current},getInput:function(){return S.current}}})),e.useEffect((function(){f.combinedRefs(S,v.inputRef)}),[S,v.inputRef]),i((function(){S.current.checked=D()}),[v.checked,v.trueValue]),a((function(){v.autoFocus&&p.focus(S.current,v.autoFocus)}));var C,N,F,R,V,A=D(),M=f.isNotEmpty(v.tooltip),T=E.getOtherProps(v),_=b({id:v.id,className:s(v.className,k("root",{checked:A,context:y})),style:v.style,"data-p-highlight":A,"data-p-disabled":v.disabled,onContextMenu:v.onContextMenu,onMouseDown:v.onMouseDown},T,x("root"));return e.createElement(e.Fragment,null,e.createElement("div",m({ref:P},_),(R=f.reduceKeys(T,p.ARIA_PROPS),V=b(w({id:v.inputId,type:"checkbox",className:k("input"),name:v.name,tabIndex:v.tabIndex,onFocus:function(e){return t=e,g(!0),void(null==v||null===(n=v.onFocus)||void 0===n||n.call(v,t));var t,n},onBlur:function(e){return t=e,g(!1),void(null==v||null===(n=v.onBlur)||void 0===n||n.call(v,t));var t,n},onChange:function(e){return I(e)},disabled:v.disabled,readOnly:v.readOnly,required:v.required,"aria-invalid":v.invalid,checked:A},R),x("input")),e.createElement("input",m({ref:S},V))),(C=b({className:k("icon")},x("icon")),N=b({className:k("box",{checked:A}),"data-p-highlight":A,"data-p-disabled":v.disabled},x("box")),F=d.getJSXIcon(A?v.icon||e.createElement(c,C):null,w({},C),{props:v,checked:A}),e.createElement("div",N,F))),M&&e.createElement(u,m({target:P,content:v.tooltip,pt:x("tooltip")},v.tooltipOptions)))})));D.displayName="Checkbox";var I=o.extend({defaultProps:{__TYPE:"MultiStateCheckbox",autoFocus:!1,className:null,dataKey:null,disabled:!1,empty:!0,iconTemplate:null,id:null,onChange:null,optionIcon:null,optionLabel:null,optionValue:null,options:null,readOnly:!1,style:null,tabIndex:"0",tooltip:null,tooltipOptions:null,value:null,children:void 0},css:{classes:{icon:function(e){return s("p-checkbox-icon p-c",h({},"".concat(e.icon),!0))},root:function(e){return s("p-multistatecheckbox p-checkbox p-component",e.props.classNames)}},inlineStyles:{checkbox:function(e){var t=e.selectedOption;return t&&t.style}}}});function C(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function N(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?C(Object(n),!0).forEach((function(t){h(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):C(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var F=e.memo(e.forwardRef((function(o,i){var c=l(),b=e.useContext(t),y=I.getProps(o,b),v=j(e.useState(!1),2),g=v[0],O=v[1],x=e.useRef(null),k=y.optionValue?null:y.dataKey,P=I.setMetaData({props:y,state:{focused:g}}),E=P.ptm,S=P.cx,w=P.sx;r(I.css.styles,P.isUnstyled,{name:"multistatecheckbox"});var C=function(e){y.disabled||y.readOnly||R(e)},F=function(e){return y.optionValue?f.resolveFieldData(e,y.optionValue):e},R=function(e){if(y.onChange){var t=F(y.options?_===y.options.length-1?y.empty?null:y.options[0]:y.options[_+1]:null);y.onChange({originalEvent:e,value:t,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{name:y.name,id:y.id,value:t}})}};e.useImperativeHandle(i,(function(){return{props:y,focus:function(){return p.focusFirstElement(x.current)},getElement:function(){return x.current}}})),a((function(){y.empty||null!==y.value||R(),y.autoFocus&&p.focusFirstElement(x.current)}));var V,A,M=(y.options&&(A=y.options.findIndex((function(e){return f.equals(y.value,F(e),k)})),V=y.options[A]),{option:V,index:A}),T=M.option,_=M.index,K=f.isNotEmpty(y.tooltip),q=I.getOtherProps(y),B=f.reduceKeys(q,p.ARIA_PROPS),J=function(){var e=T&&f.resolveFieldData(T,y.optionIcon||"icon")||"",t=s("p-checkbox-icon p-c",h({},"".concat(e),!0)),n=c({className:S("icon",{icon:e})},E("icon")),o=d.getJSXIcon(e,N({},n),{props:y});return y.iconTemplate?f.getJSXElement(y.iconTemplate,{option:T,className:t,element:o,props:y}):o}(),L=T?function(e){var t=y.optionLabel||y.optionValue;return t?f.resolveFieldData(e,t):e}(T):n("nullLabel"),U=T?"true":"false",X=c({ref:x,id:y.id,className:s(y.className,S("root")),style:y.style,onClick:C},I.getOtherProps(y),E("root")),H=c(N({className:s(y.className),style:w("checkbox",{selectedOption:T}),tabIndex:y.tabIndex,onFocus:function(){O(!0)},onBlur:function(){O(!1)},onKeyDown:function(e){32===e.keyCode&&(R(e),e.preventDefault())},role:"checkbox","aria-checked":U,onChange:C,checked:!!T,disabled:null==y?void 0:y.disabled,icon:J},B),E("checkbox")),Y=c({className:"p-hidden-accessible","aria-live":"polite"},E("srOnlyAria"));return e.createElement(e.Fragment,null,e.createElement("div",X,e.createElement(D,H),g&&e.createElement("span",Y,L)),K&&e.createElement(u,m({target:x,content:y.tooltip,pt:E("tooltip")},y.tooltipOptions)))})));F.displayName="MultiStateCheckbox";export{F as MultiStateCheckbox};
