this.primereact=this.primereact||{},this.primereact.multistatecheckbox=function(e,t,n,o,r,l,a,c){"use strict";function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var o=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,o.get?o:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var u=i(t);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},s.apply(null,arguments)}function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function d(e,t){if("object"!=p(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=p(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function f(e){var t=d(e,"string");return"symbol"==p(t)?t:t+""}function b(e,t,n){return(t=f(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e){if(Array.isArray(e))return e}function y(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,l,a,c=[],i=!0,u=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;i=!1}else for(;!(i=(o=l.call(n)).done)&&(c.push(o.value),c.length!==t);i=!0);}catch(e){u=!0,r=e}finally{try{if(!i&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return c}}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function h(e,t){if(e){if("string"==typeof e)return v(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?v(e,t):void 0}}function O(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(e,t){return m(e)||y(e,t)||h(e,t)||O()}var x=o.ComponentBase.extend({defaultProps:{__TYPE:"Checkbox",autoFocus:!1,checked:!1,className:null,disabled:!1,falseValue:!1,icon:null,id:null,inputId:null,inputRef:null,invalid:!1,variant:null,name:null,onChange:null,onContextMenu:null,onMouseDown:null,readOnly:!1,required:!1,style:null,tabIndex:null,tooltip:null,tooltipOptions:null,trueValue:!0,value:null,children:void 0},css:{classes:{box:"p-checkbox-box",input:"p-checkbox-input",icon:"p-checkbox-icon",root:function(e){var t=e.props,n=e.context;return c.classNames("p-checkbox p-component",{"p-highlight":e.checked,"p-disabled":t.disabled,"p-invalid":t.invalid,"p-variant-filled":t.variant?"filled"===t.variant:n&&"filled"===n.inputStyle})}}}});function j(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function k(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?j(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var P=u.memo(u.forwardRef((function(e,t){var i=r.useMergeProps(),p=u.useContext(n.PrimeReactContext),d=x.getProps(e,p),f=g(u.useState(!1),2),b=f[1],m=x.setMetaData({props:d,state:{focused:f[0]},context:{checked:d.checked===d.trueValue,disabled:d.disabled}}),y=m.ptm,v=m.cx;o.useHandleStyle(x.css.styles,m.isUnstyled,{name:"checkbox"});var h=u.useRef(null),O=u.useRef(d.inputRef),j=function(){return d.checked===d.trueValue},P=function(e){if(!d.disabled&&!d.readOnly&&d.onChange){var t,n=j()?d.falseValue:d.trueValue;if(null==d||null===(t=d.onChange)||void 0===t||t.call(d,{originalEvent:e,value:d.value,checked:n,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{type:"checkbox",name:d.name,id:d.id,value:d.value,checked:n}}),e.defaultPrevented)return;c.DomHandler.focus(O.current)}};u.useImperativeHandle(t,(function(){return{props:d,focus:function(){return c.DomHandler.focus(O.current)},getElement:function(){return h.current},getInput:function(){return O.current}}})),u.useEffect((function(){c.ObjectUtils.combinedRefs(O,d.inputRef)}),[O,d.inputRef]),r.useUpdateEffect((function(){O.current.checked=j()}),[d.checked,d.trueValue]),r.useMountEffect((function(){d.autoFocus&&c.DomHandler.focus(O.current,d.autoFocus)}));var E,D,S,N,w,C=j(),I=c.ObjectUtils.isNotEmpty(d.tooltip),M=x.getOtherProps(d),R=i({id:d.id,className:c.classNames(d.className,v("root",{checked:C,context:p})),style:d.style,"data-p-highlight":C,"data-p-disabled":d.disabled,onContextMenu:d.onContextMenu,onMouseDown:d.onMouseDown},M,y("root"));return u.createElement(u.Fragment,null,u.createElement("div",s({ref:h},R),(N=c.ObjectUtils.reduceKeys(M,c.DomHandler.ARIA_PROPS),w=i(k({id:d.inputId,type:"checkbox",className:v("input"),name:d.name,tabIndex:d.tabIndex,onFocus:function(e){return t=e,b(!0),void(null==d||null===(n=d.onFocus)||void 0===n||n.call(d,t));var t,n},onBlur:function(e){return t=e,b(!1),void(null==d||null===(n=d.onBlur)||void 0===n||n.call(d,t));var t,n},onChange:function(e){return P(e)},disabled:d.disabled,readOnly:d.readOnly,required:d.required,"aria-invalid":d.invalid,checked:C},N),y("input")),u.createElement("input",s({ref:O},w))),(E=i({className:v("icon")},y("icon")),D=i({className:v("box",{checked:C}),"data-p-highlight":C,"data-p-disabled":d.disabled},y("box")),S=c.IconUtils.getJSXIcon(C?d.icon||u.createElement(l.CheckIcon,E):null,k({},E),{props:d,checked:C}),u.createElement("div",D,S))),I&&u.createElement(a.Tooltip,s({target:h,content:d.tooltip,pt:y("tooltip")},d.tooltipOptions)))})));P.displayName="Checkbox";var E=o.ComponentBase.extend({defaultProps:{__TYPE:"MultiStateCheckbox",autoFocus:!1,className:null,dataKey:null,disabled:!1,empty:!0,iconTemplate:null,id:null,onChange:null,optionIcon:null,optionLabel:null,optionValue:null,options:null,readOnly:!1,style:null,tabIndex:"0",tooltip:null,tooltipOptions:null,value:null,children:void 0},css:{classes:{icon:function(e){return c.classNames("p-checkbox-icon p-c",b({},"".concat(e.icon),!0))},root:function(e){return c.classNames("p-multistatecheckbox p-checkbox p-component",e.props.classNames)}},inlineStyles:{checkbox:function(e){var t=e.selectedOption;return t&&t.style}}}});function D(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function S(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?D(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):D(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var N=u.memo(u.forwardRef((function(e,t){var l=r.useMergeProps(),i=u.useContext(n.PrimeReactContext),p=E.getProps(e,i),d=g(u.useState(!1),2),f=d[0],m=d[1],y=u.useRef(null),v=p.optionValue?null:p.dataKey,h=E.setMetaData({props:p,state:{focused:f}}),O=h.ptm,x=h.cx,j=h.sx;o.useHandleStyle(E.css.styles,h.isUnstyled,{name:"multistatecheckbox"});var k=function(e){p.disabled||p.readOnly||N(e)},D=function(e){return p.optionValue?c.ObjectUtils.resolveFieldData(e,p.optionValue):e},N=function(e){if(p.onChange){var t=D(p.options?R===p.options.length-1?p.empty?null:p.options[0]:p.options[R+1]:null);p.onChange({originalEvent:e,value:t,stopPropagation:function(){null==e||e.stopPropagation()},preventDefault:function(){null==e||e.preventDefault()},target:{name:p.name,id:p.id,value:t}})}};u.useImperativeHandle(t,(function(){return{props:p,focus:function(){return c.DomHandler.focusFirstElement(y.current)},getElement:function(){return y.current}}})),r.useMountEffect((function(){p.empty||null!==p.value||N(),p.autoFocus&&c.DomHandler.focusFirstElement(y.current)}));var w,C,I=(p.options&&(C=p.options.findIndex((function(e){return c.ObjectUtils.equals(p.value,D(e),v)})),w=p.options[C]),{option:w,index:C}),M=I.option,R=I.index,U=c.ObjectUtils.isNotEmpty(p.tooltip),F=E.getOtherProps(p),V=c.ObjectUtils.reduceKeys(F,c.DomHandler.ARIA_PROPS),A=function(){var e=M&&c.ObjectUtils.resolveFieldData(M,p.optionIcon||"icon")||"",t=c.classNames("p-checkbox-icon p-c",b({},"".concat(e),!0)),n=l({className:x("icon",{icon:e})},O("icon")),o=c.IconUtils.getJSXIcon(e,S({},n),{props:p});return p.iconTemplate?c.ObjectUtils.getJSXElement(p.iconTemplate,{option:M,className:t,element:o,props:p}):o}(),H=M?function(e){var t=p.optionLabel||p.optionValue;return t?c.ObjectUtils.resolveFieldData(e,t):e}(M):n.ariaLabel("nullLabel"),_=M?"true":"false",T=l({ref:y,id:p.id,className:c.classNames(p.className,x("root")),style:p.style,onClick:k},E.getOtherProps(p),O("root")),B=l(S({className:c.classNames(p.className),style:j("checkbox",{selectedOption:M}),tabIndex:p.tabIndex,onFocus:function(){m(!0)},onBlur:function(){m(!1)},onKeyDown:function(e){32===e.keyCode&&(N(e),e.preventDefault())},role:"checkbox","aria-checked":_,onChange:k,checked:!!M,disabled:null==p?void 0:p.disabled,icon:A},V),O("checkbox")),K=l({className:"p-hidden-accessible","aria-live":"polite"},O("srOnlyAria"));return u.createElement(u.Fragment,null,u.createElement("div",T,u.createElement(P,B),f&&u.createElement("span",K,H)),U&&u.createElement(a.Tooltip,s({target:y,content:p.tooltip,pt:O("tooltip")},p.tooltipOptions)))})));return N.displayName="MultiStateCheckbox",e.MultiStateCheckbox=N,Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,primereact.api,primereact.componentbase,primereact.hooks,primereact.icons.check,primereact.tooltip,primereact.utils);
