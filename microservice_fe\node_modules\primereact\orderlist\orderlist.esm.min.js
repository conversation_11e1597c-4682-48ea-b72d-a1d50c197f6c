import*as e from"react";import t,{ariaLabel as n,PrimeReactContext as r,FilterService as o}from"primereact/api";import{ComponentBase as i,useHandleStyle as l}from"primereact/componentbase";import{useMergeProps as a,useMountEffect as c,useUpdateEffect as u}from"primereact/hooks";import{classNames as s,ObjectUtils as d,IconUtils as p,<PERSON><PERSON><PERSON><PERSON> as f,UniqueComponentId as m}from"primereact/utils";import{Button as v}from"primereact/button";import{AngleDoubleDownIcon as g}from"primereact/icons/angledoubledown";import{AngleDoubleUpIcon as h}from"primereact/icons/angledoubleup";import{AngleDownIcon as y}from"primereact/icons/angledown";import{AngleUpIcon as b}from"primereact/icons/angleup";import{SearchIcon as I}from"primereact/icons/search";import{Ripple as w}from"primereact/ripple";function E(){return E=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},E.apply(null,arguments)}function x(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function O(e){if(Array.isArray(e))return x(e)}function D(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function S(e,t){if(e){if("string"==typeof e)return x(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?x(e,t):void 0}}function L(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function C(e){return O(e)||D(e)||S(e)||L()}function N(e){if(Array.isArray(e))return e}function k(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,l,a=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(a.push(r.value),a.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(u)throw o}}return a}}function B(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function T(e,t){return N(e)||k(e,t)||S(e,t)||B()}var K=i.extend({defaultProps:{__TYPE:"OrderList",id:null,ariaLabel:null,ariaLabelledBy:null,value:null,header:null,style:null,className:null,listStyle:null,dragdrop:!1,tabIndex:0,filterIcon:null,moveUpIcon:null,moveTopIcon:null,moveDownIcon:null,moveBottomIcon:null,dataKey:null,autoOptionFocus:!0,focusOnHover:!0,breakpoint:"960px",onChange:null,itemTemplate:null,filter:!1,filterBy:null,filterMatchMode:"contains",filterLocale:void 0,filterPlaceholder:null,filterTemplate:null,onFilter:null,children:void 0},css:{classes:{root:"p-orderlist p-component",controls:"p-orderlist-controls",droppoint:"p-orderlist-droppoint",header:"p-orderlist-header",list:"p-orderlist-list",icon:"p-orderlist-filter",filter:"p-orderlist-filter",filterInput:"p-orderlist-filter-input p-inputtext p-component",filterIcon:"p-orderlist-filter-icon",filterContainer:"p-orderlist-filter-container",container:"p-orderlist-list-container",item:function(e){return s("p-orderlist-item",{"p-highlight":e.selected,"p-focus":e.focused})}},styles:"\n@layer primereact {\n    .p-orderlist {\n        display: flex;\n    }\n\n    .p-orderlist-controls {\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n    }\n\n    .p-orderlist-list-container {\n        flex: 1 1 auto;\n    }\n\n    .p-orderlist-list {\n        list-style-type: none;\n        margin: 0;\n        padding: 0;\n        overflow: auto;\n        min-height: 12rem;\n        max-height: 24rem;\n    }\n\n    .p-orderlist-item {\n        cursor: pointer;\n        overflow: hidden;\n        position: relative;\n    }\n\n    .p-orderlist-item .p-ink {\n        pointer-events: none;\n    }\n\n    .p-orderlist-filter {\n        position: relative;\n    }\n\n    .p-orderlist-filter-icon {\n        position: absolute;\n        top: 50%;\n        margin-top: -.5rem;\n    }\n\n    .p-orderlist-filter-input {\n        width: 100%;\n    }\n\n    .p-orderlist.p-state-disabled .p-orderlist-item,\n    .p-orderlist.p-state-disabled .p-button {\n        cursor: default;\n    }\n\n    .p-orderlist.p-state-disabled .p-orderlist-list {\n        overflow: hidden;\n    }\n\n    .p-orderlist .p-orderlist-droppoint {\n        height: 0.5rem;\n    }\n\n    .p-orderlist .p-orderlist-droppoint.p-orderlist-droppoint-highlight {\n        background: var(--primary-color);\n    }\n}\n"}}),F=e.memo((function(t){var r=a(),o=t.moveUpIcon||e.createElement(b,null),i=t.moveTopIcon||e.createElement(h,null),l=t.moveDownIcon||e.createElement(y,null),c=t.moveBottomIcon||e.createElement(g,null),u=t.ptm,s=t.cx,p=t.unstyled,f=d.isEmpty(t.value)||d.isEmpty(t.selection),m=r({className:s("controls")},u("controls",{hostName:t.hostName})),I=r({type:"button",unstyled:p,icon:o,onClick:function(e){if(t.selection){for(var n=C(t.value),r=0;r<t.selection.length;r++){var o=d.findIndexInList(t.selection[r],n,t.dataKey);if(0===o)break;var i=n[o-1];n[o-1]=n[o],n[o]=i}t.onReorder&&t.onReorder({originalEvent:e,value:n,direction:"up"})}},disabled:f,"aria-label":n("moveUp"),__parentMetadata:{parent:t.metaData}},u("moveUpButton")),w=r({type:"button",unstyled:p,icon:i,onClick:function(e){if(t.selection){for(var n=C(t.value),r=t.selection.length-1;r>=0;r--){var o=d.findIndexInList(t.selection[r],n,t.dataKey);if(0===o)break;var i=n.splice(o,1)[0];n.unshift(i)}t.onReorder&&t.onReorder({originalEvent:e,value:n,direction:"top"})}},disabled:f,"aria-label":n("moveTop"),__parentMetadata:{parent:t.metaData}},u("moveTopButton")),x=r({type:"button",unstyled:p,icon:l,onClick:function(e){if(t.selection){for(var n=C(t.value),r=t.selection.length-1;r>=0;r--){var o=d.findIndexInList(t.selection[r],n,t.dataKey);if(o===n.length-1)break;var i=n[o+1];n[o+1]=n[o],n[o]=i}t.onReorder&&t.onReorder({originalEvent:e,value:n,direction:"down"})}},disabled:f,"aria-label":n("moveDown"),__parentMetadata:{parent:t.metaData}},u("moveDownButton")),O=r({type:"button",unstyled:p,icon:c,onClick:function(e){if(t.selection){for(var n=C(t.value),r=0;r<t.selection.length;r++){var o=d.findIndexInList(t.selection[r],n,t.dataKey);if(o===n.length-1)break;var i=n.splice(o,1)[0];n.push(i)}t.onReorder&&t.onReorder({originalEvent:e,value:n,direction:"bottom"})}},disabled:f,"aria-label":n("moveBottom"),__parentMetadata:{parent:t.metaData}},u("moveBottomButton"));return e.createElement("div",m,e.createElement(v,E({pt:u("moveUpButton")},I)),e.createElement(v,E({pt:u("moveTopButton")},w)),e.createElement(v,E({pt:u("moveDownButton")},x)),e.createElement(v,E({pt:u("moveBottomButton")},O)))}));function j(e){return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},j(e)}function M(e,t){if("object"!=j(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=j(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function P(e){var t=M(e,"string");return"symbol"==j(t)?t:t+""}function R(e,t,n){return(t=P(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function A(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function U(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?A(Object(n),!0).forEach((function(t){R(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):A(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}F.displayName="OrderListControls";var _=e.memo(e.forwardRef((function(t,n){var r=a(),o=t.ptm,i=t.cx,l=function(e,n){return o(e,U({hostName:t.hostName},n))},c=function(e,t){return l(t,{context:{selected:y(e)}})},u=e.useRef(null),m=e.useRef(null),v=e.useRef(null),g=e.useRef(null),h={filter:function(e){return t.onFilterInputChange(e)},reset:function(){return t.resetFilter()}},y=function(e){return-1!==d.findIndexInList(e,t.selection,t.dataKey)},b=function(e,n){m.current!==n&&m.current+1!==n&&(v.current=n,!t.isUnstyled()&&f.addClass(e.target,"p-orderlist-droppoint-highlight"),e.target.setAttribute("data-p-orderlist-droppoint-highlight",!0),e.preventDefault())},x=function(e){v.current=null,!t.isUnstyled()&&f.removeClass(e.target,"p-orderlist-droppoint-highlight"),e.target.setAttribute("data-p-orderlist-droppoint-highlight",!1)},O=function(e){var n=m.current>v.current?v.current:0===v.current?0:v.current-1,r=C(t.value);d.reorderArray(r,m.current,n),v.current=null,!t.isUnstyled()&&f.removeClass(e.target,"p-orderlist-droppoint-highlight"),e.target.setAttribute("data-p-orderlist-droppoint-highlight",!1),t.onChange&&t.onChange({originalEvent:e,value:r})},D=function(e){u.current=!1},S=function(e){if(u.current){var t=g.current.getBoundingClientRect().top+f.getWindowScrollTop(),n=t+g.current.clientHeight-e.pageY,r=e.pageY-t;n<25&&n>0?g.current.scrollTop+=15:r<25&&r>0&&(g.current.scrollTop-=15)}},L=function(e){13===e.which&&e.preventDefault()},N=function(e,n){var r;t.focusOnHover&&t.focused&&(null==t||null===(r=t.changeFocusedOptionIndex)||void 0===r||r.call(t,n))},k=function(t,n){var o=r({className:i("droppoint"),onDragOver:function(e){return b(e,t+1)},onDragLeave:x,onDrop:O},l("droppoint"));return e.createElement("li",E({key:n},o))};e.useImperativeHandle(n,(function(){return{getElement:function(){return g.current}}}));var B,T,K,F=(B=r({className:i("header")},l("header")),t.header?e.createElement("div",B,t.header):null),j=function(){var n=r({className:i("icon")},l("icon")),o=p.getJSXIcon(t.filterIcon||e.createElement(I,n),U({},n),{props:t});if(t.filter){var a=r({className:i("filter")},l("filter")),c=r({type:"text",value:t.filterValue,onChange:t.onFilter,onKeyDown:L,placeholder:t.filterPlaceholder,className:i("filterInput")},l("filterInput")),u=r({className:i("filterIcon")},l("filterIcon")),s=e.createElement("div",a,e.createElement("input",c),e.createElement("span",u,o));if(t.filterTemplate)s=d.getJSXElement(t.filterTemplate,{className:"p-orderlist-filter",inputProps:{inputClassName:"p-orderlist-filter-input p-inputtext p-component",onChange:t.onFilter,onKeyDown:L},filterOptions:h,iconClassName:"p-orderlist-filter-icon",element:s,props:t});var f=r({className:i("filterContainer")},l("filterContainer"));return e.createElement("div",f,s)}return null}(),M=(T=t.value?t.value.map((function(n,o){var l=t.itemTemplate?t.itemTemplate(n):n,a=t.parentId+"_"+o,d=t.focused&&t.focusedOptionId===a,p=y(n);if(t.dragdrop){var f=r({id:a,role:"option",draggable:"true",onClick:function(e){return t.onItemClick({originalEvent:e,value:n,index:o})},onMouseDown:t.onOptionMouseDown,onMouseMove:function(e){return N(0,o)},onDragStart:function(e){return t=o,e.dataTransfer.setData("text","orderlist"),u.current=!0,void(m.current=t);var t},onDragEnd:D,className:s(t.className,i("item",{selected:p,focused:d})),"aria-selected":p,"data-p-highlight":p,"data-p-focused":d},c(n,"item")),v=[];return 0===o&&v.push(k(n,o)),v.push(e.createElement("li",E({key:a},f),l)),v.push(k(o,a+"_droppoint")),v}var g=r({id:a,role:"option",onClick:function(e){return t.onItemClick({originalEvent:e,value:n,index:o})},onMouseDown:t.onOptionMouseDown,onMouseMove:function(e){return N(0,o)},className:s(t.className,i("item",{selected:p,focused:d})),"aria-selected":p,"data-p-highlight":p,"data-p-focused":d},c(n,"item"));return e.createElement("li",E({key:a},g),l,e.createElement(w,null))})):null,K=r({ref:g,className:i("list"),style:t.listStyle,onDragOver:S,role:"listbox",onFocus:t.onListFocus,onBlur:t.onListBlur,onKeyDown:t.onListKeyDown,tabIndex:t.tabIndex,"aria-activedescendant":t.focused?t.focusedOptionId:null,"aria-label":t.ariaLabel,"aria-labelledby":t.ariaLabelledBy,"aria-multiselectable":!0},l("list")),e.createElement("ul",K,T)),P=r({className:i("container")},l("container"));return e.createElement("div",P,F,j,M)})));_.displayName="OrderListSubList";var H=e.memo(e.forwardRef((function(n,i){var p=a(),v=e.useContext(r),g=K.getProps(n,v),h=T(e.useState([]),2),y=h[0],b=h[1],I=T(e.useState(""),2),w=I[0],x=I[1],O=T(e.useState(null),2),D=O[0],S=O[1],L=T(e.useState(!1),2),N=L[0],k=L[1],B=T(e.useState(null),2),j=B[0],M=B[1],P=T(e.useState(-1),2),R=P[0],A=P[1],U=d.isNotEmpty(w),H=e.useRef(null),V=e.useRef(null),Y=e.useRef(null),J=e.useRef(null),X=e.useRef(null),W={props:g,state:{selection:y,filterValue:w,attributeSelector:D}},$=K.setMetaData(W),q=$.ptm,z=$.cx,G=$.isUnstyled;l(K.css.styles,G,{name:"orderlist"});var Q=function(){if(U){var e=w.trim().toLocaleLowerCase(g.filterLocale),t=g.filterBy?g.filterBy.split(","):[];return o.filter(g.value,t,e,g.filterMatchMode,g.filterLocale)}return g.value}(),Z=function(){return J.current&&J.current.getElement()},ee=function(e){var t=e.originalEvent,n=e.value,r=e.index,o=d.findIndexInList(n,y),i=Z(),l=f.find(i,'[data-pc-section="item"]')[r].getAttribute("id");A(l);var a,c=t.metaKey||t.ctrlKey;a=-1!==o?c?y.filter((function(e,t){return t!==o})):[n]:c?[].concat(C(y),[n]):[n],b(a)},te=function(e){var t=Q[e],n=-1!==d.findIndexInList(t,y);b(n?y.filter((function(e){return e!==t})):[].concat(C(y),[t]))},ne=function(e){if(-1===R){var t=e&&e.children?C(e.children):[],n=re(e,t);return g.autoOptionFocus&&-1===n&&(n=oe(e,t)),n}return-1},re=function(e,t){if(y.length){var n=f.findSingle(e,'[data-p-highlight="true"]');return d.findIndexInList(n,t)}return-1},oe=function(e,t){var n=f.findSingle(e,'[data-pc-section="item"]');return d.findIndexInList(n,t)},ie=function(e){var t=de(R);fe(t),e.shiftKey&&te(t),e.preventDefault()},le=function(e){var t=pe(R);fe(t),e.shiftKey&&te(t),e.preventDefault()},ae=function(e){if(e.ctrlKey&&e.shiftKey){var t=Z(),n=f.find(t,'[data-pc-section="item"]'),r=f.findSingle(t,'[data-pc-section="item"][id='.concat(R,"]")),o=C(n).findIndex((function(e){return e===r}));b(C(Q).slice(0,o+1))}else fe(0);e.preventDefault()},ce=function(e){var t=Z();if(e.ctrlKey&&e.shiftKey){var n=f.find(t,'[data-pc-section="item"]'),r=f.findSingle(t,'[data-pc-section="item"][id='.concat(R,"]")),o=C(n).findIndex((function(e){return e===r}));b(C(Q).slice(o,n.length))}else fe(f.find(t,'[data-pc-section="item"]').length-1);e.preventDefault()},ue=function(e){var t=Z(),n=f.find(t,'[data-pc-section="item"]'),r=f.findSingle(t,'[data-pc-section="item"][id='.concat(R,"]")),o=C(n).findIndex((function(e){return e===r}));ee({originalEvent:e,value:Q[o],index:o}),e.preventDefault()},se=function(e){e.preventDefault();var t=Z();if(e.shiftKey&&y&&y.length>0){var n=f.find(t,'[data-pc-section="item"]'),r=d.findIndexInList(y[0],C(Q)),o=f.findSingle(t,'[data-pc-section="item"][id='.concat(R,"]")),i=C(n).findIndex((function(e){return e===o}));b(C(Q).slice(Math.min(r,i),Math.max(r,i)+1))}else ue(e)},de=function(e){var t=Z(),n=C(f.find(t,'[data-pc-section="item"]')).findIndex((function(t){return t.id===e}));return n>-1?n+1:0},pe=function(e){var t=Z(),n=C(f.find(t,'[data-pc-section="item"]')).findIndex((function(t){return t.id===e}));return n>-1?n-1:0},fe=function(e){var t,n=Z(),r=f.find(n,'[data-pc-section="item"]');if(e>=r.length)t=r.length-1;else{if(e<0)return;t=e}var o=r[t]?r[t].getAttribute("id"):-1;A(o),me(o)},me=function(e){var t=Z(),n=f.findSingle(t,'[data-pc-section="item"][id="'.concat(e,'"]'));n&&n.scrollIntoView&&n.scrollIntoView({block:"nearest",inline:"start"})},ve=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=e.getElementsByClassName("p-highlight");d.isNotEmpty(n)&&f.scrollInView(e,-1===t?n[0]:n[n.length-1])},ge=function(e,t){if(e)switch(t){case"up":ve(e,-1);break;case"top":e.scrollTop=0;break;case"down":ve(e,1);break;case"bottom":setTimeout((function(){return e.scrollTop=e.scrollHeight}),100)}},he=function(){if(!V.current){V.current=f.createInlineStyle(v&&v.nonce||t.nonce,v&&v.styleContainer);var e="\n@media screen and (max-width: ".concat(g.breakpoint,") {\n    .p-orderlist[").concat(D,"] {\n        flex-direction: column;\n    }\n\n    .p-orderlist[").concat(D,"] .p-orderlist-controls {\n        padding: var(--content-padding);\n        flex-direction: row;\n    }\n\n    .p-orderlist[").concat(D,"] .p-orderlist-controls .p-button {\n        margin-right: var(--inline-spacing);\n        margin-bottom: 0;\n    }\n\n    .p-orderlist[").concat(D,"] .p-orderlist-controls .p-button:last-child {\n        margin-right: 0;\n    }\n}\n");V.current.innerHTML=e}};e.useImperativeHandle(i,(function(){return{props:g,getElement:function(){return H.current}}})),c((function(){!D&&S(m())})),u((function(){return D&&(H.current.setAttribute(D,""),he()),function(){V.current=f.removeInlineStyle(V.current)}}),[D,g.breakpoint]),u((function(){M(-1!==R?R:null)}),[R]),u((function(){X.current&&(ge(X.current,Y.current),X.current=null,Y.current=null)}));var ye=p({ref:H,id:g.id,className:s(g.className,z("root")),style:g.style},K.getOtherProps(g),q("root"));return e.createElement("div",ye,e.createElement(F,{hostName:"OrderList",value:Q,selection:y,onReorder:function(e){g.onChange&&g.onChange({event:e.originalEvent,value:e.value}),Y.current=e.direction,X.current=Z()},dataKey:g.dataKey,moveUpIcon:g.moveUpIcon,moveTopIcon:g.moveTopIcon,moveDownIcon:g.moveDownIcon,moveBottomIcon:g.moveBottomIcon,ptm:q,cx:z,unstyled:g.unstyled,metaData:W}),e.createElement(_,E({ref:J,hostName:"OrderList"},g,{ariaLabel:g.ariaLabel,ariaLabelledBy:g.ariaLabelledBy,changeFocusedOptionIndex:fe,cx:z,dataKey:g.dataKey,dragdrop:g.dragdrop,filter:g.filter,filterIcon:g.filterIcon,filterPlaceholder:g.filterPlaceholder,filterTemplate:g.filterTemplate,focused:N,focusedOptionId:j,header:g.header,isUnstyled:G,itemTemplate:g.itemTemplate,listStyle:g.listStyle,onChange:g.onChange,onFilter:function(e){var t=e.target.value;x(t),g.onFilter&&g.onFilter({originalEvent:e,value:t})},onFilterInputChange:function(e){var t=e.target.value;x(t),g.onFilter&&g.onFilter({originalEvent:e,filter:t})},onItemClick:ee,onListBlur:function(e){k(!1),A(-1),g.onBlur&&g.onBlur(e)},onListFocus:function(e){k(!0);var t=Z(),n=ne(t);fe(n),g.onFocus&&g.onFocus(e)},onListKeyDown:function(e){switch(e.code){case"ArrowDown":ie(e);break;case"ArrowUp":le(e);break;case"Home":ae(e);break;case"End":ce(e);break;case"Enter":case"NumpadEnter":ue(e);break;case"Space":se(e);break;case"KeyA":e.ctrlKey&&(b(Q),e.preventDefault())}},onOptionMouseDown:function(e){A(e)},parentId:D,ptm:q,resetFilter:function(){x(""),g.onFilter&&g.onFilter({filter:""})},selection:y,tabIndex:g.tabIndex,value:Q})))})));H.displayName="OrderList";export{H as OrderList};
