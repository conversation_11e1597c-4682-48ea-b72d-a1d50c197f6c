"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),n=require("primereact/api"),t=require("primereact/componentbase"),r=require("primereact/hooks"),o=require("primereact/utils"),l=require("primereact/icons/chevrondown"),i=require("primereact/icons/chevronup");function a(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}})),n.default=e,Object.freeze(n)}var c=a(e);function s(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function u(e){if(Array.isArray(e))return s(e)}function d(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function p(e,n){if(e){if("string"==typeof e)return s(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?s(e,n):void 0}}function g(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var m=t.ComponentBase.extend({defaultProps:{__TYPE:"OrganizationChart",id:null,value:null,style:null,className:null,selectionMode:null,selection:null,nodeTemplate:null,onSelectionChange:null,onNodeSelect:null,onNodeUnselect:null,togglerIcon:null,children:void 0},css:{classes:{root:"p-organizationchart p-component",table:"p-organizationchart-table",node:function(e){var n=e.node;return o.classNames("p-organizationchart-node-content",{"p-organizationchart-selectable-node":e.nodeProps.selectionMode&&!1!==n.selectable,"p-highlight":e.selected},n.className)},nodes:"p-organizationchart-nodes",lines:"p-organizationchart-lines",lineLeft:function(e){return o.classNames("p-organizationchart-line-left",{"p-organizationchart-line-top":0!==e.index})},lineRight:function(e){return o.classNames("p-organizationchart-line-right",{"p-organizationchart-line-top":e.index!==e.nodeChildLength-1})},lineDown:"p-organizationchart-line-down",nodeTogglerIcon:"p-node-toggler-icon",nodeToggler:"p-node-toggler"},styles:"\n@layer primereact {\n    .p-organizationchart-table {\n        border-spacing: 0;\n        border-collapse: separate;\n        margin: 0 auto;\n    }\n    \n    .p-organizationchart-table > tbody > tr > td {\n        text-align: center;\n        vertical-align: top;\n        padding: 0 .75rem;\n    }\n    \n    .p-organizationchart-node-content {\n        display: inline-block;\n        position: relative;\n    }\n    \n    .p-organizationchart-node-content .p-node-toggler {\n        position: absolute;\n        bottom: -.75rem;\n        margin-left: -.75rem;\n        z-index: 2;\n        left: 50%;\n        user-select: none;\n        cursor: pointer;\n        width: 1.5rem;\n        height: 1.5rem;\n        text-decoration: none;\n    }\n    \n    .p-organizationchart-node-content .p-node-toggler .p-node-toggler-icon {\n        position: relative;\n        top: .25rem;\n    }\n    \n    .p-organizationchart-line-down {\n        margin: 0 auto;\n        height: 20px;\n        width: 1px;\n    }\n    \n    .p-organizationchart-line-right {\n        border-radius: 0px;\n    }\n    \n     .p-organizationchart-line-left {\n        border-radius: 0;\n    }\n    \n    .p-organizationchart-selectable-node {\n        cursor: pointer;\n    }\n}\n"}});function f(){return f=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},f.apply(null,arguments)}function h(e){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(e)}function b(e,n){if("object"!=h(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=h(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}function y(e){var n=b(e,"string");return"symbol"==h(n)?n:n+""}function v(e,n,t){return(n=y(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function N(e){if(Array.isArray(e))return e}function E(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,o,l,i,a=[],c=!0,s=!1;try{if(l=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=l.call(t)).done)&&(a.push(r.value),a.length!==n);c=!0);}catch(e){s=!0,o=e}finally{try{if(!c&&null!=t.return&&(i=t.return(),Object(i)!==i))return}finally{if(s)throw o}}return a}}function S(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function O(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function w(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?O(Object(t),!0).forEach((function(n){v(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):O(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var x=c.memo((function(e){var n,t,a,s,u,d,g,m,h,b,y,v,O=r.useMergeProps(),j=e.node,z=c.useState(j.expanded),C=(t=2,N(n=z)||E(n,t)||p(n,t)||S()),P=C[0],I=C[1],M=!1!==j.leaf&&!(j.children&&j.children.length),T=j.children&&j.children.length?2*j.children.length:null,D=e.isSelected(j),k=!M&&P?"inherit":"hidden",U=e.ptm,A=e.cx,_=e.sx,q=function(n,t){return U(n,w({hostName:e.hostName},t))},R=function(n){return q(n,{state:{expanded:P},context:{selected:e.isSelected(j)}})},L=function(e,n){return q(n,{context:{lineTop:e}})},H=function(n,t){e.onNodeClick(n,t)},J=function(e,n){I((function(e){return!e})),e.preventDefault()},X=function(){if(!M){var n=O({className:A("nodeTogglerIcon")},q("nodeTogglerIcon")),t=o.IconUtils.getJSXIcon(P?e.togglerIcon||c.createElement(l.ChevronDownIcon,n):e.togglerIcon||c.createElement(i.ChevronUpIcon,n),w({},n),{props:e}),r=O({className:A("nodeToggler"),tabIndex:0,onKeyDown:function(e){var n;"Enter"!==(n=e).code&&"NumpadEnter"!==n.code&&"Space"!==n.code||(J(n),n.preventDefault())},onClick:function(e){return J(e)},href:"#"},R("nodeToggler"));return c.createElement("a",r,c.createElement("i",null," ",t," "))}return null},B=function(){var n=e.nodeTemplate&&o.ObjectUtils.getJSXElement(e.nodeTemplate,j)||j.label;return c.createElement("div",null,n)},K=(a=B(),s=X(),u=O({colSpan:T},q("cell")),d=O({className:A("node",{selected:D,node:j,nodeProps:e}),style:j.style,onClick:function(e){return H(e,j)}},R("node")),g=O(q("row")),c.createElement("tr",g,c.createElement("td",u,c.createElement("div",d,a,s)))),Y=(m=O({className:A("lines"),style:{visibility:k}},q("lines")),h=O({colSpan:T},q("lineCell")),b=O({className:A("lineDown")},q("lineDown")),c.createElement("tr",m,c.createElement("td",h,c.createElement("div",b)))),$=function(){var e=j.children&&j.children.length,n=O({className:A("lines"),style:{visibility:k}},q("lines")),t=O({colSpan:T},q("lineCell")),r=O({className:A("lineDown")},q("lineDown"));return c.createElement("tr",n,j.children&&1===j.children.length&&c.createElement("td",t,c.createElement("div",r)),j.children&&j.children.length>1&&j.children.map((function(n,t){var r=O({className:A("lineLeft",{index:t})},L(0!==t,"lineLeft")),o=O({className:A("lineRight",{index:t,nodeChildLength:e})},L(t!==e-1,"lineRight"));return[c.createElement("td",f({key:t+"_lineleft"},r)," "),c.createElement("td",f({key:t+"_lineright"},o)," ")]})))}(),F=(y=O({className:A("nodes"),style:{visibility:k}},q("nodes")),v=O({colSpan:"2"},q("nodeCell")),c.createElement("tr",y,j.children&&j.children.map((function(n,t){return c.createElement("td",f({key:t},v),c.createElement(x,{node:n,nodeTemplate:e.nodeTemplate,selectionMode:e.selectionMode,onNodeClick:e.onNodeClick,isSelected:e.isSelected,togglerIcon:e.togglerIcon,ptm:U,cx:A,sx:_}))})))),G=O({className:A("table")},q("table"));return c.createElement("table",G,c.createElement("tbody",null,K,Y,$,F))}));x.displayName="OrganizationChartNode";var j=c.memo(c.forwardRef((function(e,l){var i=r.useMergeProps(),a=c.useContext(n.PrimeReactContext),s=m.getProps(e,a),f=m.setMetaData({props:s}),h=f.ptm,b=f.cx,y=f.sx;t.useHandleStyle(m.css.styles,f.isUnstyled,{name:"orgchart"});var v=c.useRef(null),N=s.value&&s.value.length?s.value[0]:null,E=function(e){if(s.selectionMode&&s.selection){if("single"===s.selectionMode)return s.selection===e?0:-1;if("multiple"===s.selectionMode)return s.selection.findIndex((function(n){return n===e}))}return-1};c.useImperativeHandle(l,(function(){return{props:s,getElement:function(){return v.current}}}));var S=i({id:s.id,ref:v,style:s.style,className:o.classNames(s.className,b("root"))},m.getOtherProps(s),h("root"));return c.createElement("div",S,c.createElement(x,{hostName:"OrganizationChart",node:N,nodeTemplate:s.nodeTemplate,selectionMode:s.selectionMode,onNodeClick:function(e,n){if(s.selectionMode){var t=e.target;if(!1===n.selectable||o.DomHandler.hasClass(t,"p-node-toggler")||o.DomHandler.hasClass(t,"p-node-toggler-icon"))return;var r,l=E(n),i=l>=0;"single"===s.selectionMode?i?(r=null,s.onNodeUnselect&&s.onNodeUnselect({originalEvent:e,node:n})):(r=n,s.onNodeSelect&&s.onNodeSelect({originalEvent:e,node:n})):"multiple"===s.selectionMode&&(i?(r=s.selection.filter((function(e,n){return n!==l})),s.onNodeUnselect&&s.onNodeUnselect({originalEvent:e,node:n})):(r=[].concat(u(a=s.selection||[])||d(a)||p(a)||g(),[n]),s.onNodeSelect&&s.onNodeSelect({originalEvent:e,node:n}))),s.onSelectionChange&&s.onSelectionChange({originalEvent:e,data:r})}var a},isSelected:function(e){return-1!==E(e)},togglerIcon:s.togglerIcon,ptm:h,cx:b,sx:y}))})));j.displayName="OrganizationChart",exports.OrganizationChart=j;
