import*as e from"react";import{PrimeReactContext as n}from"primereact/api";import{ComponentBase as t,useHandleStyle as r}from"primereact/componentbase";import{useMergeProps as o}from"primereact/hooks";import{classNames as l,IconUtils as i,ObjectUtils as a,<PERSON><PERSON><PERSON><PERSON> as c}from"primereact/utils";import{ChevronDownIcon as s}from"primereact/icons/chevrondown";import{ChevronUpIcon as d}from"primereact/icons/chevronup";function u(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function p(e){if(Array.isArray(e))return u(e)}function m(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function g(e,n){if(e){if("string"==typeof e)return u(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?u(e,n):void 0}}function f(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var h=t.extend({defaultProps:{__TYPE:"OrganizationChart",id:null,value:null,style:null,className:null,selectionMode:null,selection:null,nodeTemplate:null,onSelectionChange:null,onNodeSelect:null,onNodeUnselect:null,togglerIcon:null,children:void 0},css:{classes:{root:"p-organizationchart p-component",table:"p-organizationchart-table",node:function(e){var n=e.node;return l("p-organizationchart-node-content",{"p-organizationchart-selectable-node":e.nodeProps.selectionMode&&!1!==n.selectable,"p-highlight":e.selected},n.className)},nodes:"p-organizationchart-nodes",lines:"p-organizationchart-lines",lineLeft:function(e){return l("p-organizationchart-line-left",{"p-organizationchart-line-top":0!==e.index})},lineRight:function(e){return l("p-organizationchart-line-right",{"p-organizationchart-line-top":e.index!==e.nodeChildLength-1})},lineDown:"p-organizationchart-line-down",nodeTogglerIcon:"p-node-toggler-icon",nodeToggler:"p-node-toggler"},styles:"\n@layer primereact {\n    .p-organizationchart-table {\n        border-spacing: 0;\n        border-collapse: separate;\n        margin: 0 auto;\n    }\n    \n    .p-organizationchart-table > tbody > tr > td {\n        text-align: center;\n        vertical-align: top;\n        padding: 0 .75rem;\n    }\n    \n    .p-organizationchart-node-content {\n        display: inline-block;\n        position: relative;\n    }\n    \n    .p-organizationchart-node-content .p-node-toggler {\n        position: absolute;\n        bottom: -.75rem;\n        margin-left: -.75rem;\n        z-index: 2;\n        left: 50%;\n        user-select: none;\n        cursor: pointer;\n        width: 1.5rem;\n        height: 1.5rem;\n        text-decoration: none;\n    }\n    \n    .p-organizationchart-node-content .p-node-toggler .p-node-toggler-icon {\n        position: relative;\n        top: .25rem;\n    }\n    \n    .p-organizationchart-line-down {\n        margin: 0 auto;\n        height: 20px;\n        width: 1px;\n    }\n    \n    .p-organizationchart-line-right {\n        border-radius: 0px;\n    }\n    \n     .p-organizationchart-line-left {\n        border-radius: 0;\n    }\n    \n    .p-organizationchart-selectable-node {\n        cursor: pointer;\n    }\n}\n"}});function y(){return y=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},y.apply(null,arguments)}function b(e){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b(e)}function v(e,n){if("object"!=b(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=b(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}function E(e){var n=v(e,"string");return"symbol"==b(n)?n:n+""}function S(e,n,t){return(n=E(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function N(e){if(Array.isArray(e))return e}function O(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,o,l,i,a=[],c=!0,s=!1;try{if(l=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=l.call(t)).done)&&(a.push(r.value),a.length!==n);c=!0);}catch(e){s=!0,o=e}finally{try{if(!c&&null!=t.return&&(i=t.return(),Object(i)!==i))return}finally{if(s)throw o}}return a}}function w(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function x(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function z(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?x(Object(t),!0).forEach((function(n){S(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):x(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var C=e.memo((function(n){var t,r,l,c,u,p,m,f,h,b,v,E,S=o(),x=n.node,j=e.useState(x.expanded),I=(r=2,N(t=j)||O(t,r)||g(t,r)||w()),P=I[0],T=I[1],M=!1!==x.leaf&&!(x.children&&x.children.length),D=x.children&&x.children.length?2*x.children.length:null,k=n.isSelected(x),A=!M&&P?"inherit":"hidden",U=n.ptm,L=n.cx,R=n.sx,_=function(e,t){return U(e,z({hostName:n.hostName},t))},J=function(e){return _(e,{state:{expanded:P},context:{selected:n.isSelected(x)}})},X=function(e,n){return _(n,{context:{lineTop:e}})},H=function(e,t){n.onNodeClick(e,t)},K=function(e,n){T((function(e){return!e})),e.preventDefault()},Y=function(){if(!M){var t=S({className:L("nodeTogglerIcon")},_("nodeTogglerIcon")),r=i.getJSXIcon(P?n.togglerIcon||e.createElement(s,t):n.togglerIcon||e.createElement(d,t),z({},t),{props:n}),o=S({className:L("nodeToggler"),tabIndex:0,onKeyDown:function(e){var n;"Enter"!==(n=e).code&&"NumpadEnter"!==n.code&&"Space"!==n.code||(K(n),n.preventDefault())},onClick:function(e){return K(e)},href:"#"},J("nodeToggler"));return e.createElement("a",o,e.createElement("i",null," ",r," "))}return null},$=function(){var t=n.nodeTemplate&&a.getJSXElement(n.nodeTemplate,x)||x.label;return e.createElement("div",null,t)},q=(l=$(),c=Y(),u=S({colSpan:D},_("cell")),p=S({className:L("node",{selected:k,node:x,nodeProps:n}),style:x.style,onClick:function(e){return H(e,x)}},J("node")),m=S(_("row")),e.createElement("tr",m,e.createElement("td",u,e.createElement("div",p,l,c)))),B=(f=S({className:L("lines"),style:{visibility:A}},_("lines")),h=S({colSpan:D},_("lineCell")),b=S({className:L("lineDown")},_("lineDown")),e.createElement("tr",f,e.createElement("td",h,e.createElement("div",b)))),F=function(){var n=x.children&&x.children.length,t=S({className:L("lines"),style:{visibility:A}},_("lines")),r=S({colSpan:D},_("lineCell")),o=S({className:L("lineDown")},_("lineDown"));return e.createElement("tr",t,x.children&&1===x.children.length&&e.createElement("td",r,e.createElement("div",o)),x.children&&x.children.length>1&&x.children.map((function(t,r){var o=S({className:L("lineLeft",{index:r})},X(0!==r,"lineLeft")),l=S({className:L("lineRight",{index:r,nodeChildLength:n})},X(r!==n-1,"lineRight"));return[e.createElement("td",y({key:r+"_lineleft"},o)," "),e.createElement("td",y({key:r+"_lineright"},l)," ")]})))}(),G=(v=S({className:L("nodes"),style:{visibility:A}},_("nodes")),E=S({colSpan:"2"},_("nodeCell")),e.createElement("tr",v,x.children&&x.children.map((function(t,r){return e.createElement("td",y({key:r},E),e.createElement(C,{node:t,nodeTemplate:n.nodeTemplate,selectionMode:n.selectionMode,onNodeClick:n.onNodeClick,isSelected:n.isSelected,togglerIcon:n.togglerIcon,ptm:U,cx:L,sx:R}))})))),Q=S({className:L("table")},_("table"));return e.createElement("table",Q,e.createElement("tbody",null,q,B,F,G))}));C.displayName="OrganizationChartNode";var j=e.memo(e.forwardRef((function(t,i){var a=o(),s=e.useContext(n),d=h.getProps(t,s),u=h.setMetaData({props:d}),y=u.ptm,b=u.cx,v=u.sx;r(h.css.styles,u.isUnstyled,{name:"orgchart"});var E=e.useRef(null),S=d.value&&d.value.length?d.value[0]:null,N=function(e){if(d.selectionMode&&d.selection){if("single"===d.selectionMode)return d.selection===e?0:-1;if("multiple"===d.selectionMode)return d.selection.findIndex((function(n){return n===e}))}return-1};e.useImperativeHandle(i,(function(){return{props:d,getElement:function(){return E.current}}}));var O=a({id:d.id,ref:E,style:d.style,className:l(d.className,b("root"))},h.getOtherProps(d),y("root"));return e.createElement("div",O,e.createElement(C,{hostName:"OrganizationChart",node:S,nodeTemplate:d.nodeTemplate,selectionMode:d.selectionMode,onNodeClick:function(e,n){if(d.selectionMode){var t=e.target;if(!1===n.selectable||c.hasClass(t,"p-node-toggler")||c.hasClass(t,"p-node-toggler-icon"))return;var r,o=N(n),l=o>=0;"single"===d.selectionMode?l?(r=null,d.onNodeUnselect&&d.onNodeUnselect({originalEvent:e,node:n})):(r=n,d.onNodeSelect&&d.onNodeSelect({originalEvent:e,node:n})):"multiple"===d.selectionMode&&(l?(r=d.selection.filter((function(e,n){return n!==o})),d.onNodeUnselect&&d.onNodeUnselect({originalEvent:e,node:n})):(r=[].concat(p(i=d.selection||[])||m(i)||g(i)||f(),[n]),d.onNodeSelect&&d.onNodeSelect({originalEvent:e,node:n}))),d.onSelectionChange&&d.onSelectionChange({originalEvent:e,data:r})}var i},isSelected:function(e){return-1!==N(e)},togglerIcon:d.togglerIcon,ptm:y,cx:b,sx:v}))})));j.displayName="OrganizationChart";export{j as OrganizationChart};
