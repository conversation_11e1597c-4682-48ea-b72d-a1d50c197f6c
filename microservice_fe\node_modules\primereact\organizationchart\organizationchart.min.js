this.primereact=this.primereact||{},this.primereact.organizationchart=function(e,n,t,r,o,l,i,a){"use strict";function c(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}})),n.default=e,Object.freeze(n)}var s=c(n);function u(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function d(e){if(Array.isArray(e))return u(e)}function p(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function g(e,n){if(e){if("string"==typeof e)return u(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?u(e,n):void 0}}function m(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var f=r.ComponentBase.extend({defaultProps:{__TYPE:"OrganizationChart",id:null,value:null,style:null,className:null,selectionMode:null,selection:null,nodeTemplate:null,onSelectionChange:null,onNodeSelect:null,onNodeUnselect:null,togglerIcon:null,children:void 0},css:{classes:{root:"p-organizationchart p-component",table:"p-organizationchart-table",node:function(e){var n=e.node;return l.classNames("p-organizationchart-node-content",{"p-organizationchart-selectable-node":e.nodeProps.selectionMode&&!1!==n.selectable,"p-highlight":e.selected},n.className)},nodes:"p-organizationchart-nodes",lines:"p-organizationchart-lines",lineLeft:function(e){return l.classNames("p-organizationchart-line-left",{"p-organizationchart-line-top":0!==e.index})},lineRight:function(e){return l.classNames("p-organizationchart-line-right",{"p-organizationchart-line-top":e.index!==e.nodeChildLength-1})},lineDown:"p-organizationchart-line-down",nodeTogglerIcon:"p-node-toggler-icon",nodeToggler:"p-node-toggler"},styles:"\n@layer primereact {\n    .p-organizationchart-table {\n        border-spacing: 0;\n        border-collapse: separate;\n        margin: 0 auto;\n    }\n    \n    .p-organizationchart-table > tbody > tr > td {\n        text-align: center;\n        vertical-align: top;\n        padding: 0 .75rem;\n    }\n    \n    .p-organizationchart-node-content {\n        display: inline-block;\n        position: relative;\n    }\n    \n    .p-organizationchart-node-content .p-node-toggler {\n        position: absolute;\n        bottom: -.75rem;\n        margin-left: -.75rem;\n        z-index: 2;\n        left: 50%;\n        user-select: none;\n        cursor: pointer;\n        width: 1.5rem;\n        height: 1.5rem;\n        text-decoration: none;\n    }\n    \n    .p-organizationchart-node-content .p-node-toggler .p-node-toggler-icon {\n        position: relative;\n        top: .25rem;\n    }\n    \n    .p-organizationchart-line-down {\n        margin: 0 auto;\n        height: 20px;\n        width: 1px;\n    }\n    \n    .p-organizationchart-line-right {\n        border-radius: 0px;\n    }\n    \n     .p-organizationchart-line-left {\n        border-radius: 0;\n    }\n    \n    .p-organizationchart-selectable-node {\n        cursor: pointer;\n    }\n}\n"}});function h(){return h=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},h.apply(null,arguments)}function b(e){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b(e)}function y(e,n){if("object"!=b(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=b(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}function v(e){var n=y(e,"string");return"symbol"==b(n)?n:n+""}function N(e,n,t){return(n=v(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function E(e){if(Array.isArray(e))return e}function S(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,o,l,i,a=[],c=!0,s=!1;try{if(l=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=l.call(t)).done)&&(a.push(r.value),a.length!==n);c=!0);}catch(e){s=!0,o=e}finally{try{if(!c&&null!=t.return&&(i=t.return(),Object(i)!==i))return}finally{if(s)throw o}}return a}}function O(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function w(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function j(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?w(Object(t),!0).forEach((function(n){N(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):w(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var x=s.memo((function(e){var n,t,r,c,u,d,p,m,f,b,y,v,N=o.useMergeProps(),w=e.node,z=s.useState(w.expanded),C=(t=2,E(n=z)||S(n,t)||g(n,t)||O()),P=C[0],I=C[1],M=!1!==w.leaf&&!(w.children&&w.children.length),T=w.children&&w.children.length?2*w.children.length:null,D=e.isSelected(w),k=!M&&P?"inherit":"hidden",U=e.ptm,A=e.cx,_=e.sx,R=function(n,t){return U(n,j({hostName:e.hostName},t))},L=function(n){return R(n,{state:{expanded:P},context:{selected:e.isSelected(w)}})},H=function(e,n){return R(n,{context:{lineTop:e}})},J=function(n,t){e.onNodeClick(n,t)},X=function(e,n){I((function(e){return!e})),e.preventDefault()},B=function(){if(!M){var n=N({className:A("nodeTogglerIcon")},R("nodeTogglerIcon")),t=l.IconUtils.getJSXIcon(P?e.togglerIcon||s.createElement(i.ChevronDownIcon,n):e.togglerIcon||s.createElement(a.ChevronUpIcon,n),j({},n),{props:e}),r=N({className:A("nodeToggler"),tabIndex:0,onKeyDown:function(e){var n;"Enter"!==(n=e).code&&"NumpadEnter"!==n.code&&"Space"!==n.code||(X(n),n.preventDefault())},onClick:function(e){return X(e)},href:"#"},L("nodeToggler"));return s.createElement("a",r,s.createElement("i",null," ",t," "))}return null},K=function(){var n=e.nodeTemplate&&l.ObjectUtils.getJSXElement(e.nodeTemplate,w)||w.label;return s.createElement("div",null,n)},Y=(r=K(),c=B(),u=N({colSpan:T},R("cell")),d=N({className:A("node",{selected:D,node:w,nodeProps:e}),style:w.style,onClick:function(e){return J(e,w)}},L("node")),p=N(R("row")),s.createElement("tr",p,s.createElement("td",u,s.createElement("div",d,r,c)))),$=(m=N({className:A("lines"),style:{visibility:k}},R("lines")),f=N({colSpan:T},R("lineCell")),b=N({className:A("lineDown")},R("lineDown")),s.createElement("tr",m,s.createElement("td",f,s.createElement("div",b)))),q=function(){var e=w.children&&w.children.length,n=N({className:A("lines"),style:{visibility:k}},R("lines")),t=N({colSpan:T},R("lineCell")),r=N({className:A("lineDown")},R("lineDown"));return s.createElement("tr",n,w.children&&1===w.children.length&&s.createElement("td",t,s.createElement("div",r)),w.children&&w.children.length>1&&w.children.map((function(n,t){var r=N({className:A("lineLeft",{index:t})},H(0!==t,"lineLeft")),o=N({className:A("lineRight",{index:t,nodeChildLength:e})},H(t!==e-1,"lineRight"));return[s.createElement("td",h({key:t+"_lineleft"},r)," "),s.createElement("td",h({key:t+"_lineright"},o)," ")]})))}(),F=(y=N({className:A("nodes"),style:{visibility:k}},R("nodes")),v=N({colSpan:"2"},R("nodeCell")),s.createElement("tr",y,w.children&&w.children.map((function(n,t){return s.createElement("td",h({key:t},v),s.createElement(x,{node:n,nodeTemplate:e.nodeTemplate,selectionMode:e.selectionMode,onNodeClick:e.onNodeClick,isSelected:e.isSelected,togglerIcon:e.togglerIcon,ptm:U,cx:A,sx:_}))})))),G=N({className:A("table")},R("table"));return s.createElement("table",G,s.createElement("tbody",null,Y,$,q,F))}));x.displayName="OrganizationChartNode";var z=s.memo(s.forwardRef((function(e,n){var i=o.useMergeProps(),a=s.useContext(t.PrimeReactContext),c=f.getProps(e,a),u=f.setMetaData({props:c}),h=u.ptm,b=u.cx,y=u.sx;r.useHandleStyle(f.css.styles,u.isUnstyled,{name:"orgchart"});var v=s.useRef(null),N=c.value&&c.value.length?c.value[0]:null,E=function(e){if(c.selectionMode&&c.selection){if("single"===c.selectionMode)return c.selection===e?0:-1;if("multiple"===c.selectionMode)return c.selection.findIndex((function(n){return n===e}))}return-1};s.useImperativeHandle(n,(function(){return{props:c,getElement:function(){return v.current}}}));var S=i({id:c.id,ref:v,style:c.style,className:l.classNames(c.className,b("root"))},f.getOtherProps(c),h("root"));return s.createElement("div",S,s.createElement(x,{hostName:"OrganizationChart",node:N,nodeTemplate:c.nodeTemplate,selectionMode:c.selectionMode,onNodeClick:function(e,n){if(c.selectionMode){var t=e.target;if(!1===n.selectable||l.DomHandler.hasClass(t,"p-node-toggler")||l.DomHandler.hasClass(t,"p-node-toggler-icon"))return;var r,o=E(n),i=o>=0;"single"===c.selectionMode?i?(r=null,c.onNodeUnselect&&c.onNodeUnselect({originalEvent:e,node:n})):(r=n,c.onNodeSelect&&c.onNodeSelect({originalEvent:e,node:n})):"multiple"===c.selectionMode&&(i?(r=c.selection.filter((function(e,n){return n!==o})),c.onNodeUnselect&&c.onNodeUnselect({originalEvent:e,node:n})):(r=[].concat(d(a=c.selection||[])||p(a)||g(a)||m(),[n]),c.onNodeSelect&&c.onNodeSelect({originalEvent:e,node:n}))),c.onSelectionChange&&c.onSelectionChange({originalEvent:e,data:r})}var a},isSelected:function(e){return-1!==E(e)},togglerIcon:c.togglerIcon,ptm:h,cx:b,sx:y}))})));return z.displayName="OrganizationChart",e.OrganizationChart=z,Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,primereact.api,primereact.componentbase,primereact.hooks,primereact.utils,primereact.icons.chevrondown,primereact.icons.chevronup);
