"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),n=require("primereact/api"),r=require("primereact/componentbase"),t=require("primereact/csstransition"),o=require("primereact/hooks"),a=require("primereact/icons/times"),l=require("primereact/overlayservice"),i=require("primereact/portal"),c=require("primereact/ripple"),u=require("primereact/utils");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function p(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var t=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,t.get?t:{enumerable:!0,get:function(){return e[r]}})}})),n.default=e,Object.freeze(n)}var f=p(e),y=s(n);function d(){return d=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var t in r)({}).hasOwnProperty.call(r,t)&&(e[t]=r[t])}return e},d.apply(null,arguments)}function v(e){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},v(e)}function m(e,n){if("object"!=v(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var t=r.call(e,n||"default");if("object"!=v(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}function b(e){var n=m(e,"string");return"symbol"==v(n)?n:n+""}function g(e,n,r){return(n=b(n))in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}function O(e){if(Array.isArray(e))return e}function h(e,n){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var t,o,a,l,i=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===n){if(Object(r)!==r)return;c=!1}else for(;!(c=(t=a.call(r)).done)&&(i.push(t.value),i.length!==n);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=r.return&&(l=r.return(),Object(l)!==l))return}finally{if(u)throw o}}return i}}function S(e,n){(null==n||n>e.length)&&(n=e.length);for(var r=0,t=Array(n);r<n;r++)t[r]=e[r];return t}function x(e,n){if(e){if("string"==typeof e)return S(e,n);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?S(e,n):void 0}}function w(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function P(e,n){return O(e)||h(e,n)||x(e,n)||w()}var E=r.ComponentBase.extend({defaultProps:{__TYPE:"OverlayPanel",id:null,dismissable:!0,showCloseIcon:!1,closeIcon:null,style:null,className:null,appendTo:null,breakpoints:null,ariaCloseLabel:null,transitionOptions:null,onShow:null,onHide:null,children:void 0,closeOnEscape:!0},css:{classes:{root:function(e){var n=e.context;return u.classNames("p-overlaypanel p-component",{"p-input-filled":n&&"filled"===n.inputStyle||"filled"===y.default.inputStyle,"p-ripple-disabled":n&&!1===n.ripple||!1===y.default.ripple})},closeIcon:"p-overlaypanel-close-icon",closeButton:"p-overlaypanel-close p-link",content:"p-overlaypanel-content",transition:"p-overlaypanel"},styles:'\n@layer primereact {\n    .p-overlaypanel {\n        position: absolute;\n        margin-top: 10px;\n        /* Github #3122: Prevent animation flickering  */\n        top: -9999px;\n        left: -9999px;\n    }\n    \n    .p-overlaypanel-flipped {\n        margin-top: -10px;\n        margin-bottom: 10px;\n    }\n    \n    .p-overlaypanel-close {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        overflow: hidden;\n        position: relative;\n    }\n    \n    /* Animation */\n    .p-overlaypanel-enter {\n        opacity: 0;\n        transform: scaleY(0.8);\n    }\n    \n    .p-overlaypanel-enter-active {\n        opacity: 1;\n        transform: scaleY(1);\n        transition: transform .12s cubic-bezier(0, 0, 0.2, 1), opacity .12s cubic-bezier(0, 0, 0.2, 1);\n    }\n    \n    .p-overlaypanel-enter-done {\n        transform: none;\n    }\n    \n    .p-overlaypanel-exit {\n        opacity: 1;\n    }\n    \n    .p-overlaypanel-exit-active {\n        opacity: 0;\n        transition: opacity .1s linear;\n    }\n    \n    .p-overlaypanel:after, .p-overlaypanel:before {\n        bottom: 100%;\n        left: calc(var(--overlayArrowLeft, 0) + 1.25rem);\n        content: " ";\n        height: 0;\n        width: 0;\n        position: absolute;\n        pointer-events: none;\n    }\n    \n    .p-overlaypanel:after {\n        border-width: 8px;\n        margin-left: -8px;\n    }\n    \n    .p-overlaypanel:before {\n        border-width: 10px;\n        margin-left: -10px;\n    }\n    \n    .p-overlaypanel-flipped:after, .p-overlaypanel-flipped:before {\n        bottom: auto;\n        top: 100%;\n    }\n    \n    .p-overlaypanel.p-overlaypanel-flipped:after {\n        border-bottom-color: transparent;\n    }\n    \n    .p-overlaypanel.p-overlaypanel-flipped:before {\n        border-bottom-color: transparent\n    }\n}\n'}});function j(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),r.push.apply(r,t)}return r}function I(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?j(Object(r),!0).forEach((function(n){g(e,n,r[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))}))}return e}var D=f.forwardRef((function(e,s){var p=o.useMergeProps(),v=f.useContext(n.PrimeReactContext),m=E.getProps(e,v),b=P(f.useState(!1),2),g=b[0],O=b[1],h=E.setMetaData({props:m,state:{visible:g}}),S=h.ptm,x=h.cx,w=h.isUnstyled;r.useHandleStyle(E.css.styles,w,{name:"overlaypanel"});var j=f.useRef(""),D=f.useRef(null),k=f.useRef(null),C=f.useRef(!1),H=f.useRef(null),A=f.useRef(null),N=P(o.useOverlayListener({target:k,overlay:D,listener:function(e,n){n.valid&&("outside"===n.type?(m.dismissable&&!C.current&&Z(),C.current=!1):v.hideOverlaysOnDocumentScrolling?Z():u.DomHandler.isDocument(e.target)||J())},when:g}),2),T=N[0],R=N[1],q=g&&m.closeOnEscape,L=o.useDisplayOrder("overlay-panel",q);o.useGlobalOnEscapeKey({callback:function(){Z()},when:q&&L,priority:[o.ESC_KEY_HANDLING_PRIORITIES.OVERLAY_PANEL,L]});var _=function(e){return D&&D.current&&!(D.current.isSameNode(e)||D.current.contains(e))},M=function(e,n){return null!=k.current&&k.current!==(n||e.currentTarget||e.target)},U=function(){C.current=!0},z=function(e,n){g?(Z(),M(e,n)&&(k.current=n||e.currentTarget||e.target,setTimeout((function(){Y(e,k.current)}),200))):Y(e,n)},Y=function(e,n){k.current=n||e.currentTarget||e.target,g?J():(O(!0),A.current=function(e){!_(e.target)&&(C.current=!0)},l.OverlayService.on("overlay-click",A.current))},Z=function(){O(!1),l.OverlayService.off("overlay-click",A.current),A.current=null},B=function(){D.current.setAttribute(j.current,""),u.ZIndexUtils.set("overlay",D.current,v&&v.autoZIndex||y.default.autoZIndex,v&&v.zIndex.overlay||y.default.zIndex.overlay),u.DomHandler.addStyles(D.current,{position:"absolute",top:"0",left:"0"}),J()},G=function(){T(),m.onShow&&m.onShow()},K=function(){R()},V=function(){u.ZIndexUtils.clear(D.current),m.onHide&&m.onHide()},J=function(){if(k.current&&D.current){u.DomHandler.absolutePosition(D.current,k.current);var e=u.DomHandler.getOffset(D.current),n=u.DomHandler.getOffset(k.current),r=0;e.left<n.left&&(r=n.left-e.left),D.current.style.setProperty("--overlayArrowLeft","".concat(r,"px")),e.top<n.top?(D.current.setAttribute("data-p-overlaypanel-flipped","true"),w&&u.DomHandler.addClass(D.current,"p-overlaypanel-flipped")):(D.current.setAttribute("data-p-overlaypanel-flipped","false"),w&&u.DomHandler.removeClass(D.current,"p-overlaypanel-flipped"))}},X=function(){if(!H.current){H.current=u.DomHandler.createInlineStyle(v&&v.nonce||y.default.nonce,v&&v.styleContainer);var e="";for(var n in m.breakpoints)e+="\n                    @media screen and (max-width: ".concat(n,") {\n                        .p-overlaypanel[").concat(j.current,"] {\n                            width: ").concat(m.breakpoints[n],";\n                        }\n                    }\n                ");H.current.innerHTML=e}};o.useMountEffect((function(){j.current=u.UniqueComponentId(),m.breakpoints&&X()})),o.useUnmountEffect((function(){H.current=u.DomHandler.removeInlineStyle(H.current),A.current&&(l.OverlayService.off("overlay-click",A.current),A.current=null),u.ZIndexUtils.clear(D.current)})),f.useImperativeHandle(s,(function(){return{props:m,toggle:z,show:Y,hide:Z,align:J,isVisible:function(){return g},getElement:function(){return D.current}}}));var $,F,Q,W,ee=function(){var e=p({className:x("closeIcon"),"aria-hidden":!0},S("closeIcon")),r=u.IconUtils.getJSXIcon(m.closeIcon||f.createElement(a.TimesIcon,e),I({},e),{props:m}),t=p({type:"button",className:x("closeButton"),onClick:function(e){return n=e,Z(),void n.preventDefault();var n},"aria-label":m.ariaCloseLabel||n.ariaLabel("close")},S("closeButton"));return m.showCloseIcon?f.createElement("button",t,r,f.createElement(c.Ripple,null)):null},ne=($=ee(),F=p({id:m.id,className:u.classNames(m.className,x("root",{context:v})),style:m.style,onClick:function(e){return n=e,C.current=!0,void l.OverlayService.emit("overlay-click",{originalEvent:n,target:k.current});var n}},E.getOtherProps(m),S("root")),Q=p({className:x("content"),onClick:function(e){return U()},onMouseDown:U},E.getOtherProps(m),S("content")),W=p({classNames:x("transition"),in:g,timeout:{enter:120,exit:100},options:m.transitionOptions,unmountOnExit:!0,onEnter:B,onEntered:G,onExit:K,onExited:V},S("transition")),f.createElement(t.CSSTransition,d({nodeRef:D},W),f.createElement("div",d({ref:D},F),f.createElement("div",Q,m.children),$)));return f.createElement(i.Portal,{element:ne,appendTo:m.appendTo})}));D.displayName="OverlayPanel",exports.OverlayPanel=D;
