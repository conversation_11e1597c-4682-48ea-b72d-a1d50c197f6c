import*as e from"react";import n,{PrimeReactContext as t,a<PERSON><PERSON><PERSON><PERSON> as r}from"primereact/api";import{ComponentBase as o,useHandleStyle as l}from"primereact/componentbase";import{CSSTransition as a}from"primereact/csstransition";import{useMergeProps as i,useOverlayListener as c,useDisplayOrder as u,useGlobalOnEscapeKey as p,ESC_KEY_HANDLING_PRIORITIES as s,useMountEffect as f,useUnmountEffect as y}from"primereact/hooks";import{TimesIcon as m}from"primereact/icons/times";import{OverlayService as v}from"primereact/overlayservice";import{Portal as d}from"primereact/portal";import{Ripple as b}from"primereact/ripple";import{classNames as g,<PERSON><PERSON>and<PERSON> as h,UniqueComponentId as O,ZIndexUtils as w,IconUtils as x}from"primereact/utils";function S(){return S=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},S.apply(null,arguments)}function E(e){return E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},E(e)}function P(e,n){if("object"!=E(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=E(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}function j(e){var n=P(e,"string");return"symbol"==E(n)?n:n+""}function I(e,n,t){return(n=j(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function k(e){if(Array.isArray(e))return e}function A(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,o,l,a,i=[],c=!0,u=!1;try{if(l=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=l.call(t)).done)&&(i.push(r.value),i.length!==n);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=t.return&&(a=t.return(),Object(a)!==a))return}finally{if(u)throw o}}return i}}function C(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function N(e,n){if(e){if("string"==typeof e)return C(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?C(e,n):void 0}}function T(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function D(e,n){return k(e)||A(e,n)||N(e,n)||T()}var R=o.extend({defaultProps:{__TYPE:"OverlayPanel",id:null,dismissable:!0,showCloseIcon:!1,closeIcon:null,style:null,className:null,appendTo:null,breakpoints:null,ariaCloseLabel:null,transitionOptions:null,onShow:null,onHide:null,children:void 0,closeOnEscape:!0},css:{classes:{root:function(e){var t=e.context;return g("p-overlaypanel p-component",{"p-input-filled":t&&"filled"===t.inputStyle||"filled"===n.inputStyle,"p-ripple-disabled":t&&!1===t.ripple||!1===n.ripple})},closeIcon:"p-overlaypanel-close-icon",closeButton:"p-overlaypanel-close p-link",content:"p-overlaypanel-content",transition:"p-overlaypanel"},styles:'\n@layer primereact {\n    .p-overlaypanel {\n        position: absolute;\n        margin-top: 10px;\n        /* Github #3122: Prevent animation flickering  */\n        top: -9999px;\n        left: -9999px;\n    }\n    \n    .p-overlaypanel-flipped {\n        margin-top: -10px;\n        margin-bottom: 10px;\n    }\n    \n    .p-overlaypanel-close {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        overflow: hidden;\n        position: relative;\n    }\n    \n    /* Animation */\n    .p-overlaypanel-enter {\n        opacity: 0;\n        transform: scaleY(0.8);\n    }\n    \n    .p-overlaypanel-enter-active {\n        opacity: 1;\n        transform: scaleY(1);\n        transition: transform .12s cubic-bezier(0, 0, 0.2, 1), opacity .12s cubic-bezier(0, 0, 0.2, 1);\n    }\n    \n    .p-overlaypanel-enter-done {\n        transform: none;\n    }\n    \n    .p-overlaypanel-exit {\n        opacity: 1;\n    }\n    \n    .p-overlaypanel-exit-active {\n        opacity: 0;\n        transition: opacity .1s linear;\n    }\n    \n    .p-overlaypanel:after, .p-overlaypanel:before {\n        bottom: 100%;\n        left: calc(var(--overlayArrowLeft, 0) + 1.25rem);\n        content: " ";\n        height: 0;\n        width: 0;\n        position: absolute;\n        pointer-events: none;\n    }\n    \n    .p-overlaypanel:after {\n        border-width: 8px;\n        margin-left: -8px;\n    }\n    \n    .p-overlaypanel:before {\n        border-width: 10px;\n        margin-left: -10px;\n    }\n    \n    .p-overlaypanel-flipped:after, .p-overlaypanel-flipped:before {\n        bottom: auto;\n        top: 100%;\n    }\n    \n    .p-overlaypanel.p-overlaypanel-flipped:after {\n        border-bottom-color: transparent;\n    }\n    \n    .p-overlaypanel.p-overlaypanel-flipped:before {\n        border-bottom-color: transparent\n    }\n}\n'}});function L(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function H(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?L(Object(t),!0).forEach((function(n){I(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):L(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var z=e.forwardRef((function(o,E){var P=i(),j=e.useContext(t),I=R.getProps(o,j),k=D(e.useState(!1),2),A=k[0],C=k[1],N=R.setMetaData({props:I,state:{visible:A}}),T=N.ptm,L=N.cx,z=N.isUnstyled;l(R.css.styles,z,{name:"overlaypanel"});var M=e.useRef(""),Y=e.useRef(null),B=e.useRef(null),_=e.useRef(!1),U=e.useRef(null),V=e.useRef(null),Z=D(c({target:B,overlay:Y,listener:function(e,n){n.valid&&("outside"===n.type?(I.dismissable&&!_.current&&ee(),_.current=!1):j.hideOverlaysOnDocumentScrolling?ee():h.isDocument(e.target)||le())},when:A}),2),G=Z[0],J=Z[1],X=A&&I.closeOnEscape,$=u("overlay-panel",X);p({callback:function(){ee()},when:X&&$,priority:[s.OVERLAY_PANEL,$]});var q=function(e){return Y&&Y.current&&!(Y.current.isSameNode(e)||Y.current.contains(e))},F=function(e,n){return null!=B.current&&B.current!==(n||e.currentTarget||e.target)},K=function(){_.current=!0},Q=function(e,n){A?(ee(),F(e,n)&&(B.current=n||e.currentTarget||e.target,setTimeout((function(){W(e,B.current)}),200))):W(e,n)},W=function(e,n){B.current=n||e.currentTarget||e.target,A?le():(C(!0),V.current=function(e){!q(e.target)&&(_.current=!0)},v.on("overlay-click",V.current))},ee=function(){C(!1),v.off("overlay-click",V.current),V.current=null},ne=function(){Y.current.setAttribute(M.current,""),w.set("overlay",Y.current,j&&j.autoZIndex||n.autoZIndex,j&&j.zIndex.overlay||n.zIndex.overlay),h.addStyles(Y.current,{position:"absolute",top:"0",left:"0"}),le()},te=function(){G(),I.onShow&&I.onShow()},re=function(){J()},oe=function(){w.clear(Y.current),I.onHide&&I.onHide()},le=function(){if(B.current&&Y.current){h.absolutePosition(Y.current,B.current);var e=h.getOffset(Y.current),n=h.getOffset(B.current),t=0;e.left<n.left&&(t=n.left-e.left),Y.current.style.setProperty("--overlayArrowLeft","".concat(t,"px")),e.top<n.top?(Y.current.setAttribute("data-p-overlaypanel-flipped","true"),z&&h.addClass(Y.current,"p-overlaypanel-flipped")):(Y.current.setAttribute("data-p-overlaypanel-flipped","false"),z&&h.removeClass(Y.current,"p-overlaypanel-flipped"))}},ae=function(){if(!U.current){U.current=h.createInlineStyle(j&&j.nonce||n.nonce,j&&j.styleContainer);var e="";for(var t in I.breakpoints)e+="\n                    @media screen and (max-width: ".concat(t,") {\n                        .p-overlaypanel[").concat(M.current,"] {\n                            width: ").concat(I.breakpoints[t],";\n                        }\n                    }\n                ");U.current.innerHTML=e}};f((function(){M.current=O(),I.breakpoints&&ae()})),y((function(){U.current=h.removeInlineStyle(U.current),V.current&&(v.off("overlay-click",V.current),V.current=null),w.clear(Y.current)})),e.useImperativeHandle(E,(function(){return{props:I,toggle:Q,show:W,hide:ee,align:le,isVisible:function(){return A},getElement:function(){return Y.current}}}));var ie,ce,ue,pe,se=function(){var n=P({className:L("closeIcon"),"aria-hidden":!0},T("closeIcon")),t=x.getJSXIcon(I.closeIcon||e.createElement(m,n),H({},n),{props:I}),o=P({type:"button",className:L("closeButton"),onClick:function(e){return n=e,ee(),void n.preventDefault();var n},"aria-label":I.ariaCloseLabel||r("close")},T("closeButton"));return I.showCloseIcon?e.createElement("button",o,t,e.createElement(b,null)):null},fe=(ie=se(),ce=P({id:I.id,className:g(I.className,L("root",{context:j})),style:I.style,onClick:function(e){return n=e,_.current=!0,void v.emit("overlay-click",{originalEvent:n,target:B.current});var n}},R.getOtherProps(I),T("root")),ue=P({className:L("content"),onClick:function(e){return K()},onMouseDown:K},R.getOtherProps(I),T("content")),pe=P({classNames:L("transition"),in:A,timeout:{enter:120,exit:100},options:I.transitionOptions,unmountOnExit:!0,onEnter:ne,onEntered:te,onExit:re,onExited:oe},T("transition")),e.createElement(a,S({nodeRef:Y},pe),e.createElement("div",S({ref:Y},ce),e.createElement("div",ue,I.children),ie)));return e.createElement(d,{element:fe,appendTo:I.appendTo})}));z.displayName="OverlayPanel";export{z as OverlayPanel};
