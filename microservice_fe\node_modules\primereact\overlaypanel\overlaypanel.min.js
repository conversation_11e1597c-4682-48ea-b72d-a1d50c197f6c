this.primereact=this.primereact||{},this.primereact.overlaypanel=function(e,n,t,r,o,a,l,i,c,u,s){"use strict";function p(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function f(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}})),n.default=e,Object.freeze(n)}var y=f(n),d=p(t);function v(){return v=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},v.apply(null,arguments)}function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}function b(e,n){if("object"!=m(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=m(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}function g(e){var n=b(e,"string");return"symbol"==m(n)?n:n+""}function O(e,n,t){return(n=g(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function h(e){if(Array.isArray(e))return e}function S(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,o,a,l,i=[],c=!0,u=!1;try{if(a=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=a.call(t)).done)&&(i.push(r.value),i.length!==n);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=t.return&&(l=t.return(),Object(l)!==l))return}finally{if(u)throw o}}return i}}function w(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function x(e,n){if(e){if("string"==typeof e)return w(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?w(e,n):void 0}}function P(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function E(e,n){return h(e)||S(e,n)||x(e,n)||P()}var j=r.ComponentBase.extend({defaultProps:{__TYPE:"OverlayPanel",id:null,dismissable:!0,showCloseIcon:!1,closeIcon:null,style:null,className:null,appendTo:null,breakpoints:null,ariaCloseLabel:null,transitionOptions:null,onShow:null,onHide:null,children:void 0,closeOnEscape:!0},css:{classes:{root:function(e){var n=e.context;return s.classNames("p-overlaypanel p-component",{"p-input-filled":n&&"filled"===n.inputStyle||"filled"===d.default.inputStyle,"p-ripple-disabled":n&&!1===n.ripple||!1===d.default.ripple})},closeIcon:"p-overlaypanel-close-icon",closeButton:"p-overlaypanel-close p-link",content:"p-overlaypanel-content",transition:"p-overlaypanel"},styles:'\n@layer primereact {\n    .p-overlaypanel {\n        position: absolute;\n        margin-top: 10px;\n        /* Github #3122: Prevent animation flickering  */\n        top: -9999px;\n        left: -9999px;\n    }\n    \n    .p-overlaypanel-flipped {\n        margin-top: -10px;\n        margin-bottom: 10px;\n    }\n    \n    .p-overlaypanel-close {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        overflow: hidden;\n        position: relative;\n    }\n    \n    /* Animation */\n    .p-overlaypanel-enter {\n        opacity: 0;\n        transform: scaleY(0.8);\n    }\n    \n    .p-overlaypanel-enter-active {\n        opacity: 1;\n        transform: scaleY(1);\n        transition: transform .12s cubic-bezier(0, 0, 0.2, 1), opacity .12s cubic-bezier(0, 0, 0.2, 1);\n    }\n    \n    .p-overlaypanel-enter-done {\n        transform: none;\n    }\n    \n    .p-overlaypanel-exit {\n        opacity: 1;\n    }\n    \n    .p-overlaypanel-exit-active {\n        opacity: 0;\n        transition: opacity .1s linear;\n    }\n    \n    .p-overlaypanel:after, .p-overlaypanel:before {\n        bottom: 100%;\n        left: calc(var(--overlayArrowLeft, 0) + 1.25rem);\n        content: " ";\n        height: 0;\n        width: 0;\n        position: absolute;\n        pointer-events: none;\n    }\n    \n    .p-overlaypanel:after {\n        border-width: 8px;\n        margin-left: -8px;\n    }\n    \n    .p-overlaypanel:before {\n        border-width: 10px;\n        margin-left: -10px;\n    }\n    \n    .p-overlaypanel-flipped:after, .p-overlaypanel-flipped:before {\n        bottom: auto;\n        top: 100%;\n    }\n    \n    .p-overlaypanel.p-overlaypanel-flipped:after {\n        border-bottom-color: transparent;\n    }\n    \n    .p-overlaypanel.p-overlaypanel-flipped:before {\n        border-bottom-color: transparent\n    }\n}\n'}});function I(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function D(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?I(Object(t),!0).forEach((function(n){O(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):I(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var k=y.forwardRef((function(e,n){var p=a.useMergeProps(),f=y.useContext(t.PrimeReactContext),m=j.getProps(e,f),b=E(y.useState(!1),2),g=b[0],O=b[1],h=j.setMetaData({props:m,state:{visible:g}}),S=h.ptm,w=h.cx,x=h.isUnstyled;r.useHandleStyle(j.css.styles,x,{name:"overlaypanel"});var P=y.useRef(""),I=y.useRef(null),k=y.useRef(null),C=y.useRef(!1),H=y.useRef(null),A=y.useRef(null),N=E(a.useOverlayListener({target:k,overlay:I,listener:function(e,n){n.valid&&("outside"===n.type?(m.dismissable&&!C.current&&B(),C.current=!1):f.hideOverlaysOnDocumentScrolling?B():s.DomHandler.isDocument(e.target)||J())},when:g}),2),R=N[0],T=N[1],L=g&&m.closeOnEscape,_=a.useDisplayOrder("overlay-panel",L);a.useGlobalOnEscapeKey({callback:function(){B()},when:L&&_,priority:[a.ESC_KEY_HANDLING_PRIORITIES.OVERLAY_PANEL,_]});var M=function(e){return I&&I.current&&!(I.current.isSameNode(e)||I.current.contains(e))},U=function(e,n){return null!=k.current&&k.current!==(n||e.currentTarget||e.target)},z=function(){C.current=!0},Y=function(e,n){g?(B(),U(e,n)&&(k.current=n||e.currentTarget||e.target,setTimeout((function(){Z(e,k.current)}),200))):Z(e,n)},Z=function(e,n){k.current=n||e.currentTarget||e.target,g?J():(O(!0),A.current=function(e){!M(e.target)&&(C.current=!0)},i.OverlayService.on("overlay-click",A.current))},B=function(){O(!1),i.OverlayService.off("overlay-click",A.current),A.current=null},G=function(){I.current.setAttribute(P.current,""),s.ZIndexUtils.set("overlay",I.current,f&&f.autoZIndex||d.default.autoZIndex,f&&f.zIndex.overlay||d.default.zIndex.overlay),s.DomHandler.addStyles(I.current,{position:"absolute",top:"0",left:"0"}),J()},K=function(){R(),m.onShow&&m.onShow()},V=function(){T()},q=function(){s.ZIndexUtils.clear(I.current),m.onHide&&m.onHide()},J=function(){if(k.current&&I.current){s.DomHandler.absolutePosition(I.current,k.current);var e=s.DomHandler.getOffset(I.current),n=s.DomHandler.getOffset(k.current),t=0;e.left<n.left&&(t=n.left-e.left),I.current.style.setProperty("--overlayArrowLeft","".concat(t,"px")),e.top<n.top?(I.current.setAttribute("data-p-overlaypanel-flipped","true"),x&&s.DomHandler.addClass(I.current,"p-overlaypanel-flipped")):(I.current.setAttribute("data-p-overlaypanel-flipped","false"),x&&s.DomHandler.removeClass(I.current,"p-overlaypanel-flipped"))}},X=function(){if(!H.current){H.current=s.DomHandler.createInlineStyle(f&&f.nonce||d.default.nonce,f&&f.styleContainer);var e="";for(var n in m.breakpoints)e+="\n                    @media screen and (max-width: ".concat(n,") {\n                        .p-overlaypanel[").concat(P.current,"] {\n                            width: ").concat(m.breakpoints[n],";\n                        }\n                    }\n                ");H.current.innerHTML=e}};a.useMountEffect((function(){P.current=s.UniqueComponentId(),m.breakpoints&&X()})),a.useUnmountEffect((function(){H.current=s.DomHandler.removeInlineStyle(H.current),A.current&&(i.OverlayService.off("overlay-click",A.current),A.current=null),s.ZIndexUtils.clear(I.current)})),y.useImperativeHandle(n,(function(){return{props:m,toggle:Y,show:Z,hide:B,align:J,isVisible:function(){return g},getElement:function(){return I.current}}}));var $,F,Q,W,ee=function(){var e=p({className:w("closeIcon"),"aria-hidden":!0},S("closeIcon")),n=s.IconUtils.getJSXIcon(m.closeIcon||y.createElement(l.TimesIcon,e),D({},e),{props:m}),r=p({type:"button",className:w("closeButton"),onClick:function(e){return n=e,B(),void n.preventDefault();var n},"aria-label":m.ariaCloseLabel||t.ariaLabel("close")},S("closeButton"));return m.showCloseIcon?y.createElement("button",r,n,y.createElement(u.Ripple,null)):null},ne=($=ee(),F=p({id:m.id,className:s.classNames(m.className,w("root",{context:f})),style:m.style,onClick:function(e){return n=e,C.current=!0,void i.OverlayService.emit("overlay-click",{originalEvent:n,target:k.current});var n}},j.getOtherProps(m),S("root")),Q=p({className:w("content"),onClick:function(e){return z()},onMouseDown:z},j.getOtherProps(m),S("content")),W=p({classNames:w("transition"),in:g,timeout:{enter:120,exit:100},options:m.transitionOptions,unmountOnExit:!0,onEnter:G,onEntered:K,onExit:V,onExited:q},S("transition")),y.createElement(o.CSSTransition,v({nodeRef:I},W),y.createElement("div",v({ref:I},F),y.createElement("div",Q,m.children),$)));return y.createElement(c.Portal,{element:ne,appendTo:m.appendTo})}));return k.displayName="OverlayPanel",e.OverlayPanel=k,Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,primereact.api,primereact.componentbase,primereact.csstransition,primereact.hooks,primereact.icons.times,primereact.overlayservice,primereact.portal,primereact.ripple,primereact.utils);
