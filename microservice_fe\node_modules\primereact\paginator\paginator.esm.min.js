import*as e from"react";import{PrimeReactContext as t,aria<PERSON>abel as a,localeOption as n}from"primereact/api";import{ComponentBase as r,useHandleStyle as o}from"primereact/componentbase";import{useMergeProps as l,useUpdateEffect as i}from"primereact/hooks";import{classNames as s,ObjectUtils as p,IconUtils as c}from"primereact/utils";import{AngleDoubleLeftIcon as u}from"primereact/icons/angledoubleleft";import{Ripple as d}from"primereact/ripple";import{InputNumber as g}from"primereact/inputnumber";import{AngleDoubleRightIcon as m}from"primereact/icons/angledoubleright";import{AngleRightIcon as f}from"primereact/icons/angleright";import{AngleLeftIcon as b}from"primereact/icons/angleleft";import{Dropdown as P}from"primereact/dropdown";function v(e){if(Array.isArray(e))return e}function y(e,t){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var n,r,o,l,i=[],s=!0,p=!1;try{if(o=(a=a.call(e)).next,0===t){if(Object(a)!==a)return;s=!1}else for(;!(s=(n=o.call(a)).done)&&(i.push(n.value),i.length!==t);s=!0);}catch(e){p=!0,r=e}finally{try{if(!s&&null!=a.return&&(l=a.return(),Object(l)!==l))return}finally{if(p)throw r}}return i}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=Array(t);a<t;a++)n[a]=e[a];return n}function w(e,t){if(e){if("string"==typeof e)return h(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?h(e,t):void 0}}function O(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function E(e){return E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},E(e)}function N(e,t){if("object"!=E(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var n=a.call(e,t||"default");if("object"!=E(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function k(e){var t=N(e,"string");return"symbol"==E(t)?t:t+""}function S(e,t,a){return(t=k(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var T=r.extend({defaultProps:{__TYPE:"Paginator",__parentMetadata:null,totalRecords:0,rows:0,first:0,pageLinkSize:5,rowsPerPageOptions:null,alwaysShow:!0,style:null,className:null,template:"FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown",onPageChange:null,leftContent:null,rightContent:null,dropdownAppendTo:null,currentPageReportTemplate:"({currentPage} of {totalPages})",children:void 0},css:{classes:{root:"p-paginator p-component",left:"p-paginator-left-content",end:"p-paginator-right-content",firstPageIcon:"p-paginator-icon",firstPageButton:function(e){return s("p-paginator-first p-paginator-element p-link",{"p-disabled":e.disabled})},prevPageIcon:"p-paginator-icon",prevPageButton:function(e){return s("p-paginator-prev p-paginator-element p-link",{"p-disabled":e.disabled})},nextPageIcon:"p-paginator-icon",nextPageButton:function(e){return s("p-paginator-next p-paginator-element p-link",{"p-disabled":e.disabled})},lastPageIcon:"p-paginator-icon",lastPageButton:function(e){return s("p-paginator-last p-paginator-element p-link",{"p-disabled":e.disabled})},pageButton:function(e){var t=e.pageLink;return s("p-paginator-page p-paginator-element p-link",{"p-paginator-page-start":t===e.startPageInView,"p-paginator-page-end":t===e.endPageInView,"p-highlight":t-1===e.page})},pages:"p-paginator-pages"},styles:"\n@layer primereact {\n    .p-paginator {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-wrap: wrap;\n    }\n    \n    .p-paginator-left-content {\n        margin-right: auto;\n    }\n    \n    .p-paginator-right-content {\n        margin-left: auto;\n    }\n    \n    .p-paginator-page,\n    .p-paginator-next,\n    .p-paginator-last,\n    .p-paginator-first,\n    .p-paginator-prev,\n    .p-paginator-current {\n        cursor: pointer;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        line-height: 1;\n        user-select: none;\n        overflow: hidden;\n        position: relative;\n    }\n    \n    .p-paginator-element:focus {\n        z-index: 1;\n        position: relative;\n    }\n}\n"}}),C=r.extend({defaultProps:{__TYPE:"CurrentPageReport",pageCount:null,page:null,first:null,rows:null,totalRecords:null,reportTemplate:"({currentPage} of {totalPages})",template:null,children:void 0}}),L=r.extend({defaultProps:{__TYPE:"FirstPageLink",disabled:!1,onClick:null,template:null,firstPageLinkIcon:null,children:void 0}}),j=r.extend({defaultProps:{__TYPE:"JumpToPageInput",page:null,rows:null,pageCount:null,disabled:!1,template:null,onChange:null,children:void 0,metaData:null,ptm:null}}),x=r.extend({defaultProps:{__TYPE:"LastPageLink",disabled:!1,onClick:null,template:null,lastPageLinkIcon:null,children:void 0}}),R=r.extend({defaultProps:{__TYPE:"NextPageLink",disabled:!1,onClick:null,template:null,nextPageLinkIcon:null,children:void 0}}),_=r.extend({defaultProps:{__TYPE:"PageLinks",value:null,page:null,rows:null,pageCount:null,links:null,template:null,children:void 0}}),A=r.extend({defaultProps:{__TYPE:"PrevPageLink",disabled:!1,onClick:null,template:null,prevPageLinkIcon:null,children:void 0}}),I=r.extend({defaultProps:{__TYPE:"RowsPerPageDropdown",options:null,value:null,page:null,pageCount:null,totalRecords:0,appendTo:null,onChange:null,template:null,disabled:!1,children:void 0}});function D(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function M(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?D(Object(a),!0).forEach((function(t){S(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):D(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var B=e.memo((function(a){var n=l(),r=e.useContext(t),o=C.getProps(a,r),i={currentPage:o.page+1,totalPages:o.totalPages,first:Math.min(o.first+1,o.totalRecords),last:Math.min(o.first+o.rows,o.totalRecords),rows:o.rows,totalRecords:o.totalRecords},s=o.reportTemplate.replace("{currentPage}",i.currentPage).replace("{totalPages}",i.totalPages).replace("{first}",i.first).replace("{last}",i.last).replace("{rows}",i.rows).replace("{totalRecords}",i.totalRecords),c=n({"aria-live":"polite",className:"p-paginator-current"},o.ptm("current",{hostName:o.hostName})),u=e.createElement("span",c,s);if(o.template){var d=M(M({},i),{ariaLive:"polite",className:"p-paginator-current",element:u,props:o});return p.getJSXElement(o.template,d)}return u}));function F(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function J(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?F(Object(a),!0).forEach((function(t){S(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):F(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}B.displayName="CurrentPageReport";var H=e.memo((function(n){var r=l(),o=e.useContext(t),i=L.getProps(n,o),g=i.ptm,m=i.cx,f=function(e){return g(e,{hostName:i.hostName,context:{disabled:i.disabled}})},b=s("p-paginator-first p-paginator-element p-link",{"p-disabled":i.disabled}),P=r({className:m("firstPageIcon")},f("firstPageIcon")),v=c.getJSXIcon(i.firstPageLinkIcon||e.createElement(u,P),J({},P),{props:i}),y=r({type:"button",className:m("firstPageButton",{disabled:i.disabled}),onClick:i.onClick,disabled:i.disabled,"aria-label":a("firstPageLabel")},f("firstPageButton")),h=e.createElement("button",y,v,e.createElement(d,null));return i.template?p.getJSXElement(i.template,{onClick:i.onClick,className:b,iconClassName:"p-paginator-icon",disabled:i.disabled,element:h,props:i}):h}));function U(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,k(n.key),n)}}function Y(e,t,a){return t&&U(e.prototype,t),a&&U(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e}function W(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}H.displayName="FirstPageLink";var X=Object.freeze({STARTS_WITH:"startsWith",CONTAINS:"contains",NOT_CONTAINS:"notContains",ENDS_WITH:"endsWith",EQUALS:"equals",NOT_EQUALS:"notEquals",IN:"in",LESS_THAN:"lt",LESS_THAN_OR_EQUAL_TO:"lte",GREATER_THAN:"gt",GREATER_THAN_OR_EQUAL_TO:"gte",BETWEEN:"between",DATE_IS:"dateIs",DATE_IS_NOT:"dateIsNot",DATE_BEFORE:"dateBefore",DATE_AFTER:"dateAfter",CUSTOM:"custom"}),z=Y((function e(){W(this,e)}));S(z,"ripple",!1),S(z,"inputStyle","outlined"),S(z,"locale","en"),S(z,"appendTo",null),S(z,"cssTransition",!0),S(z,"autoZIndex",!0),S(z,"hideOverlaysOnDocumentScrolling",!1),S(z,"nonce",null),S(z,"nullSortOrder",1),S(z,"zIndex",{modal:1100,overlay:1e3,menu:1e3,tooltip:1100,toast:1200}),S(z,"pt",void 0),S(z,"filterMatchModeOptions",{text:[X.STARTS_WITH,X.CONTAINS,X.NOT_CONTAINS,X.ENDS_WITH,X.EQUALS,X.NOT_EQUALS],numeric:[X.EQUALS,X.NOT_EQUALS,X.LESS_THAN,X.LESS_THAN_OR_EQUAL_TO,X.GREATER_THAN,X.GREATER_THAN_OR_EQUAL_TO],date:[X.DATE_IS,X.DATE_IS_NOT,X.DATE_BEFORE,X.DATE_AFTER]}),S(z,"changeTheme",(function(e,t,a,n){var r,o=document.getElementById(a);if(!o)throw Error("Element with id ".concat(a," not found."));var l=o.getAttribute("href").replace(e,t),i=document.createElement("link");i.setAttribute("rel","stylesheet"),i.setAttribute("id",a),i.setAttribute("href",l),i.addEventListener("load",(function(){n&&n()})),null===(r=o.parentNode)||void 0===r||r.replaceChild(i,o)}));var Q={en:{accept:"Yes",addRule:"Add Rule",am:"AM",apply:"Apply",cancel:"Cancel",choose:"Choose",chooseDate:"Choose Date",chooseMonth:"Choose Month",chooseYear:"Choose Year",clear:"Clear",completed:"Completed",contains:"Contains",custom:"Custom",dateAfter:"Date is after",dateBefore:"Date is before",dateFormat:"mm/dd/yy",dateIs:"Date is",dateIsNot:"Date is not",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesMin:["Su","Mo","Tu","We","Th","Fr","Sa"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],emptyFilterMessage:"No results found",emptyMessage:"No available options",emptySearchMessage:"No results found",emptySelectionMessage:"No selected item",endsWith:"Ends with",equals:"Equals",fileSizeTypes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"],filter:"Filter",firstDayOfWeek:0,gt:"Greater than",gte:"Greater than or equal to",lt:"Less than",lte:"Less than or equal to",matchAll:"Match All",matchAny:"Match Any",medium:"Medium",monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],nextDecade:"Next Decade",nextHour:"Next Hour",nextMinute:"Next Minute",nextMonth:"Next Month",nextSecond:"Next Second",nextYear:"Next Year",noFilter:"No Filter",notContains:"Not contains",notEquals:"Not equals",now:"Now",passwordPrompt:"Enter a password",pending:"Pending",pm:"PM",prevDecade:"Previous Decade",prevHour:"Previous Hour",prevMinute:"Previous Minute",prevMonth:"Previous Month",prevSecond:"Previous Second",prevYear:"Previous Year",reject:"No",removeRule:"Remove Rule",searchMessage:"{0} results are available",selectionMessage:"{0} items selected",showMonthAfterYear:!1,startsWith:"Starts with",strong:"Strong",today:"Today",upload:"Upload",weak:"Weak",weekHeader:"Wk",aria:{cancelEdit:"Cancel Edit",close:"Close",collapseRow:"Row Collapsed",editRow:"Edit Row",expandRow:"Row Expanded",falseLabel:"False",filterConstraint:"Filter Constraint",filterOperator:"Filter Operator",firstPageLabel:"First Page",gridView:"Grid View",hideFilterMenu:"Hide Filter Menu",jumpToPageDropdownLabel:"Jump to Page Dropdown",jumpToPageInputLabel:"Jump to Page Input",lastPageLabel:"Last Page",listLabel:"Option List",listView:"List View",moveAllToSource:"Move All to Source",moveAllToTarget:"Move All to Target",moveBottom:"Move Bottom",moveDown:"Move Down",moveToSource:"Move to Source",moveToTarget:"Move to Target",moveTop:"Move Top",moveUp:"Move Up",navigation:"Navigation",next:"Next",nextPageLabel:"Next Page",nullLabel:"Not Selected",pageLabel:"Page {page}",otpLabel:"Please enter one time password character {0}",passwordHide:"Hide Password",passwordShow:"Show Password",previous:"Previous",prevPageLabel:"Previous Page",rotateLeft:"Rotate Left",rotateRight:"Rotate Right",rowsPerPageLabel:"Rows per page",saveEdit:"Save Edit",scrollTop:"Scroll Top",selectAll:"All items selected",selectRow:"Row Selected",showFilterMenu:"Show Filter Menu",slide:"Slide",slideNumber:"{slideNumber}",star:"1 star",stars:"{star} stars",trueLabel:"True",unselectAll:"All items unselected",unselectRow:"Row Unselected",zoomImage:"Zoom Image",zoomIn:"Zoom In",zoomOut:"Zoom Out"}}};function q(e,t){if(e.includes("__proto__")||e.includes("prototype"))throw new Error("Unsafe ariaKey detected");var a=z.locale;try{var n=G(a).aria[e];if(n)for(var r in t)t.hasOwnProperty(r)&&(n=n.replace("{".concat(r,"}"),t[r]));return n}catch(t){throw new Error("The ".concat(e," option is not found in the current locale('").concat(a,"')."))}}function G(e){var t=e||z.locale;if(t.includes("__proto__")||t.includes("prototype"))throw new Error("Unsafe locale detected");return Q[t]}var V=e.memo((function(a){l();var n=e.useContext(t),r=j.getProps(a,n),o=q("jumpToPageInputLabel"),i=function(e){r.onChange&&r.onChange(r.rows*(e.value-1),r.rows)},s=r.totalPages>0?r.page+1:0,c=e.createElement(g,{value:s,onChange:i,className:"p-paginator-page-input",disabled:r.disabled,pt:r.ptm("JTPInput"),unstyled:r.unstyled,__parentMetadata:{parent:r.metaData},"aria-label":o});return r.template?p.getJSXElement(r.template,{value:s,onChange:i,disabled:r.disabled,className:"p-paginator-page-input","aria-label":o,element:c,props:r}):c}));function Z(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function K(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?Z(Object(a),!0).forEach((function(t){S(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):Z(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}V.displayName="JumpToPageInput";var $=e.memo((function(n){var r=l(),o=e.useContext(t),i=x.getProps(n,o),u=i.ptm,g=i.cx,f=function(e){return u(e,{hostName:i.hostName,context:{disabled:i.disabled}})},b=s("p-paginator-last p-paginator-element p-link",{"p-disabled":i.disabled}),P=r({className:g("lastPageIcon")},f("lastPageIcon")),v=c.getJSXIcon(i.lastPageLinkIcon||e.createElement(m,P),K({},P),{props:i}),y=r({type:"button",className:g("lastPageButton",{disabled:i.disabled}),onClick:i.onClick,disabled:i.disabled,"aria-label":a("lastPageLabel")},f("lastPageButton")),h=e.createElement("button",y,v,e.createElement(d,null));return i.template?p.getJSXElement(i.template,{onClick:i.onClick,className:b,iconClassName:"p-paginator-icon",disabled:i.disabled,element:h,props:i}):h}));function ee(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function te(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?ee(Object(a),!0).forEach((function(t){S(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):ee(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}$.displayName="LastPageLink";var ae=e.memo((function(n){var r=l(),o=e.useContext(t),i=R.getProps(n,o),u=i.ptm,g=i.cx,m=function(e){return u(e,{hostName:i.hostName,context:{disabled:i.disabled}})},b=s("p-paginator-next p-paginator-element p-link",{"p-disabled":i.disabled}),P=r({className:g("nextPageIcon")},m("nextPageIcon")),v=c.getJSXIcon(i.nextPageLinkIcon||e.createElement(f,P),te({},P),{props:i}),y=r({type:"button",className:g("nextPageButton",{disabled:i.disabled}),onClick:i.onClick,disabled:i.disabled,"aria-label":a("nextPageLabel")},m("nextPageButton")),h=e.createElement("button",y,v,e.createElement(d,null));return i.template?p.getJSXElement(i.template,{onClick:i.onClick,className:b,iconClassName:"p-paginator-icon",disabled:i.disabled,element:h,nextPageLinkIcon:v,props:i}):h}));ae.displayName="NextPageLink";var ne=e.memo((function(n){var r,o=l(),i=e.useContext(t),c=_.getProps(n,i),u=c.ptm,g=c.cx,m=function(e,t){return u(t,{hostName:c.hostName,context:{active:e-1===c.page}})},f=function(e,t){c.onClick&&c.onClick({originalEvent:e,value:t}),e.preventDefault()};if(c.value){var b=c.value[0],P=c.value[c.value.length-1];r=c.value.map((function(t){var n=s("p-paginator-page p-paginator-element p-link",{"p-paginator-page-start":t===b,"p-paginator-page-end":t===P,"p-highlight":t-1===c.page}),r=o({type:"button",onClick:function(e){return f(e,t)},className:g("pageButton",{pageLink:t,startPageInView:b,endPageInView:P,page:c.page}),disabled:c.disabled,"aria-label":a("pageLabel",{page:t}),"aria-current":t-1===c.page?"true":void 0},m(t,"pageButton")),l=e.createElement("button",r,t,e.createElement(d,null));if(c.template){var i={onClick:function(e){return f(e,t)},className:n,view:{startPage:b-1,endPage:P-1},page:t-1,currentPage:c.page,totalPages:c.totalPages,ariaLabel:a("pageLabel",{page:t}),ariaCurrent:t-1===c.page?"true":void 0,element:l,props:c};l=p.getJSXElement(c.template,i)}return e.createElement(e.Fragment,{key:t},l)}))}var v=o({className:g("pages")},u("pages",{hostName:c.hostName}));return e.createElement("span",v,r)}));function re(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function oe(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?re(Object(a),!0).forEach((function(t){S(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):re(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}ne.displayName="PageLinks";var le=e.memo((function(n){var r=l(),o=e.useContext(t),i=A.getProps(n,o),u=i.ptm,g=i.cx,m=function(e){return u(e,{hostName:i.hostName,context:{disabled:i.disabled}})},f=s("p-paginator-prev p-paginator-element p-link",{"p-disabled":i.disabled}),P=r({className:g("prevPageIcon")},m("prevPageIcon")),v=c.getJSXIcon(i.prevPageLinkIcon||e.createElement(b,P),oe({},P),{props:i}),y=r({type:"button",className:g("prevPageButton",{disabled:i.disabled}),onClick:i.onClick,disabled:i.disabled,"aria-label":a("prevPageLabel")},m("prevPageButton")),h=e.createElement("button",y,v,e.createElement(d,null));return i.template?p.getJSXElement(i.template,{onClick:i.onClick,className:f,iconClassName:"p-paginator-icon",disabled:i.disabled,element:h,props:i}):h}));le.displayName="PrevPageLink";var ie=e.memo((function(a){l();var r=e.useContext(t),o=I.getProps(a,r),i=o.options&&o.options.length>0,s=i?o.options.map((function(e){return{label:String(e),value:e}})):[],c=n("choose"),u=q("jumpToPageDropdownLabel"),d=i?e.createElement(e.Fragment,null,e.createElement(P,{value:o.value,options:s,onChange:o.onChange,appendTo:o.appendTo,disabled:o.disabled,placeholder:c,"aria-label":u,pt:o.ptm("RPPDropdown"),unstyled:o.unstyled,__parentMetadata:{parent:o.metaData}})):null;return o.template?p.getJSXElement(o.template,{value:o.value,options:s,onChange:o.onChange,appendTo:o.appendTo,currentPage:o.page,totalPages:o.pageCount,totalRecords:o.totalRecords,disabled:o.disabled,ariaLabel:u,element:d,props:o}):d}));function se(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function pe(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?se(Object(a),!0).forEach((function(t){S(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):se(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}ie.displayName="RowsPerPageDropdown";var ce=e.memo(e.forwardRef((function(a,n){var r=l(),c=e.useContext(t),u=T.getProps(a,c),d=pe({props:u},u.__parentMetadata),g=T.setMetaData(d),m=g.ptm,f=g.cx;o(T.css.styles,g.isUnstyled,{name:"paginator"});var b=e.useRef(null),P=Math.floor(u.first/u.rows),h=Math.ceil(u.totalRecords/u.rows),N=0===P,k=P===h-1,S=0===h,C=function(){var e=h,t=Math.min(u.pageLinkSize,e),a=Math.max(0,Math.ceil(P-t/2)),n=Math.min(e-1,a+t-1);return[a=Math.max(0,a-(u.pageLinkSize-(n-a+1))),n]},L=function(){for(var e=[],t=C(),a=t[1],n=t[0];n<=a;n++)e.push(n+1);return e},j=function(e,t){var a=h,n=Math.floor(e/t);n>=0&&n<a&&(u.onPageChange&&u.onPageChange({first:e,rows:t,page:n,totalPages:a}))},x=function(e){j(0,u.rows),e.preventDefault()},R=function(e){j(u.first-u.rows,u.rows),e.preventDefault()},_=function(e){j((e.value-1)*u.rows,u.rows)},A=function(e){j(u.first+u.rows,u.rows),e.preventDefault()},I=function(e){j((h-1)*u.rows,u.rows),e.preventDefault()},D=function(e){j(0,e.value)};e.useImperativeHandle(n,(function(){return{props:u,getElement:function(){return b.current}}})),i((function(){P>0&&u.first>=u.totalRecords&&j((h-1)*u.rows,u.rows)}),[u.totalRecords]);var M=function(t,a){var n;switch(t){case"FirstPageLink":n=e.createElement(H,{hostName:"Paginator",key:t,page:P,totalPages:h,totalRecords:u.totalRecords,rows:u.rows,onClick:x,disabled:N||S,template:a,firstPageLinkIcon:u.firstPageLinkIcon,ptm:m,cx:f});break;case"PrevPageLink":n=e.createElement(le,{hostName:"Paginator",key:t,page:P,totalPages:h,totalRecords:u.totalRecords,rows:u.rows,onClick:R,disabled:N||S,template:a,prevPageLinkIcon:u.prevPageLinkIcon,ptm:m,cx:f});break;case"NextPageLink":n=e.createElement(ae,{hostName:"Paginator",key:t,page:P,totalPages:h,totalRecords:u.totalRecords,rows:u.rows,onClick:A,disabled:k||S,template:a,nextPageLinkIcon:u.nextPageLinkIcon,ptm:m,cx:f});break;case"LastPageLink":n=e.createElement($,{hostName:"Paginator",key:t,page:P,totalPages:h,totalRecords:u.totalRecords,rows:u.rows,onClick:I,disabled:k||S,template:a,lastPageLinkIcon:u.lastPageLinkIcon,ptm:m,cx:f});break;case"PageLinks":n=e.createElement(ne,{hostName:"Paginator",key:t,page:P,totalPages:h,totalRecords:u.totalRecords,rows:u.rows,value:L(),onClick:_,template:a,ptm:m,cx:f});break;case"RowsPerPageDropdown":n=e.createElement(ie,{hostName:"Paginator",key:t,value:u.rows,page:P,totalPages:h,totalRecords:u.totalRecords,options:u.rowsPerPageOptions,onChange:D,appendTo:u.dropdownAppendTo,template:a,disabled:S,unstyled:u.unstyled,ptm:m,cx:f,metaData:d});break;case"CurrentPageReport":n=e.createElement(B,{hostName:"Paginator",reportTemplate:u.currentPageReportTemplate,key:t,page:P,totalPages:h,totalRecords:u.totalRecords,rows:u.rows,first:u.first,template:a,ptm:m});break;case"JumpToPageInput":n=e.createElement(V,{hostName:"Paginator",key:t,rows:u.rows,page:P,totalPages:h,onChange:j,disabled:S,template:a,ptm:m,unstyled:u.unstyled,metaData:d});break;default:n=null}return n};if(!u.alwaysShow&&h<=1)return null;var F,J=p.getJSXElement(u.leftContent,u),U=p.getJSXElement(u.rightContent,u),Y=(F=u.template)?"object"===E(F)?F.layout?F.layout.split(" ").map((function(e){var t=e.trim();return M(t,F[t])})):Object.entries(F).map((function(e){var t,a,n=(a=2,v(t=e)||y(t,a)||w(t,a)||O());return M(n[0],n[1])})):F.split(" ").map((function(e){return M(e.trim())})):null,W=r({className:f("left")},m("left")),X=J&&e.createElement("div",W,J),z=r({className:f("end")},m("end")),Q=U&&e.createElement("div",z,U),q=r({ref:b,className:s(u.className,f("root")),style:u.style},T.getOtherProps(u),m("root"));return e.createElement("div",q,X,Y,Q)})));ce.displayName="Paginator";export{ce as Paginator};
