"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("primereact/api"),n=require("primereact/componentbase"),l=require("primereact/csstransition"),r=require("primereact/hooks"),a=require("primereact/icons/minus"),o=require("primereact/icons/plus"),i=require("primereact/ripple"),s=require("primereact/utils");function c(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var l=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,l.get?l:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var p=c(e);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},u.apply(null,arguments)}function g(e){if(Array.isArray(e))return e}function d(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var l,r,a,o,i=[],s=!0,c=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(l=a.call(n)).done)&&(i.push(l.value),i.length!==t);s=!0);}catch(e){c=!0,r=e}finally{try{if(!s&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return i}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,l=Array(t);n<t;n++)l[n]=e[n];return l}function f(e,t){if(e){if("string"==typeof e)return m(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(e,t):void 0}}function b(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(e,t){return g(e)||d(e,t)||f(e,t)||b()}var v=n.ComponentBase.extend({defaultProps:{__TYPE:"Panel",id:null,header:null,headerTemplate:null,footer:null,footerTemplate:null,toggleable:null,style:null,className:null,collapsed:null,expandIcon:null,collapseIcon:null,icons:null,transitionOptions:null,onExpand:null,onCollapse:null,onToggle:null,children:void 0},css:{classes:{root:function(e){return s.classNames("p-panel p-component",{"p-panel-toggleable":e.props.toggleable})},header:"p-panel-header",title:"p-panel-title",icons:"p-panel-icons",toggler:"p-panel-header-icon p-panel-toggler p-link",togglerIcon:"p-panel-header-icon p-panel-toggler p-link",toggleableContent:"p-toggleable-content",content:"p-panel-content",footer:"p-panel-footer",transition:"p-toggleable-content"},styles:"\n        @layer primereact {\n            .p-panel-header {\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\n            }\n            \n            .p-panel-title {\n              line-height: 1;\n            }\n            \n            .p-panel-header-icon {\n              display: inline-flex;\n              justify-content: center;\n              align-items: center;\n              cursor: pointer;\n              text-decoration: none;\n              overflow: hidden;\n              position: relative;\n            }\n        }\n        "}}),y=p.forwardRef((function(e,c){var g=r.useMergeProps(),d=p.useContext(t.PrimeReactContext),m=v.getProps(e,d),f=h(p.useState(m.id),2),b=f[0],y=f[1],E=h(p.useState(m.collapsed),2),O=E[0],j=E[1],x=p.useRef(null),N=p.useRef(null),C=!!m.toggleable&&(m.onToggle?m.collapsed:O),S=b+"_header",T=b+"_content",I=v.setMetaData({props:m,state:{id:b,collapsed:C}}),P=I.ptm,q=I.cx;n.useHandleStyle(v.css.styles,I.isUnstyled,{name:"panel"});var _=function(e){m.toggleable&&(C?U(e):w(e),e&&(m.onToggle&&m.onToggle({originalEvent:e,value:!C}),e.preventDefault()))},U=function(e){m.onToggle||j(!1),m.onExpand&&e&&m.onExpand(e)},w=function(e){m.onToggle||j(!0),m.onCollapse&&e&&m.onCollapse(e)};p.useImperativeHandle(c,(function(){return{props:m,toggle:_,expand:U,collapse:w,getElement:function(){return x.current},getContent:function(){return N.current}}})),r.useMountEffect((function(){b||y(s.UniqueComponentId())}));var k,M,A,J=function(){if(m.toggleable){var e=b+"_label",t=g({className:q("toggler"),onClick:_,id:e,"aria-controls":T,"aria-expanded":!C,type:"button",role:"button","aria-label":m.header},P("toggler")),n=g(P("togglericon")),l=s.IconUtils.getJSXIcon(C?m.expandIcon||p.createElement(o.PlusIcon,n):m.collapseIcon||p.createElement(a.MinusIcon,n),n,{props:m,collapsed:C});return p.createElement("button",t,l,p.createElement(i.Ripple,null))}return null},R=g({id:b,ref:x,style:m.style,className:s.classNames(m.className,q("root"))},v.getOtherProps(m),P("root")),X=function(){var e=s.ObjectUtils.getJSXElement(m.header,m),t=s.ObjectUtils.getJSXElement(m.icons,m),n=J(),l=g({id:S,className:q("title")},P("title")),r=p.createElement("span",l,e),a=g({className:q("icons")},P("icons")),o=p.createElement("div",a,t,n),i=g({className:q("header")},P("header")),c=p.createElement("div",i,r,o);return m.headerTemplate?s.ObjectUtils.getJSXElement(m.headerTemplate,{className:"p-panel-header",titleClassName:"p-panel-title",iconsClassName:"p-panel-icons",togglerClassName:"p-panel-header-icon p-panel-toggler p-link",onTogglerClick:_,titleElement:r,iconsElement:o,togglerElement:n,element:c,id:b+"_header",props:m,collapsed:C}):m.header||m.toggleable?c:null}(),D=(k=g({ref:N,className:q("toggleableContent"),"aria-hidden":C,role:"region",id:T,"aria-labelledby":S},P("toggleablecontent")),M=g({className:q("content")},P("content")),A=g({classNames:q("transition"),timeout:{enter:1e3,exit:450},in:!C,unmountOnExit:!0,options:m.transitionOptions},P("transition")),p.createElement(l.CSSTransition,u({nodeRef:N},A),p.createElement("div",k,p.createElement("div",M,m.children)))),H=function(){var e=s.ObjectUtils.getJSXElement(m.footer,m),t=g({className:q("footer")},P("footer")),n=p.createElement("div",t,e);if(m.footerTemplate){var l={className:q("footer"),element:n,props:m};return s.ObjectUtils.getJSXElement(m.footerTemplate,l)}return m.footer?n:null}();return p.createElement("div",R,X,D,H)}));y.displayName="Panel",exports.Panel=y;
