import*as e from"react";import{PrimeReactContext as n}from"primereact/api";import{ComponentBase as t,useHandleStyle as l}from"primereact/componentbase";import{CSSTransition as r}from"primereact/csstransition";import{useMergeProps as o,useMountEffect as a}from"primereact/hooks";import{MinusIcon as i}from"primereact/icons/minus";import{PlusIcon as s}from"primereact/icons/plus";import{Ripple as c}from"primereact/ripple";import{classNames as p,UniqueComponentId as u,ObjectUtils as m,IconUtils as g}from"primereact/utils";function d(){return d=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var l in t)({}).hasOwnProperty.call(t,l)&&(e[l]=t[l])}return e},d.apply(null,arguments)}function f(e){if(Array.isArray(e))return e}function h(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var l,r,o,a,i=[],s=!0,c=!1;try{if(o=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;s=!1}else for(;!(s=(l=o.call(t)).done)&&(i.push(l.value),i.length!==n);s=!0);}catch(e){c=!0,r=e}finally{try{if(!s&&null!=t.return&&(a=t.return(),Object(a)!==a))return}finally{if(c)throw r}}return i}}function b(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,l=Array(n);t<n;t++)l[t]=e[t];return l}function v(e,n){if(e){if("string"==typeof e)return b(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?b(e,n):void 0}}function y(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function E(e,n){return f(e)||h(e,n)||v(e,n)||y()}var x=t.extend({defaultProps:{__TYPE:"Panel",id:null,header:null,headerTemplate:null,footer:null,footerTemplate:null,toggleable:null,style:null,className:null,collapsed:null,expandIcon:null,collapseIcon:null,icons:null,transitionOptions:null,onExpand:null,onCollapse:null,onToggle:null,children:void 0},css:{classes:{root:function(e){return p("p-panel p-component",{"p-panel-toggleable":e.props.toggleable})},header:"p-panel-header",title:"p-panel-title",icons:"p-panel-icons",toggler:"p-panel-header-icon p-panel-toggler p-link",togglerIcon:"p-panel-header-icon p-panel-toggler p-link",toggleableContent:"p-toggleable-content",content:"p-panel-content",footer:"p-panel-footer",transition:"p-toggleable-content"},styles:"\n        @layer primereact {\n            .p-panel-header {\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\n            }\n            \n            .p-panel-title {\n              line-height: 1;\n            }\n            \n            .p-panel-header-icon {\n              display: inline-flex;\n              justify-content: center;\n              align-items: center;\n              cursor: pointer;\n              text-decoration: none;\n              overflow: hidden;\n              position: relative;\n            }\n        }\n        "}}),N=e.forwardRef((function(t,f){var h=o(),b=e.useContext(n),v=x.getProps(t,b),y=E(e.useState(v.id),2),N=y[0],T=y[1],C=E(e.useState(v.collapsed),2),S=C[0],I=C[1],O=e.useRef(null),j=e.useRef(null),w=!!v.toggleable&&(v.onToggle?v.collapsed:S),P=N+"_header",k=N+"_content",A=x.setMetaData({props:v,state:{id:N,collapsed:w}}),J=A.ptm,X=A.cx;l(x.css.styles,A.isUnstyled,{name:"panel"});var _=function(e){v.toggleable&&(w?R(e):D(e),e&&(v.onToggle&&v.onToggle({originalEvent:e,value:!w}),e.preventDefault()))},R=function(e){v.onToggle||I(!1),v.onExpand&&e&&v.onExpand(e)},D=function(e){v.onToggle||I(!0),v.onCollapse&&e&&v.onCollapse(e)};e.useImperativeHandle(f,(function(){return{props:v,toggle:_,expand:R,collapse:D,getElement:function(){return O.current},getContent:function(){return j.current}}})),a((function(){N||T(u())}));var M,U,H,Y=function(){if(v.toggleable){var n=N+"_label",t=h({className:X("toggler"),onClick:_,id:n,"aria-controls":k,"aria-expanded":!w,type:"button",role:"button","aria-label":v.header},J("toggler")),l=h(J("togglericon")),r=g.getJSXIcon(w?v.expandIcon||e.createElement(s,l):v.collapseIcon||e.createElement(i,l),l,{props:v,collapsed:w});return e.createElement("button",t,r,e.createElement(c,null))}return null},$=h({id:N,ref:O,style:v.style,className:p(v.className,X("root"))},x.getOtherProps(v),J("root")),q=function(){var n=m.getJSXElement(v.header,v),t=m.getJSXElement(v.icons,v),l=Y(),r=h({id:P,className:X("title")},J("title")),o=e.createElement("span",r,n),a=h({className:X("icons")},J("icons")),i=e.createElement("div",a,t,l),s=h({className:X("header")},J("header")),c=e.createElement("div",s,o,i);return v.headerTemplate?m.getJSXElement(v.headerTemplate,{className:"p-panel-header",titleClassName:"p-panel-title",iconsClassName:"p-panel-icons",togglerClassName:"p-panel-header-icon p-panel-toggler p-link",onTogglerClick:_,titleElement:o,iconsElement:i,togglerElement:l,element:c,id:N+"_header",props:v,collapsed:w}):v.header||v.toggleable?c:null}(),z=(M=h({ref:j,className:X("toggleableContent"),"aria-hidden":w,role:"region",id:k,"aria-labelledby":P},J("toggleablecontent")),U=h({className:X("content")},J("content")),H=h({classNames:X("transition"),timeout:{enter:1e3,exit:450},in:!w,unmountOnExit:!0,options:v.transitionOptions},J("transition")),e.createElement(r,d({nodeRef:j},H),e.createElement("div",M,e.createElement("div",U,v.children)))),B=function(){var n=m.getJSXElement(v.footer,v),t=h({className:X("footer")},J("footer")),l=e.createElement("div",t,n);if(v.footerTemplate){var r={className:X("footer"),element:l,props:v};return m.getJSXElement(v.footerTemplate,r)}return v.footer?l:null}();return e.createElement("div",$,q,z,B)}));N.displayName="Panel";export{N as Panel};
