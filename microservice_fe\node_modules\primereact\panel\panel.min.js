this.primereact=this.primereact||{},this.primereact.panel=function(e,t,n,l,r,a,o,i,c,s){"use strict";function p(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var l=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,l.get?l:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var u=p(t);function g(){return g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},g.apply(null,arguments)}function m(e){if(Array.isArray(e))return e}function d(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var l,r,a,o,i=[],c=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(l=a.call(n)).done)&&(i.push(l.value),i.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(s)throw r}}return i}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,l=Array(t);n<t;n++)l[n]=e[n];return l}function b(e,t){if(e){if("string"==typeof e)return f(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(e,t){return m(e)||d(e,t)||b(e,t)||h()}var v=l.ComponentBase.extend({defaultProps:{__TYPE:"Panel",id:null,header:null,headerTemplate:null,footer:null,footerTemplate:null,toggleable:null,style:null,className:null,collapsed:null,expandIcon:null,collapseIcon:null,icons:null,transitionOptions:null,onExpand:null,onCollapse:null,onToggle:null,children:void 0},css:{classes:{root:function(e){return s.classNames("p-panel p-component",{"p-panel-toggleable":e.props.toggleable})},header:"p-panel-header",title:"p-panel-title",icons:"p-panel-icons",toggler:"p-panel-header-icon p-panel-toggler p-link",togglerIcon:"p-panel-header-icon p-panel-toggler p-link",toggleableContent:"p-toggleable-content",content:"p-panel-content",footer:"p-panel-footer",transition:"p-toggleable-content"},styles:"\n        @layer primereact {\n            .p-panel-header {\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\n            }\n            \n            .p-panel-title {\n              line-height: 1;\n            }\n            \n            .p-panel-header-icon {\n              display: inline-flex;\n              justify-content: center;\n              align-items: center;\n              cursor: pointer;\n              text-decoration: none;\n              overflow: hidden;\n              position: relative;\n            }\n        }\n        "}}),E=u.forwardRef((function(e,t){var p=a.useMergeProps(),m=u.useContext(n.PrimeReactContext),d=v.getProps(e,m),f=y(u.useState(d.id),2),b=f[0],h=f[1],E=y(u.useState(d.collapsed),2),O=E[0],j=E[1],N=u.useRef(null),x=u.useRef(null),C=!!d.toggleable&&(d.onToggle?d.collapsed:O),S=b+"_header",T=b+"_content",I=v.setMetaData({props:d,state:{id:b,collapsed:C}}),P=I.ptm,_=I.cx;l.useHandleStyle(v.css.styles,I.isUnstyled,{name:"panel"});var U=function(e){d.toggleable&&(C?w(e):k(e),e&&(d.onToggle&&d.onToggle({originalEvent:e,value:!C}),e.preventDefault()))},w=function(e){d.onToggle||j(!1),d.onExpand&&e&&d.onExpand(e)},k=function(e){d.onToggle||j(!0),d.onCollapse&&e&&d.onCollapse(e)};u.useImperativeHandle(t,(function(){return{props:d,toggle:U,expand:w,collapse:k,getElement:function(){return N.current},getContent:function(){return x.current}}})),a.useMountEffect((function(){b||h(s.UniqueComponentId())}));var M,R,A,J=function(){if(d.toggleable){var e=b+"_label",t=p({className:_("toggler"),onClick:U,id:e,"aria-controls":T,"aria-expanded":!C,type:"button",role:"button","aria-label":d.header},P("toggler")),n=p(P("togglericon")),l=s.IconUtils.getJSXIcon(C?d.expandIcon||u.createElement(i.PlusIcon,n):d.collapseIcon||u.createElement(o.MinusIcon,n),n,{props:d,collapsed:C});return u.createElement("button",t,l,u.createElement(c.Ripple,null))}return null},X=p({id:b,ref:N,style:d.style,className:s.classNames(d.className,_("root"))},v.getOtherProps(d),P("root")),D=function(){var e=s.ObjectUtils.getJSXElement(d.header,d),t=s.ObjectUtils.getJSXElement(d.icons,d),n=J(),l=p({id:S,className:_("title")},P("title")),r=u.createElement("span",l,e),a=p({className:_("icons")},P("icons")),o=u.createElement("div",a,t,n),i=p({className:_("header")},P("header")),c=u.createElement("div",i,r,o);return d.headerTemplate?s.ObjectUtils.getJSXElement(d.headerTemplate,{className:"p-panel-header",titleClassName:"p-panel-title",iconsClassName:"p-panel-icons",togglerClassName:"p-panel-header-icon p-panel-toggler p-link",onTogglerClick:U,titleElement:r,iconsElement:o,togglerElement:n,element:c,id:b+"_header",props:d,collapsed:C}):d.header||d.toggleable?c:null}(),H=(M=p({ref:x,className:_("toggleableContent"),"aria-hidden":C,role:"region",id:T,"aria-labelledby":S},P("toggleablecontent")),R=p({className:_("content")},P("content")),A=p({classNames:_("transition"),timeout:{enter:1e3,exit:450},in:!C,unmountOnExit:!0,options:d.transitionOptions},P("transition")),u.createElement(r.CSSTransition,g({nodeRef:x},A),u.createElement("div",M,u.createElement("div",R,d.children)))),q=function(){var e=s.ObjectUtils.getJSXElement(d.footer,d),t=p({className:_("footer")},P("footer")),n=u.createElement("div",t,e);if(d.footerTemplate){var l={className:_("footer"),element:n,props:d};return s.ObjectUtils.getJSXElement(d.footerTemplate,l)}return d.footer?n:null}();return u.createElement("div",X,D,H,q)}));return E.displayName="Panel",e.Panel=E,Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,primereact.api,primereact.componentbase,primereact.csstransition,primereact.hooks,primereact.icons.minus,primereact.icons.plus,primereact.ripple,primereact.utils);
