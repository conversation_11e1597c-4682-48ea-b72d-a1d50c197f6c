"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),n=require("primereact/api"),t=require("primereact/componentbase"),r=require("primereact/csstransition"),a=require("primereact/hooks"),i=require("primereact/icons/chevrondown"),o=require("primereact/icons/chevronright"),l=require("primereact/utils"),c=require("primereact/ripple");function u(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}})),n.default=e,Object.freeze(n)}var s=u(e);function m(){return m=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},m.apply(null,arguments)}function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function p(e,n){if("object"!=d(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=d(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}function f(e){var n=p(e,"string");return"symbol"==d(n)?n:n+""}function b(e,n,t){return(n=f(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function v(e){if(Array.isArray(e))return e}function y(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,a,i,o,l=[],c=!0,u=!1;try{if(i=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=i.call(t)).done)&&(l.push(r.value),l.length!==n);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=t.return&&(o=t.return(),Object(o)!==o))return}finally{if(u)throw a}}return l}}function g(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function h(e,n){if(e){if("string"==typeof e)return g(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?g(e,n):void 0}}function O(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function E(e,n){return v(e)||y(e,n)||h(e,n)||O()}var I=t.ComponentBase.extend({defaultProps:{__TYPE:"PanelMenu",id:null,model:null,style:null,expandedKeys:null,className:null,onExpandedKeysChange:null,onOpen:null,onClose:null,multiple:!1,transitionOptions:null,expandIcon:null,collapseIcon:null,children:void 0},css:{classes:{headerIcon:function(e){return l.classNames("p-menuitem-icon",e.item.icon)},headerSubmenuIcon:"p-submenu-icon",headerLabel:"p-menuitem-text",headerAction:"p-panelmenu-header-link",panel:function(e){return l.classNames("p-panelmenu-panel",e.item.className)},header:function(e){var n=e.item;return l.classNames("p-component p-panelmenu-header",{"p-highlight":e.active&&!!n.items,"p-disabled":n.disabled})},headerContent:"p-panelmenu-header-content",menuContent:"p-panelmenu-content",root:"p-panelmenu p-component",separator:"p-menuitem-separator",toggleableContent:function(e){return l.classNames("p-toggleable-content",{"p-toggleable-content-collapsed":!e.active})},icon:function(e){return l.classNames("p-menuitem-icon",e.item.icon)},label:"p-menuitem-text",submenuicon:"p-submenu-icon",content:"p-menuitem-content",action:function(e){return l.classNames("p-menuitem-link",{"p-disabled":e.item.disabled})},menuitem:function(e){return l.classNames("p-menuitem",e.item.className,{"p-focus":e.focused,"p-disabled":e.disabled})},menu:"p-panelmenu-root-list",submenu:"p-submenu-list",transition:"p-toggleable-content"},styles:"\n@layer primereact {\n    .p-panelmenu .p-panelmenu-header-link {\n        display: flex;\n        align-items: center;\n        user-select: none;\n        cursor: pointer;\n        position: relative;\n        text-decoration: none;\n    }\n\n    .p-panelmenu .p-panelmenu-header-link:focus {\n        z-index: 1;\n    }\n\n    .p-panelmenu .p-submenu-list {\n        margin: 0;\n        padding: 0;\n        list-style: none;\n    }\n\n    .p-panelmenu .p-menuitem-link {\n        display: flex;\n        align-items: center;\n        user-select: none;\n        cursor: pointer;\n        text-decoration: none;\n        text-decoration: none;\n        position: relative;\n        overflow: hidden;\n    }\n\n    .p-panelmenu .p-menuitem-text {\n        line-height: 1;\n    }\n}\n"}});function x(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function j(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?x(Object(t),!0).forEach((function(n){b(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):x(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var N=s.memo(s.forwardRef((function(e,n){var t=a.useMergeProps(),u=e.ptm,d=e.cx,p=s.useRef(null),f=function(n,t){return u(n,j({hostName:e.hostName},t))},b=function(e,n,t){return f(n,{context:{item:e,index:t,active:g(e),focused:E(e),disabled:O(e)}})},v=function(n){return"".concat(e.panelId,"_").concat(n.key)},y=function(e,n,t){return e&&e.item?l.ObjectUtils.getItemValue(e.item[n],t):void 0},g=function(n){var t;return e.activeItemPath&&e.activeItemPath.some((function(e){return e.key===n.key}))||!(null===(t=n.item)||void 0===t||!t.expanded)},h=function(e){return!1!==y(e,"visible")},O=function(e){return y(e,"disabled")},E=function(n){return e.focusedItemId===v(n)},I=function(e){return l.ObjectUtils.isNotEmpty(e.items)},x=function(e,n){y(n,"url")||e.preventDefault(),y(n,"command",{originalEvent:e,item:n.item}),k({processedItem:n,expanded:!g(n)})},k=function(n){e.onItemToggle(n)},S=function(n){return n-e.model.slice(0,n).filter((function(e){return h(e)&&y(e,"separator")})).length+1};s.useImperativeHandle(n,(function(){return{getElement:function(){return p.current}}}));var P=function(n){var r=e.id+"_sep_"+n,a=t({id:r,className:d("separator"),role:"separator"},f("separator"));return s.createElement("li",m({},a,{key:r}))},D=function(n,a){var i=s.createRef(),o=t({className:d("toggleableContent",{active:a})},f("toggleableContent"));if(h(n)&&I(n)){var l=t({classNames:d("transition"),timeout:{enter:1e3,exit:450},in:a,unmountOnExit:!0},f("transition"));return s.createElement(r.CSSTransition,m({nodeRef:i},l),s.createElement("div",m({ref:i},o),s.createElement(N,{id:v(n)+"_list",role:"group",panelId:e.panelId,level:e.level+1,focusedItemId:e.focusedItemId,activeItemPath:e.activeItemPath,onItemToggle:k,menuProps:e.menuProps,model:n.items,expandIcon:e.expandIcon,collapseIcon:e.collapseIcon,ptm:u,cx:d})))}return null},w=function(n,r){var a=n.item;if(!1===h(n))return null;var u=v(n),p=g(n),f=E(n),N=O(a),k=l.classNames("p-menuitem-link",{"p-disabled":a.disabled}),P=l.classNames("p-menuitem-icon",a.icon),w=t({className:d("icon",{item:a})},b(n,"icon",r)),C=l.IconUtils.getJSXIcon(a.icon,j({},w),{props:e.menuProps}),U=t({className:d("label")},b(n,"label",r)),H=a.label&&s.createElement("span",U,a.label),K=t({className:d("submenuicon")},b(n,"submenuicon",r)),A=a.items&&l.IconUtils.getJSXIcon(p?e.collapseIcon||s.createElement(i.ChevronDownIcon,K):e.expandIcon||s.createElement(o.ChevronRightIcon,K)),T=D(n,p),R=t({href:a.url||"#",className:d("action",{item:a}),target:a.target,onFocus:function(e){return e.stopPropagation()},tabIndex:"-1"},b(n,"action",r)),_=s.createElement("a",R,A,C,H,s.createElement(c.Ripple,null));a.template&&(_=l.ObjectUtils.getJSXElement(a.template,a,{className:k,labelClassName:"p-menuitem-text",iconClassName:P,submenuIconClassName:"p-panelmenu-icon",element:_,props:e,leaf:!a.items,active:p}));var q=t({onClick:function(e){return x(e,n)},className:d("content")},b(n,"content",r)),M=t({id:u,className:d("menuitem",{item:a,focused:f,disabled:N}),style:a.style,role:"treeitem","aria-label":a.label,"aria-expanded":I(a)?p:void 0,"aria-level":e.level+1,"aria-setsize":e.model.filter((function(e){return h(e)&&!y(e,"separator")})).length,"aria-posinset":S(r),"data-p-focused":f,"data-p-disabled":N},b(n,"menuitem",r));return s.createElement("li",m({},M,{key:u}),s.createElement("div",q,_),T)},C=e.model?e.model.map((function(e,n){return!1===e.visible?null:y(e,"separator")?P(n):w(e,n)})):null,U=e.root?"menu":"submenu",H=t({id:e.id,ref:p,tabIndex:e.tabIndex,onFocus:e.onFocus,onBlur:e.onBlur,onKeyDown:e.onKeyDown,"aria-activedescendant":e.ariaActivedescendant,role:e.role,className:l.classNames(d(U),e.className)},u(U));return s.createElement("ul",H,C)})));function k(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function S(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?k(Object(t),!0).forEach((function(n){b(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):k(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}N.displayName="PanelMenuSub";var P=s.memo((function(e){var n,t,r,a=e.ptm,i=e.cx,o=E(s.useState(!1),2),c=o[0],u=o[1],m=E(s.useState(null),2),d=m[0],p=m[1],f=E(s.useState(null),2),b=f[0],v=f[1],y=E(s.useState([]),2),g=y[0],h=y[1],O=E(s.useState(null),2),I=O[0],x=O[1],j=E(s.useState([]),2),k=j[0],P=j[1],D=s.useRef(null),w=s.useRef(null),C=s.useRef(null),U=function(e,n){return e&&e.item?l.ObjectUtils.getItemValue(e.item[n]):void 0},H=function(e){return U(e,"label")},K=function(e){return!1!==U(e,"visible")},A=function(e){return U(e,"disabled")},T=function(e){return g&&g.some((function(n){return n.key===e.parentKey}))},R=function(){return C.current&&C.current.getElement()},_=function(e){var n=l.ObjectUtils.isNotEmpty(d)?G(d):Y();ee({originalEvent:e,processedItem:n,focusOnNext:!0}),e.preventDefault()},q=function(e){var n=l.ObjectUtils.isNotEmpty(d)?Q(d):$();ee({originalEvent:e,processedItem:n,selfCheck:!0}),e.preventDefault()},M=function(e){l.ObjectUtils.isNotEmpty(d)&&(g.some((function(e){return e.key===d.key}))?h(g.filter((function(e){return e.key!==d.key}))):p(l.ObjectUtils.isNotEmpty(d.parent)?d.parent:d),e.preventDefault())},L=function(e){if(l.ObjectUtils.isNotEmpty(d)){if(l.ObjectUtils.isNotEmpty(d.items))if(g.some((function(e){return e.key===d.key})))_(e);else{var n=g.filter((function(e){return e.parentKey!==d.parentKey}));n.push(d),h(n)}e.preventDefault()}},F=function(e){ee({originalEvent:e,processedItem:Y(),allowHeaderFocus:!1}),e.preventDefault()},J=function(e){ee({originalEvent:e,processedItem:$(),focusOnNext:!0,allowHeaderFocus:!1}),e.preventDefault()},X=function(e){if(l.ObjectUtils.isNotEmpty(d)){var n=l.DomHandler.findSingle(R(),'li[id="'.concat("".concat(b),'"]')),t=n&&(l.DomHandler.findSingle(n,'[data-pc-section="action"]')||l.DomHandler.findSingle(n,"a,button"));t?t.click():n&&n.click()}e.preventDefault()},B=function(e){X(e)},V=function(e){return W(e)&&H(e).toLocaleLowerCase().startsWith(D.current.toLocaleLowerCase())},z=function(e){return!!e&&(0===e.level||T(e))&&K(e)},W=function(e){return!!e&&!A(e)&&!U(e,"separator")},Y=function(){return k.find((function(e){return W(e)}))},$=function(){return l.ObjectUtils.findLast(k,(function(e){return W(e)}))},G=function(e){var n=k.findIndex((function(n){return n.key===e.key}));return(n<k.length-1?k.slice(n+1).find((function(e){return W(e)})):void 0)||e},Q=function(e){var n=k.findIndex((function(n){return n.key===e.key}));return(n>0?l.ObjectUtils.findLast(k.slice(0,n),(function(e){return W(e)})):void 0)||e},Z=function(e,n){D.current=(D.current||"")+n;var t=null,r=!1;if(l.ObjectUtils.isNotEmpty(d)){var a=k.findIndex((function(e){return e.key===d.key}));t=k.slice(a).find((function(e){return V(e)})),t=l.ObjectUtils.isEmpty(t)?k.slice(0,a).find((function(e){return V(e)})):t}else t=k.find((function(e){return V(e)}));return l.ObjectUtils.isNotEmpty(t)&&(r=!0),l.ObjectUtils.isEmpty(t)&&l.ObjectUtils.isEmpty(d)&&(t=Y()),l.ObjectUtils.isNotEmpty(t)&&ee({originalEvent:e,processedItem:t,allowHeaderFocus:!1}),w&&clearTimeout(w.current),w.current=setTimeout((function(){D.current="",w.currentt=null}),500),r},ee=function(n){var t=n.originalEvent,r=n.processedItem,a=n.focusOnNext,i=n.selfCheck,o=n.allowHeaderFocus,c=void 0===o||o;l.ObjectUtils.isNotEmpty(d)&&d.key!==r.key?(p(r),ne()):c&&e.onHeaderFocus&&e.onHeaderFocus({originalEvent:t,focusOnNext:a,selfCheck:i})},ne=function(){var e=l.DomHandler.findSingle(R(),'li[id="'.concat("".concat(b),'"]'));e&&e.scrollIntoView&&e.scrollIntoView({block:"nearest",inline:"start"})},te=function(n,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=t||0===r&&e.model;if(!a)return null;for(var i=0;i<a.length;i++){var o=a[i];if((U(o,"key")||o.key)===n)return o;var l=te(n,o.items,r+1);if(l)return l}},re=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",a=[];return e&&e.forEach((function(e,i){var o=e.key?e.key:(""!==r?r+"_":"")+i,l={item:e,index:i,level:n,key:o,parent:t,parentKey:r};l.items=re(e.items,n+1,l,o),a.push(l)})),a},ae=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e&&e.forEach((function(e){z(e)&&(n.push(e),ae(e.items,n))})),n};return s.useEffect((function(){var n=re(e.model);x(n)}),[e.model]),s.useEffect((function(){var e=ae(I);P(e)}),[I,g]),s.useEffect((function(){var n;n=Object.entries(e.expandedKeys||{}).reduce((function(e,n){var t=E(n,2);if(t[1]){var r=te(t[0]);r&&e.push(r)}return e}),[]),h(n)}),[e.expandedKeys]),n=function(){var n=l.ObjectUtils.isNotEmpty(d)?"".concat(e.panelId,"_").concat(d.key):null;v(n)},t=[e.panelId,d],r=s.useRef(!1),s.useEffect((function(){if(r.current)return n&&n();r.current=!0}),t),s.createElement(N,{hostName:"PanelMenu",id:e.panelId+"_list",ref:C,role:"tree",tabIndex:-1,ariaActivedescendant:c?b:void 0,panelId:e.panelId,focusedItemId:c?b:void 0,model:I,activeItemPath:g,menuProps:e.menuProps,onFocus:function(e){u(!0)},onBlur:function(){u(!1),p(null),D.current=""},onKeyDown:function(e){var n=e.metaKey||e.ctrlKey;switch(e.code){case"ArrowDown":_(e);break;case"ArrowUp":q(e);break;case"ArrowLeft":M(e);break;case"ArrowRight":L(e);break;case"Home":F(e);break;case"End":J(e);break;case"Space":B(e);break;case"Enter":case"NumpadEnter":X(e);break;case"Escape":case"Tab":case"PageDown":case"PageUp":case"Backspace":case"ShiftLeft":case"ShiftRight":break;default:!n&&l.ObjectUtils.isPrintableCharacter(e.key)&&Z(e,e.key)}},onItemToggle:function(n){var t=n.processedItem,r=n.expanded;if(e.expandedKeys)e.onToggle&&e.onToggle({item:t.item,expanded:r});else{var a=g.filter((function(e){return e.parentKey!==t.parentKey}));r&&a.push(t),h(a)}t.item&&(t.item=S(S({},t.item),{},{expanded:r})),l.DomHandler.focus(R()),p(t)},level:0,className:i("submenu"),expandIcon:e.expandIcon,collapseIcon:e.collapseIcon,root:!0,ptm:a,cx:i})}));function D(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function w(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?D(Object(t),!0).forEach((function(n){b(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):D(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}P.displayName="PanelMenuList";var C=s.memo(s.forwardRef((function(e,c){var u=a.useMergeProps(),d=s.useContext(n.PrimeReactContext),p=I.getProps(e,d),f=E(s.useState(p.id),2),b=f[0],v=f[1],y=E(s.useState(null),2),g=y[0],h=y[1],O=E(s.useState([]),2),x=O[0],j=O[1],N=E(s.useState(!1),2),k=N[1],S=s.useRef(null),D=I.setMetaData({props:p,state:{id:b,activeItem:g}}),C=D.ptm,U=D.cx;t.useHandleStyle(I.css.styles,D.isUnstyled,{name:"panelmenu"});var H=function(e,n){n.disabled?e.preventDefault():(n.command&&n.command({originalEvent:e,item:n}),n.items&&Z(e,n),n.url||(e.preventDefault(),e.stopPropagation()))},K=function(e,n){return e?l.ObjectUtils.getItemValue(e[n]):void 0},A=function(e){return p.expandedKeys?p.expandedKeys[K(e,"key")]:p.multiple?x.some((function(n){return l.ObjectUtils.equals(e,n)})):l.ObjectUtils.equals(e,g)},T=function(e){return!1!==K(e,"visible")},R=function(e){return K(e,"disabled")},_=function(e){return l.ObjectUtils.equals(e,g)},q=function(e){return"".concat(b,"_").concat(e)},M=function(e,n){return"".concat(e||q(n),"_header")},L=function(e,n){return"".concat(e||q(n),"_content")},F=function(e,n){switch(e.code){case"ArrowDown":J(e);break;case"ArrowUp":X(e);break;case"Home":B(e);break;case"End":V(e);break;case"Enter":case"NumpadEnter":case"Space":z(e,n)}},J=function(e){var n=!0===l.DomHandler.getAttribute(e.currentTarget,"data-p-highlight")?l.DomHandler.findSingle(e.currentTarget.nextElementSibling,'[data-pc-section="menu"]'):null;n?l.DomHandler.focus(n):Q({originalEvent:e,focusOnNext:!0}),e.preventDefault()},X=function(e){var n=Y(e.currentTarget.parentElement)||G(),t=!0===l.DomHandler.getAttribute(n,"data-p-highlight")?l.DomHandler.findSingle(n.nextElementSibling,'[data-pc-section="menu"]'):null;t?l.DomHandler.focus(t):Q({originalEvent:e,focusOnNext:!1}),e.preventDefault()},B=function(e){ne(e,$()),e.preventDefault()},V=function(e){ne(e,G()),e.preventDefault()},z=function(e,n){var t=l.DomHandler.findSingle(e.currentTarget,'[data-pc-section="headeraction"]');t?t.click():H(e,n),e.preventDefault()},W=function(e){var n=l.DomHandler.findSingle(arguments.length>1&&void 0!==arguments[1]&&arguments[1]?e:e.nextElementSibling,'[data-pc-section="header"]');return n?l.DomHandler.getAttribute(n,"data-p-disabled")?W(n.parentElement):n:null},Y=function(e){var n=l.DomHandler.findSingle(arguments.length>1&&void 0!==arguments[1]&&arguments[1]?e:e.previousElementSibling,'[data-pc-section="header"]');return n?l.DomHandler.getAttribute(n,"data-p-disabled")?Y(n.parentElement):n:null},$=function(){return W(S.current.firstElementChild,!0)},G=function(){return Y(S.current.lastElementChild,!0)},Q=function(e){var n=e.originalEvent,t=e.focusOnNext,r=e.selfCheck,a=n.currentTarget.closest('[data-pc-section="panel"]'),i=r?l.DomHandler.findSingle(a,'[data-pc-section="header"]'):t?W(a):Y(a);i?ne(n,i):t?B(n):V(n)},Z=function(e,n){if(!R(n)){var t=!A(n),r=g&&l.ObjectUtils.equals(n,g)?null:n;if(h(r),p.multiple){var a=x;x.some((function(e){return l.ObjectUtils.equals(n,e)}))?a=x.filter((function(e){return!l.ObjectUtils.equals(n,e)})):a.push(n),j(a)}ee({item:n,expanded:t}),t&&e?p.onOpen&&p.onOpen({originalEvent:e,item:n}):p.onClose&&p.onClose({originalEvent:e,item:n})}},ee=function(e){var n=e.item,t=e.expanded,r=void 0!==t&&t;if(p.expandedKeys){var a=w({},p.expandedKeys);r?a[n.key]=!0:delete a[n.key],p.onExpandedKeysChange&&p.onExpandedKeysChange(a)}},ne=function(e,n){n&&l.DomHandler.focus(n)},te=function(e,n,t){return C(n,{context:{active:A(e),focused:_(e),disabled:R(e),index:t}})};s.useImperativeHandle(c,(function(){return{props:p,getElement:function(){return S.current}}})),a.useMountEffect((function(){!b&&v(l.UniqueComponentId())})),s.useEffect((function(){k(!0),p.model&&p.model.forEach((function(e){e.expanded&&Z(null,e)}))}),[p.model]);var re=function(){k(!1)},ae=p.model?p.model.map((function(e,n){if(!T(e))return null;var t=e.id||b+"_"+n,a=A(e),c=l.classNames("p-menuitem-icon",e.icon),d=u({className:U("headerIcon",{item:e})},te(e,"headerIcon",n)),f=l.IconUtils.getJSXIcon(e.icon,w({},d),{props:p}),v=u({className:U("headerSubmenuIcon")},te(e,"headerSubmenuIcon",n)),y=e.items&&l.IconUtils.getJSXIcon(a?p.collapseIcon||s.createElement(i.ChevronDownIcon,v):p.expandIcon||s.createElement(o.ChevronRightIcon,v)),g=u({className:U("headerLabel")},te(e,"headerLabel",n)),h=e.label&&s.createElement("span",g,e.label),O=s.createRef(),E=u({href:e.url||"#",tabIndex:"-1",className:U("headerAction")},te(e,"headerAction",n)),I=s.createElement("a",E,y,f,h);e.template&&(I=l.ObjectUtils.getJSXElement(e.template,e,{onClick:function(n){return H(n,e)},className:"p-panelmenu-header-link",labelClassName:"p-menuitem-text",submenuIconClassName:"p-panelmenu-icon",iconClassName:c,element:I,props:p,leaf:!e.items,active:a}));var x=u({id:(null==e?void 0:e.id)||q(n),className:U("panel",{item:e}),style:e.style},te(e,"panel",n)),j=u({id:M(null==e?void 0:e.id,n),className:U("header",{active:a,item:e}),"aria-label":e.label,"aria-expanded":a,"aria-disabled":e.disabled,"aria-controls":L(null==e?void 0:e.id,n),tabIndex:e.disabled?null:"0",onClick:function(n){return H(n,e)},onKeyDown:function(n){return F(n,e)},"data-p-disabled":e.disabled,"data-p-highlight":a,role:"button",style:e.style},te(e,"header",n)),N=u({className:U("headerContent")},te(e,"headerContent",n)),k=u({className:U("menuContent")},te(e,"menuContent",n)),S=u({className:U("toggleableContent",{active:a}),role:"region","aria-labelledby":M(null==e?void 0:e.id,n)},te(e,"toggleableContent",n)),D=u({classNames:U("transition"),timeout:{enter:1e3,exit:450},onEnter:re,in:a,unmountOnExit:!0,options:p.transitionOptions},te(e,"transition",n));return s.createElement("div",m({},x,{key:t}),s.createElement("div",j,s.createElement("div",N,I)),s.createElement(r.CSSTransition,m({nodeRef:O},D),s.createElement("div",m({id:L(null==e?void 0:e.id,n),ref:O},S),s.createElement("div",k,s.createElement(P,{panelId:(null==e?void 0:e.id)||q(n),menuProps:p,onToggle:ee,onHeaderFocus:Q,level:0,model:e.items,expandedKeys:p.expandedKeys,className:"p-panelmenu-root-submenu",submenuIcon:p.submenuIcon,ptm:C,cx:U})))))})):null,ie=u({ref:S,className:l.classNames(p.className,U("root")),id:p.id,style:p.style},I.getOtherProps(p),C("root"));return s.createElement("div",ie,ae)})));C.displayName="PanelMenu",exports.PanelMenu=C;
