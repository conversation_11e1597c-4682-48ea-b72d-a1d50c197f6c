import*as e from"react";import{PrimeReactContext as n}from"primereact/api";import{ComponentBase as t,useHandleStyle as r}from"primereact/componentbase";import{CSSTransition as a}from"primereact/csstransition";import{useMergeProps as o,useMountEffect as i}from"primereact/hooks";import{ChevronDownIcon as l}from"primereact/icons/chevrondown";import{ChevronRightIcon as c}from"primereact/icons/chevronright";import{classNames as u,ObjectUtils as s,IconUtils as m,<PERSON><PERSON><PERSON><PERSON> as p,UniqueComponentId as d}from"primereact/utils";import{Ripple as f}from"primereact/ripple";function v(){return v=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},v.apply(null,arguments)}function b(e){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b(e)}function y(e,n){if("object"!=b(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=b(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}function g(e){var n=y(e,"string");return"symbol"==b(n)?n:n+""}function h(e,n,t){return(n=g(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function E(e){if(Array.isArray(e))return e}function I(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,a,o,i,l=[],c=!0,u=!1;try{if(o=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=o.call(t)).done)&&(l.push(r.value),l.length!==n);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=t.return&&(i=t.return(),Object(i)!==i))return}finally{if(u)throw a}}return l}}function x(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function O(e,n){if(e){if("string"==typeof e)return x(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?x(e,n):void 0}}function k(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function N(e,n){return E(e)||I(e,n)||O(e,n)||k()}var S=t.extend({defaultProps:{__TYPE:"PanelMenu",id:null,model:null,style:null,expandedKeys:null,className:null,onExpandedKeysChange:null,onOpen:null,onClose:null,multiple:!1,transitionOptions:null,expandIcon:null,collapseIcon:null,children:void 0},css:{classes:{headerIcon:function(e){return u("p-menuitem-icon",e.item.icon)},headerSubmenuIcon:"p-submenu-icon",headerLabel:"p-menuitem-text",headerAction:"p-panelmenu-header-link",panel:function(e){return u("p-panelmenu-panel",e.item.className)},header:function(e){var n=e.item;return u("p-component p-panelmenu-header",{"p-highlight":e.active&&!!n.items,"p-disabled":n.disabled})},headerContent:"p-panelmenu-header-content",menuContent:"p-panelmenu-content",root:"p-panelmenu p-component",separator:"p-menuitem-separator",toggleableContent:function(e){return u("p-toggleable-content",{"p-toggleable-content-collapsed":!e.active})},icon:function(e){return u("p-menuitem-icon",e.item.icon)},label:"p-menuitem-text",submenuicon:"p-submenu-icon",content:"p-menuitem-content",action:function(e){return u("p-menuitem-link",{"p-disabled":e.item.disabled})},menuitem:function(e){return u("p-menuitem",e.item.className,{"p-focus":e.focused,"p-disabled":e.disabled})},menu:"p-panelmenu-root-list",submenu:"p-submenu-list",transition:"p-toggleable-content"},styles:"\n@layer primereact {\n    .p-panelmenu .p-panelmenu-header-link {\n        display: flex;\n        align-items: center;\n        user-select: none;\n        cursor: pointer;\n        position: relative;\n        text-decoration: none;\n    }\n\n    .p-panelmenu .p-panelmenu-header-link:focus {\n        z-index: 1;\n    }\n\n    .p-panelmenu .p-submenu-list {\n        margin: 0;\n        padding: 0;\n        list-style: none;\n    }\n\n    .p-panelmenu .p-menuitem-link {\n        display: flex;\n        align-items: center;\n        user-select: none;\n        cursor: pointer;\n        text-decoration: none;\n        text-decoration: none;\n        position: relative;\n        overflow: hidden;\n    }\n\n    .p-panelmenu .p-menuitem-text {\n        line-height: 1;\n    }\n}\n"}});function P(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function w(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?P(Object(t),!0).forEach((function(n){h(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):P(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var j=e.memo(e.forwardRef((function(n,t){var r=o(),i=n.ptm,p=n.cx,d=e.useRef(null),b=function(e,t){return i(e,w({hostName:n.hostName},t))},y=function(e,n,t){return b(n,{context:{item:e,index:t,active:E(e),focused:O(e),disabled:x(e)}})},g=function(e){return"".concat(n.panelId,"_").concat(e.key)},h=function(e,n,t){return e&&e.item?s.getItemValue(e.item[n],t):void 0},E=function(e){var t;return n.activeItemPath&&n.activeItemPath.some((function(n){return n.key===e.key}))||!(null===(t=e.item)||void 0===t||!t.expanded)},I=function(e){return!1!==h(e,"visible")},x=function(e){return h(e,"disabled")},O=function(e){return n.focusedItemId===g(e)},k=function(e){return s.isNotEmpty(e.items)},N=function(e,n){h(n,"url")||e.preventDefault(),h(n,"command",{originalEvent:e,item:n.item}),S({processedItem:n,expanded:!E(n)})},S=function(e){n.onItemToggle(e)},P=function(e){return e-n.model.slice(0,e).filter((function(e){return I(e)&&h(e,"separator")})).length+1};e.useImperativeHandle(t,(function(){return{getElement:function(){return d.current}}}));var C=function(t){var a=n.id+"_sep_"+t,o=r({id:a,className:p("separator"),role:"separator"},b("separator"));return e.createElement("li",v({},o,{key:a}))},D=function(t,o){var l=e.createRef(),c=r({className:p("toggleableContent",{active:o})},b("toggleableContent"));if(I(t)&&k(t)){var u=r({classNames:p("transition"),timeout:{enter:1e3,exit:450},in:o,unmountOnExit:!0},b("transition"));return e.createElement(a,v({nodeRef:l},u),e.createElement("div",v({ref:l},c),e.createElement(j,{id:g(t)+"_list",role:"group",panelId:n.panelId,level:n.level+1,focusedItemId:n.focusedItemId,activeItemPath:n.activeItemPath,onItemToggle:S,menuProps:n.menuProps,model:t.items,expandIcon:n.expandIcon,collapseIcon:n.collapseIcon,ptm:i,cx:p})))}return null},K=function(t,a){var o=t.item;if(!1===I(t))return null;var i=g(t),d=E(t),b=O(t),S=x(o),j=u("p-menuitem-link",{"p-disabled":o.disabled}),C=u("p-menuitem-icon",o.icon),K=r({className:p("icon",{item:o})},y(t,"icon",a)),A=m.getJSXIcon(o.icon,w({},K),{props:n.menuProps}),T=r({className:p("label")},y(t,"label",a)),R=o.label&&e.createElement("span",T,o.label),_=r({className:p("submenuicon")},y(t,"submenuicon",a)),L=o.items&&m.getJSXIcon(d?n.collapseIcon||e.createElement(l,_):n.expandIcon||e.createElement(c,_)),F=D(t,d),H=r({href:o.url||"#",className:p("action",{item:o}),target:o.target,onFocus:function(e){return e.stopPropagation()},tabIndex:"-1"},y(t,"action",a)),M=e.createElement("a",H,L,A,R,e.createElement(f,null));o.template&&(M=s.getJSXElement(o.template,o,{className:j,labelClassName:"p-menuitem-text",iconClassName:C,submenuIconClassName:"p-panelmenu-icon",element:M,props:n,leaf:!o.items,active:d}));var q=r({onClick:function(e){return N(e,t)},className:p("content")},y(t,"content",a)),J=r({id:i,className:p("menuitem",{item:o,focused:b,disabled:S}),style:o.style,role:"treeitem","aria-label":o.label,"aria-expanded":k(o)?d:void 0,"aria-level":n.level+1,"aria-setsize":n.model.filter((function(e){return I(e)&&!h(e,"separator")})).length,"aria-posinset":P(a),"data-p-focused":b,"data-p-disabled":S},y(t,"menuitem",a));return e.createElement("li",v({},J,{key:i}),e.createElement("div",q,M),F)},A=n.model?n.model.map((function(e,n){return!1===e.visible?null:h(e,"separator")?C(n):K(e,n)})):null,T=n.root?"menu":"submenu",R=r({id:n.id,ref:d,tabIndex:n.tabIndex,onFocus:n.onFocus,onBlur:n.onBlur,onKeyDown:n.onKeyDown,"aria-activedescendant":n.ariaActivedescendant,role:n.role,className:u(p(T),n.className)},i(T));return e.createElement("ul",R,A)})));function C(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function D(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?C(Object(t),!0).forEach((function(n){h(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):C(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}j.displayName="PanelMenuSub";var K=e.memo((function(n){var t,r,a,o=n.ptm,i=n.cx,l=N(e.useState(!1),2),c=l[0],u=l[1],m=N(e.useState(null),2),d=m[0],f=m[1],v=N(e.useState(null),2),b=v[0],y=v[1],g=N(e.useState([]),2),h=g[0],E=g[1],I=N(e.useState(null),2),x=I[0],O=I[1],k=N(e.useState([]),2),S=k[0],P=k[1],w=e.useRef(null),C=e.useRef(null),K=e.useRef(null),A=function(e,n){return e&&e.item?s.getItemValue(e.item[n]):void 0},T=function(e){return A(e,"label")},R=function(e){return!1!==A(e,"visible")},_=function(e){return A(e,"disabled")},L=function(e){return h&&h.some((function(n){return n.key===e.parentKey}))},F=function(){return K.current&&K.current.getElement()},H=function(e){var n=s.isNotEmpty(d)?Q(d):$();ne({originalEvent:e,processedItem:n,focusOnNext:!0}),e.preventDefault()},M=function(e){var n=s.isNotEmpty(d)?Z(d):G();ne({originalEvent:e,processedItem:n,selfCheck:!0}),e.preventDefault()},q=function(e){s.isNotEmpty(d)&&(h.some((function(e){return e.key===d.key}))?E(h.filter((function(e){return e.key!==d.key}))):f(s.isNotEmpty(d.parent)?d.parent:d),e.preventDefault())},J=function(e){if(s.isNotEmpty(d)){if(s.isNotEmpty(d.items))if(h.some((function(e){return e.key===d.key})))H(e);else{var n=h.filter((function(e){return e.parentKey!==d.parentKey}));n.push(d),E(n)}e.preventDefault()}},X=function(e){ne({originalEvent:e,processedItem:$(),allowHeaderFocus:!1}),e.preventDefault()},U=function(e){ne({originalEvent:e,processedItem:G(),focusOnNext:!0,allowHeaderFocus:!1}),e.preventDefault()},V=function(e){if(s.isNotEmpty(d)){var n=p.findSingle(F(),'li[id="'.concat("".concat(b),'"]')),t=n&&(p.findSingle(n,'[data-pc-section="action"]')||p.findSingle(n,"a,button"));t?t.click():n&&n.click()}e.preventDefault()},B=function(e){V(e)},z=function(e){return Y(e)&&T(e).toLocaleLowerCase().startsWith(w.current.toLocaleLowerCase())},W=function(e){return!!e&&(0===e.level||L(e))&&R(e)},Y=function(e){return!!e&&!_(e)&&!A(e,"separator")},$=function(){return S.find((function(e){return Y(e)}))},G=function(){return s.findLast(S,(function(e){return Y(e)}))},Q=function(e){var n=S.findIndex((function(n){return n.key===e.key}));return(n<S.length-1?S.slice(n+1).find((function(e){return Y(e)})):void 0)||e},Z=function(e){var n=S.findIndex((function(n){return n.key===e.key}));return(n>0?s.findLast(S.slice(0,n),(function(e){return Y(e)})):void 0)||e},ee=function(e,n){w.current=(w.current||"")+n;var t=null,r=!1;if(s.isNotEmpty(d)){var a=S.findIndex((function(e){return e.key===d.key}));t=S.slice(a).find((function(e){return z(e)})),t=s.isEmpty(t)?S.slice(0,a).find((function(e){return z(e)})):t}else t=S.find((function(e){return z(e)}));return s.isNotEmpty(t)&&(r=!0),s.isEmpty(t)&&s.isEmpty(d)&&(t=$()),s.isNotEmpty(t)&&ne({originalEvent:e,processedItem:t,allowHeaderFocus:!1}),C&&clearTimeout(C.current),C.current=setTimeout((function(){w.current="",C.currentt=null}),500),r},ne=function(e){var t=e.originalEvent,r=e.processedItem,a=e.focusOnNext,o=e.selfCheck,i=e.allowHeaderFocus,l=void 0===i||i;s.isNotEmpty(d)&&d.key!==r.key?(f(r),te()):l&&n.onHeaderFocus&&n.onHeaderFocus({originalEvent:t,focusOnNext:a,selfCheck:o})},te=function(){var e=p.findSingle(F(),'li[id="'.concat("".concat(b),'"]'));e&&e.scrollIntoView&&e.scrollIntoView({block:"nearest",inline:"start"})},re=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=t||0===r&&n.model;if(!a)return null;for(var o=0;o<a.length;o++){var i=a[o];if((A(i,"key")||i.key)===e)return i;var l=re(e,i.items,r+1);if(l)return l}},ae=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",a=[];return e&&e.forEach((function(e,o){var i=e.key?e.key:(""!==r?r+"_":"")+o,l={item:e,index:o,level:n,key:i,parent:t,parentKey:r};l.items=ae(e.items,n+1,l,i),a.push(l)})),a},oe=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e&&e.forEach((function(e){W(e)&&(n.push(e),oe(e.items,n))})),n};return e.useEffect((function(){var e=ae(n.model);O(e)}),[n.model]),e.useEffect((function(){var e=oe(x);P(e)}),[x,h]),e.useEffect((function(){var e;e=Object.entries(n.expandedKeys||{}).reduce((function(e,n){var t=N(n,2);if(t[1]){var r=re(t[0]);r&&e.push(r)}return e}),[]),E(e)}),[n.expandedKeys]),t=function(){var e=s.isNotEmpty(d)?"".concat(n.panelId,"_").concat(d.key):null;y(e)},r=[n.panelId,d],a=e.useRef(!1),e.useEffect((function(){if(a.current)return t&&t();a.current=!0}),r),e.createElement(j,{hostName:"PanelMenu",id:n.panelId+"_list",ref:K,role:"tree",tabIndex:-1,ariaActivedescendant:c?b:void 0,panelId:n.panelId,focusedItemId:c?b:void 0,model:x,activeItemPath:h,menuProps:n.menuProps,onFocus:function(e){u(!0)},onBlur:function(){u(!1),f(null),w.current=""},onKeyDown:function(e){var n=e.metaKey||e.ctrlKey;switch(e.code){case"ArrowDown":H(e);break;case"ArrowUp":M(e);break;case"ArrowLeft":q(e);break;case"ArrowRight":J(e);break;case"Home":X(e);break;case"End":U(e);break;case"Space":B(e);break;case"Enter":case"NumpadEnter":V(e);break;case"Escape":case"Tab":case"PageDown":case"PageUp":case"Backspace":case"ShiftLeft":case"ShiftRight":break;default:!n&&s.isPrintableCharacter(e.key)&&ee(e,e.key)}},onItemToggle:function(e){var t=e.processedItem,r=e.expanded;if(n.expandedKeys)n.onToggle&&n.onToggle({item:t.item,expanded:r});else{var a=h.filter((function(e){return e.parentKey!==t.parentKey}));r&&a.push(t),E(a)}t.item&&(t.item=D(D({},t.item),{},{expanded:r})),p.focus(F()),f(t)},level:0,className:i("submenu"),expandIcon:n.expandIcon,collapseIcon:n.collapseIcon,root:!0,ptm:o,cx:i})}));function A(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function T(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?A(Object(t),!0).forEach((function(n){h(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):A(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}K.displayName="PanelMenuList";var R=e.memo(e.forwardRef((function(t,f){var b=o(),y=e.useContext(n),g=S.getProps(t,y),h=N(e.useState(g.id),2),E=h[0],I=h[1],x=N(e.useState(null),2),O=x[0],k=x[1],P=N(e.useState([]),2),w=P[0],j=P[1],C=N(e.useState(!1),2),D=C[1],A=e.useRef(null),R=S.setMetaData({props:g,state:{id:E,activeItem:O}}),_=R.ptm,L=R.cx;r(S.css.styles,R.isUnstyled,{name:"panelmenu"});var F=function(e,n){n.disabled?e.preventDefault():(n.command&&n.command({originalEvent:e,item:n}),n.items&&ae(e,n),n.url||(e.preventDefault(),e.stopPropagation()))},H=function(e,n){return e?s.getItemValue(e[n]):void 0},M=function(e){return g.expandedKeys?g.expandedKeys[H(e,"key")]:g.multiple?w.some((function(n){return s.equals(e,n)})):s.equals(e,O)},q=function(e){return!1!==H(e,"visible")},J=function(e){return H(e,"disabled")},X=function(e){return s.equals(e,O)},U=function(e){return"".concat(E,"_").concat(e)},V=function(e,n){return"".concat(e||U(n),"_header")},B=function(e,n){return"".concat(e||U(n),"_content")},z=function(e,n){switch(e.code){case"ArrowDown":W(e);break;case"ArrowUp":Y(e);break;case"Home":$(e);break;case"End":G(e);break;case"Enter":case"NumpadEnter":case"Space":Q(e,n)}},W=function(e){var n=!0===p.getAttribute(e.currentTarget,"data-p-highlight")?p.findSingle(e.currentTarget.nextElementSibling,'[data-pc-section="menu"]'):null;n?p.focus(n):re({originalEvent:e,focusOnNext:!0}),e.preventDefault()},Y=function(e){var n=ee(e.currentTarget.parentElement)||te(),t=!0===p.getAttribute(n,"data-p-highlight")?p.findSingle(n.nextElementSibling,'[data-pc-section="menu"]'):null;t?p.focus(t):re({originalEvent:e,focusOnNext:!1}),e.preventDefault()},$=function(e){ie(e,ne()),e.preventDefault()},G=function(e){ie(e,te()),e.preventDefault()},Q=function(e,n){var t=p.findSingle(e.currentTarget,'[data-pc-section="headeraction"]');t?t.click():F(e,n),e.preventDefault()},Z=function(e){var n=p.findSingle(arguments.length>1&&void 0!==arguments[1]&&arguments[1]?e:e.nextElementSibling,'[data-pc-section="header"]');return n?p.getAttribute(n,"data-p-disabled")?Z(n.parentElement):n:null},ee=function(e){var n=p.findSingle(arguments.length>1&&void 0!==arguments[1]&&arguments[1]?e:e.previousElementSibling,'[data-pc-section="header"]');return n?p.getAttribute(n,"data-p-disabled")?ee(n.parentElement):n:null},ne=function(){return Z(A.current.firstElementChild,!0)},te=function(){return ee(A.current.lastElementChild,!0)},re=function(e){var n=e.originalEvent,t=e.focusOnNext,r=e.selfCheck,a=n.currentTarget.closest('[data-pc-section="panel"]'),o=r?p.findSingle(a,'[data-pc-section="header"]'):t?Z(a):ee(a);o?ie(n,o):t?$(n):G(n)},ae=function(e,n){if(!J(n)){var t=!M(n),r=O&&s.equals(n,O)?null:n;if(k(r),g.multiple){var a=w;w.some((function(e){return s.equals(n,e)}))?a=w.filter((function(e){return!s.equals(n,e)})):a.push(n),j(a)}oe({item:n,expanded:t}),t&&e?g.onOpen&&g.onOpen({originalEvent:e,item:n}):g.onClose&&g.onClose({originalEvent:e,item:n})}},oe=function(e){var n=e.item,t=e.expanded,r=void 0!==t&&t;if(g.expandedKeys){var a=T({},g.expandedKeys);r?a[n.key]=!0:delete a[n.key],g.onExpandedKeysChange&&g.onExpandedKeysChange(a)}},ie=function(e,n){n&&p.focus(n)},le=function(e,n,t){return _(n,{context:{active:M(e),focused:X(e),disabled:J(e),index:t}})};e.useImperativeHandle(f,(function(){return{props:g,getElement:function(){return A.current}}})),i((function(){!E&&I(d())})),e.useEffect((function(){D(!0),g.model&&g.model.forEach((function(e){e.expanded&&ae(null,e)}))}),[g.model]);var ce=function(){D(!1)},ue=g.model?g.model.map((function(n,t){if(!q(n))return null;var r=n.id||E+"_"+t,o=M(n),i=u("p-menuitem-icon",n.icon),p=b({className:L("headerIcon",{item:n})},le(n,"headerIcon",t)),d=m.getJSXIcon(n.icon,T({},p),{props:g}),f=b({className:L("headerSubmenuIcon")},le(n,"headerSubmenuIcon",t)),y=n.items&&m.getJSXIcon(o?g.collapseIcon||e.createElement(l,f):g.expandIcon||e.createElement(c,f)),h=b({className:L("headerLabel")},le(n,"headerLabel",t)),I=n.label&&e.createElement("span",h,n.label),x=e.createRef(),O=b({href:n.url||"#",tabIndex:"-1",className:L("headerAction")},le(n,"headerAction",t)),k=e.createElement("a",O,y,d,I);n.template&&(k=s.getJSXElement(n.template,n,{onClick:function(e){return F(e,n)},className:"p-panelmenu-header-link",labelClassName:"p-menuitem-text",submenuIconClassName:"p-panelmenu-icon",iconClassName:i,element:k,props:g,leaf:!n.items,active:o}));var N=b({id:(null==n?void 0:n.id)||U(t),className:L("panel",{item:n}),style:n.style},le(n,"panel",t)),S=b({id:V(null==n?void 0:n.id,t),className:L("header",{active:o,item:n}),"aria-label":n.label,"aria-expanded":o,"aria-disabled":n.disabled,"aria-controls":B(null==n?void 0:n.id,t),tabIndex:n.disabled?null:"0",onClick:function(e){return F(e,n)},onKeyDown:function(e){return z(e,n)},"data-p-disabled":n.disabled,"data-p-highlight":o,role:"button",style:n.style},le(n,"header",t)),P=b({className:L("headerContent")},le(n,"headerContent",t)),w=b({className:L("menuContent")},le(n,"menuContent",t)),j=b({className:L("toggleableContent",{active:o}),role:"region","aria-labelledby":V(null==n?void 0:n.id,t)},le(n,"toggleableContent",t)),C=b({classNames:L("transition"),timeout:{enter:1e3,exit:450},onEnter:ce,in:o,unmountOnExit:!0,options:g.transitionOptions},le(n,"transition",t));return e.createElement("div",v({},N,{key:r}),e.createElement("div",S,e.createElement("div",P,k)),e.createElement(a,v({nodeRef:x},C),e.createElement("div",v({id:B(null==n?void 0:n.id,t),ref:x},j),e.createElement("div",w,e.createElement(K,{panelId:(null==n?void 0:n.id)||U(t),menuProps:g,onToggle:oe,onHeaderFocus:re,level:0,model:n.items,expandedKeys:g.expandedKeys,className:"p-panelmenu-root-submenu",submenuIcon:g.submenuIcon,ptm:_,cx:L})))))})):null,se=b({ref:A,className:u(g.className,L("root")),id:g.id,style:g.style},S.getOtherProps(g),_("root"));return e.createElement("div",se,ue)})));R.displayName="PanelMenu";export{R as PanelMenu};
