this.primereact=this.primereact||{},this.primereact.panelmenu=function(e,n,t,r,a,i,o,l,c,u){"use strict";function s(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}})),n.default=e,Object.freeze(n)}var m=s(n);function d(){return d=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},d.apply(null,arguments)}function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function f(e,n){if("object"!=p(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=p(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}function b(e){var n=f(e,"string");return"symbol"==p(n)?n:n+""}function v(e,n,t){return(n=b(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function y(e){if(Array.isArray(e))return e}function g(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,a,i,o,l=[],c=!0,u=!1;try{if(i=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=i.call(t)).done)&&(l.push(r.value),l.length!==n);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=t.return&&(o=t.return(),Object(o)!==o))return}finally{if(u)throw a}}return l}}function h(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function O(e,n){if(e){if("string"==typeof e)return h(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?h(e,n):void 0}}function E(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function I(e,n){return y(e)||g(e,n)||O(e,n)||E()}var j=r.ComponentBase.extend({defaultProps:{__TYPE:"PanelMenu",id:null,model:null,style:null,expandedKeys:null,className:null,onExpandedKeysChange:null,onOpen:null,onClose:null,multiple:!1,transitionOptions:null,expandIcon:null,collapseIcon:null,children:void 0},css:{classes:{headerIcon:function(e){return c.classNames("p-menuitem-icon",e.item.icon)},headerSubmenuIcon:"p-submenu-icon",headerLabel:"p-menuitem-text",headerAction:"p-panelmenu-header-link",panel:function(e){return c.classNames("p-panelmenu-panel",e.item.className)},header:function(e){var n=e.item;return c.classNames("p-component p-panelmenu-header",{"p-highlight":e.active&&!!n.items,"p-disabled":n.disabled})},headerContent:"p-panelmenu-header-content",menuContent:"p-panelmenu-content",root:"p-panelmenu p-component",separator:"p-menuitem-separator",toggleableContent:function(e){return c.classNames("p-toggleable-content",{"p-toggleable-content-collapsed":!e.active})},icon:function(e){return c.classNames("p-menuitem-icon",e.item.icon)},label:"p-menuitem-text",submenuicon:"p-submenu-icon",content:"p-menuitem-content",action:function(e){return c.classNames("p-menuitem-link",{"p-disabled":e.item.disabled})},menuitem:function(e){return c.classNames("p-menuitem",e.item.className,{"p-focus":e.focused,"p-disabled":e.disabled})},menu:"p-panelmenu-root-list",submenu:"p-submenu-list",transition:"p-toggleable-content"},styles:"\n@layer primereact {\n    .p-panelmenu .p-panelmenu-header-link {\n        display: flex;\n        align-items: center;\n        user-select: none;\n        cursor: pointer;\n        position: relative;\n        text-decoration: none;\n    }\n\n    .p-panelmenu .p-panelmenu-header-link:focus {\n        z-index: 1;\n    }\n\n    .p-panelmenu .p-submenu-list {\n        margin: 0;\n        padding: 0;\n        list-style: none;\n    }\n\n    .p-panelmenu .p-menuitem-link {\n        display: flex;\n        align-items: center;\n        user-select: none;\n        cursor: pointer;\n        text-decoration: none;\n        text-decoration: none;\n        position: relative;\n        overflow: hidden;\n    }\n\n    .p-panelmenu .p-menuitem-text {\n        line-height: 1;\n    }\n}\n"}});function x(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function N(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?x(Object(t),!0).forEach((function(n){v(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):x(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var k=m.memo(m.forwardRef((function(e,n){var t=i.useMergeProps(),r=e.ptm,s=e.cx,p=m.useRef(null),f=function(n,t){return r(n,N({hostName:e.hostName},t))},b=function(e,n,t){return f(n,{context:{item:e,index:t,active:g(e),focused:E(e),disabled:O(e)}})},v=function(n){return"".concat(e.panelId,"_").concat(n.key)},y=function(e,n,t){return e&&e.item?c.ObjectUtils.getItemValue(e.item[n],t):void 0},g=function(n){var t;return e.activeItemPath&&e.activeItemPath.some((function(e){return e.key===n.key}))||!(null===(t=n.item)||void 0===t||!t.expanded)},h=function(e){return!1!==y(e,"visible")},O=function(e){return y(e,"disabled")},E=function(n){return e.focusedItemId===v(n)},I=function(e){return c.ObjectUtils.isNotEmpty(e.items)},j=function(e,n){y(n,"url")||e.preventDefault(),y(n,"command",{originalEvent:e,item:n.item}),x({processedItem:n,expanded:!g(n)})},x=function(n){e.onItemToggle(n)},S=function(n){return n-e.model.slice(0,n).filter((function(e){return h(e)&&y(e,"separator")})).length+1};m.useImperativeHandle(n,(function(){return{getElement:function(){return p.current}}}));var P=function(n){var r=e.id+"_sep_"+n,a=t({id:r,className:s("separator"),role:"separator"},f("separator"));return m.createElement("li",d({},a,{key:r}))},D=function(n,i){var o=m.createRef(),l=t({className:s("toggleableContent",{active:i})},f("toggleableContent"));if(h(n)&&I(n)){var c=t({classNames:s("transition"),timeout:{enter:1e3,exit:450},in:i,unmountOnExit:!0},f("transition"));return m.createElement(a.CSSTransition,d({nodeRef:o},c),m.createElement("div",d({ref:o},l),m.createElement(k,{id:v(n)+"_list",role:"group",panelId:e.panelId,level:e.level+1,focusedItemId:e.focusedItemId,activeItemPath:e.activeItemPath,onItemToggle:x,menuProps:e.menuProps,model:n.items,expandIcon:e.expandIcon,collapseIcon:e.collapseIcon,ptm:r,cx:s})))}return null},w=function(n,r){var a=n.item;if(!1===h(n))return null;var i=v(n),p=g(n),f=E(n),x=O(a),k=c.classNames("p-menuitem-link",{"p-disabled":a.disabled}),P=c.classNames("p-menuitem-icon",a.icon),w=t({className:s("icon",{item:a})},b(n,"icon",r)),C=c.IconUtils.getJSXIcon(a.icon,N({},w),{props:e.menuProps}),U=t({className:s("label")},b(n,"label",r)),H=a.label&&m.createElement("span",U,a.label),K=t({className:s("submenuicon")},b(n,"submenuicon",r)),A=a.items&&c.IconUtils.getJSXIcon(p?e.collapseIcon||m.createElement(o.ChevronDownIcon,K):e.expandIcon||m.createElement(l.ChevronRightIcon,K)),R=D(n,p),T=t({href:a.url||"#",className:s("action",{item:a}),target:a.target,onFocus:function(e){return e.stopPropagation()},tabIndex:"-1"},b(n,"action",r)),_=m.createElement("a",T,A,C,H,m.createElement(u.Ripple,null));a.template&&(_=c.ObjectUtils.getJSXElement(a.template,a,{className:k,labelClassName:"p-menuitem-text",iconClassName:P,submenuIconClassName:"p-panelmenu-icon",element:_,props:e,leaf:!a.items,active:p}));var M=t({onClick:function(e){return j(e,n)},className:s("content")},b(n,"content",r)),L=t({id:i,className:s("menuitem",{item:a,focused:f,disabled:x}),style:a.style,role:"treeitem","aria-label":a.label,"aria-expanded":I(a)?p:void 0,"aria-level":e.level+1,"aria-setsize":e.model.filter((function(e){return h(e)&&!y(e,"separator")})).length,"aria-posinset":S(r),"data-p-focused":f,"data-p-disabled":x},b(n,"menuitem",r));return m.createElement("li",d({},L,{key:i}),m.createElement("div",M,_),R)},C=e.model?e.model.map((function(e,n){return!1===e.visible?null:y(e,"separator")?P(n):w(e,n)})):null,U=e.root?"menu":"submenu",H=t({id:e.id,ref:p,tabIndex:e.tabIndex,onFocus:e.onFocus,onBlur:e.onBlur,onKeyDown:e.onKeyDown,"aria-activedescendant":e.ariaActivedescendant,role:e.role,className:c.classNames(s(U),e.className)},r(U));return m.createElement("ul",H,C)})));function S(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function P(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?S(Object(t),!0).forEach((function(n){v(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):S(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}k.displayName="PanelMenuSub";var D=m.memo((function(e){var n,t,r,a=e.ptm,i=e.cx,o=I(m.useState(!1),2),l=o[0],u=o[1],s=I(m.useState(null),2),d=s[0],p=s[1],f=I(m.useState(null),2),b=f[0],v=f[1],y=I(m.useState([]),2),g=y[0],h=y[1],O=I(m.useState(null),2),E=O[0],j=O[1],x=I(m.useState([]),2),N=x[0],S=x[1],D=m.useRef(null),w=m.useRef(null),C=m.useRef(null),U=function(e,n){return e&&e.item?c.ObjectUtils.getItemValue(e.item[n]):void 0},H=function(e){return U(e,"label")},K=function(e){return!1!==U(e,"visible")},A=function(e){return U(e,"disabled")},R=function(e){return g&&g.some((function(n){return n.key===e.parentKey}))},T=function(){return C.current&&C.current.getElement()},_=function(e){var n=c.ObjectUtils.isNotEmpty(d)?G(d):Y();ee({originalEvent:e,processedItem:n,focusOnNext:!0}),e.preventDefault()},M=function(e){var n=c.ObjectUtils.isNotEmpty(d)?Q(d):$();ee({originalEvent:e,processedItem:n,selfCheck:!0}),e.preventDefault()},L=function(e){c.ObjectUtils.isNotEmpty(d)&&(g.some((function(e){return e.key===d.key}))?h(g.filter((function(e){return e.key!==d.key}))):p(c.ObjectUtils.isNotEmpty(d.parent)?d.parent:d),e.preventDefault())},F=function(e){if(c.ObjectUtils.isNotEmpty(d)){if(c.ObjectUtils.isNotEmpty(d.items))if(g.some((function(e){return e.key===d.key})))_(e);else{var n=g.filter((function(e){return e.parentKey!==d.parentKey}));n.push(d),h(n)}e.preventDefault()}},q=function(e){ee({originalEvent:e,processedItem:Y(),allowHeaderFocus:!1}),e.preventDefault()},J=function(e){ee({originalEvent:e,processedItem:$(),focusOnNext:!0,allowHeaderFocus:!1}),e.preventDefault()},X=function(e){if(c.ObjectUtils.isNotEmpty(d)){var n=c.DomHandler.findSingle(T(),'li[id="'.concat("".concat(b),'"]')),t=n&&(c.DomHandler.findSingle(n,'[data-pc-section="action"]')||c.DomHandler.findSingle(n,"a,button"));t?t.click():n&&n.click()}e.preventDefault()},B=function(e){X(e)},V=function(e){return W(e)&&H(e).toLocaleLowerCase().startsWith(D.current.toLocaleLowerCase())},z=function(e){return!!e&&(0===e.level||R(e))&&K(e)},W=function(e){return!!e&&!A(e)&&!U(e,"separator")},Y=function(){return N.find((function(e){return W(e)}))},$=function(){return c.ObjectUtils.findLast(N,(function(e){return W(e)}))},G=function(e){var n=N.findIndex((function(n){return n.key===e.key}));return(n<N.length-1?N.slice(n+1).find((function(e){return W(e)})):void 0)||e},Q=function(e){var n=N.findIndex((function(n){return n.key===e.key}));return(n>0?c.ObjectUtils.findLast(N.slice(0,n),(function(e){return W(e)})):void 0)||e},Z=function(e,n){D.current=(D.current||"")+n;var t=null,r=!1;if(c.ObjectUtils.isNotEmpty(d)){var a=N.findIndex((function(e){return e.key===d.key}));t=N.slice(a).find((function(e){return V(e)})),t=c.ObjectUtils.isEmpty(t)?N.slice(0,a).find((function(e){return V(e)})):t}else t=N.find((function(e){return V(e)}));return c.ObjectUtils.isNotEmpty(t)&&(r=!0),c.ObjectUtils.isEmpty(t)&&c.ObjectUtils.isEmpty(d)&&(t=Y()),c.ObjectUtils.isNotEmpty(t)&&ee({originalEvent:e,processedItem:t,allowHeaderFocus:!1}),w&&clearTimeout(w.current),w.current=setTimeout((function(){D.current="",w.currentt=null}),500),r},ee=function(n){var t=n.originalEvent,r=n.processedItem,a=n.focusOnNext,i=n.selfCheck,o=n.allowHeaderFocus,l=void 0===o||o;c.ObjectUtils.isNotEmpty(d)&&d.key!==r.key?(p(r),ne()):l&&e.onHeaderFocus&&e.onHeaderFocus({originalEvent:t,focusOnNext:a,selfCheck:i})},ne=function(){var e=c.DomHandler.findSingle(T(),'li[id="'.concat("".concat(b),'"]'));e&&e.scrollIntoView&&e.scrollIntoView({block:"nearest",inline:"start"})},te=function(n,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=t||0===r&&e.model;if(!a)return null;for(var i=0;i<a.length;i++){var o=a[i];if((U(o,"key")||o.key)===n)return o;var l=te(n,o.items,r+1);if(l)return l}},re=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",a=[];return e&&e.forEach((function(e,i){var o=e.key?e.key:(""!==r?r+"_":"")+i,l={item:e,index:i,level:n,key:o,parent:t,parentKey:r};l.items=re(e.items,n+1,l,o),a.push(l)})),a},ae=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e&&e.forEach((function(e){z(e)&&(n.push(e),ae(e.items,n))})),n};return m.useEffect((function(){var n=re(e.model);j(n)}),[e.model]),m.useEffect((function(){var e=ae(E);S(e)}),[E,g]),m.useEffect((function(){var n;n=Object.entries(e.expandedKeys||{}).reduce((function(e,n){var t=I(n,2);if(t[1]){var r=te(t[0]);r&&e.push(r)}return e}),[]),h(n)}),[e.expandedKeys]),n=function(){var n=c.ObjectUtils.isNotEmpty(d)?"".concat(e.panelId,"_").concat(d.key):null;v(n)},t=[e.panelId,d],r=m.useRef(!1),m.useEffect((function(){if(r.current)return n&&n();r.current=!0}),t),m.createElement(k,{hostName:"PanelMenu",id:e.panelId+"_list",ref:C,role:"tree",tabIndex:-1,ariaActivedescendant:l?b:void 0,panelId:e.panelId,focusedItemId:l?b:void 0,model:E,activeItemPath:g,menuProps:e.menuProps,onFocus:function(e){u(!0)},onBlur:function(){u(!1),p(null),D.current=""},onKeyDown:function(e){var n=e.metaKey||e.ctrlKey;switch(e.code){case"ArrowDown":_(e);break;case"ArrowUp":M(e);break;case"ArrowLeft":L(e);break;case"ArrowRight":F(e);break;case"Home":q(e);break;case"End":J(e);break;case"Space":B(e);break;case"Enter":case"NumpadEnter":X(e);break;case"Escape":case"Tab":case"PageDown":case"PageUp":case"Backspace":case"ShiftLeft":case"ShiftRight":break;default:!n&&c.ObjectUtils.isPrintableCharacter(e.key)&&Z(e,e.key)}},onItemToggle:function(n){var t=n.processedItem,r=n.expanded;if(e.expandedKeys)e.onToggle&&e.onToggle({item:t.item,expanded:r});else{var a=g.filter((function(e){return e.parentKey!==t.parentKey}));r&&a.push(t),h(a)}t.item&&(t.item=P(P({},t.item),{},{expanded:r})),c.DomHandler.focus(T()),p(t)},level:0,className:i("submenu"),expandIcon:e.expandIcon,collapseIcon:e.collapseIcon,root:!0,ptm:a,cx:i})}));function w(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function C(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?w(Object(t),!0).forEach((function(n){v(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):w(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}D.displayName="PanelMenuList";var U=m.memo(m.forwardRef((function(e,n){var u=i.useMergeProps(),s=m.useContext(t.PrimeReactContext),p=j.getProps(e,s),f=I(m.useState(p.id),2),b=f[0],v=f[1],y=I(m.useState(null),2),g=y[0],h=y[1],O=I(m.useState([]),2),E=O[0],x=O[1],N=I(m.useState(!1),2),k=N[1],S=m.useRef(null),P=j.setMetaData({props:p,state:{id:b,activeItem:g}}),w=P.ptm,U=P.cx;r.useHandleStyle(j.css.styles,P.isUnstyled,{name:"panelmenu"});var H=function(e,n){n.disabled?e.preventDefault():(n.command&&n.command({originalEvent:e,item:n}),n.items&&Z(e,n),n.url||(e.preventDefault(),e.stopPropagation()))},K=function(e,n){return e?c.ObjectUtils.getItemValue(e[n]):void 0},A=function(e){return p.expandedKeys?p.expandedKeys[K(e,"key")]:p.multiple?E.some((function(n){return c.ObjectUtils.equals(e,n)})):c.ObjectUtils.equals(e,g)},R=function(e){return!1!==K(e,"visible")},T=function(e){return K(e,"disabled")},_=function(e){return c.ObjectUtils.equals(e,g)},M=function(e){return"".concat(b,"_").concat(e)},L=function(e,n){return"".concat(e||M(n),"_header")},F=function(e,n){return"".concat(e||M(n),"_content")},q=function(e,n){switch(e.code){case"ArrowDown":J(e);break;case"ArrowUp":X(e);break;case"Home":B(e);break;case"End":V(e);break;case"Enter":case"NumpadEnter":case"Space":z(e,n)}},J=function(e){var n=!0===c.DomHandler.getAttribute(e.currentTarget,"data-p-highlight")?c.DomHandler.findSingle(e.currentTarget.nextElementSibling,'[data-pc-section="menu"]'):null;n?c.DomHandler.focus(n):Q({originalEvent:e,focusOnNext:!0}),e.preventDefault()},X=function(e){var n=Y(e.currentTarget.parentElement)||G(),t=!0===c.DomHandler.getAttribute(n,"data-p-highlight")?c.DomHandler.findSingle(n.nextElementSibling,'[data-pc-section="menu"]'):null;t?c.DomHandler.focus(t):Q({originalEvent:e,focusOnNext:!1}),e.preventDefault()},B=function(e){ne(e,$()),e.preventDefault()},V=function(e){ne(e,G()),e.preventDefault()},z=function(e,n){var t=c.DomHandler.findSingle(e.currentTarget,'[data-pc-section="headeraction"]');t?t.click():H(e,n),e.preventDefault()},W=function(e){var n=c.DomHandler.findSingle(arguments.length>1&&void 0!==arguments[1]&&arguments[1]?e:e.nextElementSibling,'[data-pc-section="header"]');return n?c.DomHandler.getAttribute(n,"data-p-disabled")?W(n.parentElement):n:null},Y=function(e){var n=c.DomHandler.findSingle(arguments.length>1&&void 0!==arguments[1]&&arguments[1]?e:e.previousElementSibling,'[data-pc-section="header"]');return n?c.DomHandler.getAttribute(n,"data-p-disabled")?Y(n.parentElement):n:null},$=function(){return W(S.current.firstElementChild,!0)},G=function(){return Y(S.current.lastElementChild,!0)},Q=function(e){var n=e.originalEvent,t=e.focusOnNext,r=e.selfCheck,a=n.currentTarget.closest('[data-pc-section="panel"]'),i=r?c.DomHandler.findSingle(a,'[data-pc-section="header"]'):t?W(a):Y(a);i?ne(n,i):t?B(n):V(n)},Z=function(e,n){if(!T(n)){var t=!A(n),r=g&&c.ObjectUtils.equals(n,g)?null:n;if(h(r),p.multiple){var a=E;E.some((function(e){return c.ObjectUtils.equals(n,e)}))?a=E.filter((function(e){return!c.ObjectUtils.equals(n,e)})):a.push(n),x(a)}ee({item:n,expanded:t}),t&&e?p.onOpen&&p.onOpen({originalEvent:e,item:n}):p.onClose&&p.onClose({originalEvent:e,item:n})}},ee=function(e){var n=e.item,t=e.expanded,r=void 0!==t&&t;if(p.expandedKeys){var a=C({},p.expandedKeys);r?a[n.key]=!0:delete a[n.key],p.onExpandedKeysChange&&p.onExpandedKeysChange(a)}},ne=function(e,n){n&&c.DomHandler.focus(n)},te=function(e,n,t){return w(n,{context:{active:A(e),focused:_(e),disabled:T(e),index:t}})};m.useImperativeHandle(n,(function(){return{props:p,getElement:function(){return S.current}}})),i.useMountEffect((function(){!b&&v(c.UniqueComponentId())})),m.useEffect((function(){k(!0),p.model&&p.model.forEach((function(e){e.expanded&&Z(null,e)}))}),[p.model]);var re=function(){k(!1)},ae=p.model?p.model.map((function(e,n){if(!R(e))return null;var t=e.id||b+"_"+n,r=A(e),i=c.classNames("p-menuitem-icon",e.icon),s=u({className:U("headerIcon",{item:e})},te(e,"headerIcon",n)),f=c.IconUtils.getJSXIcon(e.icon,C({},s),{props:p}),v=u({className:U("headerSubmenuIcon")},te(e,"headerSubmenuIcon",n)),y=e.items&&c.IconUtils.getJSXIcon(r?p.collapseIcon||m.createElement(o.ChevronDownIcon,v):p.expandIcon||m.createElement(l.ChevronRightIcon,v)),g=u({className:U("headerLabel")},te(e,"headerLabel",n)),h=e.label&&m.createElement("span",g,e.label),O=m.createRef(),E=u({href:e.url||"#",tabIndex:"-1",className:U("headerAction")},te(e,"headerAction",n)),I=m.createElement("a",E,y,f,h);e.template&&(I=c.ObjectUtils.getJSXElement(e.template,e,{onClick:function(n){return H(n,e)},className:"p-panelmenu-header-link",labelClassName:"p-menuitem-text",submenuIconClassName:"p-panelmenu-icon",iconClassName:i,element:I,props:p,leaf:!e.items,active:r}));var j=u({id:(null==e?void 0:e.id)||M(n),className:U("panel",{item:e}),style:e.style},te(e,"panel",n)),x=u({id:L(null==e?void 0:e.id,n),className:U("header",{active:r,item:e}),"aria-label":e.label,"aria-expanded":r,"aria-disabled":e.disabled,"aria-controls":F(null==e?void 0:e.id,n),tabIndex:e.disabled?null:"0",onClick:function(n){return H(n,e)},onKeyDown:function(n){return q(n,e)},"data-p-disabled":e.disabled,"data-p-highlight":r,role:"button",style:e.style},te(e,"header",n)),N=u({className:U("headerContent")},te(e,"headerContent",n)),k=u({className:U("menuContent")},te(e,"menuContent",n)),S=u({className:U("toggleableContent",{active:r}),role:"region","aria-labelledby":L(null==e?void 0:e.id,n)},te(e,"toggleableContent",n)),P=u({classNames:U("transition"),timeout:{enter:1e3,exit:450},onEnter:re,in:r,unmountOnExit:!0,options:p.transitionOptions},te(e,"transition",n));return m.createElement("div",d({},j,{key:t}),m.createElement("div",x,m.createElement("div",N,I)),m.createElement(a.CSSTransition,d({nodeRef:O},P),m.createElement("div",d({id:F(null==e?void 0:e.id,n),ref:O},S),m.createElement("div",k,m.createElement(D,{panelId:(null==e?void 0:e.id)||M(n),menuProps:p,onToggle:ee,onHeaderFocus:Q,level:0,model:e.items,expandedKeys:p.expandedKeys,className:"p-panelmenu-root-submenu",submenuIcon:p.submenuIcon,ptm:w,cx:U})))))})):null,ie=u({ref:S,className:c.classNames(p.className,U("root")),id:p.id,style:p.style},j.getOtherProps(p),w("root"));return m.createElement("div",ie,ae)})));return U.displayName="PanelMenu",e.PanelMenu=U,Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,primereact.api,primereact.componentbase,primereact.csstransition,primereact.hooks,primereact.icons.chevrondown,primereact.icons.chevronright,primereact.utils,primereact.ripple);
