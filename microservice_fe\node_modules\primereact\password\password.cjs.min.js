"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("primereact/api"),n=require("primereact/componentbase"),r=require("primereact/csstransition"),o=require("primereact/hooks"),a=require("primereact/utils"),i=require("primereact/icons/eye"),l=require("primereact/icons/eyeslash"),s=require("primereact/inputtext"),c=require("primereact/overlayservice"),u=require("primereact/portal");function p(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function f(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var d=p(e),m=f(e),y=p(t);function b(){return b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},b.apply(null,arguments)}function g(e){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},g(e)}function w(e,t){if("object"!=g(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=g(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function v(e){var t=w(e,"string");return"symbol"==g(t)?t:t+""}function h(e,t,n){return(t=v(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function O(e){if(Array.isArray(e))return e}function P(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,l=[],s=!0,c=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(c)throw o}}return l}}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function j(e,t){if(e){if("string"==typeof e)return E(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?E(e,t):void 0}}function x(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function S(e,t){return O(e)||P(e,t)||j(e,t)||x()}var I=n.ComponentBase.extend({defaultProps:{__TYPE:"IconField",__parentMetadata:null,children:void 0,className:null,iconPosition:"right"},css:{classes:{root:function(e){var t=e.props;return a.classNames("p-icon-field",{"p-icon-field-right":"right"===t.iconPosition,"p-icon-field-left":"left"===t.iconPosition})}}}});function N(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function k(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?N(Object(n),!0).forEach((function(t){h(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):N(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var R=d.default.memo(d.default.forwardRef((function(n,r){var i=e.useRef(r),l=o.useMergeProps(),s=e.useContext(t.PrimeReactContext),c=I.getProps(n,s),u=I.setMetaData(k(k({props:c},c.__parentMetadata),{},{context:{iconPosition:c.iconPosition}})),p=u.ptm,f=l({className:a.classNames(c.className,(0,u.cx)("root",{iconPosition:c.iconPosition}))},I.getOtherProps(c),p("root"));return d.default.createElement("div",b({},f,{ref:i}),e.Children.map(c.children,(function(t,n){return e.cloneElement(t,{iconPosition:c.iconPosition})})))})));R.displayName="IconField";var D=n.ComponentBase.extend({defaultProps:{__TYPE:"InputIcon",__parentMetadata:null,className:null,iconPosition:null},css:{classes:{root:"p-input-icon"}}});function C(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?C(Object(n),!0).forEach((function(t){h(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):C(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var M=d.default.memo(d.default.forwardRef((function(n,r){var i=e.useRef(r),l=o.useMergeProps(),s=e.useContext(t.PrimeReactContext),c=D.getProps(n,s),u=D.setMetaData(_(_({props:c},c.__parentMetadata),{},{context:{iconPosition:c.iconPosition}})),p=u.ptm,f=l({className:a.classNames(c.className,(0,u.cx)("root"))},D.getOtherProps(c),p("root"));return d.default.createElement(d.default.Fragment,null,d.default.createElement("span",b({},f,{ref:i}),c.children))})));M.displayName="InputIcon";var U=n.ComponentBase.extend({defaultProps:{__TYPE:"Password",id:null,inputId:null,inputRef:null,promptLabel:null,weakLabel:null,mediumLabel:null,strongLabel:null,mediumRegex:"^(((?=.*[a-z])(?=.*[A-Z]))|((?=.*[a-z])(?=.*[0-9]))|((?=.*[A-Z])(?=.*[0-9])))(?=.{6,})",strongRegex:"^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.{8,})",feedback:!0,toggleMask:!1,appendTo:null,header:null,content:null,footer:null,showIcon:null,hideIcon:null,icon:null,tooltip:null,tooltipOptions:null,style:null,className:null,inputStyle:null,inputClassName:null,invalid:!1,variant:null,panelStyle:null,panelClassName:null,transitionOptions:null,tabIndex:null,value:void 0,onInput:null,onShow:null,onHide:null,children:void 0},css:{classes:{root:function(e){return a.classNames("p-password p-component p-inputwrapper",{"p-inputwrapper-filled":e.isFilled,"p-inputwrapper-focus":e.focusedState,"p-input-icon-right":e.props.toggleMask})},input:function(e){return a.classNames("p-password-input",e.props.inputClassName)},panel:function(e){var t=e.context;return a.classNames("p-password-panel p-component",e.props.panelClassName,{"p-input-filled":t&&"filled"===t.inputStyle||"filled"===y.default.inputStyle,"p-ripple-disabled":t&&!1===t.ripple||!1===y.default.ripple})},meter:"p-password-meter",meterLabel:function(e){return a.classNames("p-password-strength",e.strength)},info:function(e){return a.classNames("p-password-info",e.strength)},showIcon:"p-password-show-icon",hideIcon:"p-password-hide-icon",transition:"p-connected-overlay"},styles:"\n@layer primereact {\n    .p-password {\n        position: relative;\n        display: inline-flex;\n    }\n    \n    .p-password-panel {\n        position: absolute;\n        top: 0;\n        left: 0;\n    }\n    \n    .p-password .p-password-panel {\n        min-width: 100%;\n    }\n    \n    .p-password-meter {\n        height: 10px;\n    }\n    \n    .p-password-strength {\n        height: 100%;\n        width: 0%;\n        transition: width 1s ease-in-out;\n    }\n    \n    .p-fluid .p-password {\n        display: flex;\n    }\n    \n    .p-password-input::-ms-reveal,\n    .p-password-input::-ms-clear {\n        display: none;\n    }\n\n    .p-password .p-password-show-icon,\n    .p-password .p-password-hide-icon {\n        line-height: 1.5;\n        cursor: pointer;\n    }\n}\n"}});function H(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function L(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?H(Object(n),!0).forEach((function(t){h(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):H(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var T=m.memo(m.forwardRef((function(e,p){var f=o.useMergeProps(),d=m.useContext(t.PrimeReactContext),g=U.getProps(e,d),w=g.promptLabel||t.localeOption("passwordPrompt"),v=g.weakLabel||t.localeOption("weak"),h=g.mediumLabel||t.localeOption("medium"),O=g.strongLabel||t.localeOption("strong"),P=S(m.useState(!1),2),E=P[0],j=P[1],x=S(m.useState(null),2),I=x[0],N=x[1],k=S(m.useState(w),2),D=k[0],C=k[1],_=S(m.useState(!1),2),H=_[0],T=_[1],q=S(m.useState(!1),2),A=q[0],F=q[1],Z=m.useRef(null),K=m.useRef(null),z=m.useRef(g.inputRef),B=m.useRef(new RegExp(g.mediumRegex)),J=m.useRef(new RegExp(g.strongRegex)),X=A?"text":"password",Y={props:g,state:{overlayVisible:E,meter:I,infoText:D,focused:H,unmasked:A}},V=U.setMetaData(Y),G=V.ptm,W=V.cx;n.useHandleStyle(U.css.styles,V.isUnstyled,{name:"password"});var $=o.useDisplayOrder("password",E);o.useGlobalOnEscapeKey({callback:function(){ce()},when:E&&g.feedback&&$,priority:[o.ESC_KEY_HANDLING_PRIORITIES.PASSWORD,$]});var Q=S(o.useOverlayListener({target:Z,overlay:K,listener:function(e,t){t.valid&&("outside"===t.type||d.hideOverlaysOnDocumentScrolling?ce():a.DomHandler.isDocument(e.target)||ue())},when:E}),2),ee=Q[0],te=Q[1],ne=z.current&&z.current.value,re=m.useMemo((function(){return a.ObjectUtils.isNotEmpty(g.value)||a.ObjectUtils.isNotEmpty(g.defaultValue)||a.ObjectUtils.isNotEmpty(ne)}),[g.value,g.defaultValue,ne]),oe=function(){if(I){var e=null;switch(I.strength){case"weak":e=v;break;case"medium":e=h;break;case"strong":e=O}e&&D!==e&&C(e)}else D!==w&&C(w)},ae=function(e){if(!g.feedback)return!1;var t=null,n=null;switch(ye(e)){case 1:t=v,n={strength:"weak",width:"33.33%"};break;case 2:t=h,n={strength:"medium",width:"66.66%"};break;case 3:t=O,n={strength:"strong",width:"100%"};break;default:t=w,n=null}return N(n),C(t),!0},ie=function(e){g.feedback&&c.OverlayService.emit("overlay-click",{originalEvent:e,target:Z.current})},le=function(){F((function(e){return!e}))},se=function(){oe(),j(!0)},ce=function(){j(!1)},ue=function(){z.current&&a.DomHandler.alignOverlay(K.current,z.current.parentElement,g.appendTo||d&&d.appendTo||y.default.appendTo)},pe=function(){a.ZIndexUtils.set("overlay",K.current,d&&d.autoZIndex||y.default.autoZIndex,d&&d.zIndex.overlay||y.default.zIndex.overlay),a.DomHandler.addStyles(K.current,{position:"absolute",top:"0",left:"0"}),ue()},fe=function(){ee(),g.onShow&&g.onShow()},de=function(){te()},me=function(){a.ZIndexUtils.clear(K.current),g.onHide&&g.onHide()},ye=function(e){return e&&0!==e.length?J.current.test(e)?3:B.current.test(e)?2:e.length>0?1:0:0};m.useImperativeHandle(p,(function(){return{props:g,toggleMask:le,focus:function(){return a.DomHandler.focus(z.current)},getElement:function(){return Z.current},getOverlay:function(){return K.current},getInput:function(){return z.current}}})),m.useEffect((function(){a.ObjectUtils.combinedRefs(z,g.inputRef)}),[z,g.inputRef]),m.useEffect((function(){B.current=new RegExp(g.mediumRegex)}),[g.mediumRegex]),m.useEffect((function(){J.current=new RegExp(g.strongRegex)}),[g.strongRegex]),m.useEffect((function(){!re&&a.DomHandler.hasClass(Z.current,"p-inputwrapper-filled")&&a.DomHandler.removeClass(Z.current,"p-inputwrapper-filled")}),[re]),o.useUpdateEffect((function(){ae(g.value)}),[g.value]),o.useMountEffect((function(){ue()})),o.useUnmountEffect((function(){a.ZIndexUtils.clear(K.current)}));var be=function(e){"Enter"!==e.key&&"Space"!==e.code||(le(),e.preventDefault())},ge=a.classNames("p-password p-component p-inputwrapper",{"p-inputwrapper-filled":re,"p-inputwrapper-focus":H,"p-input-icon-right":g.toggleMask},g.className),we=U.getOtherProps(g),ve=function(){if(!g.toggleMask)return null;var e=f({role:"switch",tabIndex:g.tabIndex||"0",className:W("hideIcon"),onClick:le,onKeyDown:be,"aria-label":t.ariaLabel("passwordHide")||"Hide Password","aria-checked":"false"},G("hideIcon")),n=f({role:"switch",tabIndex:g.tabIndex||"0",className:W("showIcon"),onClick:le,onKeyDown:be,"aria-label":t.ariaLabel("passwordShow")||"Show Password","aria-checked":"true"},G("showIcon")),r=a.IconUtils.getJSXIcon(A?g.hideIcon||m.createElement(l.EyeSlashIcon,e):g.showIcon||m.createElement(i.EyeIcon,n),L({},A?e:n),{props:g});g.icon&&(r=a.ObjectUtils.getJSXElement(g.icon,{onClick:le,className:ge,element:r,props:g}));return r}(),he=function(){var e=I||{strength:"",width:"0%"},t=e.strength,n=e.width,o=a.ObjectUtils.getJSXElement(g.header,g),i=a.ObjectUtils.getJSXElement(g.footer,g),l=f({className:W("panel",{context:d}),style:g.panelStyle,onClick:ie},G("panel")),s=f({className:W("meter")},G("meter")),c=f({className:W("meterLabel",{strength:t}),style:{width:n}},G("meterLabel")),p=f({className:W("info",{strength:t})},G("info")),y=g.content?a.ObjectUtils.getJSXElement(g.content,g):m.createElement(m.Fragment,null,m.createElement("div",s,m.createElement("div",c)),m.createElement("div",p,D)),w=f({classNames:W("transition"),in:E,timeout:{enter:120,exit:100},options:g.transitionOptions,unmountOnExit:!0,onEnter:pe,onEntered:fe,onExit:de,onExited:me},G("transition")),v=m.createElement(r.CSSTransition,b({nodeRef:K},w),m.createElement("div",b({ref:K},l),o,y,i));return m.createElement(u.Portal,{element:v,appendTo:g.appendTo})}(),Oe=f({ref:Z,id:g.id,className:a.classNames(g.className,W("root",{isFilled:re,focusedState:H})),style:g.style},G("root")),Pe=f(L(L({ref:z,id:g.inputId},we),{},{className:a.classNames(g.inputClassName,W("input")),onBlur:function(e){T(!1),g.feedback&&ce(),g.onBlur&&g.onBlur(e)},onFocus:function(e){T(!0),g.feedback&&se(),g.onFocus&&g.onFocus(e)},onInput:function(e,t){g.onInput&&g.onInput(e,t),g.onChange||(a.ObjectUtils.isNotEmpty(e.target.value)?a.DomHandler.addClass(Z.current,"p-inputwrapper-filled"):a.DomHandler.removeClass(Z.current,"p-inputwrapper-filled"))},onKeyUp:function(e){var t=e.code;g.feedback&&t&&"Escape"!==t&&!E&&se(),g.onKeyUp&&g.onKeyUp(e)},invalid:g.invalid,variant:g.variant,style:g.inputStyle,unstyled:g.unstyled,tabIndex:g.tabIndex||"0",tooltip:g.tooltip,tooltipOptions:g.tooltipOptions,type:X,value:g.value,__parentMetadata:{parent:Y}}),G("input")),Ee=m.createElement(s.InputText,Pe);return ve&&(Ee=m.createElement(R,{className:W("iconField"),pt:G("iconField"),__parentMetadata:{parent:Y}},Ee,m.createElement(M,{className:W("inputIcon"),pt:G("inputIcon"),__parentMetadata:{parent:Y}},ve))),m.createElement("div",Oe,Ee,he)})));T.displayName="Password",exports.Password=T;
