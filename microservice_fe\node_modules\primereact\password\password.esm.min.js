import*as e from"react";import t,{useRef as n,useContext as r,Children as o,cloneElement as i}from"react";import a,{PrimeReactContext as l,localeOption as s,ariaLabel as c}from"primereact/api";import{ComponentBase as p,useHandleStyle as u}from"primereact/componentbase";import{CSSTransition as f}from"primereact/csstransition";import{useMergeProps as d,useDisplayOrder as m,useGlobalOnEscapeKey as y,ESC_KEY_HANDLING_PRIORITIES as g,useOverlayListener as b,useUpdateEffect as w,useMountEffect as h,useUnmountEffect as v}from"primereact/hooks";import{classNames as O,DomHandler as P,ObjectUtils as E,ZIndexUtils as x,IconUtils as S}from"primereact/utils";import{EyeIcon as j}from"primereact/icons/eye";import{EyeSlashIcon as I}from"primereact/icons/eyeslash";import{InputText as k}from"primereact/inputtext";import{OverlayService as N}from"primereact/overlayservice";import{Portal as R}from"primereact/portal";function D(){return D=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},D.apply(null,arguments)}function _(e){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_(e)}function M(e,t){if("object"!=_(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=_(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function C(e){var t=M(e,"string");return"symbol"==_(t)?t:t+""}function T(e,t,n){return(t=C(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function F(e){if(Array.isArray(e))return e}function L(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}function A(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function H(e,t){if(e){if("string"==typeof e)return A(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?A(e,t):void 0}}function z(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function J(e,t){return F(e)||L(e,t)||H(e,t)||z()}var K=p.extend({defaultProps:{__TYPE:"IconField",__parentMetadata:null,children:void 0,className:null,iconPosition:"right"},css:{classes:{root:function(e){var t=e.props;return O("p-icon-field",{"p-icon-field-right":"right"===t.iconPosition,"p-icon-field-left":"left"===t.iconPosition})}}}});function U(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function X(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?U(Object(n),!0).forEach((function(t){T(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):U(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Z=t.memo(t.forwardRef((function(e,a){var s=n(a),c=d(),p=r(l),u=K.getProps(e,p),f=K.setMetaData(X(X({props:u},u.__parentMetadata),{},{context:{iconPosition:u.iconPosition}})),m=f.ptm,y=c({className:O(u.className,(0,f.cx)("root",{iconPosition:u.iconPosition}))},K.getOtherProps(u),m("root"));return t.createElement("div",D({},y,{ref:s}),o.map(u.children,(function(e,t){return i(e,{iconPosition:u.iconPosition})})))})));Z.displayName="IconField";var B=p.extend({defaultProps:{__TYPE:"InputIcon",__parentMetadata:null,className:null,iconPosition:null},css:{classes:{root:"p-input-icon"}}});function V(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?V(Object(n),!0).forEach((function(t){T(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):V(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var W=t.memo(t.forwardRef((function(e,o){var i=n(o),a=d(),s=r(l),c=B.getProps(e,s),p=B.setMetaData(Y(Y({props:c},c.__parentMetadata),{},{context:{iconPosition:c.iconPosition}})),u=p.ptm,f=a({className:O(c.className,(0,p.cx)("root"))},B.getOtherProps(c),u("root"));return t.createElement(t.Fragment,null,t.createElement("span",D({},f,{ref:i}),c.children))})));W.displayName="InputIcon";var $=p.extend({defaultProps:{__TYPE:"Password",id:null,inputId:null,inputRef:null,promptLabel:null,weakLabel:null,mediumLabel:null,strongLabel:null,mediumRegex:"^(((?=.*[a-z])(?=.*[A-Z]))|((?=.*[a-z])(?=.*[0-9]))|((?=.*[A-Z])(?=.*[0-9])))(?=.{6,})",strongRegex:"^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.{8,})",feedback:!0,toggleMask:!1,appendTo:null,header:null,content:null,footer:null,showIcon:null,hideIcon:null,icon:null,tooltip:null,tooltipOptions:null,style:null,className:null,inputStyle:null,inputClassName:null,invalid:!1,variant:null,panelStyle:null,panelClassName:null,transitionOptions:null,tabIndex:null,value:void 0,onInput:null,onShow:null,onHide:null,children:void 0},css:{classes:{root:function(e){return O("p-password p-component p-inputwrapper",{"p-inputwrapper-filled":e.isFilled,"p-inputwrapper-focus":e.focusedState,"p-input-icon-right":e.props.toggleMask})},input:function(e){return O("p-password-input",e.props.inputClassName)},panel:function(e){var t=e.context;return O("p-password-panel p-component",e.props.panelClassName,{"p-input-filled":t&&"filled"===t.inputStyle||"filled"===a.inputStyle,"p-ripple-disabled":t&&!1===t.ripple||!1===a.ripple})},meter:"p-password-meter",meterLabel:function(e){return O("p-password-strength",e.strength)},info:function(e){return O("p-password-info",e.strength)},showIcon:"p-password-show-icon",hideIcon:"p-password-hide-icon",transition:"p-connected-overlay"},styles:"\n@layer primereact {\n    .p-password {\n        position: relative;\n        display: inline-flex;\n    }\n    \n    .p-password-panel {\n        position: absolute;\n        top: 0;\n        left: 0;\n    }\n    \n    .p-password .p-password-panel {\n        min-width: 100%;\n    }\n    \n    .p-password-meter {\n        height: 10px;\n    }\n    \n    .p-password-strength {\n        height: 100%;\n        width: 0%;\n        transition: width 1s ease-in-out;\n    }\n    \n    .p-fluid .p-password {\n        display: flex;\n    }\n    \n    .p-password-input::-ms-reveal,\n    .p-password-input::-ms-clear {\n        display: none;\n    }\n\n    .p-password .p-password-show-icon,\n    .p-password .p-password-hide-icon {\n        line-height: 1.5;\n        cursor: pointer;\n    }\n}\n"}});function q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function G(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?q(Object(n),!0).forEach((function(t){T(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):q(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Q=e.memo(e.forwardRef((function(t,n){var r=d(),o=e.useContext(l),i=$.getProps(t,o),p=i.promptLabel||s("passwordPrompt"),_=i.weakLabel||s("weak"),M=i.mediumLabel||s("medium"),C=i.strongLabel||s("strong"),T=J(e.useState(!1),2),F=T[0],L=T[1],A=J(e.useState(null),2),H=A[0],z=A[1],K=J(e.useState(p),2),U=K[0],X=K[1],B=J(e.useState(!1),2),V=B[0],Y=B[1],q=J(e.useState(!1),2),Q=q[0],ee=q[1],te=e.useRef(null),ne=e.useRef(null),re=e.useRef(i.inputRef),oe=e.useRef(new RegExp(i.mediumRegex)),ie=e.useRef(new RegExp(i.strongRegex)),ae=Q?"text":"password",le={props:i,state:{overlayVisible:F,meter:H,infoText:U,focused:V,unmasked:Q}},se=$.setMetaData(le),ce=se.ptm,pe=se.cx;u($.css.styles,se.isUnstyled,{name:"password"});var ue=m("password",F);y({callback:function(){Pe()},when:F&&i.feedback&&ue,priority:[g.PASSWORD,ue]});var fe=J(b({target:te,overlay:ne,listener:function(e,t){t.valid&&("outside"===t.type||o.hideOverlaysOnDocumentScrolling?Pe():P.isDocument(e.target)||Ee())},when:F}),2),de=fe[0],me=fe[1],ye=re.current&&re.current.value,ge=e.useMemo((function(){return E.isNotEmpty(i.value)||E.isNotEmpty(i.defaultValue)||E.isNotEmpty(ye)}),[i.value,i.defaultValue,ye]),be=function(){if(H){var e=null;switch(H.strength){case"weak":e=_;break;case"medium":e=M;break;case"strong":e=C}e&&U!==e&&X(e)}else U!==p&&X(p)},we=function(e){if(!i.feedback)return!1;var t=null,n=null;switch(ke(e)){case 1:t=_,n={strength:"weak",width:"33.33%"};break;case 2:t=M,n={strength:"medium",width:"66.66%"};break;case 3:t=C,n={strength:"strong",width:"100%"};break;default:t=p,n=null}return z(n),X(t),!0},he=function(e){i.feedback&&N.emit("overlay-click",{originalEvent:e,target:te.current})},ve=function(){ee((function(e){return!e}))},Oe=function(){be(),L(!0)},Pe=function(){L(!1)},Ee=function(){re.current&&P.alignOverlay(ne.current,re.current.parentElement,i.appendTo||o&&o.appendTo||a.appendTo)},xe=function(){x.set("overlay",ne.current,o&&o.autoZIndex||a.autoZIndex,o&&o.zIndex.overlay||a.zIndex.overlay),P.addStyles(ne.current,{position:"absolute",top:"0",left:"0"}),Ee()},Se=function(){de(),i.onShow&&i.onShow()},je=function(){me()},Ie=function(){x.clear(ne.current),i.onHide&&i.onHide()},ke=function(e){return e&&0!==e.length?ie.current.test(e)?3:oe.current.test(e)?2:e.length>0?1:0:0};e.useImperativeHandle(n,(function(){return{props:i,toggleMask:ve,focus:function(){return P.focus(re.current)},getElement:function(){return te.current},getOverlay:function(){return ne.current},getInput:function(){return re.current}}})),e.useEffect((function(){E.combinedRefs(re,i.inputRef)}),[re,i.inputRef]),e.useEffect((function(){oe.current=new RegExp(i.mediumRegex)}),[i.mediumRegex]),e.useEffect((function(){ie.current=new RegExp(i.strongRegex)}),[i.strongRegex]),e.useEffect((function(){!ge&&P.hasClass(te.current,"p-inputwrapper-filled")&&P.removeClass(te.current,"p-inputwrapper-filled")}),[ge]),w((function(){we(i.value)}),[i.value]),h((function(){Ee()})),v((function(){x.clear(ne.current)}));var Ne=function(e){"Enter"!==e.key&&"Space"!==e.code||(ve(),e.preventDefault())},Re=O("p-password p-component p-inputwrapper",{"p-inputwrapper-filled":ge,"p-inputwrapper-focus":V,"p-input-icon-right":i.toggleMask},i.className),De=$.getOtherProps(i),_e=function(){if(!i.toggleMask)return null;var t=r({role:"switch",tabIndex:i.tabIndex||"0",className:pe("hideIcon"),onClick:ve,onKeyDown:Ne,"aria-label":c("passwordHide")||"Hide Password","aria-checked":"false"},ce("hideIcon")),n=r({role:"switch",tabIndex:i.tabIndex||"0",className:pe("showIcon"),onClick:ve,onKeyDown:Ne,"aria-label":c("passwordShow")||"Show Password","aria-checked":"true"},ce("showIcon")),o=S.getJSXIcon(Q?i.hideIcon||e.createElement(I,t):i.showIcon||e.createElement(j,n),G({},Q?t:n),{props:i});i.icon&&(o=E.getJSXElement(i.icon,{onClick:ve,className:Re,element:o,props:i}));return o}(),Me=function(){var t=H||{strength:"",width:"0%"},n=t.strength,a=t.width,l=E.getJSXElement(i.header,i),s=E.getJSXElement(i.footer,i),c=r({className:pe("panel",{context:o}),style:i.panelStyle,onClick:he},ce("panel")),p=r({className:pe("meter")},ce("meter")),u=r({className:pe("meterLabel",{strength:n}),style:{width:a}},ce("meterLabel")),d=r({className:pe("info",{strength:n})},ce("info")),m=i.content?E.getJSXElement(i.content,i):e.createElement(e.Fragment,null,e.createElement("div",p,e.createElement("div",u)),e.createElement("div",d,U)),y=r({classNames:pe("transition"),in:F,timeout:{enter:120,exit:100},options:i.transitionOptions,unmountOnExit:!0,onEnter:xe,onEntered:Se,onExit:je,onExited:Ie},ce("transition")),g=e.createElement(f,D({nodeRef:ne},y),e.createElement("div",D({ref:ne},c),l,m,s));return e.createElement(R,{element:g,appendTo:i.appendTo})}(),Ce=r({ref:te,id:i.id,className:O(i.className,pe("root",{isFilled:ge,focusedState:V})),style:i.style},ce("root")),Te=r(G(G({ref:re,id:i.inputId},De),{},{className:O(i.inputClassName,pe("input")),onBlur:function(e){Y(!1),i.feedback&&Pe(),i.onBlur&&i.onBlur(e)},onFocus:function(e){Y(!0),i.feedback&&Oe(),i.onFocus&&i.onFocus(e)},onInput:function(e,t){i.onInput&&i.onInput(e,t),i.onChange||(E.isNotEmpty(e.target.value)?P.addClass(te.current,"p-inputwrapper-filled"):P.removeClass(te.current,"p-inputwrapper-filled"))},onKeyUp:function(e){var t=e.code;i.feedback&&t&&"Escape"!==t&&!F&&Oe(),i.onKeyUp&&i.onKeyUp(e)},invalid:i.invalid,variant:i.variant,style:i.inputStyle,unstyled:i.unstyled,tabIndex:i.tabIndex||"0",tooltip:i.tooltip,tooltipOptions:i.tooltipOptions,type:ae,value:i.value,__parentMetadata:{parent:le}}),ce("input")),Fe=e.createElement(k,Te);return _e&&(Fe=e.createElement(Z,{className:pe("iconField"),pt:ce("iconField"),__parentMetadata:{parent:le}},Fe,e.createElement(W,{className:pe("inputIcon"),pt:ce("inputIcon"),__parentMetadata:{parent:le}},_e))),e.createElement("div",Ce,Fe,Me)})));Q.displayName="Password";export{Q as Password};
