this.primereact=this.primereact||{},this.primereact.password=function(e,t,n,r,o,a,i,l,s,c,u,p){"use strict";function f(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function d(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var m=f(t),y=d(t),b=f(n);function g(){return g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},g.apply(null,arguments)}function w(e){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},w(e)}function h(e,t){if("object"!=w(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=w(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function v(e){var t=h(e,"string");return"symbol"==w(t)?t:t+""}function O(e,t,n){return(t=v(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function P(e){if(Array.isArray(e))return e}function E(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,l=[],s=!0,c=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(c)throw o}}return l}}function j(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function x(e,t){if(e){if("string"==typeof e)return j(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?j(e,t):void 0}}function S(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function I(e,t){return P(e)||E(e,t)||x(e,t)||S()}var N=r.ComponentBase.extend({defaultProps:{__TYPE:"IconField",__parentMetadata:null,children:void 0,className:null,iconPosition:"right"},css:{classes:{root:function(e){var t=e.props;return i.classNames("p-icon-field",{"p-icon-field-right":"right"===t.iconPosition,"p-icon-field-left":"left"===t.iconPosition})}}}});function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function R(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach((function(t){O(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var D=m.default.memo(m.default.forwardRef((function(e,r){var o=t.useRef(r),l=a.useMergeProps(),s=t.useContext(n.PrimeReactContext),c=N.getProps(e,s),u=N.setMetaData(R(R({props:c},c.__parentMetadata),{},{context:{iconPosition:c.iconPosition}})),p=u.ptm,f=l({className:i.classNames(c.className,(0,u.cx)("root",{iconPosition:c.iconPosition}))},N.getOtherProps(c),p("root"));return m.default.createElement("div",g({},f,{ref:o}),t.Children.map(c.children,(function(e,n){return t.cloneElement(e,{iconPosition:c.iconPosition})})))})));D.displayName="IconField";var C=r.ComponentBase.extend({defaultProps:{__TYPE:"InputIcon",__parentMetadata:null,className:null,iconPosition:null},css:{classes:{root:"p-input-icon"}}});function _(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function M(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_(Object(n),!0).forEach((function(t){O(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var U=m.default.memo(m.default.forwardRef((function(e,r){var o=t.useRef(r),l=a.useMergeProps(),s=t.useContext(n.PrimeReactContext),c=C.getProps(e,s),u=C.setMetaData(M(M({props:c},c.__parentMetadata),{},{context:{iconPosition:c.iconPosition}})),p=u.ptm,f=l({className:i.classNames(c.className,(0,u.cx)("root"))},C.getOtherProps(c),p("root"));return m.default.createElement(m.default.Fragment,null,m.default.createElement("span",g({},f,{ref:o}),c.children))})));U.displayName="InputIcon";var H=r.ComponentBase.extend({defaultProps:{__TYPE:"Password",id:null,inputId:null,inputRef:null,promptLabel:null,weakLabel:null,mediumLabel:null,strongLabel:null,mediumRegex:"^(((?=.*[a-z])(?=.*[A-Z]))|((?=.*[a-z])(?=.*[0-9]))|((?=.*[A-Z])(?=.*[0-9])))(?=.{6,})",strongRegex:"^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.{8,})",feedback:!0,toggleMask:!1,appendTo:null,header:null,content:null,footer:null,showIcon:null,hideIcon:null,icon:null,tooltip:null,tooltipOptions:null,style:null,className:null,inputStyle:null,inputClassName:null,invalid:!1,variant:null,panelStyle:null,panelClassName:null,transitionOptions:null,tabIndex:null,value:void 0,onInput:null,onShow:null,onHide:null,children:void 0},css:{classes:{root:function(e){return i.classNames("p-password p-component p-inputwrapper",{"p-inputwrapper-filled":e.isFilled,"p-inputwrapper-focus":e.focusedState,"p-input-icon-right":e.props.toggleMask})},input:function(e){return i.classNames("p-password-input",e.props.inputClassName)},panel:function(e){var t=e.context;return i.classNames("p-password-panel p-component",e.props.panelClassName,{"p-input-filled":t&&"filled"===t.inputStyle||"filled"===b.default.inputStyle,"p-ripple-disabled":t&&!1===t.ripple||!1===b.default.ripple})},meter:"p-password-meter",meterLabel:function(e){return i.classNames("p-password-strength",e.strength)},info:function(e){return i.classNames("p-password-info",e.strength)},showIcon:"p-password-show-icon",hideIcon:"p-password-hide-icon",transition:"p-connected-overlay"},styles:"\n@layer primereact {\n    .p-password {\n        position: relative;\n        display: inline-flex;\n    }\n    \n    .p-password-panel {\n        position: absolute;\n        top: 0;\n        left: 0;\n    }\n    \n    .p-password .p-password-panel {\n        min-width: 100%;\n    }\n    \n    .p-password-meter {\n        height: 10px;\n    }\n    \n    .p-password-strength {\n        height: 100%;\n        width: 0%;\n        transition: width 1s ease-in-out;\n    }\n    \n    .p-fluid .p-password {\n        display: flex;\n    }\n    \n    .p-password-input::-ms-reveal,\n    .p-password-input::-ms-clear {\n        display: none;\n    }\n\n    .p-password .p-password-show-icon,\n    .p-password .p-password-hide-icon {\n        line-height: 1.5;\n        cursor: pointer;\n    }\n}\n"}});function L(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function T(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?L(Object(n),!0).forEach((function(t){O(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):L(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var A=y.memo(y.forwardRef((function(e,t){var f=a.useMergeProps(),d=y.useContext(n.PrimeReactContext),m=H.getProps(e,d),w=m.promptLabel||n.localeOption("passwordPrompt"),h=m.weakLabel||n.localeOption("weak"),v=m.mediumLabel||n.localeOption("medium"),O=m.strongLabel||n.localeOption("strong"),P=I(y.useState(!1),2),E=P[0],j=P[1],x=I(y.useState(null),2),S=x[0],N=x[1],k=I(y.useState(w),2),R=k[0],C=k[1],_=I(y.useState(!1),2),M=_[0],L=_[1],A=I(y.useState(!1),2),F=A[0],Z=A[1],K=y.useRef(null),z=y.useRef(null),B=y.useRef(m.inputRef),J=y.useRef(new RegExp(m.mediumRegex)),X=y.useRef(new RegExp(m.strongRegex)),Y=F?"text":"password",V={props:m,state:{overlayVisible:E,meter:S,infoText:R,focused:M,unmasked:F}},G=H.setMetaData(V),W=G.ptm,$=G.cx;r.useHandleStyle(H.css.styles,G.isUnstyled,{name:"password"});var q=a.useDisplayOrder("password",E);a.useGlobalOnEscapeKey({callback:function(){ce()},when:E&&m.feedback&&q,priority:[a.ESC_KEY_HANDLING_PRIORITIES.PASSWORD,q]});var Q=I(a.useOverlayListener({target:K,overlay:z,listener:function(e,t){t.valid&&("outside"===t.type||d.hideOverlaysOnDocumentScrolling?ce():i.DomHandler.isDocument(e.target)||ue())},when:E}),2),ee=Q[0],te=Q[1],ne=B.current&&B.current.value,re=y.useMemo((function(){return i.ObjectUtils.isNotEmpty(m.value)||i.ObjectUtils.isNotEmpty(m.defaultValue)||i.ObjectUtils.isNotEmpty(ne)}),[m.value,m.defaultValue,ne]),oe=function(){if(S){var e=null;switch(S.strength){case"weak":e=h;break;case"medium":e=v;break;case"strong":e=O}e&&R!==e&&C(e)}else R!==w&&C(w)},ae=function(e){if(!m.feedback)return!1;var t=null,n=null;switch(ye(e)){case 1:t=h,n={strength:"weak",width:"33.33%"};break;case 2:t=v,n={strength:"medium",width:"66.66%"};break;case 3:t=O,n={strength:"strong",width:"100%"};break;default:t=w,n=null}return N(n),C(t),!0},ie=function(e){m.feedback&&u.OverlayService.emit("overlay-click",{originalEvent:e,target:K.current})},le=function(){Z((function(e){return!e}))},se=function(){oe(),j(!0)},ce=function(){j(!1)},ue=function(){B.current&&i.DomHandler.alignOverlay(z.current,B.current.parentElement,m.appendTo||d&&d.appendTo||b.default.appendTo)},pe=function(){i.ZIndexUtils.set("overlay",z.current,d&&d.autoZIndex||b.default.autoZIndex,d&&d.zIndex.overlay||b.default.zIndex.overlay),i.DomHandler.addStyles(z.current,{position:"absolute",top:"0",left:"0"}),ue()},fe=function(){ee(),m.onShow&&m.onShow()},de=function(){te()},me=function(){i.ZIndexUtils.clear(z.current),m.onHide&&m.onHide()},ye=function(e){return e&&0!==e.length?X.current.test(e)?3:J.current.test(e)?2:e.length>0?1:0:0};y.useImperativeHandle(t,(function(){return{props:m,toggleMask:le,focus:function(){return i.DomHandler.focus(B.current)},getElement:function(){return K.current},getOverlay:function(){return z.current},getInput:function(){return B.current}}})),y.useEffect((function(){i.ObjectUtils.combinedRefs(B,m.inputRef)}),[B,m.inputRef]),y.useEffect((function(){J.current=new RegExp(m.mediumRegex)}),[m.mediumRegex]),y.useEffect((function(){X.current=new RegExp(m.strongRegex)}),[m.strongRegex]),y.useEffect((function(){!re&&i.DomHandler.hasClass(K.current,"p-inputwrapper-filled")&&i.DomHandler.removeClass(K.current,"p-inputwrapper-filled")}),[re]),a.useUpdateEffect((function(){ae(m.value)}),[m.value]),a.useMountEffect((function(){ue()})),a.useUnmountEffect((function(){i.ZIndexUtils.clear(z.current)}));var be=function(e){"Enter"!==e.key&&"Space"!==e.code||(le(),e.preventDefault())},ge=i.classNames("p-password p-component p-inputwrapper",{"p-inputwrapper-filled":re,"p-inputwrapper-focus":M,"p-input-icon-right":m.toggleMask},m.className),we=H.getOtherProps(m),he=function(){if(!m.toggleMask)return null;var e=f({role:"switch",tabIndex:m.tabIndex||"0",className:$("hideIcon"),onClick:le,onKeyDown:be,"aria-label":n.ariaLabel("passwordHide")||"Hide Password","aria-checked":"false"},W("hideIcon")),t=f({role:"switch",tabIndex:m.tabIndex||"0",className:$("showIcon"),onClick:le,onKeyDown:be,"aria-label":n.ariaLabel("passwordShow")||"Show Password","aria-checked":"true"},W("showIcon")),r=i.IconUtils.getJSXIcon(F?m.hideIcon||y.createElement(s.EyeSlashIcon,e):m.showIcon||y.createElement(l.EyeIcon,t),T({},F?e:t),{props:m});m.icon&&(r=i.ObjectUtils.getJSXElement(m.icon,{onClick:le,className:ge,element:r,props:m}));return r}(),ve=function(){var e=S||{strength:"",width:"0%"},t=e.strength,n=e.width,r=i.ObjectUtils.getJSXElement(m.header,m),a=i.ObjectUtils.getJSXElement(m.footer,m),l=f({className:$("panel",{context:d}),style:m.panelStyle,onClick:ie},W("panel")),s=f({className:$("meter")},W("meter")),c=f({className:$("meterLabel",{strength:t}),style:{width:n}},W("meterLabel")),u=f({className:$("info",{strength:t})},W("info")),b=m.content?i.ObjectUtils.getJSXElement(m.content,m):y.createElement(y.Fragment,null,y.createElement("div",s,y.createElement("div",c)),y.createElement("div",u,R)),w=f({classNames:$("transition"),in:E,timeout:{enter:120,exit:100},options:m.transitionOptions,unmountOnExit:!0,onEnter:pe,onEntered:fe,onExit:de,onExited:me},W("transition")),h=y.createElement(o.CSSTransition,g({nodeRef:z},w),y.createElement("div",g({ref:z},l),r,b,a));return y.createElement(p.Portal,{element:h,appendTo:m.appendTo})}(),Oe=f({ref:K,id:m.id,className:i.classNames(m.className,$("root",{isFilled:re,focusedState:M})),style:m.style},W("root")),Pe=f(T(T({ref:B,id:m.inputId},we),{},{className:i.classNames(m.inputClassName,$("input")),onBlur:function(e){L(!1),m.feedback&&ce(),m.onBlur&&m.onBlur(e)},onFocus:function(e){L(!0),m.feedback&&se(),m.onFocus&&m.onFocus(e)},onInput:function(e,t){m.onInput&&m.onInput(e,t),m.onChange||(i.ObjectUtils.isNotEmpty(e.target.value)?i.DomHandler.addClass(K.current,"p-inputwrapper-filled"):i.DomHandler.removeClass(K.current,"p-inputwrapper-filled"))},onKeyUp:function(e){var t=e.code;m.feedback&&t&&"Escape"!==t&&!E&&se(),m.onKeyUp&&m.onKeyUp(e)},invalid:m.invalid,variant:m.variant,style:m.inputStyle,unstyled:m.unstyled,tabIndex:m.tabIndex||"0",tooltip:m.tooltip,tooltipOptions:m.tooltipOptions,type:Y,value:m.value,__parentMetadata:{parent:V}}),W("input")),Ee=y.createElement(c.InputText,Pe);return he&&(Ee=y.createElement(D,{className:$("iconField"),pt:W("iconField"),__parentMetadata:{parent:V}},Ee,y.createElement(U,{className:$("inputIcon"),pt:W("inputIcon"),__parentMetadata:{parent:V}},he))),y.createElement("div",Oe,Ee,ve)})));return A.displayName="Password",e.Password=A,Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,primereact.api,primereact.componentbase,primereact.csstransition,primereact.hooks,primereact.utils,primereact.icons.eye,primereact.icons.eyeslash,primereact.inputtext,primereact.overlayservice,primereact.portal);
