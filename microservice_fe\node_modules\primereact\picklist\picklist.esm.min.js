import*as e from"react";import t,{aria<PERSON>abel as n,PrimeReactContext as o,FilterService as r}from"primereact/api";import{ComponentBase as a,useHandleStyle as i}from"primereact/componentbase";import{useMergeProps as l,useMatchMedia as c,useUpdateEffect as s}from"primereact/hooks";import{classNames as u,ObjectUtils as p,IconUtils as m,UniqueComponentId as d,<PERSON><PERSON><PERSON><PERSON> as f}from"primereact/utils";import{Button as v}from"primereact/button";import{AngleDoubleDownIcon as g}from"primereact/icons/angledoubledown";import{AngleDoubleUpIcon as h}from"primereact/icons/angledoubleup";import{AngleDownIcon as y}from"primereact/icons/angledown";import{AngleUpIcon as T}from"primereact/icons/angleup";import{SearchIcon as S}from"primereact/icons/search";import{Ripple as b}from"primereact/ripple";import{AngleDoubleLeftIcon as I}from"primereact/icons/angledoubleleft";import{AngleDoubleRightIcon as E}from"primereact/icons/angledoubleright";import{AngleLeftIcon as w}from"primereact/icons/angleleft";import{AngleRightIcon as N}from"primereact/icons/angleright";function A(e){throw new TypeError('"'+e+'" is read-only')}function k(e){return k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},k(e)}function C(e,t){if("object"!=k(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=k(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function O(e){var t=C(e,"string");return"symbol"==k(t)?t:t+""}function L(e,t,n){return(t=O(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function M(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function D(e){if(Array.isArray(e))return M(e)}function x(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function F(e,t){if(e){if("string"==typeof e)return M(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?M(e,t):void 0}}function P(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _(e){return D(e)||x(e)||F(e)||P()}function R(e){if(Array.isArray(e))return e}function K(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}function B(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function j(e,t){return R(e)||K(e,t)||F(e,t)||B()}var H=a.extend({defaultProps:{__TYPE:"PickList",id:null,source:null,target:null,sourceHeader:null,targetHeader:null,style:null,className:null,sourceStyle:null,targetStyle:null,sourceSelection:null,targetSelection:null,showSourceControls:!0,showTargetControls:!0,metaKeySelection:!1,onFocus:null,onBlur:null,filter:!1,filterBy:null,filterMatchMode:"contains",targetFilterIcon:null,sourceFilterIcon:null,moveAllToSourceIcon:null,moveToSourceIcon:null,moveAllToTargetIcon:null,moveToTargetIcon:null,moveBottomIcon:null,moveUpIcon:null,moveTopIcon:null,moveDownIcon:null,filterLocale:void 0,sourceFilterValue:null,targetFilterValue:null,showSourceFilter:!0,showTargetFilter:!0,sourceFilterPlaceholder:null,targetFilterPlaceholder:null,sourceFilterTemplate:null,targetFilterTemplate:null,tabIndex:0,dataKey:null,autoOptionFocus:!0,focusOnHover:!0,breakpoint:"960px",itemTemplate:null,sourceItemTemplate:null,targetItemTemplate:null,onChange:null,onMoveToSource:null,onMoveAllToSource:null,onMoveToTarget:null,onMoveAllToTarget:null,onSourceSelectionChange:null,onTargetSelectionChange:null,onSourceFilterChange:null,onTargetFilterChange:null,children:void 0},css:{classes:{root:"p-picklist p-component",buttons:"p-picklist-buttons p-picklist-transfer-buttons",header:"p-picklist-header",filterIcon:"p-picklist-filter-icon",filter:"p-picklist-filter",filterInput:"p-picklist-filter-input p-inputtext p-component",filterContainer:"p-picklist-filter-container",list:"p-picklist-list",listWrapper:"p-picklist-list-wrapper",listSourceWrapper:"p-picklist-list-wrapper p-picklist-source-wrapper",listTargetWrapper:"p-picklist-list-wrapper p-picklist-target-wrapper",listSource:"p-picklist-list p-picklist-source",listTarget:"p-picklist-list p-picklist-target",item:function(e){return u("p-picklist-item",{"p-highlight":e.selected,"p-focus":e.focused})},sourceControls:"p-picklist-source-controls p-picklist-buttons",targetControls:"p-picklist-target-controls p-picklist-buttons"},styles:"\n@layer primereact {\n    .p-picklist {\n        display: flex;\n    }\n\n    .p-picklist-buttons {\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n    }\n\n    .p-picklist-list-wrapper {\n        flex: 1 1 50%;\n    }\n\n    .p-picklist-list {\n        list-style-type: none;\n        margin: 0;\n        padding: 0;\n        overflow: auto;\n        min-height: 12rem;\n        max-height: 24rem;\n    }\n\n    .p-picklist-item {\n        cursor: pointer;\n        overflow: hidden;\n        position: relative;\n    }\n\n    .p-picklist-item .p-ink {\n        pointer-events: none;\n    }\n\n    .p-picklist-filter {\n        position: relative;\n    }\n\n    .p-picklist-filter-icon {\n        position: absolute;\n        top: 50%;\n        margin-top: -.5rem;\n    }\n\n    .p-picklist-filter-input {\n        width: 100%;\n    }\n}\n"}}),U=e.memo((function(t){var o=l(),r=t.ptm,a=t.cx,i=t.unstyled,c=t.moveUpIcon||e.createElement(T,null),s=t.moveTopIcon||e.createElement(h,null),m=t.moveDownIcon||e.createElement(y,null),d=t.moveBottomIcon||e.createElement(g,null),f=!t.selection||!t.selection.length,S=o({className:u(t.className,a("controls"))},r("controls",{hostName:t.hostName}));return e.createElement("div",S,e.createElement(v,{disabled:f,type:"button",icon:c,onClick:function(e){var n=t.selection;if(n&&n.length){for(var o=_(t.list),r=0;r<n.length;r++){var a=p.findIndexInList(n[r],o,t.dataKey);if(0===a)break;var i=o[a-1];o[a-1]=o[a],o[a]=i}t.onReorder&&t.onReorder({originalEvent:e,value:o,direction:"up"})}},pt:r("moveUpButton"),unstyled:i,"aria-label":n("moveUp"),__parentMetadata:{parent:t.metaData}}),e.createElement(v,{disabled:f,type:"button",icon:s,onClick:function(e){var n=t.selection;if(n&&n.length){for(var o=_(t.list),r=0;r<n.length;r++){var a=p.findIndexInList(n[r],o,t.dataKey);if(0===a)break;var i=o.splice(a,1)[0];o.unshift(i)}t.onReorder&&t.onReorder({originalEvent:e,value:o,direction:"top"})}},pt:r("moveTopButton"),unstyled:i,"aria-label":n("moveTop"),__parentMetadata:{parent:t.metaData}}),e.createElement(v,{disabled:f,type:"button",icon:m,onClick:function(e){var n=t.selection;if(n&&n.length){for(var o=_(t.list),r=n.length-1;r>=0;r--){var a=p.findIndexInList(n[r],o,t.dataKey);if(a===o.length-1)break;var i=o[a+1];o[a+1]=o[a],o[a]=i}t.onReorder&&t.onReorder({originalEvent:e,value:o,direction:"down"})}},pt:r("moveDownButton"),unstyled:i,"aria-label":n("moveDown"),__parentMetadata:{parent:t.metaData}}),e.createElement(v,{disabled:f,type:"button",icon:d,onClick:function(e){var n=t.selection;if(n&&n.length){for(var o=_(t.list),r=n.length-1;r>=0;r--){var a=p.findIndexInList(n[r],o,t.dataKey);if(a===o.length-1)break;var i=o.splice(a,1)[0];o.push(i)}t.onReorder&&t.onReorder({originalEvent:e,value:o,direction:"bottom"})}},pt:r("moveBottomButton"),unstyled:i,"aria-label":n("moveBottom"),__parentMetadata:{parent:t.metaData}}))}));U.displayName="PickListControls";var W=e.memo((function(t){var n=l(),o=t.ptm,r=t.cx,a=t.template?t.template(t.value):t.value,i=n({className:u(t.className,r("item",{selected:t.selected,focused:t.focused})),id:t.id,onClick:function(e){t.onClick&&t.onClick({originalEvent:e,value:t.value,id:t.id})},onKeyDown:function(e){t.onKeyDown&&t.onKeyDown({originalEvent:e,value:t.value})},onFocus:function(e){t.onFocus&&t.onFocus(e)},onMouseDown:function(e){t.onMouseDown&&t.onMouseDown(e)},onMouseMove:t.onMouseMove,role:"option","aria-selected":t.selected,"data-p-highlight":t.selected,"data-p-focused":t.focused},o("item",{hostName:t.hostName,context:{selected:t.selected}}));return e.createElement("li",i,a,e.createElement(b,null))}));function V(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function J(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?V(Object(n),!0).forEach((function(t){L(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):V(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}W.displayName="PickListItem";var Q=e.memo(e.forwardRef((function(t,n){var o=l(),r=e.useRef(null),a=t.ptm,i=t.cx,c=function(e,n){return a(e,J({hostName:t.hostName},n))},s=function(e){return-1!==p.findIndexInList(e,t.selection,t.dataKey)},d=function(e){t.onFilter&&t.onFilter({originalEvent:e,value:e.target.value,type:t.type})},f=function(e){13===e.which&&e.preventDefault()};e.useImperativeHandle(n,(function(){return{getElement:function(){return r.current}}}));var v,g,h,y=function(e){var n;t.focusOnHover&&t.focusedList[t.type]&&(null==t||null===(n=t.changeFocusedOptionIndex)||void 0===n||n.call(t,e,t.type))},T=(v=o({className:i("header")},c("header")),t.header?e.createElement("div",v,p.getJSXElement(t.header,t)):null),b=function(){var n=o({className:i("filterIcon")},c("filterIcon")),r=m.getJSXIcon("source"===t.type?t.sourceFilterIcon||e.createElement(S,n):t.targetFilterIcon||e.createElement(S,n),J({},n),{props:t});if(t.showFilter){var a=o({className:i("filter")},c("filter")),l=o({type:"text",value:t.filterValue,onChange:d,onKeyDown:f,placeholder:t.placeholder,className:i("filterInput")},c("filterInput")),s=e.createElement("div",a,e.createElement("input",l),e.createElement("span",null," ",r," "));if(t.filterTemplate)s=p.getJSXElement(t.filterTemplate,{className:"p-picklist-filter",inputProps:{className:"p-picklist-filter-input p-inputtext p-component",onChange:d,onKeyDown:f},iconClassName:"p-picklist-filter-icon",element:s,props:t});var u=o({className:i("filterContainer")},c("filterContainer"));return e.createElement("div",u,s)}return null}(),I=(g=t.list?t.list.map((function(n,o){var r=t.parentId+"_"+t.type+"_"+o,l=s(n);return e.createElement(W,{hostName:t.hostName,key:r,id:r,index:o,focused:r===t.focusedOptionId,value:n,template:t.itemTemplate,selected:l,onClick:t.onItemClick,onKeyDown:t.onItemKeyDown,onMouseDown:function(e){return t.onOptionMouseDown(J(J({},e),{},{index:o,type:t.type}))},onMouseMove:function(){return y(o)},ptm:a,cx:i})})):null,h=o({ref:r,className:u(t.listClassName,i("list")),role:"listbox",id:t.parentId+"_"+t.type+"_list","aria-multiselectable":!0,"aria-activedescendant":t.ariaActivedescendant,tabIndex:t.list&&t.list.length>0?t.tabIndex:-1,onKeyDown:t.onListKeyDown,onFocus:function(e){t.onListFocus(e,t.type)},onBlur:t.onListBlur,style:t.style},c("list")),e.createElement("ul",h,g)),E=o({className:u(t.className,i("listWrapper"))},c("listWrapper"));return e.createElement("div",E,T,b,I)})));function Y(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,O(o.key),o)}}function q(e,t,n){return t&&Y(e.prototype,t),n&&Y(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function G(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Q.displayName="PickListSubList";var X=Object.freeze({STARTS_WITH:"startsWith",CONTAINS:"contains",NOT_CONTAINS:"notContains",ENDS_WITH:"endsWith",EQUALS:"equals",NOT_EQUALS:"notEquals",IN:"in",LESS_THAN:"lt",LESS_THAN_OR_EQUAL_TO:"lte",GREATER_THAN:"gt",GREATER_THAN_OR_EQUAL_TO:"gte",BETWEEN:"between",DATE_IS:"dateIs",DATE_IS_NOT:"dateIsNot",DATE_BEFORE:"dateBefore",DATE_AFTER:"dateAfter",CUSTOM:"custom"}),z=q((function e(){G(this,e)}));L(z,"ripple",!1),L(z,"inputStyle","outlined"),L(z,"locale","en"),L(z,"appendTo",null),L(z,"cssTransition",!0),L(z,"autoZIndex",!0),L(z,"hideOverlaysOnDocumentScrolling",!1),L(z,"nonce",null),L(z,"nullSortOrder",1),L(z,"zIndex",{modal:1100,overlay:1e3,menu:1e3,tooltip:1100,toast:1200}),L(z,"pt",void 0),L(z,"filterMatchModeOptions",{text:[X.STARTS_WITH,X.CONTAINS,X.NOT_CONTAINS,X.ENDS_WITH,X.EQUALS,X.NOT_EQUALS],numeric:[X.EQUALS,X.NOT_EQUALS,X.LESS_THAN,X.LESS_THAN_OR_EQUAL_TO,X.GREATER_THAN,X.GREATER_THAN_OR_EQUAL_TO],date:[X.DATE_IS,X.DATE_IS_NOT,X.DATE_BEFORE,X.DATE_AFTER]}),L(z,"changeTheme",(function(e,t,n,o){var r,a=document.getElementById(n);if(!a)throw Error("Element with id ".concat(n," not found."));var i=a.getAttribute("href").replace(e,t),l=document.createElement("link");l.setAttribute("rel","stylesheet"),l.setAttribute("id",n),l.setAttribute("href",i),l.addEventListener("load",(function(){o&&o()})),null===(r=a.parentNode)||void 0===r||r.replaceChild(l,a)}));var Z={en:{accept:"Yes",addRule:"Add Rule",am:"AM",apply:"Apply",cancel:"Cancel",choose:"Choose",chooseDate:"Choose Date",chooseMonth:"Choose Month",chooseYear:"Choose Year",clear:"Clear",completed:"Completed",contains:"Contains",custom:"Custom",dateAfter:"Date is after",dateBefore:"Date is before",dateFormat:"mm/dd/yy",dateIs:"Date is",dateIsNot:"Date is not",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesMin:["Su","Mo","Tu","We","Th","Fr","Sa"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],emptyFilterMessage:"No results found",emptyMessage:"No available options",emptySearchMessage:"No results found",emptySelectionMessage:"No selected item",endsWith:"Ends with",equals:"Equals",fileSizeTypes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"],filter:"Filter",firstDayOfWeek:0,gt:"Greater than",gte:"Greater than or equal to",lt:"Less than",lte:"Less than or equal to",matchAll:"Match All",matchAny:"Match Any",medium:"Medium",monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],nextDecade:"Next Decade",nextHour:"Next Hour",nextMinute:"Next Minute",nextMonth:"Next Month",nextSecond:"Next Second",nextYear:"Next Year",noFilter:"No Filter",notContains:"Not contains",notEquals:"Not equals",now:"Now",passwordPrompt:"Enter a password",pending:"Pending",pm:"PM",prevDecade:"Previous Decade",prevHour:"Previous Hour",prevMinute:"Previous Minute",prevMonth:"Previous Month",prevSecond:"Previous Second",prevYear:"Previous Year",reject:"No",removeRule:"Remove Rule",searchMessage:"{0} results are available",selectionMessage:"{0} items selected",showMonthAfterYear:!1,startsWith:"Starts with",strong:"Strong",today:"Today",upload:"Upload",weak:"Weak",weekHeader:"Wk",aria:{cancelEdit:"Cancel Edit",close:"Close",collapseRow:"Row Collapsed",editRow:"Edit Row",expandRow:"Row Expanded",falseLabel:"False",filterConstraint:"Filter Constraint",filterOperator:"Filter Operator",firstPageLabel:"First Page",gridView:"Grid View",hideFilterMenu:"Hide Filter Menu",jumpToPageDropdownLabel:"Jump to Page Dropdown",jumpToPageInputLabel:"Jump to Page Input",lastPageLabel:"Last Page",listLabel:"Option List",listView:"List View",moveAllToSource:"Move All to Source",moveAllToTarget:"Move All to Target",moveBottom:"Move Bottom",moveDown:"Move Down",moveToSource:"Move to Source",moveToTarget:"Move to Target",moveTop:"Move Top",moveUp:"Move Up",navigation:"Navigation",next:"Next",nextPageLabel:"Next Page",nullLabel:"Not Selected",pageLabel:"Page {page}",otpLabel:"Please enter one time password character {0}",passwordHide:"Hide Password",passwordShow:"Show Password",previous:"Previous",prevPageLabel:"Previous Page",rotateLeft:"Rotate Left",rotateRight:"Rotate Right",rowsPerPageLabel:"Rows per page",saveEdit:"Save Edit",scrollTop:"Scroll Top",selectAll:"All items selected",selectRow:"Row Selected",showFilterMenu:"Show Filter Menu",slide:"Slide",slideNumber:"{slideNumber}",star:"1 star",stars:"{star} stars",trueLabel:"True",unselectAll:"All items unselected",unselectRow:"Row Unselected",zoomImage:"Zoom Image",zoomIn:"Zoom In",zoomOut:"Zoom Out"}}};function $(e,t){if(e.includes("__proto__")||e.includes("prototype"))throw new Error("Unsafe ariaKey detected");var n=z.locale;try{var o=ee(n).aria[e];if(o)for(var r in t)t.hasOwnProperty(r)&&(o=o.replace("{".concat(r,"}"),t[r]));return o}catch(t){throw new Error("The ".concat(e," option is not found in the current locale('").concat(n,"')."))}}function ee(e){var t=e||z.locale;if(t.includes("__proto__")||t.includes("prototype"))throw new Error("Unsafe locale detected");return Z[t]}var te=e.memo((function(t){var n=l(),o=c("(max-width: ".concat(t.breakpoint,")"),t.breakpoint),r=t.ptm,a=t.cx,i=t.unstyled;function s(n){switch(n){case"moveToTargetIcon":return t.moveToTargetIcon||o?t.moveToTargetIcon||e.createElement(y,null):t.moveToTargetIcon||e.createElement(N,null);case"moveAllToTargetIcon":return t.moveAllToTargetIcon||o?t.moveAllToTargetIcon||e.createElement(g,null):t.moveAllToTargetIcon||e.createElement(E,null);case"moveToSourceIcon":return t.moveToSourceIcon||o?t.moveToSourceIcon||e.createElement(T,null):t.moveToSourceIcon||e.createElement(w,null);case"moveAllToSourceIcon":return t.moveAllToSourceIcon||o?t.moveAllToSourceIcon||e.createElement(h,null):t.moveAllToSourceIcon||e.createElement(I,null);default:return null}}var d=m.getJSXIcon(s("moveToTargetIcon"),void 0,{props:t,viewChanged:o}),f=m.getJSXIcon(s("moveAllToTargetIcon"),void 0,{props:t,viewChanged:o}),S=m.getJSXIcon(s("moveToSourceIcon"),void 0,{props:t,viewChanged:o}),b=m.getJSXIcon(s("moveAllToSourceIcon"),void 0,{props:t,viewChanged:o}),A=p.isEmpty(t.sourceSelection)||p.isEmpty(t.visibleSourceList),k=p.isEmpty(t.targetSelection)||p.isEmpty(t.visibleTargetList),C=p.isEmpty(t.visibleSourceList),O=p.isEmpty(t.visibleTargetList),L=n({className:u(t.className,a("buttons"))},r("buttons",{hostName:t.hostName}));return e.createElement("div",L,e.createElement(v,{disabled:A,type:"button",icon:d,onClick:function(e){var n=t.sourceSelection;if(p.isNotEmpty(n)){for(var o=_(t.target),r=_(t.source),a=0;a<n.length;a++){var i=n[a];-1===p.findIndexInList(i,o,t.dataKey)&&o.push(r.splice(p.findIndexInList(i,r,t.dataKey),1)[0])}t.onTransfer&&t.onTransfer({originalEvent:e,source:r,target:o,direction:"toTarget"})}},pt:r("moveToTargetButton"),unstyled:i,"aria-label":$("moveToTarget"),__parentMetadata:{parent:t.metaData}}),e.createElement(v,{disabled:C,type:"button",icon:f,onClick:function(e){if(t.source){var n=[].concat(_(t.target),_(t.visibleSourceList)),o=t.source.filter((function(e){return!t.visibleSourceList.some((function(t){return t===e}))}));t.onTransfer&&t.onTransfer({originalEvent:e,source:o,target:n,direction:"allToTarget"})}},pt:r("moveAllToTargetButton"),unstyled:i,"aria-label":$("moveAllToTarget"),__parentMetadata:{parent:t.metaData}}),e.createElement(v,{disabled:k,type:"button",icon:S,onClick:function(e){var n=t.targetSelection;if(p.isNotEmpty(n)){for(var o=_(t.target),r=_(t.source),a=0;a<n.length;a++){var i=n[a];-1===p.findIndexInList(i,r,t.dataKey)&&r.push(o.splice(p.findIndexInList(i,o,t.dataKey),1)[0])}t.onTransfer&&t.onTransfer({originalEvent:e,source:r,target:o,direction:"toSource"})}},pt:r("moveToSourceButton"),unstyled:i,"aria-label":$("moveToSource"),__parentMetadata:{parent:t.metaData}}),e.createElement(v,{disabled:O,type:"button",icon:b,onClick:function(e){if(t.source){var n=[].concat(_(t.source),_(t.visibleTargetList)),o=t.target.filter((function(e){return!t.visibleTargetList.some((function(t){return t===e}))}));t.onTransfer&&t.onTransfer({originalEvent:e,source:n,target:o,direction:"allToSource"})}},pt:r("moveAllToSourceButton"),unstyled:i,"aria-label":$("moveAllToSource"),__parentMetadata:{parent:t.metaData}}))}));function ne(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function oe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ne(Object(n),!0).forEach((function(t){L(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ne(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}te.displayName="PickListTransferControls";var re=e.memo(e.forwardRef((function(n,a){var c=l(),m=e.useContext(o),v=H.getProps(n,m),g=j(e.useState([]),2),h=g[0],y=g[1],T=j(e.useState([]),2),S=T[0],b=T[1],I=j(e.useState(""),2),E=I[0],w=I[1],N=j(e.useState(""),2),k=N[0],C=N[1],O=j(e.useState(v.id),2),M=O[0],D=O[1],x=j(e.useState(-1),2),F=x[0],P=x[1],R=j(e.useState(null),2),K=R[0],B=R[1],W=j(e.useState({source:!1,target:!1}),2),V=W[0],J=W[1],Y={props:v,state:{sourceSelection:h,targetSelection:S,sourceFilterValue:E,targetFilterValue:k,attributeSelector:M}},q=H.setMetaData(Y),G=q.ptm,X=q.cx;i(H.css.styles,q.isUnstyled,{name:"picklist"});var z=e.useRef(null),Z=e.useRef(null),$=e.useRef(null),ee=e.useRef(null),ne=e.useRef(null),re=e.useRef(null),ae=v.sourceSelection?v.sourceSelection:h,ie=v.targetSelection?v.targetSelection:S,le=v.onSourceFilterChange?v.sourceFilterValue:E,ce=v.onTargetFilterChange?v.targetFilterValue:k,se=p.isNotEmpty(v.filterBy),ue=se&&v.showSourceFilter,pe=se&&v.showTargetFilter,me=function(e,t){if(e)switch(t){case"up":fe(e,-1);break;case"top":e.scrollTop=0;break;case"down":fe(e,1);break;case"bottom":setTimeout((function(){return e.scrollTop=e.scrollHeight}),100)}},de=function(e,t,n){v.onChange&&v.onChange({originalEvent:e.originalEvent,source:t,target:n})},fe=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=e.getElementsByClassName("p-highlight");p.isNotEmpty(n)&&f.scrollInView(e,-1===t?n[0]:n[n.length-1])},ve=function(e,t,n){"sourceSelection"===t?y(e.value):b(e.value),n&&n(e)},ge=function(e){var t=e.originalEvent,n=e.value,o=j("source"===e.type?[w,v.onSourceFilterChange]:[C,v.onTargetFilterChange],2),r=o[0],a=o[1];a?a({originalEvent:t,value:n}):r(n)},he=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").trim().toLocaleLowerCase(v.filterLocale);return Te(v.source,e)},ye=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").trim().toLocaleLowerCase(v.filterLocale);return Te(v.target,e)},Te=function(e,t){var n=se?v.filterBy.split(","):[];return r.filter(e,n,t,v.filterMatchMode,v.filterLocale)},Se=function(e,t){var n=j("source"===t?[le,he]:[ce,ye],2),o=n[0],r=n[1];return se&&p.isNotEmpty(o)?r(o):e},be=Se(v.source,"source"),Ie=Se(v.target,"target"),Ee=function(e){if(-1===F){var t=e&&e.children?_(e.children):[],n=we(e,t);return v.autoOptionFocus&&-1===n&&(n=Ne(e,t)),n}return-1},we=function(e,t){if(h.length||S.length){var n=f.findSingle(e,'[data-p-highlight="true"]');return p.findIndexInList(n,t)}return-1},Ne=function(e,t){var n=f.findSingle(e,'[data-pc-section="item"]');return p.findIndexInList(n,t)},Ae=function(e,t){J(oe(oe({},V),{},L({},t,!0)));var n=He(t),o=Ee(n);Be(o,t),v.onFocus&&v.onFocus(e)},ke=function(e,t){J(oe(oe({},V),{},L({},t,!1))),P(-1),v.onBlur&&v.onBlur(e)},Ce=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=e.originalEvent,r=e.value,a=e.id,i="source"===t,l=_(i?ae:ie),c=p.findIndexInList(r,l,v.dataKey),s=-1!==c,u=v.metaKeySelection;if(n||P(a),u){var m=o.metaKey||o.ctrlKey||o.shiftKey;s&&m?l.splice(c,1):(m||(l.length=0),l.push(r))}else s?l.splice(c,1):l.push(r);i?ve({originalEvent:o,value:l},"sourceSelection",v.onSourceSelectionChange):ve({originalEvent:o,value:l},"targetSelection",v.onTargetSelectionChange)},Oe=function(e){var t=e.index,n=e.type;J(oe(oe({},V),{},L({},n,!0))),P(t)},Le=function(e,t){switch(e.code){case"ArrowDown":Me(e,t);break;case"ArrowUp":De(e,t);break;case"Home":Pe(e,t);break;case"End":_e(e,t);break;case"Enter":case"NumpadEnter":xe(e,t);break;case"Space":Fe(e,t);break;case"KeyA":if(e.ctrlKey){var n="source"===t;n?y(_(be)):b(_(Ie)),ve({originalEvent:e,value:_(n?be:Ie)},n?"sourceSelection":"targetSelection",n?v.onSourceSelectionChange:v.onTargetSelectionChange),e.preventDefault()}break;case"KeyD":if(e.ctrlKey){var o="source"===t;o?y([]):b([]),ve({originalEvent:e,value:[]},o?"sourceSelection":"targetSelection",o?v.onSourceSelectionChange:v.onTargetSelectionChange),e.preventDefault()}}},Me=function(e,t){var n=Re(F,t),o=Se("source"===t?v.source:v.target,t);Be(n,t),o&&o.length>0&&e.shiftKey&&Ce({originalEvent:e,value:o[n]},t,!0),e.preventDefault()},De=function(e,t){var n=Ke(F,t),o=Se("source"===t?v.source:v.target,t);Be(n,t),o&&o.length>0&&e.shiftKey&&Ce({originalEvent:e,value:o[n]},t,!0),e.preventDefault()},xe=function(e,t){var n=He(t),o=Se("source"===t?v.source:v.target,t),r=f.find(n,'[data-pc-section="item"]'),a=f.findSingle(n,'[data-pc-section="item"][id='.concat(F,"]")),i=a&&a.getAttribute("id"),l=_(r).findIndex((function(e){return e===a}));o&&o.length>0&&Ce({originalEvent:e,value:o[l],id:i},t),e.preventDefault()},Fe=function(e,t){e.preventDefault();var n="source"===t,o=n?h:S;if(e.shiftKey&&o&&o.length>0){var r=n?be:Ie,a=He(t),i=f.find(a,'[data-pc-section="item"]'),l=p.findIndexInList(o[0],_(r)),c=f.findSingle(a,'[data-pc-section="item"][id='.concat(F,"]")),s=_(i).findIndex((function(e){return e===c}));_(r).slice(Math.min(l,s),Math.max(l,s)+1),A("selection"),n?ve({originalEvent:e,value:o},"sourceSelection",v.onSourceSelectionChange):ve({originalEvent:e,value:o},"targetSelection",v.onTargetSelectionChange)}else xe(e,t)},Pe=function(e,t){if(e.ctrlKey&&e.shiftKey){var n="source"===t,o=n?be:Ie,r=He(t),a=f.find(r,'[data-pc-section="item"]'),i=f.findSingle(r,'[data-pc-section="item"][id='.concat(F,"]")),l=_(a).findIndex((function(e){return e===i})),c=_(o).slice(0,l+1);n?ve({originalEvent:e,value:c},"sourceSelection",v.onSourceSelectionChange):ve({originalEvent:e,value:c},"targetSelection",v.onTargetSelectionChange)}else Be(0,t);e.preventDefault()},_e=function(e,t){var n=He(t),o=f.find(n,'[data-pc-section="item"]');if(e.ctrlKey&&e.shiftKey){var r="source"===t,a=r?be:Ie,i=f.findSingle(n,'[data-pc-section="item"][id='.concat(F,"]")),l=_(o).findIndex((function(e){return e===i})),c=_(a).slice(l,o.length);r?ve({originalEvent:e,value:c},"sourceSelection",v.onSourceSelectionChange):ve({originalEvent:e,value:c},"targetSelection",v.onTargetSelectionChange)}else Be(o.length-1,t);e.preventDefault()},Re=function(e,t){var n=He(t),o=_(f.find(n,'[data-pc-section="item"]')).findIndex((function(t){return t.id===e}));return o>-1?o+1:0},Ke=function(e,t){var n=He(t),o=_(f.find(n,'[data-pc-section="item"]')).findIndex((function(t){return t.id===e}));return o>-1?o-1:0},Be=function(e,t){var n,o=He(t),r=f.find(o,'[data-pc-section="item"]');if(e>=r.length)n=r.length-1;else{if(e<0)return;n=e}P(r[n].getAttribute("id")),je(r[n].getAttribute("id"),t)},je=function(e,t){var n=He(t),o=f.findSingle(n,'[data-pc-section="item"][id="'.concat(e,'"]'));o&&o.scrollIntoView&&o.scrollIntoView({block:"nearest",inline:"start"})},He=function(e){return"source"===e?Z.current.getElement():$.current.getElement()},Ue=function(){if(!re.current){re.current=f.createInlineStyle(m&&m.nonce||t.nonce,m&&m.styleContainer);var e="\n@media screen and (max-width: ".concat(v.breakpoint,") {\n    .p-picklist[").concat(M,"] {\n        flex-direction: column;\n    }\n\n    .p-picklist[").concat(M,"] .p-picklist-buttons {\n        padding: var(--content-padding);\n        flex-direction: row;\n    }\n\n    .p-picklist[").concat(M,"] .p-picklist-buttons .p-button {\n        margin-right: var(--inline-spacing);\n        margin-bottom: 0;\n    }\n\n    .p-picklist[").concat(M,"] .p-picklist-buttons .p-button:last-child {\n        margin-right: 0;\n    }\n}\n");re.current.innerHTML=e}};e.useImperativeHandle(a,(function(){return{props:v,getElement:function(){return z.current}}})),s((function(){return M&&(z.current.setAttribute(M,""),Ue()),function(){re.current=f.removeInlineStyle(re.current)}}),[M,v.breakpoint]),s((function(){v.id||M||D(d()),ee.current&&(me(ee.current,ne.current),ee.current=null,ne.current=null)})),s((function(){B(-1!==F?F:null)}),[F]);var We=v.sourceItemTemplate?v.sourceItemTemplate:v.itemTemplate,Ve=v.targetItemTemplate?v.targetItemTemplate:v.itemTemplate,Je=c({id:M,ref:z,className:u(v.className,X("root")),style:v.style},H.getOtherProps(v),G("root"));return e.createElement("div",Je,v.showSourceControls&&e.createElement(U,{hostName:"PickList",list:v.source,selection:ae,onReorder:function(e){de(e,e.value,v.target),ee.current=He("source"),ne.current=e.direction},className:X("sourceControls"),dataKey:v.dataKey,moveUpIcon:v.moveUpIcon,moveTopIcon:v.moveTopIcon,moveDownIcon:v.moveDownIcon,moveBottomIcon:v.moveBottomIcon,ptm:G,cx:X,unstyled:v.unstyled,metaData:Y}),e.createElement(Q,{hostName:"PickList",ref:Z,type:"source",list:be,parentId:M,selection:ae,onSelectionChange:function(e){return ve(e,"sourceSelection",v.onSourceSelectionChange)},onListKeyDown:function(e){return Le(e,"source")},onListFocus:function(e){return Ae(e,"source")},onListBlur:function(e){return ke(e,"source")},onOptionMouseDown:function(e){return Oe(e)},onItemClick:function(e){return Ce(e,"source")},focusedOptionId:V.source?K:null,ariaActivedescendant:V.source?K:null,itemTemplate:We,header:v.sourceHeader,style:v.sourceStyle,className:X("listSourceWrapper"),listClassName:X("listSource"),metaKeySelection:v.metaKeySelection,tabIndex:v.tabIndex,dataKey:v.dataKey,filterValue:le,onFilter:ge,showFilter:ue,placeholder:v.sourceFilterPlaceholder,filterTemplate:v.sourceFilterTemplate,sourceFilterIcon:v.sourceFilterIcon,ptm:G,cx:X,focusedList:V,changeFocusedOptionIndex:Be,focusOnHover:v.focusOnHover}),e.createElement(te,{hostName:"PickList",onTransfer:function(e){var t=e.originalEvent,n=e.source,o=e.target,r=[];switch(e.direction){case"toTarget":r=ae,v.onMoveToTarget&&v.onMoveToTarget({originalEvent:t,value:r});break;case"allToTarget":r=v.source,v.onMoveAllToTarget&&v.onMoveAllToTarget({originalEvent:t,value:r}),r=[];break;case"toSource":r=ie,v.onMoveToSource&&v.onMoveToSource({originalEvent:t,value:r});break;case"allToSource":r=v.target,v.onMoveAllToSource&&v.onMoveAllToSource({originalEvent:t,value:r}),r=[]}ve({originalEvent:t,value:r},"sourceSelection",v.onSourceSelectionChange),ve({originalEvent:t,value:r},"targetSelection",v.onTargetSelectionChange),b([]),y([]),de(e,n,o)},source:v.source,visibleSourceList:be,target:v.target,breakpoint:v.breakpoint,visibleTargetList:Ie,sourceSelection:ae,targetSelection:ie,dataKey:v.dataKey,moveToTargetIcon:v.moveToTargetIcon,moveAllToTargetIcon:v.moveAllToTargetIcon,moveToSourceIcon:v.moveToSourceIcon,moveAllToSourceIcon:v.moveAllToSourceIcon,ptm:G,cx:X,unstyled:v.unstyled,metaData:Y}),e.createElement(Q,{hostName:"PickList",ref:$,type:"target",list:Ie,selection:ie,parentId:M,onSelectionChange:function(e){return ve(e,"targetSelection",v.onTargetSelectionChange)},onListKeyDown:function(e){return Le(e,"target")},onListFocus:function(e){return Ae(e,"target")},onListBlur:function(e){return ke(e,"target")},onOptionMouseDown:function(e){return Oe(e)},onItemClick:function(e){return Ce(e,"target")},focusedOptionId:V.target?K:null,ariaActivedescendant:V.target?K:null,itemTemplate:Ve,header:v.targetHeader,style:v.targetStyle,className:X("listTargetWrapper"),listClassName:X("listWrapper"),metaKeySelection:v.metaKeySelection,tabIndex:v.tabIndex,dataKey:v.dataKey,filterValue:ce,onFilter:ge,showFilter:pe,placeholder:v.targetFilterPlaceholder,filterTemplate:v.targetFilterTemplate,targetFilterIcon:v.targetFilterIcon,ptm:G,cx:X,focusedList:V,changeFocusedOptionIndex:Be,focusOnHover:v.focusOnHover}),v.showTargetControls&&e.createElement(U,{hostName:"PickList",list:v.target,selection:ie,onReorder:function(e){de(e,v.source,e.value),ee.current=He("target"),ne.current=e.direction},className:X("targetControls"),dataKey:v.dataKey,moveUpIcon:v.moveUpIcon,moveTopIcon:v.moveTopIcon,moveDownIcon:v.moveDownIcon,moveBottomIcon:v.moveBottomIcon,ptm:G,cx:X,unstyled:v.unstyled,metaData:Y}))})));re.displayName="PickList";export{re as PickList};
