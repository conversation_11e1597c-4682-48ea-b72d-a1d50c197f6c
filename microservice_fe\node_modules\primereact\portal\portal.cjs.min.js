"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("react-dom"),r=require("primereact/api"),n=require("primereact/hooks"),o=require("primereact/utils");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function l(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=l(e),a=u(t),c=u(r);function f(e){if(Array.isArray(e))return e}function s(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,u,l,i=[],a=!0,c=!1;try{if(u=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;a=!1}else for(;!(a=(n=u.call(r)).done)&&(i.push(n.value),i.length!==t);a=!0);}catch(e){c=!0,o=e}finally{try{if(!a&&null!=r.return&&(l=r.return(),Object(l)!==l))return}finally{if(c)throw o}}return i}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function p(e,t){if(e){if("string"==typeof e)return d(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?d(e,t):void 0}}function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var b={defaultProps:{__TYPE:"Portal",element:null,appendTo:null,visible:!1,onMounted:null,onUnmounted:null,children:void 0},getProps:function(e){return o.ObjectUtils.getMergedProps(e,b.defaultProps)},getOtherProps:function(e){return o.ObjectUtils.getDiffProps(e,b.defaultProps)}},y=i.memo((function(e){var t,u,l=b.getProps(e),d=i.useContext(r.PrimeReactContext),y=i.useState(l.visible&&o.DomHandler.isClient()),v=(u=2,f(t=y)||s(t,u)||p(t,u)||m()),P=v[0],g=v[1];n.useMountEffect((function(){o.DomHandler.isClient()&&!P&&(g(!0),l.onMounted&&l.onMounted())})),n.useUpdateEffect((function(){l.onMounted&&l.onMounted()}),[P]),n.useUnmountEffect((function(){l.onUnmounted&&l.onUnmounted()}));var h=l.element||l.children;if(h&&P){var j=l.appendTo||d&&d.appendTo||c.default.appendTo;return o.ObjectUtils.isFunction(j)&&(j=j()),j||(j=document.body),"self"===j?h:a.default.createPortal(h,j)}return null}));y.displayName="Portal",exports.Portal=y;
