import*as t from"react";import r from"react-dom";import e,{PrimeReactContext as n}from"primereact/api";import{useMountEffect as o,useUpdateEffect as i,useUnmountEffect as l}from"primereact/hooks";import{ObjectUtils as u,<PERSON><PERSON><PERSON><PERSON> as a}from"primereact/utils";function f(t){if(Array.isArray(t))return t}function c(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,l,u=[],a=!0,f=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;a=!1}else for(;!(a=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);a=!0);}catch(t){f=!0,o=t}finally{try{if(!a&&null!=e.return&&(l=e.return(),Object(l)!==l))return}finally{if(f)throw o}}return u}}function p(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function m(t,r){if(t){if("string"==typeof t)return p(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?p(t,r):void 0}}function s(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var d={defaultProps:{__TYPE:"Portal",element:null,appendTo:null,visible:!1,onMounted:null,onUnmounted:null,children:void 0},getProps:function(t){return u.getMergedProps(t,d.defaultProps)},getOtherProps:function(t){return u.getDiffProps(t,d.defaultProps)}},y=t.memo((function(p){var y,v,b=d.getProps(p),h=t.useContext(n),g=t.useState(b.visible&&a.isClient()),P=(v=2,f(y=g)||c(y,v)||m(y,v)||s()),M=P[0],A=P[1];o((function(){a.isClient()&&!M&&(A(!0),b.onMounted&&b.onMounted())})),i((function(){b.onMounted&&b.onMounted()}),[M]),l((function(){b.onUnmounted&&b.onUnmounted()}));var S=b.element||b.children;if(S&&M){var T=b.appendTo||h&&h.appendTo||e.appendTo;return u.isFunction(T)&&(T=T()),T||(T=document.body),"self"===T?S:r.createPortal(S,T)}return null}));y.displayName="Portal";export{y as Portal};
