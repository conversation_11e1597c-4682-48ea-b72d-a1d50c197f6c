this.primereact = this.primereact || {};
this.primereact.portal = (function (exports, React, ReactDOM, PrimeReact, hooks, utils) {
  'use strict';

  function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

  function _interopNamespace(e) {
    if (e && e.__esModule) return e;
    var n = Object.create(null);
    if (e) {
      Object.keys(e).forEach(function (k) {
        if (k !== 'default') {
          var d = Object.getOwnPropertyDescriptor(e, k);
          Object.defineProperty(n, k, d.get ? d : {
            enumerable: true,
            get: function () { return e[k]; }
          });
        }
      });
    }
    n["default"] = e;
    return Object.freeze(n);
  }

  var React__namespace = /*#__PURE__*/_interopNamespace(React);
  var ReactDOM__default = /*#__PURE__*/_interopDefaultLegacy(ReactDOM);
  var PrimeReact__default = /*#__PURE__*/_interopDefaultLegacy(PrimeReact);

  function _arrayWithHoles(r) {
    if (Array.isArray(r)) return r;
  }

  function _iterableToArrayLimit(r, l) {
    var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"];
    if (null != t) {
      var e,
        n,
        i,
        u,
        a = [],
        f = !0,
        o = !1;
      try {
        if (i = (t = t.call(r)).next, 0 === l) {
          if (Object(t) !== t) return;
          f = !1;
        } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);
      } catch (r) {
        o = !0, n = r;
      } finally {
        try {
          if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return;
        } finally {
          if (o) throw n;
        }
      }
      return a;
    }
  }

  function _arrayLikeToArray(r, a) {
    (null == a || a > r.length) && (a = r.length);
    for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];
    return n;
  }

  function _unsupportedIterableToArray(r, a) {
    if (r) {
      if ("string" == typeof r) return _arrayLikeToArray(r, a);
      var t = {}.toString.call(r).slice(8, -1);
      return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;
    }
  }

  function _nonIterableRest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }

  function _slicedToArray(r, e) {
    return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();
  }

  var PortalBase = {
    defaultProps: {
      __TYPE: 'Portal',
      element: null,
      appendTo: null,
      visible: false,
      onMounted: null,
      onUnmounted: null,
      children: undefined
    },
    getProps: function getProps(props) {
      return utils.ObjectUtils.getMergedProps(props, PortalBase.defaultProps);
    },
    getOtherProps: function getOtherProps(props) {
      return utils.ObjectUtils.getDiffProps(props, PortalBase.defaultProps);
    }
  };

  var Portal = /*#__PURE__*/React__namespace.memo(function (inProps) {
    var props = PortalBase.getProps(inProps);
    var context = React__namespace.useContext(PrimeReact.PrimeReactContext);
    var _React$useState = React__namespace.useState(props.visible && utils.DomHandler.isClient()),
      _React$useState2 = _slicedToArray(_React$useState, 2),
      mountedState = _React$useState2[0],
      setMountedState = _React$useState2[1];
    hooks.useMountEffect(function () {
      if (utils.DomHandler.isClient() && !mountedState) {
        setMountedState(true);
        props.onMounted && props.onMounted();
      }
    });
    hooks.useUpdateEffect(function () {
      props.onMounted && props.onMounted();
    }, [mountedState]);
    hooks.useUnmountEffect(function () {
      props.onUnmounted && props.onUnmounted();
    });
    var element = props.element || props.children;
    if (element && mountedState) {
      var appendTo = props.appendTo || context && context.appendTo || PrimeReact__default["default"].appendTo;
      if (utils.ObjectUtils.isFunction(appendTo)) {
        appendTo = appendTo();
      }
      if (!appendTo) {
        appendTo = document.body;
      }
      return appendTo === 'self' ? element : /*#__PURE__*/ReactDOM__default["default"].createPortal(element, appendTo);
    }
    return null;
  });
  Portal.displayName = 'Portal';

  exports.Portal = Portal;

  Object.defineProperty(exports, '__esModule', { value: true });

  return exports;

})({}, React, ReactDOM, primereact.api, primereact.hooks, primereact.utils);
