this.primereact=this.primereact||{},this.primereact.portal=function(e,t,r,n,o,u){"use strict";function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var a=i(t),c=l(r),f=l(n);function s(e){if(Array.isArray(e))return e}function d(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,u,l,i=[],a=!0,c=!1;try{if(u=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;a=!1}else for(;!(a=(n=u.call(r)).done)&&(i.push(n.value),i.length!==t);a=!0);}catch(e){c=!0,o=e}finally{try{if(!a&&null!=r.return&&(l=r.return(),Object(l)!==l))return}finally{if(c)throw o}}return i}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function m(e,t){if(e){if("string"==typeof e)return p(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(e,t):void 0}}function b(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var y={defaultProps:{__TYPE:"Portal",element:null,appendTo:null,visible:!1,onMounted:null,onUnmounted:null,children:void 0},getProps:function(e){return u.ObjectUtils.getMergedProps(e,y.defaultProps)},getOtherProps:function(e){return u.ObjectUtils.getDiffProps(e,y.defaultProps)}},v=a.memo((function(e){var t,r,l=y.getProps(e),i=a.useContext(n.PrimeReactContext),p=a.useState(l.visible&&u.DomHandler.isClient()),v=(r=2,s(t=p)||d(t,r)||m(t,r)||b()),h=v[0],P=v[1];o.useMountEffect((function(){u.DomHandler.isClient()&&!h&&(P(!0),l.onMounted&&l.onMounted())})),o.useUpdateEffect((function(){l.onMounted&&l.onMounted()}),[h]),o.useUnmountEffect((function(){l.onUnmounted&&l.onUnmounted()}));var g=l.element||l.children;if(g&&h){var O=l.appendTo||i&&i.appendTo||f.default.appendTo;return u.ObjectUtils.isFunction(O)&&(O=O()),O||(O=document.body),"self"===O?g:c.default.createPortal(g,O)}return null}));return v.displayName="Portal",e.Portal=v,Object.defineProperty(e,"__esModule",{value:!0}),e}({},React,ReactDOM,primereact.api,primereact.hooks,primereact.utils);
