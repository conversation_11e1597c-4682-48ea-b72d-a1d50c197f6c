{"name": "microservice_fe", "version": "0.1.0", "private": true, "dependencies": {"@types/node": "^16.18.126", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "axios": "^1.9.0", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primereact": "^10.9.5", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "react-scripts": "5.0.1", "typescript": "^4.9.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}