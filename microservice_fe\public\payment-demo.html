<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON> to<PERSON> hợp đồng kh<PERSON>ch hàng</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
  <style>
    body {
      font-family: 'Roboto', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #1976d2;
      color: white;
      padding: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .logo {
      font-size: 20px;
      font-weight: 500;
    }
    .nav-menu {
      display: flex;
      gap: 20px;
    }
    .nav-item {
      color: white;
      text-decoration: none;
      padding: 8px 12px;
      border-radius: 4px;
    }
    .nav-item:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
    .nav-item.active {
      background-color: rgba(255, 255, 255, 0.2);
    }
    .page-title {
      margin: 20px 0;
      font-size: 24px;
      font-weight: 500;
      color: #333;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      overflow: hidden;
    }
    .card-header {
      padding: 16px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
      font-weight: 500;
    }
    .card-content {
      padding: 16px;
    }
    .search-box {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }
    .search-input {
      flex: 1;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
    }
    .search-button {
      background-color: #1976d2;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 0 16px;
      font-size: 16px;
      cursor: pointer;
    }
    .search-button:hover {
      background-color: #1565c0;
    }
    .customer-table {
      width: 100%;
      border-collapse: collapse;
    }
    .customer-table th, .customer-table td {
      padding: 12px 16px;
      text-align: left;
      border-bottom: 1px solid #e0e0e0;
    }
    .customer-table th {
      background-color: #f5f5f5;
      font-weight: 500;
    }
    .customer-table tr:hover {
      background-color: #f9f9f9;
    }
    .action-button {
      background-color: #1976d2;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 16px;
      cursor: pointer;
      font-size: 14px;
    }
    .action-button:hover {
      background-color: #1565c0;
    }
    .contract-card {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      margin-bottom: 16px;
      overflow: hidden;
    }
    .contract-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
    }
    .contract-code {
      font-weight: 500;
    }
    .contract-status {
      background-color: #ff9800;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
    }
    .contract-details {
      padding: 16px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }
    .contract-detail-item {
      margin-bottom: 8px;
    }
    .detail-label {
      color: #666;
      font-size: 14px;
      margin-bottom: 4px;
    }
    .detail-value {
      font-weight: 500;
    }
    .payment-summary {
      background-color: #e3f2fd;
      padding: 12px 16px;
      border-radius: 4px;
      margin-top: 12px;
    }
    .payment-actions {
      padding: 12px 16px;
      border-top: 1px solid #e0e0e0;
      display: flex;
      justify-content: flex-end;
    }
    .payment-dialog {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }
    .payment-dialog-content {
      background-color: white;
      border-radius: 8px;
      width: 100%;
      max-width: 600px;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
    .payment-dialog-header {
      background-color: #1976d2;
      color: white;
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .payment-dialog-title {
      font-size: 18px;
      font-weight: 500;
    }
    .close-button {
      background: none;
      border: none;
      color: white;
      font-size: 24px;
      cursor: pointer;
    }
    .payment-dialog-body {
      padding: 20px;
    }
    .payment-form-group {
      margin-bottom: 16px;
    }
    .payment-label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }
    .payment-input {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
    }
    .payment-select {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
    }
    .payment-textarea {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
      min-height: 100px;
      resize: vertical;
    }
    .payment-dialog-footer {
      padding: 16px;
      border-top: 1px solid #e0e0e0;
      display: flex;
      justify-content: space-between;
    }
    .cancel-button {
      background-color: #f5f5f5;
      color: #333;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 8px 16px;
      cursor: pointer;
      font-size: 16px;
    }
    .confirm-button {
      background-color: #1976d2;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 16px;
      cursor: pointer;
      font-size: 16px;
    }
    .success-notification {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background-color: #4caf50;
      color: white;
      padding: 16px;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      z-index: 1000;
    }
    @media (max-width: 768px) {
      .contract-details {
        grid-template-columns: 1fr;
      }
      .search-box {
        flex-direction: column;
      }
      .customer-table {
        font-size: 14px;
      }
      .customer-table th, .customer-table td {
        padding: 8px;
      }
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo">Hệ Thống Quản Lý Nhân Công</div>
    <div class="nav-menu">
      <a href="#" class="nav-item">Quản lý đầu việc</a>
      <a href="#" class="nav-item active">Khách hàng</a>
      <a href="#" class="nav-item">Thống kê</a>
    </div>
  </div>

  <div class="container">
    <h1 class="page-title">Thanh toán hợp đồng khách hàng</h1>

    <div class="card">
      <div class="card-header">Danh sách khách hàng</div>
      <div class="card-content">
        <div class="search-box">
          <input type="text" class="search-input" placeholder="Tìm kiếm theo tên hoặc số điện thoại">
          <button class="search-button">Tìm kiếm</button>
        </div>

        <table class="customer-table">
          <thead>
            <tr>
              <th>Tên khách hàng</th>
              <th>Tên doanh nghiệp</th>
              <th>Số điện thoại</th>
              <th>Địa chỉ</th>
              <th>Thao tác</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Nguyễn Văn Huân</td>
              <td>-</td>
              <td>0887872745</td>
              <td>Hà Đông, Hà Nội</td>
              <td><button class="action-button">Thanh toán</button></td>
            </tr>
            <tr>
              <td>Nguyễn Thị Bích</td>
              <td>-</td>
              <td>0989823321</td>
              <td>Hà Nội</td>
              <td><button class="action-button">Thanh toán</button></td>
            </tr>
            <tr>
              <td>Trần Văn Bình</td>
              <td>Samsung</td>
              <td>0981173123</td>
              <td>Ninh Bình</td>
              <td><button class="action-button">Thanh toán</button></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="card">
      <div class="card-header">Hợp đồng của khách hàng: Nguyễn Văn Huân</div>
      <div class="card-content">
        <div class="contract-card">
          <div class="contract-header">
            <div class="contract-code">CC19</div>
            <div class="contract-status">Chờ xử lý</div>
          </div>
          <div class="contract-details">
            <div class="contract-detail-item">
              <div class="detail-label">Ngày bắt đầu</div>
              <div class="detail-value">30/04/2025</div>
            </div>
            <div class="contract-detail-item">
              <div class="detail-label">Ngày kết thúc</div>
              <div class="detail-value">30/05/2025</div>
            </div>
            <div class="contract-detail-item">
              <div class="detail-label">Tổng giá trị</div>
              <div class="detail-value">2.000.000 đ</div>
            </div>
            <div class="contract-detail-item">
              <div class="detail-label">Đã thanh toán</div>
              <div class="detail-value">0 đ</div>
            </div>
            <div class="payment-summary">
              <div class="detail-label">Còn lại cần thanh toán</div>
              <div class="detail-value">2.000.000 đ</div>
            </div>
          </div>
          <div class="payment-actions">
            <button class="action-button">Thanh toán</button>
          </div>
        </div>

        <div class="contract-card">
          <div class="contract-header">
            <div class="contract-code">CC18</div>
            <div class="contract-status">Chờ xử lý</div>
          </div>
          <div class="contract-details">
            <div class="contract-detail-item">
              <div class="detail-label">Ngày bắt đầu</div>
              <div class="detail-value">25/04/2025</div>
            </div>
            <div class="contract-detail-item">
              <div class="detail-label">Ngày kết thúc</div>
              <div class="detail-value">22/05/2025</div>
            </div>
            <div class="contract-detail-item">
              <div class="detail-label">Tổng giá trị</div>
              <div class="detail-value">1.000.000 đ</div>
            </div>
            <div class="contract-detail-item">
              <div class="detail-label">Đã thanh toán</div>
              <div class="detail-value">0 đ</div>
            </div>
            <div class="payment-summary">
              <div class="detail-label">Còn lại cần thanh toán</div>
              <div class="detail-value">1.000.000 đ</div>
            </div>
          </div>
          <div class="payment-actions">
            <button class="action-button">Thanh toán</button>
          </div>
        </div>
      </div>
    </div>

    <div class="payment-dialog">
      <div class="payment-dialog-content">
        <div class="payment-dialog-header">
          <div class="payment-dialog-title">Thanh toán hợp đồng</div>
          <button class="close-button">&times;</button>
        </div>
        <div class="payment-dialog-body">
          <div class="card">
            <div class="card-header">Thông tin hợp đồng</div>
            <div class="card-content">
              <div class="contract-details">
                <div class="contract-detail-item">
                  <div class="detail-label">Mã hợp đồng</div>
                  <div class="detail-value">CC19</div>
                </div>
                <div class="contract-detail-item">
                  <div class="detail-label">Khách hàng</div>
                  <div class="detail-value">Nguyễn Văn Huân</div>
                </div>
                <div class="contract-detail-item">
                  <div class="detail-label">Tổng giá trị hợp đồng</div>
                  <div class="detail-value">2.000.000 đ</div>
                </div>
                <div class="contract-detail-item">
                  <div class="detail-label">Đã thanh toán</div>
                  <div class="detail-value">0 đ</div>
                </div>
                <div class="contract-detail-item">
                  <div class="detail-label">Còn lại</div>
                  <div class="detail-value">2.000.000 đ</div>
                </div>
              </div>
            </div>
          </div>

          <div class="card" style="margin-top: 16px;">
            <div class="card-header">Thông tin thanh toán</div>
            <div class="card-content">
              <div class="payment-form-group">
                <label class="payment-label">Số tiền thanh toán *</label>
                <input type="number" class="payment-input" value="2000000">
              </div>
              <div class="payment-form-group">
                <label class="payment-label">Phương thức thanh toán *</label>
                <select class="payment-select">
                  <option value="0">Tiền mặt</option>
                  <option value="1">Chuyển khoản</option>
                  <option value="2">Thẻ tín dụng</option>
                </select>
              </div>
              <div class="payment-form-group">
                <label class="payment-label">Ghi chú</label>
                <textarea class="payment-textarea" placeholder="Nhập ghi chú về thanh toán (nếu có)"></textarea>
              </div>
            </div>
          </div>
        </div>
        <div class="payment-dialog-footer">
          <button class="cancel-button">Hủy</button>
          <button class="confirm-button">Xác nhận thanh toán</button>
        </div>
      </div>
    </div>

    <div class="success-notification">
      Thanh toán đã được ghi nhận thành công!
    </div>
  </div>
</body>
</html>
