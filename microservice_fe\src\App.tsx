import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout } from './components/layout';
import {
  HomePage,
  CreateContractPage,
  ContractDetailsPage,
  ContractsListPage,
  CustomerStatisticsPage,
  CustomerPaymentPage,
  NotFoundPage,
} from './pages';

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/contracts" element={<ContractsListPage />} />
          <Route path="/contracts/create" element={<CreateContractPage />} />
          <Route path="/contracts/:id" element={<ContractDetailsPage />} />
          <Route path="/statistics" element={<CustomerStatisticsPage />} />
          <Route path="/payments" element={<CustomerPaymentPage />} />
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;
