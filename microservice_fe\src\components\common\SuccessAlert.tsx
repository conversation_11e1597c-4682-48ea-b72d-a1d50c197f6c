import React, { useState, useEffect } from 'react';
import { Alert, AlertTitle, Box, Collapse, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

interface SuccessAlertProps {
  title?: string;
  message: string;
  autoHideDuration?: number;
  onClose?: () => void;
  showIcon?: boolean;
}

const SuccessAlert: React.FC<SuccessAlertProps> = ({
  title = 'Thành công',
  message,
  autoHideDuration = 5000,
  onClose,
  showIcon = true
}) => {
  const [open, setOpen] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setOpen(false);
      if (onClose) {
        onClose();
      }
    }, autoHideDuration);

    return () => {
      clearTimeout(timer);
    };
  }, [autoHideDuration, onClose]);

  const handleClose = () => {
    setOpen(false);
    if (onClose) {
      onClose();
    }
  };

  return (
    <Box sx={{ my: 2 }}>
      <Collapse in={open}>
        <Alert
          severity="success"
          icon={showIcon ? <CheckCircleIcon fontSize="inherit" /> : false}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={handleClose}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          sx={{
            '& .MuiAlert-message': {
              wordBreak: 'break-word',
              whiteSpace: 'pre-wrap'
            }
          }}
        >
          <AlertTitle>{title}</AlertTitle>
          {message}
        </Alert>
      </Collapse>
    </Box>
  );
};

export default SuccessAlert;
