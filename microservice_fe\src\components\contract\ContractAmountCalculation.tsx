import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  useTheme,
} from '@mui/material';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CalculateIcon from '@mui/icons-material/Calculate';
import InfoIcon from '@mui/icons-material/Info';
import { CustomerContract } from '../../models';
import { calculateContractAmount, ContractCalculationBreakdown } from '../../utils/contractCalculationUtils';
import { formatCurrency } from '../../utils/currencyUtils';

interface ContractAmountCalculationProps {
  contract: Partial<CustomerContract>;
}

const ContractAmountCalculation: React.FC<ContractAmountCalculationProps> = ({ contract }) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(false);

  const calculation = calculateContractAmount(contract);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  if (calculation.totalAmount === 0) {
    return (
      <Card variant="outlined" sx={{ mb: 2, borderColor: theme.palette.warning.light }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <InfoIcon sx={{ mr: 1, color: theme.palette.warning.main }} />
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: theme.palette.warning.main }}>
              Tổng giá trị hợp đồng
            </Typography>
          </Box>
          <Typography variant="body2" color="text.secondary">
            Vui lòng thêm chi tiết công việc và ca làm việc để tính toán tự động
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card variant="outlined" sx={{ mb: 2, borderColor: theme.palette.success.light }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <MonetizationOnIcon sx={{ mr: 1, color: theme.palette.success.main }} />
          <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>
            Tổng giá trị hợp đồng (Tự động tính toán)
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography 
            variant="h4" 
            sx={{ 
              fontWeight: 'bold', 
              color: theme.palette.success.main,
              mr: 2 
            }}
          >
            {formatCurrency(calculation.totalAmount)}
          </Typography>
          <Chip 
            label="Tự động" 
            size="small" 
            color="success" 
            variant="outlined"
          />
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Summary */}
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
          <Box>
            <Typography variant="body2" color="text.secondary">Tổng số ca làm việc</Typography>
            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
              {calculation.summary.totalWorkShifts} ca
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2" color="text.secondary">Tổng số nhân công</Typography>
            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
              {calculation.summary.totalWorkers} người
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2" color="text.secondary">Tổng ngày làm việc</Typography>
            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
              {calculation.summary.totalWorkingDays} ngày
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2" color="text.secondary">Thời gian hợp đồng</Typography>
            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
              {calculation.summary.contractDuration} ngày
            </Typography>
          </Box>
        </Box>

        {/* Detailed breakdown */}
        <Accordion expanded={expanded} onChange={handleExpandClick} sx={{ boxShadow: 'none' }}>
          <AccordionSummary 
            expandIcon={<ExpandMoreIcon />}
            sx={{ 
              backgroundColor: theme.palette.action.hover,
              borderRadius: '4px',
              minHeight: '36px',
              '& .MuiAccordionSummary-content': { margin: '4px 0' }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <CalculateIcon fontSize="small" sx={{ mr: 1 }} />
              <Typography variant="body2">Chi tiết tính toán</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails sx={{ pt: 2, pb: 1 }}>
            {calculation.breakdown.map((job, jobIndex) => (
              <Box key={jobIndex} sx={{ mb: 3 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                  {job.jobCategoryName}
                </Typography>
                
                <TableContainer sx={{ border: '1px solid #e0e0e0', borderRadius: '4px', mb: 2 }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow sx={{ backgroundColor: theme.palette.action.hover }}>
                        <TableCell>Ca làm việc</TableCell>
                        <TableCell align="right">Lương/ca</TableCell>
                        <TableCell align="right">Số người</TableCell>
                        <TableCell align="right">Số ngày</TableCell>
                        <TableCell align="right">Thành tiền</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {job.workShifts.map((shift, shiftIndex) => (
                        <TableRow key={shiftIndex}>
                          <TableCell>
                            Ca {shiftIndex + 1} ({shift.startTime} - {shift.endTime})
                          </TableCell>
                          <TableCell align="right">
                            {formatCurrency(shift.salary)}
                          </TableCell>
                          <TableCell align="right">
                            {shift.numberOfWorkers}
                          </TableCell>
                          <TableCell align="right">
                            {shift.workingDaysCount}
                          </TableCell>
                          <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                            {formatCurrency(shift.shiftAmount)}
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow sx={{ backgroundColor: theme.palette.action.hover }}>
                        <TableCell colSpan={4} sx={{ fontWeight: 'bold' }}>
                          Tổng {job.jobCategoryName}
                        </TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                          {formatCurrency(job.jobTotal)}
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            ))}
          </AccordionDetails>
        </Accordion>
      </CardContent>
    </Card>
  );
};

export default ContractAmountCalculation;
