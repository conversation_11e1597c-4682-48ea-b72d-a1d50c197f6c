import React from 'react';
import {
  Box,
  Typography,
  Chip,
  Card,
  CardContent,
  Divider,
  useTheme,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import DateRangeIcon from '@mui/icons-material/DateRange';
import DescriptionIcon from '@mui/icons-material/Description';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import WorkIcon from '@mui/icons-material/Work';

import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import { CustomerContract, ContractStatusMap } from '../../models';
import { formatDateLocalized } from '../../utils/dateUtils';
import { calculateWorkingDates } from '../../utils/workingDaysUtils';
import { formatCurrency } from '../../utils/currencyUtils';



interface ContractDetailsProps {
  contract: CustomerContract;
}



const ContractDetails: React.FC<ContractDetailsProps> = ({ contract }) => {
  const theme = useTheme();

  const getStatusColor = (status: number) => {
    switch (status) {
      case 0: // Pending
        return 'warning';
      case 1: // Active
        return 'success';
      case 2: // Completed
        return 'info';
      case 3: // Cancelled
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusBgColor = (status: number) => {
    switch (status) {
      case 0: // Pending
        return theme.palette.warning.light;
      case 1: // Active
        return theme.palette.success.light;
      case 2: // Completed
        return theme.palette.info.light;
      case 3: // Cancelled
        return theme.palette.error.light;
      default:
        return theme.palette.grey[200];
    }
  };

  return (
    <Box>
      <Card
        elevation={3}
        sx={{
          mb: 4,
          borderRadius: '8px',
          border: '1px solid #e0e0e0',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Contract header with status */}
        <Box
          sx={{
            p: 3,
            backgroundColor: getStatusBgColor(contract.status || 0),
            borderBottom: '1px solid #e0e0e0',
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar
                sx={{
                  bgcolor: theme.palette.primary.main,
                  mr: 2,
                  width: 56,
                  height: 56,
                }}
              >
                <DescriptionIcon fontSize="large" />
              </Avatar>
              <Box>
                <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                  HỢP ĐỒNG #{contract.id}
                </Typography>
                <Typography variant="subtitle1" color="text.secondary">
                  Mã hợp đồng: {contract.id}
                </Typography>
              </Box>
            </Box>
            <Chip
              label={ContractStatusMap[contract.status || 0]}
              color={getStatusColor(contract.status || 0)}
              sx={{
                fontSize: '1rem',
                py: 2,
                px: 3,
                fontWeight: 'bold',
              }}
            />
          </Box>
        </Box>

        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
            {/* Customer information */}
            <Box sx={{ width: { xs: '100%', md: '48%' } }}>
              <Card variant="outlined" sx={{ mb: 2, height: '100%' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <PersonIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      Thông tin khách hàng
                    </Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />
                  <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {contract.customerName}
                  </Typography>
                </CardContent>
              </Card>
            </Box>



            {/* Contract dates */}
            <Box sx={{ width: { xs: '100%', md: '48%' } }}>
              <Card variant="outlined" sx={{ mb: 2 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <DateRangeIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      Thời gian thực hiện
                    </Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                    <Box sx={{ width: { xs: '100%', sm: '30%' } }}>
                      <Typography variant="body2" color="text.secondary">
                        Ngày bắt đầu:
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                        {formatDateLocalized(contract.startingDate)}
                      </Typography>
                    </Box>
                    <Box sx={{ width: { xs: '100%', sm: '30%' } }}>
                      <Typography variant="body2" color="text.secondary">
                        Ngày kết thúc:
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                        {formatDateLocalized(contract.endingDate)}
                      </Typography>
                    </Box>

                  </Box>
                </CardContent>
              </Card>
            </Box>

            {/* Financial information */}
            <Box sx={{ width: { xs: '100%', md: '48%' } }}>
              <Card variant="outlined" sx={{ mb: 2 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <MonetizationOnIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      Thông tin thanh toán
                    </Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                    <Box sx={{ width: { xs: '100%', sm: '48%' } }}>
                      <Typography variant="body2" color="text.secondary">
                        Tổng giá trị hợp đồng:
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                        {formatCurrency(contract.totalAmount)}
                      </Typography>
                    </Box>
                    <Box sx={{ width: { xs: '100%', sm: '48%' } }}>
                      <Typography variant="body2" color="text.secondary">
                        Đã thanh toán:
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>
                        {formatCurrency(contract.totalPaid || 0)}
                      </Typography>
                    </Box>
                    <Box sx={{ width: { xs: '100%', sm: '48%' } }}>
                      <Typography variant="body2" color="text.secondary">
                        Còn lại:
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'bold', color: theme.palette.error.main }}>
                        {formatCurrency(contract.totalAmount - (contract.totalPaid || 0))}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Box>

            {/* Description if available */}
            {contract.description && (
              <Box sx={{ width: '100%' }}>
                <Card variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <DescriptionIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                      <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                        Mô tả hợp đồng
                      </Typography>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    <Typography variant="body1">
                      {contract.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Box>
            )}
          </Box>
        </CardContent>
      </Card>

      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <WorkIcon sx={{ mr: 1, color: theme.palette.secondary.main, fontSize: 28 }} />
          <Typography variant="h5" sx={{ fontWeight: 'bold', color: theme.palette.secondary.main }}>
            CHI TIẾT CÔNG VIỆC
          </Typography>
        </Box>

        {contract.jobDetails.map((jobDetail, index) => (
          <Card
            key={index}
            variant="outlined"
            sx={{
              mb: 3,
              borderRadius: '8px',
              border: '1px solid #e0e0e0',
              position: 'relative',
              overflow: 'hidden',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '6px',
                background: theme.palette.secondary.main,
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <WorkIcon sx={{ mr: 1, color: theme.palette.secondary.main }} />
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  {jobDetail.jobCategoryName}
                </Typography>
              </Box>

              <Divider sx={{ mb: 3 }} />

              {/* Job Summary Table */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2, color: theme.palette.secondary.main }}>
                  Tổng quan công việc
                </Typography>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableBody>
                      <TableRow>
                        <TableCell sx={{ fontWeight: 'bold', width: '30%', backgroundColor: theme.palette.grey[50] }}>
                          Địa điểm làm việc
                        </TableCell>
                        <TableCell>{jobDetail.workLocation}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell sx={{ fontWeight: 'bold', backgroundColor: theme.palette.grey[50] }}>
                          Thời gian thực hiện
                        </TableCell>
                        <TableCell>
                          {formatDateLocalized(jobDetail.startDate)} - {formatDateLocalized(jobDetail.endDate)}
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell sx={{ fontWeight: 'bold', backgroundColor: theme.palette.grey[50] }}>
                          Tổng số ca làm việc
                        </TableCell>
                        <TableCell>
                          {(() => {
                            let totalShifts = 0;
                            jobDetail.workShifts.forEach((shift: any) => {
                              const workingDates = calculateWorkingDates(
                                jobDetail.startDate,
                                jobDetail.endDate,
                                shift.workingDays
                              );
                              totalShifts += workingDates.length;
                            });
                            return totalShifts;
                          })()} ca
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell sx={{ fontWeight: 'bold', backgroundColor: theme.palette.grey[50] }}>
                          Tổng quan ngày làm việc
                        </TableCell>
                        <TableCell>
                          {(() => {
                            const allDays = new Set<string>();
                            jobDetail.workShifts.forEach((shift: any) => {
                              const workingDates = calculateWorkingDates(
                                jobDetail.startDate,
                                jobDetail.endDate,
                                shift.workingDays
                              );
                              workingDates.forEach(date => allDays.add(date));
                            });
                            return `${allDays.size} ngày làm việc`;
                          })()}
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>

              {/* Lịch làm việc chi tiết cho loại công việc này */}
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <CalendarMonthIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                    Lịch làm việc chi tiết
                  </Typography>
                </Box>
                {(() => {
                  // Generate detailed work schedule for this job category
                  let allShifts: {
                    date: string;
                    startTime: string;
                    endTime: string;
                    numberOfWorkers: number;
                    salary: number;
                  }[] = [];

                  jobDetail.workShifts.forEach((shift: any) => {
                    const workingDates = calculateWorkingDates(
                      jobDetail.startDate,
                      jobDetail.endDate,
                      shift.workingDays
                    );
                    workingDates.forEach(date => {
                      allShifts.push({
                        date,
                        startTime: shift.startTime,
                        endTime: shift.endTime,
                        numberOfWorkers: shift.numberOfWorkers || 0,
                        salary: shift.salary || 0
                      });
                    });
                  });

                  // Sort by date first, then by time
                  allShifts.sort((a, b) => {
                    const [d1, m1, y1] = a.date.split('/').map(Number);
                    const [d2, m2, y2] = b.date.split('/').map(Number);
                    const dateCompare = new Date(y1, m1 - 1, d1).getTime() - new Date(y2, m2 - 1, d2).getTime();

                    if (dateCompare !== 0) return dateCompare;

                    // If same date, sort by start time
                    const [h1, min1] = a.startTime.split(':').map(Number);
                    const [h2, min2] = b.startTime.split(':').map(Number);
                    return (h1 * 60 + min1) - (h2 * 60 + min2);
                  });

                  // Helper function to get Vietnamese day name
                  const getDayOfWeek = (dateStr: string) => {
                    const [d, m, y] = dateStr.split('/').map(Number);
                    const date = new Date(y, m - 1, d);
                    const day = date.getDay();
                    const dayNames = ['chủ nhật', 'thứ hai', 'thứ ba', 'thứ tư', 'thứ năm', 'thứ sáu', 'thứ bảy'];
                    return dayNames[day];
                  };

                  return allShifts.length === 0 ? (
                    <Typography color="text.secondary">Không có lịch làm việc</Typography>
                  ) : (
                    <TableContainer
                      component={Paper}
                      variant="outlined"
                      sx={{
                        mt: 2,
                        maxHeight: 400,
                        overflow: 'auto',
                        '&::-webkit-scrollbar': {
                          width: '8px',
                        },
                        '&::-webkit-scrollbar-track': {
                          backgroundColor: theme.palette.grey[100],
                        },
                        '&::-webkit-scrollbar-thumb': {
                          backgroundColor: theme.palette.grey[400],
                          borderRadius: '4px',
                        },
                      }}
                    >
                      <Table size="small" sx={{ minWidth: 700 }} stickyHeader>
                        <TableHead>
                          <TableRow>
                            <TableCell sx={{ fontWeight: 'bold', fontSize: '0.9rem', backgroundColor: theme.palette.grey[50] }}>
                              Ngày
                            </TableCell>
                            <TableCell sx={{ fontWeight: 'bold', fontSize: '0.9rem', backgroundColor: theme.palette.grey[50] }}>
                              Thứ
                            </TableCell>
                            <TableCell sx={{ fontWeight: 'bold', fontSize: '0.9rem', backgroundColor: theme.palette.grey[50] }}>
                              Ca làm việc
                            </TableCell>
                            <TableCell sx={{ fontWeight: 'bold', fontSize: '0.9rem', backgroundColor: theme.palette.grey[50], textAlign: 'center' }}>
                              Số công nhân
                            </TableCell>
                            <TableCell sx={{ fontWeight: 'bold', fontSize: '0.9rem', backgroundColor: theme.palette.grey[50], textAlign: 'right' }}>
                              Lương/người
                            </TableCell>
                            <TableCell sx={{ fontWeight: 'bold', fontSize: '0.9rem', backgroundColor: theme.palette.grey[50], textAlign: 'right' }}>
                              Tổng chi phí
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {allShifts.map((item, idx) => {
                            const totalCost = item.numberOfWorkers * item.salary;
                            return (
                              <TableRow
                                key={idx}
                                sx={{
                                  '&:nth-of-type(odd)': {
                                    backgroundColor: theme.palette.action.hover
                                  },
                                  '&:hover': {
                                    backgroundColor: theme.palette.action.selected
                                  }
                                }}
                              >
                                <TableCell sx={{ fontSize: '0.9rem', py: 1 }}>
                                  {item.date}
                                </TableCell>
                                <TableCell sx={{ fontSize: '0.9rem', py: 1, textTransform: 'capitalize' }}>
                                  {getDayOfWeek(item.date)}
                                </TableCell>
                                <TableCell sx={{ fontSize: '0.9rem', py: 1 }}>
                                  {item.startTime} - {item.endTime}
                                </TableCell>
                                <TableCell sx={{ fontSize: '0.9rem', py: 1, textAlign: 'center' }}>
                                  {item.numberOfWorkers} người
                                </TableCell>
                                <TableCell sx={{ fontSize: '0.9rem', py: 1, textAlign: 'right' }}>
                                  {item.salary.toLocaleString('vi-VN')} ₫
                                </TableCell>
                                <TableCell sx={{ fontSize: '0.9rem', py: 1, textAlign: 'right', fontWeight: 'medium' }}>
                                  {totalCost.toLocaleString('vi-VN')} ₫
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  );
                })()}
              </Box>
            </CardContent>
          </Card>
        ))}
      </Box>
    </Box>
  );
};

export default ContractDetails;
