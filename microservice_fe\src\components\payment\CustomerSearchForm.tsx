import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  Divider,
  CircularProgress,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import PersonIcon from '@mui/icons-material/Person';
import { Customer } from '../../models';

interface CustomerSearchFormProps {
  onSelectCustomer: (customer: Customer) => void;
  onSearch: (fullname?: string, phoneNumber?: string) => Promise<Customer[]>;
  loading: boolean;
}

const CustomerSearchForm: React.FC<CustomerSearchFormProps> = ({
  onSelectCustomer,
  onSearch,
  loading,
}) => {
  const [fullname, setFullname] = useState<string>('');
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  const [searchResults, setSearchResults] = useState<Customer[]>([]);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = async () => {
    if (!fullname && !phoneNumber) {
      setError('Vui lòng nhập tên hoặc số điện thoại để tìm kiếm');
      return;
    }

    setError(null);
    try {
      const results = await onSearch(fullname, phoneNumber);
      setSearchResults(results);
      if (results.length === 0) {
        setError('Không tìm thấy khách hàng nào');
      }
    } catch (err) {
      console.error('Error searching customers:', err);
      setError('Đã xảy ra lỗi khi tìm kiếm khách hàng');
    }
  };

  const handleSelectCustomer = (customer: Customer) => {
    onSelectCustomer(customer);
    setSearchResults([]);
    setFullname('');
    setPhoneNumber('');
  };

  return (
    <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        Tìm kiếm khách hàng
      </Typography>
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
        <TextField
          label="Tên khách hàng"
          value={fullname}
          onChange={(e) => setFullname(e.target.value)}
          sx={{ flex: '1 1 45%', minWidth: '250px' }}
        />
        <TextField
          label="Số điện thoại"
          value={phoneNumber}
          onChange={(e) => setPhoneNumber(e.target.value)}
          sx={{ flex: '1 1 45%', minWidth: '250px' }}
        />
        <Button
          variant="contained"
          startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SearchIcon />}
          onClick={handleSearch}
          disabled={loading}
          sx={{ flex: '1 1 auto', minWidth: '120px', height: '56px' }}
        >
          Tìm kiếm
        </Button>
      </Box>

      {error && (
        <Typography color="error" sx={{ mb: 2 }}>
          {error}
        </Typography>
      )}

      {searchResults.length > 0 && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Kết quả tìm kiếm
          </Typography>
          <List sx={{ width: '100%', bgcolor: 'background.paper' }}>
            {searchResults.map((customer, index) => (
              <React.Fragment key={customer.id}>
                <ListItem disablePadding>
                  <ListItemButton onClick={() => handleSelectCustomer(customer)}>
                    <PersonIcon sx={{ mr: 2 }} />
                    <ListItemText
                      primary={customer.fullName}
                      secondary={
                        <>
                          {customer.phoneNumber}
                          {customer.companyName && ` | ${customer.companyName}`}
                        </>
                      }
                    />
                  </ListItemButton>
                </ListItem>
                {index < searchResults.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </Box>
      )}
    </Paper>
  );
};

export default CustomerSearchForm;
