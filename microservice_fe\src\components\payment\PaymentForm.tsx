import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Paper,
  Divider,
  Alert,
  IconButton,
} from '@mui/material';

import CloseIcon from '@mui/icons-material/Close';
import PaymentIcon from '@mui/icons-material/Payment';
import ReceiptIcon from '@mui/icons-material/Receipt';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { CustomerContract, CustomerPayment, PaymentMethodMap } from '../../models';
import { formatCurrency } from '../../utils/formatters';
import { formatDateLocalized } from '../../utils/dateUtils';

interface PaymentFormProps {
  open: boolean;
  contract: CustomerContract | null;
  onClose: () => void;
  onSubmit: (payment: CustomerPayment) => void;
  remainingAmount: number;
  loading?: boolean;
}

const PaymentForm: React.FC<PaymentFormProps> = ({
  open,
  contract,
  onClose,
  onSubmit,
  remainingAmount,
  loading = false,
}) => {


  const [paymentAmount, setPaymentAmount] = useState<number>(0);
  const [paymentMethod, setPaymentMethod] = useState<number>(0); // Default: Tiền mặt
  const [note, setNote] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open && contract) {
      // Reset form when dialog opens
      setPaymentAmount(remainingAmount);
      setPaymentMethod(0);
      setNote('');
      setError(null);
    }
  }, [open, contract, remainingAmount]);

  const handleSubmit = (e?: React.FormEvent) => {
    // Prevent default form submission behavior
    if (e) {
      e.preventDefault();
    }

    if (!contract || loading) return;

    // Clear previous errors
    setError(null);

    // Validate payment amount
    if (!paymentAmount || paymentAmount <= 0) {
      setError('Số tiền thanh toán phải lớn hơn 0');
      return;
    }

    if (paymentAmount > remainingAmount) {
      setError(`Số tiền thanh toán không được vượt quá số tiền còn lại (${formatCurrency(remainingAmount)})`);
      return;
    }

    // Validate contract data
    if (!contract.id) {
      setError('Thông tin hợp đồng không hợp lệ');
      return;
    }

    if (!contract.customerId) {
      setError('Thông tin khách hàng không hợp lệ');
      return;
    }

    console.log('🚀 Preparing payment submission:', {
      contractId: contract.id,
      customerId: contract.customerId,
      amount: paymentAmount,
      method: paymentMethod,
      remainingAmount
    });

    // Create payment object
    const payment: CustomerPayment = {
      paymentDate: new Date().toISOString(),
      paymentMethod,
      paymentAmount,
      note: note || undefined,
      customerContractId: contract.id!,
      customerId: contract.customerId,
    };

    onSubmit(payment);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return formatDateLocalized(dateString);
  };



  if (!contract) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: 2,
          boxShadow: 24,
        }
      }}
    >
      <DialogTitle sx={{
        bgcolor: 'primary.main',
        color: 'primary.contrastText',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        px: 3,
        py: 2
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PaymentIcon />
          <Typography variant="h6">Thanh toán hợp đồng</Typography>
        </Box>
        <IconButton
          edge="end"
          color="inherit"
          onClick={onClose}
          aria-label="close"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <InfoOutlinedIcon fontSize="small" color="primary" />
            Thông tin hợp đồng
          </Typography>

          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2 }}>
            <Box>
              <Typography variant="body2" color="text.secondary">
                Mã hợp đồng
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                #{contract.id}
              </Typography>
            </Box>

            <Box>
              <Typography variant="body2" color="text.secondary">
                Khách hàng
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                {contract.customerName}
              </Typography>
            </Box>

            <Box>
              <Typography variant="body2" color="text.secondary">
                Ngày bắt đầu
              </Typography>
              <Typography variant="body1">
                {formatDate(contract.startingDate)}
              </Typography>
            </Box>

            <Box>
              <Typography variant="body2" color="text.secondary">
                Ngày kết thúc
              </Typography>
              <Typography variant="body1">
                {formatDate(contract.endingDate)}
              </Typography>
            </Box>


          </Box>

          <Divider sx={{ my: 2 }} />

          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr 1fr' }, gap: 2 }}>
            <Paper
              variant="outlined"
              sx={{
                p: 1.5,
                bgcolor: 'background.default',
                borderColor: 'divider'
              }}
            >
              <Typography variant="body2" color="text.secondary">
                Tổng giá trị hợp đồng
              </Typography>
              <Typography variant="h6">
                {formatCurrency(contract.totalAmount)}
              </Typography>
            </Paper>

            <Paper
              variant="outlined"
              sx={{
                p: 1.5,
                bgcolor: 'background.default',
                borderColor: 'divider'
              }}
            >
              <Typography variant="body2" color="text.secondary">
                Đã thanh toán
              </Typography>
              <Typography variant="h6">
                {formatCurrency(contract.totalPaid || 0)}
              </Typography>
            </Paper>

            <Paper
              sx={{
                p: 1.5,
                bgcolor: 'primary.light',
                color: 'primary.contrastText',
                borderRadius: 1
              }}
            >
              <Typography variant="body2" color="inherit">
                Còn lại cần thanh toán
              </Typography>
              <Typography variant="h6" fontWeight="bold">
                {formatCurrency(remainingAmount)}
              </Typography>
            </Paper>
          </Box>
        </Paper>

        <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ReceiptIcon fontSize="small" color="primary" />
            Thông tin thanh toán
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box
            component="form"
            id="payment-form"
            onSubmit={handleSubmit}
            sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 3 }}
          >
            <TextField
              label="Số tiền thanh toán"
              type="number"
              fullWidth
              value={paymentAmount}
              onChange={(e) => setPaymentAmount(Number(e.target.value))}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleSubmit();
                }
              }}
              sx={{
                '& .MuiInputBase-input': { paddingRight: '40px' },
                '& .MuiInputBase-root': {
                  position: 'relative',
                  '&::after': {
                    content: '"VND"',
                    position: 'absolute',
                    right: '14px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: 'rgba(0, 0, 0, 0.54)',
                    pointerEvents: 'none'
                  }
                }
              }}
              error={!!error}
              required
            />

            <FormControl fullWidth required>
              <InputLabel>Phương thức thanh toán</InputLabel>
              <Select
                value={paymentMethod}
                label="Phương thức thanh toán"
                onChange={(e) => setPaymentMethod(Number(e.target.value))}
              >
                {Object.entries(PaymentMethodMap).map(([value, label]) => (
                  <MenuItem key={value} value={value}>
                    {label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Box sx={{ gridColumn: { xs: '1', sm: '1 / span 2' } }}>
              <TextField
                label="Ghi chú"
                fullWidth
                multiline
                rows={3}
                value={note}
                onChange={(e) => setNote(e.target.value)}
                placeholder="Nhập ghi chú về thanh toán (nếu có)"
                onKeyDown={(e) => {
                  // Prevent Enter from submitting in multiline text field
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.stopPropagation();
                  }
                }}
              />
            </Box>
          </Box>
        </Paper>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, justifyContent: 'space-between' }}>
        <Button
          onClick={onClose}
          color="inherit"
          variant="outlined"
          startIcon={<CloseIcon />}
        >
          Hủy
        </Button>
        <Button
          type="submit"
          form="payment-form"
          variant="contained"
          color="primary"
          startIcon={<PaymentIcon />}
          size="large"
          disabled={loading || !paymentAmount || paymentAmount <= 0}
        >
          {loading ? 'Đang xử lý...' : 'Xác nhận thanh toán'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PaymentForm;
