import React from 'react';
import { Box, Paper, Typography, useTheme } from '@mui/material';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { TimeBasedRevenue } from '../../models';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface BarChartDisplayProps {
  data: TimeBasedRevenue[];
  periodType: string;
  title?: string;
}

const BarChartDisplay: React.FC<BarChartDisplayProps> = ({
  data,
  periodType,
  title = 'Biểu đồ doanh thu'
}) => {
  const theme = useTheme();

  // Get period type label
  const getPeriodTypeLabel = (): string => {
    switch (periodType) {
      case 'daily':
        return 'Doanh thu theo ngày';
      case 'monthly':
        return 'Doanh thu theo tháng';
      case 'yearly':
        return '<PERSON>anh thu theo năm';
      default:
        return '<PERSON>anh thu theo thời gian';
    }
  };

  // Format currency for tooltip
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      maximumFractionDigits: 0
    }).format(value);
  };

  // Prepare chart data
  const chartData = {
    labels: data.map(item => item.label),
    datasets: [
      {
        label: 'Doanh thu',
        data: data.map(item => item.totalRevenue),
        backgroundColor: theme.palette.primary.main,
        borderColor: theme.palette.primary.dark,
        borderWidth: 1,
      },
    ],
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: getPeriodTypeLabel(),
        font: {
          size: 16,
          weight: 'bold' as const,
        },
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += formatCurrency(context.parsed.y);
            }
            return label;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value: any) {
            return formatCurrency(value);
          }
        }
      }
    },
  };

  return (
    <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {title}
      </Typography>
      <Box sx={{ height: 400, position: 'relative' }}>
        {data.length > 0 ? (
          <Bar data={chartData} options={chartOptions} />
        ) : (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%'
            }}
          >
            <Typography variant="subtitle1" color="text.secondary">
              Không có dữ liệu để hiển thị
            </Typography>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default BarChartDisplay;
