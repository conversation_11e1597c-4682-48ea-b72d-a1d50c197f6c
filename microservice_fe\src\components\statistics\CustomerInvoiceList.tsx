import React from 'react';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Box,
  Chip
} from '@mui/material';
import { formatDateForDisplay } from '../../utils/dateUtils';
import { CustomerPayment, PaymentMethodMap } from '../../models/CustomerPayment';

interface CustomerInvoiceListProps {
  invoices: CustomerPayment[];
  customerName: string;
}

const CustomerInvoiceList: React.FC<CustomerInvoiceListProps> = ({ invoices }) => {
  // Format currency to VND
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);
  };

  // Format date to Vietnamese format (DD/MM/YYYY)
  const formatDate = (dateValue: string | Date): string => {
    if (!dateValue) {
      return 'Không xác định';
    }

    try {
      return formatDateForDisplay(dateValue) || 'Không xác định';
    } catch (error) {
      console.error('Error formatting date:', error, dateValue);
      return 'Không xác định';
    }
  };

  // Get payment method name
  const getPaymentMethodName = (methodId: number): string => {
    return PaymentMethodMap[methodId] || 'Không xác định';
  };

  return (
    <Paper elevation={2}>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Mã hóa đơn</TableCell>
              <TableCell>Mã hợp đồng</TableCell>
              <TableCell>Ngày thanh toán</TableCell>
              <TableCell>Phương thức</TableCell>
              <TableCell align="right">Số tiền</TableCell>
              <TableCell>Ghi chú</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {invoices.length > 0 ? (
              invoices.map((invoice) => (
                <TableRow key={invoice.id}>
                  <TableCell>#{invoice.id}</TableCell>
                  <TableCell>{`HĐ-${invoice.customerContractId}`}</TableCell>
                  <TableCell>{formatDate(invoice.paymentDate)}</TableCell>
                  <TableCell>
                    <Chip
                      label={getPaymentMethodName(invoice.paymentMethod)}
                      color={invoice.paymentMethod === 0 ? "primary" : "default"}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                    {formatCurrency(invoice.paymentAmount)}
                  </TableCell>
                  <TableCell>{invoice.note || '-'}</TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  <Box sx={{ py: 3 }}>
                    <Typography variant="subtitle1">
                      Không có hóa đơn nào trong khoảng thời gian đã chọn
                    </Typography>
                  </Box>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
};

export default CustomerInvoiceList;
