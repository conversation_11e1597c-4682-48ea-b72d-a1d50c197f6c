import React from 'react';
import { Box, Paper, Typography, Grid, Divider } from '@mui/material';
import { TimeBasedRevenue } from '../../models';

interface StatisticsSummaryProps {
  data: TimeBasedRevenue[];
  periodType: string;
  periodLabel?: string;
}

const StatisticsSummary: React.FC<StatisticsSummaryProps> = ({
  data,
  periodType,
  periodLabel
}) => {
  // Calculate total revenue
  const totalRevenue = data.reduce((sum, item) => sum + item.totalRevenue, 0);

  // Calculate total invoices
  const totalInvoices = data.reduce((sum, item) => sum + item.invoiceCount, 0);

  // Format currency
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      maximumFractionDigits: 0
    }).format(value);
  };

  // Get period type label
  const getPeriodTypeLabel = (): string => {
    switch (periodType) {
      case 'daily':
        return 'ngày';
      case 'monthly':
        return 'tháng';
      case 'quarterly':
        return 'quý';
      case 'yearly':
        return 'năm';
      default:
        return 'khoảng thời gian';
    }
  };

  // Get summary title
  const getSummaryTitle = (): string => {
    if (periodLabel) {
      return `Tổng hợp doanh thu ${getPeriodTypeLabel()} ${periodLabel}`;
    }
    return `Tổng hợp doanh thu theo ${getPeriodTypeLabel()}`;
  };

  return (
    <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {getSummaryTitle()}
      </Typography>
      <Divider sx={{ mb: 2 }} />

      <Grid container spacing={3}>
        <Grid size={{ xs: 12, sm: 6 }}>
          <Box sx={{ p: 2, borderRadius: 1, bgcolor: 'primary.light', width: '100%' }}>
            <Typography variant="subtitle2" color="primary.contrastText">
              Tổng doanh thu
            </Typography>
            <Typography variant="h4" color="primary.contrastText" sx={{ fontWeight: 'bold' }}>
              {formatCurrency(totalRevenue)}
            </Typography>
          </Box>
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <Box sx={{ p: 2, borderRadius: 1, bgcolor: 'secondary.light', width: '100%' }}>
            <Typography variant="subtitle2" color="secondary.contrastText">
              Tổng số hóa đơn
            </Typography>
            <Typography variant="h4" color="secondary.contrastText" sx={{ fontWeight: 'bold' }}>
              {totalInvoices}
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default StatisticsSummary;
