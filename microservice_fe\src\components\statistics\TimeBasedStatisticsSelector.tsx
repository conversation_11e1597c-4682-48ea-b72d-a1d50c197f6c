import React, { useState } from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Button,
  Typography,
  Paper,
  Grid,
  ToggleButton,
  ToggleButtonGroup,
  FormLabel
} from '@mui/material';
import { formatDateForDisplay } from '../../utils/dateUtils';

interface TimeBasedStatisticsSelectorProps {
  periodType: string;
  startDate: Date;
  endDate: Date;
  selectedDay: number;
  selectedMonth: number;
  selectedYear: number;
  onPeriodTypeChange: (periodType: string) => void;
  onStartDateChange: (date: Date) => void;
  onEndDateChange: (date: Date) => void;
  onDayChange: (day: number) => void;
  onMonthChange: (month: number) => void;
  onYearChange: (year: number) => void;
  onApplyFilter?: () => void;
}

const TimeBasedStatisticsSelector: React.FC<TimeBasedStatisticsSelectorProps> = ({
  periodType,
  // Các props không sử dụng trực tiếp nhưng vẫn cần để truyền từ component cha
  // startDate,
  // endDate,
  // selectedDay,
  selectedYear,
  onPeriodTypeChange,
  // onStartDateChange,
  // onEndDateChange,
  // onDayChange,
  // onMonthChange,
  onYearChange,
  // onApplyFilter
}) => {
  // Using the centralized formatDateForDisplay function from dateUtils.ts

  const handlePeriodTypeChange = (
    _: React.MouseEvent<HTMLElement>,
    newPeriodType: string,
  ) => {
    if (newPeriodType !== null) {
      // Chỉ gọi onPeriodTypeChange, việc áp dụng filter sẽ được xử lý ở component cha
      onPeriodTypeChange(newPeriodType);
    }
  };

  const handleYearChange = (event: SelectChangeEvent) => {
    // Chỉ gọi onYearChange, việc áp dụng filter sẽ được xử lý ở component cha
    onYearChange(Number(event.target.value));
  };

  // Generate years options (current year - 5 to current year + 5)
  const currentYear = new Date().getFullYear();
  const yearsOptions = Array.from(
    { length: 11 },
    (_, i) => currentYear - 5 + i
  );

  return (
    <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        Thống kê doanh thu theo thời gian
      </Typography>

      <Grid container spacing={2} alignItems="center" sx={{ mb: 3 }}>
        <Box sx={{ p: 1 }}>
          <ToggleButtonGroup
            value={periodType}
            exclusive
            onChange={handlePeriodTypeChange}
            aria-label="Loại thời gian"
          >
            <ToggleButton value="monthly" aria-label="Theo tháng" sx={{ px: 3 }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <Box
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: '50%',
                    bgcolor: periodType === 'monthly' ? 'primary.main' : 'grey.200',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 1
                  }}
                >
                  <Box
                    sx={{
                      width: 20,
                      height: 20,
                      borderRadius: '50%',
                      bgcolor: 'white'
                    }}
                  />
                </Box>
                <Typography>Theo tháng</Typography>
              </Box>
            </ToggleButton>
            <ToggleButton value="quarterly" aria-label="Theo quý" sx={{ px: 3 }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <Box
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: '50%',
                    bgcolor: periodType === 'quarterly' ? 'primary.main' : 'grey.200',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 1
                  }}
                >
                  <Box
                    sx={{
                      width: 20,
                      height: 20,
                      borderRadius: '50%',
                      bgcolor: 'white'
                    }}
                  />
                </Box>
                <Typography>Theo quý</Typography>
              </Box>
            </ToggleButton>
            <ToggleButton value="yearly" aria-label="Theo năm" sx={{ px: 3 }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <Box
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: '50%',
                    bgcolor: periodType === 'yearly' ? 'primary.main' : 'grey.200',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 1
                  }}
                >
                  <Box
                    sx={{
                      width: 20,
                      height: 20,
                      borderRadius: '50%',
                      bgcolor: 'white'
                    }}
                  />
                </Box>
                <Typography>Theo năm</Typography>
              </Box>
            </ToggleButton>
          </ToggleButtonGroup>
        </Box>
      </Grid>

      <Grid container spacing={2} alignItems="flex-end">
        {(periodType === 'monthly' || periodType === 'quarterly') && (
          <Box sx={{ width: '100%', p: 1 }}>
            <FormControl fullWidth>
              <InputLabel id="year-select-label">Năm</InputLabel>
              <Select
                labelId="year-select-label"
                id="year-select"
                value={selectedYear.toString()}
                label="Năm"
                onChange={handleYearChange}
              >
                {yearsOptions.map(year => (
                  <MenuItem key={`year-${year}`} value={year}>{year}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        )}
      </Grid>
    </Paper>
  );
};

export default TimeBasedStatisticsSelector;
