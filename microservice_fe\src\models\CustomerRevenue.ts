import { Customer } from './Customer';

export interface CustomerRevenue extends Omit<Customer, 'createdAt' | 'updatedAt'> {
  contractCount?: number; // Optional since we don't need it
  totalRevenue: number;
  createdAt?: string | number[] | Date;
  updatedAt?: string | number[] | Date;
}

// Helper function to convert array date format [year, month, day, hour, minute, second, nano] to Date
export const arrayToDate = (dateArray: number[] | undefined): Date | undefined => {
  if (!dateArray || !Array.isArray(dateArray) || dateArray.length < 3) {
    return undefined;
  }

  try {
    // Format: [year, month, day, hour, minute, second, nano]
    const [year, month, day, hour = 0, minute = 0, second = 0] = dateArray;
    // Note: JavaScript months are 0-based (0 = January, 11 = December)
    return new Date(year, month - 1, day, hour, minute, second);
  } catch (error) {
    console.error('Error converting array to date:', error, dateArray);
    return undefined;
  }
};

// Helper function to normalize CustomerRevenue data from API
export const normalizeCustomerRevenue = (data: any): CustomerRevenue => {
  try {
    if (!data) {
      console.error('Invalid customer revenue data: data is null or undefined');
      // Return a default object instead of throwing an error
      return {
        id: 0,
        fullName: 'Unknown',
        phoneNumber: '',
        address: '',
        totalRevenue: 0
      } as CustomerRevenue;
    }

    // Ensure totalRevenue is a number
    let totalRevenue = 0;
    if (typeof data.totalRevenue === 'number') {
      totalRevenue = data.totalRevenue;
    } else if (typeof data.totalRevenue === 'string') {
      totalRevenue = parseFloat(data.totalRevenue) || 0;
    }

    // Safely access properties with fallbacks
    const id = typeof data.id === 'number' ? data.id :
               typeof data.id === 'string' ? parseInt(data.id, 10) || 0 : 0;

    // Create a normalized customer revenue object
    return {
      id: id,
      fullName: data.fullName || '',
      companyName: data.companyName || '',
      phoneNumber: data.phoneNumber || '',
      email: data.email || '',
      address: data.address || '',
      isDeleted: Boolean(data.isDeleted),
      totalRevenue: totalRevenue,
      // Handle different date formats safely
      createdAt: safelyParseDate(data.createdAt),
      updatedAt: safelyParseDate(data.updatedAt)
    };
  } catch (error) {
    console.error('Error in normalizeCustomerRevenue:', error);
    // Return a default object in case of any error
    return {
      id: 0,
      fullName: 'Error',
      phoneNumber: '',
      address: '',
      totalRevenue: 0
    } as CustomerRevenue;
  }
};

// Helper function to safely parse different date formats
const safelyParseDate = (dateValue: any): Date | undefined => {
  try {
    if (!dateValue) return undefined;

    if (Array.isArray(dateValue)) {
      return arrayToDate(dateValue);
    } else if (typeof dateValue === 'string') {
      // Kiểm tra các định dạng chuỗi ngày tháng khác nhau

      // Kiểm tra định dạng ISO (YYYY-MM-DDTHH:mm:ss.sssZ)
      if (dateValue.includes('T') && (dateValue.includes('Z') || dateValue.includes('+'))) {
        const date = new Date(dateValue);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      // Kiểm tra định dạng YYYY-MM-DD
      if (dateValue.match(/^\d{4}-\d{2}-\d{2}$/)) {
        const [year, month, day] = dateValue.split('-').map(Number);
        return new Date(year, month - 1, day, 12, 0, 0);
      }

      // Kiểm tra định dạng DD/MM/YYYY
      if (dateValue.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
        const [day, month, year] = dateValue.split('/').map(Number);
        return new Date(year, month - 1, day, 12, 0, 0);
      }

      // Thử parse với Date constructor
      const date = new Date(dateValue);
      if (!isNaN(date.getTime())) {
        return date;
      }

      console.warn('Unrecognized date format:', dateValue);
      return undefined;
    } else if (dateValue instanceof Date) {
      return dateValue;
    }
    return undefined;
  } catch (error) {
    console.error('Error parsing date:', error, dateValue);
    return undefined;
  }
};
