export interface TimeBasedRevenue {
  date: string;
  label: string;
  totalRevenue: number;
  invoiceCount: number;
  periodType: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
}

// Helper function to normalize TimeBasedRevenue data from API
export const normalizeTimeBasedRevenue = (data: any): TimeBasedRevenue => {
  try {
    if (!data) {
      console.error('Invalid time-based revenue data: data is null or undefined');
      // Return a default object instead of throwing an error
      return {
        date: new Date().toISOString().split('T')[0],
        label: 'Không xác định',
        totalRevenue: 0,
        invoiceCount: 0,
        periodType: 'daily'
      } as TimeBasedRevenue;
    }

    // Ensure totalRevenue is a number
    let totalRevenue = 0;
    if (typeof data.totalRevenue === 'number') {
      totalRevenue = data.totalRevenue;
    } else if (typeof data.totalRevenue === 'string') {
      totalRevenue = parseFloat(data.totalRevenue) || 0;
    }

    // Ensure invoiceCount is a number
    let invoiceCount = 0;
    if (typeof data.invoiceCount === 'number') {
      invoiceCount = data.invoiceCount;
    } else if (typeof data.invoiceCount === 'string') {
      invoiceCount = parseInt(data.invoiceCount, 10) || 0;
    }

    // Create a normalized time-based revenue object
    return {
      date: data.date || new Date().toISOString().split('T')[0],
      label: data.label || 'Không xác định',
      totalRevenue: totalRevenue,
      invoiceCount: invoiceCount,
      periodType: data.periodType || 'daily'
    };
  } catch (error) {
    console.error('Error normalizing time-based revenue data:', error);
    return {
      date: new Date().toISOString().split('T')[0],
      label: 'Lỗi dữ liệu',
      totalRevenue: 0,
      invoiceCount: 0,
      periodType: 'daily'
    };
  }
};
