import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Button,
  IconButton,
  Tooltip,
  Tab,
  Tabs
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import RefreshIcon from '@mui/icons-material/Refresh';
import axios from 'axios';
import { formatDateForInput, formatDateForDisplay } from '../utils/dateUtils';
import { <PERSON><PERSON><PERSON>er, LoadingSpinner, <PERSON>rror<PERSON><PERSON>t, DatePickerField } from '../components/common';
import {
  CustomerRevenueList,
  CustomerInvoiceList,
  TimeBasedStatisticsSelector,
  TimeBasedRevenueDisplay
} from '../components/statistics';
import { customerStatisticsService } from '../services/statistics/customerStatisticsService';
import { customerService } from '../services/customer/customerService';
import { CustomerRevenue, CustomerPayment, Customer, TimeBasedRevenue } from '../models';

const CustomerStatisticsPage: React.FC = () => {
  // State for date range
  const [startDate, setStartDate] = useState<Date>(new Date(new Date().getFullYear(), 0, 1)); // January 1st of current year
  const [endDate, setEndDate] = useState<Date>(new Date());

  // State for data
  const [customers, setCustomers] = useState<CustomerRevenue[]>([]);
  const [invoices, setInvoices] = useState<CustomerPayment[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  // State for time-based statistics
  const [activeTab, setActiveTab] = useState<number>(0);
  const [periodType, setPeriodType] = useState<string>('monthly');
  const [timeBasedData, setTimeBasedData] = useState<TimeBasedRevenue[]>([]);
  const [timeBasedLoading, setTimeBasedLoading] = useState<boolean>(false);
  const [timeBasedError, setTimeBasedError] = useState<string | null>(null);

  // State for date selectors
  const currentDate = new Date();
  const [selectedDay, setSelectedDay] = useState<number>(currentDate.getDate());
  const [selectedMonth, setSelectedMonth] = useState<number>(currentDate.getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState<number>(currentDate.getFullYear());

  // State for UI
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showInvoices, setShowInvoices] = useState<boolean>(false);

  // Using the centralized date formatting functions from dateUtils.ts

  // Helper function to parse date from display format (DD/MM/YYYY) - removed as unused

  // Load customer revenue statistics
  const loadCustomerStatistics = async () => {
    setLoading(true);
    setError(null);
    setShowInvoices(false);
    setSelectedCustomer(null);

    try {
      console.log('Calling API with dates:', formatDateForInput(startDate), formatDateForInput(endDate));

      // Check if dates are valid before making the API call
      if (startDate > endDate) {
        setError('Ngày bắt đầu không thể sau ngày kết thúc. Vui lòng chọn lại.');
        setCustomers([]);
        setLoading(false);
        return;
      }

      // Thêm xử lý trường hợp ngày không hợp lệ
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        setError('Ngày không hợp lệ. Vui lòng chọn lại.');
        setCustomers([]);
        setLoading(false);
        return;
      }

      // Thêm thông báo đang tải dữ liệu
      console.log('Fetching customer statistics data...');

      try {
        const data = await customerStatisticsService.getCustomerRevenueStatistics(startDate, endDate);
        console.log('API response:', data);

        // Validate data before setting state
        if (data && Array.isArray(data)) {
          if (data.length === 0) {
            console.log('No customer revenue data found for the selected period');
          }
          setCustomers(data);
        } else {
          console.error('Invalid data format received:', data);
          setError('Định dạng dữ liệu không hợp lệ. Vui lòng thử lại sau.');
          setCustomers([]);
        }
      } catch (apiError: any) {
        // Xử lý lỗi từ API service
        throw apiError;
      }
    } catch (err: any) {
      console.error('Error loading customer statistics:', err);

      // Hiển thị thông báo lỗi chi tiết hơn
      let errorMessage = 'Không thể tải dữ liệu thống kê khách hàng. Vui lòng thử lại sau.';

      if (axios.isAxiosError(err)) {
        console.error('Response data:', err.response?.data);
        console.error('Response status:', err.response?.status);

        // Sử dụng thông báo lỗi từ API nếu có
        if (err.response?.data?.error) {
          errorMessage = err.response.data.error;
        }
        // Nếu không, sử dụng thông báo lỗi dựa trên mã lỗi
        else if (err.code === 'ECONNABORTED') {
          errorMessage = 'Yêu cầu đã hết thời gian chờ. Máy chủ mất quá nhiều thời gian để phản hồi. Vui lòng thử lại sau.';
        } else if (err.message.includes('Network Error') || !err.response) {
          errorMessage = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối của bạn hoặc máy chủ có thể đang gặp sự cố.';
        } else if (err.response?.status === 404) {
          errorMessage = 'Không tìm thấy dữ liệu thống kê. Vui lòng kiểm tra kết nối mạng và thử lại.';
        } else if (err.response?.status === 500) {
          errorMessage = 'Lỗi máy chủ. Vui lòng thử lại sau hoặc liên hệ với quản trị viên hệ thống.';
        } else if (err.response?.status === 400) {
          errorMessage = 'Yêu cầu không hợp lệ. Vui lòng kiểm tra lại thông tin nhập vào.';
        }
      } else {
        // Nếu là lỗi thông thường, sử dụng thông báo lỗi từ exception
        errorMessage = err.message || errorMessage;
      }

      setError(errorMessage);
      setCustomers([]);
    } finally {
      setLoading(false);
    }
  };

  // Load customer invoices
  const loadCustomerInvoices = async (customerId: number) => {
    setLoading(true);
    setError(null);

    try {
      // Kiểm tra customerId
      if (!customerId) {
        setError('ID khách hàng không hợp lệ.');
        setInvoices([]);
        setLoading(false);
        return;
      }

      // Get customer details
      try {
        const customer = await customerService.getCustomerById(customerId);
        setSelectedCustomer(customer);
      } catch (customerErr) {
        console.error('Error loading customer details:', customerErr);
        // Continue with invoices even if customer details fail to load
        setSelectedCustomer({ id: customerId, fullName: 'Khách hàng #' + customerId, phoneNumber: '', address: '' });
      }

      // Check if dates are valid before making the API call
      if (startDate > endDate) {
        setError('Ngày bắt đầu không thể sau ngày kết thúc. Vui lòng chọn lại.');
        setInvoices([]);
        setLoading(false);
        return;
      }

      // Thêm xử lý trường hợp ngày không hợp lệ
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        setError('Ngày không hợp lệ. Vui lòng chọn lại.');
        setInvoices([]);
        setLoading(false);
        return;
      }

      // Get customer invoices
      console.log('Calling invoices API with dates:', formatDateForInput(startDate), formatDateForInput(endDate));
      console.log('Fetching invoices for customer ID:', customerId);

      try {
        const invoices = await customerStatisticsService.getCustomerInvoices(customerId, startDate, endDate);
        console.log('Invoices API response:', invoices);

        // Validate invoices data
        if (invoices && Array.isArray(invoices)) {
          if (invoices.length === 0) {
            console.log('No invoices found for customer in the selected period');
          }
          setInvoices(invoices);
          setShowInvoices(true);
        } else {
          console.error('Invalid invoices data format:', invoices);
          setError('Định dạng dữ liệu hóa đơn không hợp lệ. Vui lòng thử lại sau.');
          setInvoices([]);
        }
      } catch (apiError: any) {
        // Xử lý lỗi từ API service
        throw apiError;
      }
    } catch (err: any) {
      console.error('Error loading customer invoices:', err);

      // Hiển thị thông báo lỗi chi tiết hơn
      let errorMessage = 'Không thể tải dữ liệu hóa đơn. Vui lòng thử lại sau.';

      if (axios.isAxiosError(err)) {
        console.error('Response data:', err.response?.data);
        console.error('Response status:', err.response?.status);

        // Sử dụng thông báo lỗi từ API nếu có
        if (err.response?.data?.error) {
          errorMessage = err.response.data.error;
        }
        // Nếu không, sử dụng thông báo lỗi dựa trên mã lỗi
        else if (err.code === 'ECONNABORTED') {
          errorMessage = 'Yêu cầu đã hết thời gian chờ. Máy chủ mất quá nhiều thời gian để phản hồi. Vui lòng thử lại sau.';
        } else if (err.message.includes('Network Error') || !err.response) {
          errorMessage = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối của bạn hoặc máy chủ có thể đang gặp sự cố.';
        } else if (err.response?.status === 404) {
          errorMessage = 'Không tìm thấy dữ liệu hóa đơn. Vui lòng kiểm tra kết nối mạng và thử lại.';
        } else if (err.response?.status === 500) {
          errorMessage = 'Lỗi máy chủ khi tải dữ liệu hóa đơn. Vui lòng thử lại sau hoặc liên hệ với quản trị viên hệ thống.';
        } else if (err.response?.status === 400) {
          errorMessage = 'Yêu cầu không hợp lệ. Vui lòng kiểm tra lại thông tin nhập vào.';
        }
      } else {
        // Nếu là lỗi thông thường, sử dụng thông báo lỗi từ exception
        errorMessage = err.message || errorMessage;
      }

      setError(errorMessage);
      setInvoices([]);
      // Nếu lỗi nghiêm trọng, quay lại màn hình thống kê
      if (err.message.includes('Network Error') || err.code === 'ECONNABORTED') {
        setShowInvoices(false);
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle view invoices button click
  const handleViewInvoices = (customerId: number) => {
    loadCustomerInvoices(customerId);
  };

  // Handle back button click
  const handleBackToStatistics = () => {
    setShowInvoices(false);
    setSelectedCustomer(null);
  };

  // Load time-based statistics
  const loadTimeBasedStatistics = async () => {
    try {
      setTimeBasedLoading(true);
      setTimeBasedError(null);

      let data: TimeBasedRevenue[] = [];

      // Tạo thông báo log để debug
      console.log(`Đang tải thống kê theo ${periodType} từ ${formatDateForDisplay(startDate)} đến ${formatDateForDisplay(endDate)}`);

      switch (periodType) {
        case 'yearly':
          try {
            // Lấy dữ liệu thực từ API
            const apiData = await customerStatisticsService.getYearlyRevenueStatistics(
              new Date(selectedYear - 5, 0, 1),
              new Date(selectedYear + 5, 11, 31)
            );

            // Tạo mảng chứa các năm gần đây
            const allYearsData: TimeBasedRevenue[] = [];

            // Tạo dữ liệu cho các năm gần đây (5 năm trước đến 5 năm sau)
            for (let year = selectedYear - 5; year <= selectedYear + 5; year++) {
              // Tìm dữ liệu thực cho năm này nếu có
              const yearStr = `${year}`;
              const existingData = apiData.find(item => item.label === yearStr);

              if (existingData) {
                // Nếu có dữ liệu thực, sử dụng nó
                allYearsData.push(existingData);
              } else {
                // Nếu không có dữ liệu thực, tạo dữ liệu trống
                allYearsData.push({
                  date: new Date(year, 0, 1).toISOString().split('T')[0],
                  label: yearStr,
                  totalRevenue: 0,
                  invoiceCount: 0,
                  periodType: 'yearly'
                });
              }
            }

            // Sắp xếp theo năm
            allYearsData.sort((a, b) => {
              const yearA = parseInt(a.label);
              const yearB = parseInt(b.label);
              return yearA - yearB;
            });

            data = allYearsData;
          } catch (error) {
            console.error('Error loading yearly data:', error);
            // Fallback to empty data if our custom logic fails
            data = [];
          }
          break;

        case 'quarterly':
          try {
            // Lấy dữ liệu thực từ API (sử dụng monthly data và nhóm theo quý)
            const apiData = await customerStatisticsService.getMonthlyRevenueStatistics(
              new Date(selectedYear, 0, 1),
              new Date(selectedYear, 11, 31)
            );

            // Tạo mảng chứa tất cả các quý trong năm
            const allQuartersData: TimeBasedRevenue[] = [];

            // Tạo dữ liệu cho mỗi quý trong năm (4 quý)
            for (let quarter = 1; quarter <= 4; quarter++) {
              // Tính tháng bắt đầu và kết thúc của quý
              const startMonth = (quarter - 1) * 3 + 1;
              const endMonth = quarter * 3;

              // Tính tổng doanh thu và số hóa đơn cho quý này
              let quarterlyRevenue = 0;
              let quarterlyInvoiceCount = 0;

              // Lọc các tháng thuộc quý này từ dữ liệu API
              for (let month = startMonth; month <= endMonth; month++) {
                const monthStr = `${month < 10 ? '0' + month : month}/${selectedYear}`;
                const monthData = apiData.find(item => item.label === monthStr);

                if (monthData) {
                  quarterlyRevenue += monthData.totalRevenue;
                  quarterlyInvoiceCount += monthData.invoiceCount;
                }
              }

              // Tạo dữ liệu cho quý
              allQuartersData.push({
                date: new Date(selectedYear, startMonth - 1, 1).toISOString().split('T')[0],
                label: `Q${quarter}/${selectedYear}`,
                totalRevenue: quarterlyRevenue,
                invoiceCount: quarterlyInvoiceCount,
                periodType: 'quarterly'
              });
            }

            data = allQuartersData;
          } catch (error) {
            console.error('Error loading quarterly data for year:', error);
            // Fallback to empty data if our custom logic fails
            data = [];
          }
          break;

        case 'monthly':
          try {
            // Lấy dữ liệu thực từ API
            const apiData = await customerStatisticsService.getMonthlyRevenueStatistics(
              new Date(selectedYear, 0, 1),
              new Date(selectedYear, 11, 31)
            );

            // Tạo mảng chứa tất cả các tháng trong năm
            const allMonthsData: TimeBasedRevenue[] = [];

            // Tạo dữ liệu cho mỗi tháng trong năm
            for (let month = 1; month <= 12; month++) {
              // Tìm dữ liệu thực cho tháng này nếu có
              const monthStr = `${month < 10 ? '0' + month : month}/${selectedYear}`;
              const existingData = apiData.find(item => item.label === monthStr);

              if (existingData) {
                // Nếu có dữ liệu thực, sử dụng nó
                allMonthsData.push(existingData);
              } else {
                // Nếu không có dữ liệu thực, tạo dữ liệu trống
                allMonthsData.push({
                  date: new Date(selectedYear, month - 1, 1).toISOString().split('T')[0],
                  label: monthStr,
                  totalRevenue: 0,
                  invoiceCount: 0,
                  periodType: 'monthly'
                });
              }
            }

            // Sắp xếp theo tháng
            allMonthsData.sort((a, b) => {
              const monthA = parseInt(a.label.split('/')[0]);
              const monthB = parseInt(b.label.split('/')[0]);
              return monthA - monthB;
            });

            data = allMonthsData;
          } catch (error) {
            console.error('Error loading monthly data for year:', error);
            // Fallback to empty data if our custom logic fails
            data = [];
          }
          break;

        default:
          // Mặc định là theo tháng (hiển thị dữ liệu các tháng trong năm)
          try {
            const apiData = await customerStatisticsService.getMonthlyRevenueStatistics(
              new Date(selectedYear, 0, 1),
              new Date(selectedYear, 11, 31)
            );

            // Tạo mảng chứa tất cả các tháng trong năm
            const allMonthsData: TimeBasedRevenue[] = [];

            // Tạo dữ liệu cho mỗi tháng trong năm
            for (let month = 1; month <= 12; month++) {
              const monthStr = `${month < 10 ? '0' + month : month}/${selectedYear}`;
              const existingData = apiData.find(item => item.label === monthStr);

              if (existingData) {
                allMonthsData.push(existingData);
              } else {
                allMonthsData.push({
                  date: new Date(selectedYear, month - 1, 1).toISOString().split('T')[0],
                  label: monthStr,
                  totalRevenue: 0,
                  invoiceCount: 0,
                  periodType: 'monthly'
                });
              }
            }

            // Sắp xếp theo tháng
            allMonthsData.sort((a, b) => {
              const monthA = parseInt(a.label.split('/')[0]);
              const monthB = parseInt(b.label.split('/')[0]);
              return monthA - monthB;
            });

            data = allMonthsData;
          } catch (error) {
            console.error('Error loading monthly data for default case:', error);
            data = [];
          }
      }

      console.log(`Đã nhận ${data.length} kết quả thống kê`);
      setTimeBasedData(data);
    } catch (error: any) {
      console.error('Error loading time-based statistics:', error);
      setTimeBasedError(error.message || 'Không thể tải dữ liệu thống kê theo thời gian');
    } finally {
      setTimeBasedLoading(false);
    }
  };

  // Handle period type change
  const handlePeriodTypeChange = (newPeriodType: string) => {
    setPeriodType(newPeriodType);

    // Cập nhật ngày bắt đầu và kết thúc dựa trên loại thống kê mới
    if (newPeriodType === 'monthly' || newPeriodType === 'quarterly') {
      // Nếu là theo tháng hoặc theo quý, tạo ngày đầu năm và cuối năm
      const firstDayOfYear = new Date(selectedYear, 0, 1);
      const lastDayOfYear = new Date(selectedYear, 11, 31);
      setStartDate(firstDayOfYear);
      setEndDate(lastDayOfYear);
    } else if (newPeriodType === 'yearly') {
      // Nếu là theo năm, tạo ngày đầu năm và cuối năm cho nhiều năm
      const firstDay = new Date(selectedYear - 5, 0, 1);
      const lastDay = new Date(selectedYear + 5, 11, 31);
      setStartDate(firstDay);
      setEndDate(lastDay);
    }

    // Gọi hàm tải dữ liệu ngay lập tức
    setTimeout(() => loadTimeBasedStatistics(), 0);
  };

  // Handle day change
  const handleDayChange = (day: number) => {
    setSelectedDay(day);
  };

  // Handle month change
  const handleMonthChange = (month: number) => {
    setSelectedMonth(month);

    // Nếu đang ở chế độ "Theo năm" (hiển thị các ngày trong tháng), cập nhật dữ liệu ngay lập tức
    if (periodType === 'yearly') {
      // Cập nhật ngày bắt đầu và kết thúc
      const firstDayOfMonth = new Date(selectedYear, month - 1, 1);
      const lastDayOfMonth = new Date(selectedYear, month, 0);
      setStartDate(firstDayOfMonth);
      setEndDate(lastDayOfMonth);

      // Tải dữ liệu mới
      setTimeout(() => loadTimeBasedStatistics(), 0);
    }
  };

  // Handle year change
  const handleYearChange = (year: number) => {
    setSelectedYear(year);

    // Cập nhật dữ liệu dựa trên loại thống kê hiện tại
    if (periodType === 'monthly') {
      // Nếu đang ở chế độ "Theo tháng" (hiển thị các tháng trong năm)
      const firstDayOfYear = new Date(year, 0, 1);
      const lastDayOfYear = new Date(year, 11, 31);
      setStartDate(firstDayOfYear);
      setEndDate(lastDayOfYear);
    } else if (periodType === 'yearly') {
      // Nếu đang ở chế độ "Theo năm" (hiển thị các ngày trong tháng)
      const firstDayOfMonth = new Date(year, selectedMonth - 1, 1);
      const lastDayOfMonth = new Date(year, selectedMonth, 0);
      setStartDate(firstDayOfMonth);
      setEndDate(lastDayOfMonth);
    }

    // Tải dữ liệu mới
    setTimeout(() => loadTimeBasedStatistics(), 0);
  };

  // Get period label for display
  const getPeriodLabel = (): string => {
    switch (periodType) {
      case 'monthly':
        // Khi chọn "Theo tháng", hiển thị năm
        return `${selectedYear}`;
      case 'quarterly':
        // Khi chọn "Theo quý", hiển thị năm
        return `${selectedYear}`;
      case 'yearly':
        // Khi chọn "Theo năm", hiển thị năm
        return `${selectedYear}`;
      default:
        return '';
    }
  };

  // Handle tab change
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);

    // Load appropriate data based on active tab
    if (newValue === 0) {
      loadCustomerStatistics();
    } else {
      loadTimeBasedStatistics();
    }
  };

  // Apply time-based filter
  const handleApplyTimeBasedFilter = () => {
    // Tạo ngày dựa trên các giá trị đã chọn
    if (periodType === 'monthly' || periodType === 'quarterly') {
      // Nếu là theo tháng hoặc theo quý (hiển thị các tháng/quý trong năm), tạo ngày đầu năm và cuối năm
      const firstDayOfYear = new Date(selectedYear, 0, 1);
      const lastDayOfYear = new Date(selectedYear, 11, 31);
      setStartDate(firstDayOfYear);
      setEndDate(lastDayOfYear);
    } else if (periodType === 'yearly') {
      // Nếu là theo năm, tạo ngày đầu năm và cuối năm cho nhiều năm
      const firstDay = new Date(selectedYear - 5, 0, 1);
      const lastDay = new Date(selectedYear + 5, 11, 31);
      setStartDate(firstDay);
      setEndDate(lastDay);
    }

    // Gọi hàm tải dữ liệu
    loadTimeBasedStatistics();
  };

  // Load statistics on initial render
  useEffect(() => {
    loadCustomerStatistics();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Box>
      <PageHeader
        title={showInvoices ? "Chi tiết hóa đơn khách hàng" : "Thống kê doanh thu khách hàng"}
        subtitle={showInvoices ? `Khách hàng: ${selectedCustomer?.fullName}` : "Xem thống kê doanh thu theo khách hàng"}
      />

      {showInvoices && (
        <Box sx={{ mb: 2 }}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={handleBackToStatistics}
            variant="outlined"
          >
            Quay lại danh sách
          </Button>
        </Box>
      )}

      {!showInvoices && (
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            aria-label="statistics tabs"
          >
            <Tab label="Thống kê theo khách hàng" />
            <Tab label="Thống kê theo thời gian" />
          </Tabs>
        </Box>
      )}

      {!showInvoices && (
        <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
            <Box sx={{ flex: '1 1 30%', minWidth: '250px' }}>
              <DatePickerField
                label="Từ ngày"
                value={startDate}
                onChange={(date) => {
                  if (date) {
                    const newDate = new Date(date);
                    console.log('New start date:', formatDateForDisplay(newDate));
                    setStartDate(newDate);
                  }
                }}
                fullWidth
              />
            </Box>
            <Box sx={{ flex: '1 1 30%', minWidth: '250px' }}>
              <DatePickerField
                label="Đến ngày"
                value={endDate}
                onChange={(date) => {
                  if (date) {
                    const newDate = new Date(date);
                    console.log('New end date:', formatDateForDisplay(newDate));
                    setEndDate(newDate);
                  }
                }}
                fullWidth
              />
            </Box>
            <Box sx={{ flex: '1 1 30%', minWidth: '250px', display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={loadCustomerStatistics}
                fullWidth
              >
                Xem thống kê
              </Button>
              <Tooltip title="Làm mới">
                <IconButton onClick={loadCustomerStatistics} color="primary">
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        </Paper>
      )}

        {activeTab === 0 && (
          <>
            {error && (
              <ErrorAlert
                message={error}
                onRetry={showInvoices ?
                  () => loadCustomerInvoices(selectedCustomer?.id || 0) :
                  loadCustomerStatistics
                }
                autoHide={false}
              />
            )}

            {loading ? (
              <LoadingSpinner />
            ) : (
              <>
                {!showInvoices ? (
                  customers.length > 0 ? (
                    <CustomerRevenueList
                      customers={customers}
                      onViewInvoices={handleViewInvoices}
                    />
                  ) : !error && (
                    <Box sx={{ textAlign: 'center', py: 4 }}>
                      Không có dữ liệu doanh thu trong khoảng thời gian đã chọn
                    </Box>
                  )
                ) : (
                  invoices.length > 0 ? (
                    <CustomerInvoiceList
                      invoices={invoices}
                      customerName={selectedCustomer?.fullName || ''}
                    />
                  ) : !error && (
                    <Box sx={{ textAlign: 'center', py: 4 }}>
                      Không có hóa đơn nào trong khoảng thời gian đã chọn
                    </Box>
                  )
                )}
              </>
            )}
          </>
        )}

        {activeTab === 1 && !showInvoices && (
          <>
            <TimeBasedStatisticsSelector
              periodType={periodType}
              startDate={startDate}
              endDate={endDate}
              selectedDay={selectedDay}
              selectedMonth={selectedMonth}
              selectedYear={selectedYear}
              onPeriodTypeChange={handlePeriodTypeChange}
              onStartDateChange={setStartDate}
              onEndDateChange={setEndDate}
              onDayChange={handleDayChange}
              onMonthChange={handleMonthChange}
              onYearChange={handleYearChange}
              onApplyFilter={handleApplyTimeBasedFilter}
            />

            {timeBasedError && (
              <ErrorAlert
                message={timeBasedError}
                onRetry={loadTimeBasedStatistics}
                autoHide={false}
              />
            )}

            {timeBasedLoading ? (
              <LoadingSpinner />
            ) : (
              <TimeBasedRevenueDisplay
                data={timeBasedData}
                periodType={periodType}
                periodLabel={getPeriodLabel()}
              />
            )}
          </>
        )}
      </Box>
  );
};

export default CustomerStatisticsPage;
