import React from 'react';
import { Box, Typography, Button, Paper, Card, CardContent, CardActions } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { PageHeader } from '../components/common';

const HomePage: React.FC = () => {
  const features = [
    {
      title: '<PERSON>uản lý Hợp đồng',
      description: 'Tạo và quản lý hợp đồng khách hàng',
      link: '/contracts',
    },
    {
      title: '<PERSON><PERSON> to<PERSON> Hợp đồng',
      description: '<PERSON><PERSON> to<PERSON> hợp đồng khách hàng',
      link: '/payments',
    },
    {
      title: 'Thống kê Doanh thu',
      description: '<PERSON>em thống kê doanh thu theo khách hàng',
      link: '/statistics',
    },
    {
      title: '<PERSON><PERSON><PERSON>ợp đồng <PERSON>',
      description: 'Tạo hợp đồng khách hàng mới',
      link: '/contracts/create',
      primary: true,
    },
  ];

  return (
    <Box>
      <PageHeader
        title="<PERSON><PERSON> thống Quản lý Lao động"
        subtitle="Quản lý hiệu quả hoạt động thuê lao động của bạn"
      />

      <Paper elevation={1} sx={{ p: 3, mb: 4 }}>
        <Typography variant="body1" sx={{ mb: 2 }}>
          Chào mừng đến với Hệ thống Quản lý Lao động. Nền tảng này giúp bạn quản lý hiệu quả hoạt động thuê lao động.
          Sử dụng menu điều hướng để truy cập các tính năng khác nhau của hệ thống.
        </Typography>
      </Paper>

      <Typography variant="h5" gutterBottom>
        Truy cập Nhanh
      </Typography>

      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))', gap: 3 }}>
        {features.map((feature, index) => (
          <Card
            key={index}
            elevation={3}
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              backgroundColor: feature.primary ? '#f5f9ff' : 'white'
            }}
          >
            <CardContent sx={{ flexGrow: 1 }}>
              <Typography variant="h6" component="h2" gutterBottom>
                {feature.title}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {feature.description}
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                component={RouterLink}
                to={feature.link}
                color={feature.primary ? 'primary' : 'inherit'}
                variant={feature.primary ? 'contained' : 'text'}
                fullWidth
              >
                {feature.primary ? 'Tạo ngay' : 'Xem'}
              </Button>
            </CardActions>
          </Card>
        ))}
      </Box>
    </Box>
  );
};

export default HomePage;
