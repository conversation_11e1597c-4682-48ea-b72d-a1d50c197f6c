import React, { useState } from 'react';
import { Box, Button, Typography, Alert, Paper } from '@mui/material';
import { contractService } from '../services/contract/contractService';

const TestContractPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const testMinimalContract = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    const testContract = {
      customerId: 1,
      startingDate: "2024-12-20",
      endingDate: "2024-12-31",
      totalAmount: 1000000,
      description: "Test contract - minimal data",
      jobDetails: [
        {
          jobCategoryId: 1,
          startDate: "2024-12-20",
          endDate: "2024-12-31",
          // workLocation: intentionally omitted
          workShifts: [
            {
              startTime: "08:00",
              endTime: "17:00",
              numberOfWorkers: 1,
              salary: 500000,
              workingDays: "1,2,3,4,5"
            }
          ]
        }
      ]
    };

    try {
      console.log('🚀 Testing contract creation with minimal data:', testContract);
      const createdContract = await contractService.createContract(testContract as any);
      console.log('✅ Contract created successfully:', createdContract);
      setResult(`✅ Contract created successfully! ID: ${createdContract.id}, Total: ${createdContract.totalAmount?.toLocaleString('vi-VN')} VNĐ`);
    } catch (err: any) {
      console.error('❌ Contract creation failed:', err);
      setError(`❌ Error: ${err.message || err.toString()}`);
    } finally {
      setLoading(false);
    }
  };

  const testWithWorkLocation = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    const testContract = {
      customerId: 1,
      startingDate: "2024-12-20",
      endingDate: "2024-12-31",
      totalAmount: 1000000,
      description: "Test contract - with work location",
      jobDetails: [
        {
          jobCategoryId: 1,
          startDate: "2024-12-20",
          endDate: "2024-12-31",
          workLocation: "Test Work Location",
          workShifts: [
            {
              startTime: "08:00",
              endTime: "17:00",
              numberOfWorkers: 1,
              salary: 500000,
              workingDays: "1,2,3,4,5"
            }
          ]
        }
      ]
    };

    try {
      console.log('🚀 Testing contract creation with work location:', testContract);
      const createdContract = await contractService.createContract(testContract as any);
      console.log('✅ Contract created successfully:', createdContract);
      setResult(`✅ Contract created successfully! ID: ${createdContract.id}, Total: ${createdContract.totalAmount?.toLocaleString('vi-VN')} VNĐ`);
    } catch (err: any) {
      console.error('❌ Contract creation failed:', err);
      setError(`❌ Error: ${err.message || err.toString()}`);
    } finally {
      setLoading(false);
    }
  };

  const clearCache = () => {
    localStorage.clear();
    sessionStorage.clear();
    setResult('✅ Cache cleared successfully!');
    setError(null);
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" sx={{ mb: 3 }}>
        🧪 Test Contract Creation
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3 }}>
        Trang này để test việc tạo hợp đồng và xác định nguồn gốc lỗi "Vui lòng nhập địa chỉ hợp đồng".
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Test Cases
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            onClick={testMinimalContract}
            disabled={loading}
            color="primary"
          >
            {loading ? 'Testing...' : 'Test Minimal Contract (No Work Location)'}
          </Button>
          
          <Button
            variant="contained"
            onClick={testWithWorkLocation}
            disabled={loading}
            color="secondary"
          >
            {loading ? 'Testing...' : 'Test With Work Location'}
          </Button>
          
          <Button
            variant="outlined"
            onClick={clearCache}
            color="warning"
          >
            Clear Cache
          </Button>
        </Box>

        {result && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {result}
          </Alert>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Debug Information
        </Typography>
        
        <Typography variant="body2" component="pre" sx={{ 
          backgroundColor: '#f5f5f5', 
          p: 2, 
          borderRadius: 1,
          fontSize: '12px',
          overflow: 'auto'
        }}>
          {`Current URL: ${window.location.href}
User Agent: ${navigator.userAgent}
LocalStorage items: ${localStorage.length}
SessionStorage items: ${sessionStorage.length}
Timestamp: ${new Date().toISOString()}`}
        </Typography>
      </Paper>
    </Box>
  );
};

export default TestContractPage;
