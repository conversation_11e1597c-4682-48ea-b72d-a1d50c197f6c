import { Customer } from '../../models';
import { get, post, put, del } from '../api/apiClient';

const BASE_URL = '/api/customer';

export const customerService = {
  // Get all customers
  getAllCustomers: async (): Promise<Customer[]> => {
    return get<Customer[]>(BASE_URL);
  },

  // Get customer by ID
  getCustomerById: async (id: number): Promise<Customer> => {
    return get<Customer>(`${BASE_URL}/${id}`);
  },

  // Create a new customer
  createCustomer: async (customer: Customer): Promise<Customer> => {
    return post<Customer>(BASE_URL, customer);
  },

  // Update an existing customer
  updateCustomer: async (customer: Customer): Promise<Customer> => {
    return put<Customer>(BASE_URL, customer);
  },

  // Delete a customer
  deleteCustomer: async (id: number): Promise<void> => {
    return del<void>(`${BASE_URL}/${id}`);
  },

  // Search customers by name and/or phone number
  searchCustomers: async (fullName?: string, phoneNumber?: string): Promise<Customer[]> => {
    let url = `${BASE_URL}/search`;
    const params = [];

    if (fullName) {
      params.push(`fullName=${encodeURIComponent(fullName)}`);
    }

    if (phoneNumber) {
      params.push(`phoneNumber=${encodeURIComponent(phoneNumber)}`);
    }

    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }

    return get<Customer[]>(url);
  }
};
