import { JobCategory } from '../../models';
import { get, post, put, del } from '../api/apiClient';

const BASE_URL = '/api/job-category';

export const jobCategoryService = {
  // Get all job categories
  getAllJobCategories: async (): Promise<JobCategory[]> => {
    return get<JobCategory[]>(BASE_URL);
  },

  // Get job category by ID
  getJobCategoryById: async (id: number): Promise<JobCategory> => {
    return get<JobCategory>(`${BASE_URL}/${id}`);
  },

  // Create a new job category
  createJobCategory: async (jobCategory: JobCategory): Promise<JobCategory> => {
    return post<JobCategory>(BASE_URL, jobCategory);
  },

  // Update an existing job category
  updateJobCategory: async (jobCategory: JobCategory): Promise<JobCategory> => {
    return put<JobCategory>(BASE_URL, jobCategory);
  },

  // Delete a job category
  deleteJobCategory: async (id: number): Promise<void> => {
    return del<void>(`${BASE_URL}/${id}`);
  }
};
