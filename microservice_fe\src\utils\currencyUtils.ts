/**
 * Format a number as Vietnamese currency (VND)
 * @param amount The amount to format
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number | undefined): string => {
  if (amount === undefined || amount === null) return '0 VNĐ';
  
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    maximumFractionDigits: 0
  }).format(amount);
};

/**
 * Format a number as Vietnamese currency without the currency symbol
 * @param amount The amount to format
 * @returns Formatted number string
 */
export const formatNumber = (amount: number | undefined): string => {
  if (amount === undefined || amount === null) return '0';
  
  return new Intl.NumberFormat('vi-VN', {
    maximumFractionDigits: 0
  }).format(amount);
};
