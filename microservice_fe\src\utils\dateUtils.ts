/**
 * Format a date string to YYYY-MM-DD format for HTML input fields
 * @param dateString The date string to format
 * @returns Formatted date string in YYYY-MM-DD format
 */
export const formatDateForInput = (dateString: string | Date | undefined): string => {
  if (!dateString) return '';

  try {
    const date = dateString instanceof Date ? dateString : new Date(dateString);

    if (isNaN(date.getTime())) {
      console.error('Invalid date provided to formatDateForInput:', dateString);
      return '';
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('Error in formatDateForInput:', error);
    return '';
  }
};

/**
 * Format a date string to a localized Vietnamese date format (DD/MM/YYYY)
 * @param dateString The date string to format
 * @returns Formatted date string in DD/MM/YYYY format
 */
export const formatDateLocalized = (dateString: string | Date | undefined): string => {
  if (!dateString) return '';

  try {
    const date = dateString instanceof Date ? dateString : new Date(dateString);

    if (isNaN(date.getTime())) {
      console.error('Invalid date provided to formatDateLocalized:', dateString);
      return '';
    }

    // Format date as DD/MM/YYYY for Vietnamese format
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  } catch (error) {
    console.error('Error in formatDateLocalized:', error);
    return '';
  }
};

/**
 * Get today's date in YYYY-MM-DD format for input fields
 * @returns Today's date string in YYYY-MM-DD format
 */
export const getTodayDate = (): string => {
  return formatDateForInput(new Date());
};

/**
 * Format a date string to display format (DD/MM/YYYY)
 * This is an alias for formatDateLocalized for better semantic naming
 * @param dateString The date string to format
 * @returns Formatted date string in DD/MM/YYYY format
 */
export const formatDateForDisplay = (dateString: string | Date | undefined): string => {
  return formatDateLocalized(dateString);
};

/**
 * Check if a date is in the future
 * @param dateString The date string to check
 * @returns True if the date is in the future
 */
export const isFutureDate = (dateString: string): boolean => {
  const date = new Date(dateString);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return date > today;
};

/**
 * Check if a date is in the past
 * @param dateString The date string to check
 * @returns True if the date is in the past
 */
export const isPastDate = (dateString: string): boolean => {
  const date = new Date(dateString);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return date < today;
};

/**
 * Chuyển đổi giá trị ngày tháng từ nhiều định dạng khác nhau thành đối tượng Date
 * Hỗ trợ chuỗi ISO, đối tượng Date, và mảng số [năm, tháng, ngày, giờ, phút, giây, nano]
 * @param dateValue Giá trị ngày tháng cần chuyển đổi
 * @returns Đối tượng Date
 */
export const parseDate = (dateValue: string | Date | number[] | undefined): Date => {
  if (!dateValue) {
    return new Date(); // Trả về ngày hiện tại nếu không có giá trị
  }

  // Nếu đã là đối tượng Date, trả về luôn
  if (dateValue instanceof Date) {
    return dateValue;
  }

  // Nếu là mảng số [năm, tháng, ngày, giờ, phút, giây, nano]
  if (Array.isArray(dateValue)) {
    try {
      const [year, month, day, hour = 0, minute = 0, second = 0] = dateValue;
      // Lưu ý: Tháng trong JavaScript bắt đầu từ 0 (0 = tháng 1, 11 = tháng 12)
      return new Date(year, month - 1, day, hour, minute, second);
    } catch (error) {
      console.error('Lỗi khi chuyển đổi mảng thành Date:', error, dateValue);
      return new Date(); // Trả về ngày hiện tại nếu có lỗi
    }
  }

  // Nếu là chuỗi
  try {
    return new Date(dateValue);
  } catch (error) {
    console.error('Lỗi khi chuyển đổi chuỗi thành Date:', error, dateValue);
    return new Date(); // Trả về ngày hiện tại nếu có lỗi
  }
};
