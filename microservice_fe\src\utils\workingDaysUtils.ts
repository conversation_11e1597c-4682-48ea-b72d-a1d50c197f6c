/**
 * Convert a comma-separated string of day numbers to day names
 * @param workingDays Comma-separated string of day numbers (1-7)
 * @returns Array of day names
 */
export const workingDaysToNames = (workingDays: string): string[] => {
  if (!workingDays) return [];

  const dayMap: Record<string, string> = {
    '1': 'Th<PERSON> Hai',
    '2': 'Th<PERSON> Ba',
    '3': 'Th<PERSON> Tư',
    '4': 'Thứ <PERSON>ăm',
    '5': '<PERSON>h<PERSON>',
    '6': 'Th<PERSON>',
    '7': '<PERSON><PERSON>'
  };

  return workingDays.split(',').map(day => dayMap[day] || '');
};

/**
 * Convert a comma-separated string of day numbers to a formatted string
 * @param workingDays Comma-separated string of day numbers (1-7)
 * @returns Formatted string of day names
 */
export const formatWorkingDays = (workingDays: string): string => {
  return workingDaysToNames(workingDays).join(', ');
};

/**
 * Convert an array of day numbers to a comma-separated string
 * @param days Array of day numbers (1-7)
 * @returns Comma-separated string of day numbers
 */
export const daysArrayToString = (days: number[]): string => {
  return days.join(',');
};

/**
 * Convert a comma-separated string of day numbers to an array of numbers
 * @param workingDays Comma-separated string of day numbers (1-7)
 * @returns Array of day numbers
 */
export const stringToDaysArray = (workingDays: string): number[] => {
  if (!workingDays) return [];
  return workingDays.split(',').map(day => parseInt(day, 10));
};

/**
 * Get all available working days as options
 * @returns Array of day options with value and label
 */
export const getWorkingDayOptions = (): { value: number; label: string }[] => {
  return [
    { value: 1, label: 'Thứ Hai' },
    { value: 2, label: 'Thứ Ba' },
    { value: 3, label: 'Thứ Tư' },
    { value: 4, label: 'Thứ Năm' },
    { value: 5, label: 'Thứ Sáu' },
    { value: 6, label: 'Thứ Bảy' },
    { value: 7, label: 'Chủ Nhật' }
  ];
};

/**
 * Calculate actual working dates based on contract start/end dates and selected working days
 * @param startDate Contract start date (YYYY-MM-DD format)
 * @param endDate Contract end date (YYYY-MM-DD format)
 * @param workingDays Comma-separated string of day numbers (1-7)
 * @returns Array of date strings in DD/MM/YYYY format
 */
export const calculateWorkingDates = (
  startDate: string,
  endDate: string,
  workingDays: string
): string[] => {
  if (!startDate || !endDate || !workingDays) return [];

  const start = new Date(startDate);
  const end = new Date(endDate);
  const workingDaysArray = stringToDaysArray(workingDays);

  // Ensure valid dates
  if (isNaN(start.getTime()) || isNaN(end.getTime())) return [];

  const result: string[] = [];
  const currentDate = new Date(start);

  // Loop through all days from start to end
  while (currentDate <= end) {
    // JavaScript getDay() returns 0 for Sunday, 1 for Monday, etc.
    // Convert to our format where 1 = Monday, 7 = Sunday
    let dayOfWeek = currentDate.getDay();
    dayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek; // Convert Sunday from 0 to 7

    // Check if this day is in our working days
    if (workingDaysArray.includes(dayOfWeek)) {
      // Format date as DD/MM/YYYY
      const day = String(currentDate.getDate()).padStart(2, '0');
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const year = currentDate.getFullYear();
      result.push(`${day}/${month}/${year}`);
    }

    // Move to next day
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return result;
};
