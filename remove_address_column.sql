-- Script để xóa column address khỏi bảng customer_contracts
-- Chạy script này để hoàn tất việc loại bỏ address field

-- 1. <PERSON><PERSON><PERSON> tra xem column address có tồn tại không
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'customer_contracts' 
  AND column_name = 'address'
  AND table_schema = 'customercontractdb';

-- 2. Backup dữ liệu trước khi xóa (optional)
-- CREATE TABLE customer_contracts_backup AS SELECT * FROM customer_contracts;

-- 3. Xóa column address
ALTER TABLE customer_contracts DROP COLUMN IF EXISTS address;

-- 4. Kiểm tra lại cấu trúc bảng sau khi xóa
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'customer_contracts' 
  AND table_schema = 'customercontractdb'
ORDER BY ordinal_position;

-- 5. <PERSON><PERSON><PERSON> tra constraints hiện tại
SELECT 
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name
WHERE tc.table_name = 'customer_contracts'
  AND tc.table_schema = 'customercontractdb';

-- 6. Verify data integrity
SELECT COUNT(*) as total_contracts FROM customer_contracts;
SELECT COUNT(*) as contracts_with_description FROM customer_contracts WHERE description IS NOT NULL;

COMMIT;
