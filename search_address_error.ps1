# Script để tìm kiếm text "địa chỉ hợp đồng" trong toàn bộ project
Write-Host "🔍 Searching for 'địa chỉ hợp đồng' in entire project..." -ForegroundColor Green

# Tìm trong frontend
Write-Host "`n📁 Searching in frontend..." -ForegroundColor Yellow
$frontendFiles = Get-ChildItem -Path "microservice_fe" -Recurse -Include "*.tsx", "*.ts", "*.js", "*.json" -ErrorAction SilentlyContinue
foreach ($file in $frontendFiles) {
    try {
        $content = Get-Content $file.FullName -Raw -Encoding UTF8 -ErrorAction SilentlyContinue
        if ($content -and ($content -match "địa chỉ.*hợp đồng" -or $content -match "hợp đồng.*địa chỉ")) {
            Write-Host "❌ Found in: $($file.FullName)" -ForegroundColor Red
            $lines = Get-Content $file.FullName -ErrorAction SilentlyContinue
            for ($i = 0; $i -lt $lines.Count; $i++) {
                if ($lines[$i] -match "địa chỉ.*hợp đồng" -or $lines[$i] -match "hợp đồng.*địa chỉ") {
                    Write-Host "   Line $($i + 1): $($lines[$i].Trim())" -ForegroundColor Red
                }
            }
        }
    } catch {
        # Skip files that can't be read
    }
}

# Tìm trong backend
Write-Host "`n📁 Searching in backend..." -ForegroundColor Yellow
$backendDirs = @("customer-contract-service", "customer-service", "job-service", "customer-payment-service", "customer-statistics-service", "api-gateway")
foreach ($dir in $backendDirs) {
    if (Test-Path $dir) {
        $backendFiles = Get-ChildItem -Path $dir -Recurse -Include "*.java", "*.properties", "*.yml", "*.yaml" -ErrorAction SilentlyContinue
        foreach ($file in $backendFiles) {
            try {
                $content = Get-Content $file.FullName -Raw -Encoding UTF8 -ErrorAction SilentlyContinue
                if ($content -and ($content -match "địa chỉ.*hợp đồng" -or $content -match "hợp đồng.*địa chỉ")) {
                    Write-Host "❌ Found in: $($file.FullName)" -ForegroundColor Red
                    $lines = Get-Content $file.FullName -ErrorAction SilentlyContinue
                    for ($i = 0; $i -lt $lines.Count; $i++) {
                        if ($lines[$i] -match "địa chỉ.*hợp đồng" -or $lines[$i] -match "hợp đồng.*địa chỉ") {
                            Write-Host "   Line $($i + 1): $($lines[$i].Trim())" -ForegroundColor Red
                        }
                    }
                }
            } catch {
                # Skip files that can't be read
            }
        }
    }
}

# Tìm trong root files
Write-Host "`n📁 Searching in root files..." -ForegroundColor Yellow
$rootFiles = Get-ChildItem -Path "." -Include "*.md", "*.txt", "*.ps1", "*.sql", "*.js", "*.html" -ErrorAction SilentlyContinue
foreach ($file in $rootFiles) {
    try {
        $content = Get-Content $file.FullName -Raw -Encoding UTF8 -ErrorAction SilentlyContinue
        if ($content -and ($content -match "địa chỉ.*hợp đồng" -or $content -match "hợp đồng.*địa chỉ")) {
            Write-Host "❌ Found in: $($file.FullName)" -ForegroundColor Red
            $lines = Get-Content $file.FullName -ErrorAction SilentlyContinue
            for ($i = 0; $i -lt $lines.Count; $i++) {
                if ($lines[$i] -match "địa chỉ.*hợp đồng" -or $lines[$i] -match "hợp đồng.*địa chỉ") {
                    Write-Host "   Line $($i + 1): $($lines[$i].Trim())" -ForegroundColor Red
                }
            }
        }
    } catch {
        # Skip files that can't be read
    }
}

# Tìm kiếm các từ khóa liên quan
Write-Host "`n🔍 Searching for related keywords..." -ForegroundColor Yellow
$keywords = @("Vui lòng nhập địa chỉ", "address.*required", "contract.*address", "địa chỉ.*bắt buộc")

foreach ($keyword in $keywords) {
    Write-Host "`nSearching for: $keyword" -ForegroundColor Cyan
    
    # Search in frontend
    $frontendFiles = Get-ChildItem -Path "microservice_fe" -Recurse -Include "*.tsx", "*.ts", "*.js" -ErrorAction SilentlyContinue
    foreach ($file in $frontendFiles) {
        try {
            $content = Get-Content $file.FullName -Raw -Encoding UTF8 -ErrorAction SilentlyContinue
            if ($content -and $content -match $keyword) {
                Write-Host "   Found in: $($file.FullName)" -ForegroundColor Yellow
            }
        } catch {
            # Skip files that can't be read
        }
    }
}

Write-Host "`n✅ Search completed!" -ForegroundColor Green
Write-Host "If no results found, the error might be coming from:" -ForegroundColor Yellow
Write-Host "1. Browser cache" -ForegroundColor Yellow
Write-Host "2. Compiled JavaScript bundles" -ForegroundColor Yellow
Write-Host "3. Runtime validation" -ForegroundColor Yellow
Write-Host "4. External libraries" -ForegroundColor Yellow
