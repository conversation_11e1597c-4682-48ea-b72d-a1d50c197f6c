# Simple search for address contract error
Write-Host "Searching for address contract error..." -ForegroundColor Green

# Search for exact text
$searchText = "dia chi hop dong"
Write-Host "Searching for: $searchText" -ForegroundColor Yellow

# Search in frontend files
$frontendPath = "microservice_fe\src"
if (Test-Path $frontendPath) {
    Write-Host "Searching in frontend..." -ForegroundColor Cyan
    Get-ChildItem -Path $frontendPath -Recurse -Include "*.tsx", "*.ts", "*.js" | ForEach-Object {
        $content = Get-Content $_.FullName -Raw -ErrorAction SilentlyContinue
        if ($content -and $content.ToLower().Contains($searchText.ToLower())) {
            Write-Host "Found in: $($_.FullName)" -ForegroundColor Red
        }
    }
}

# Search for "Vui long nhap"
$searchText2 = "Vui long nhap"
Write-Host "`nSearching for: $searchText2" -ForegroundColor Yellow
if (Test-Path $frontendPath) {
    Get-ChildItem -Path $frontendPath -Recurse -Include "*.tsx", "*.ts", "*.js" | ForEach-Object {
        $content = Get-Content $_.FullName -Raw -ErrorAction SilentlyContinue
        if ($content -and $content.Contains($searchText2)) {
            Write-Host "Found in: $($_.FullName)" -ForegroundColor Red
            # Show the line
            $lines = Get-Content $_.FullName
            for ($i = 0; $i -lt $lines.Count; $i++) {
                if ($lines[$i].Contains($searchText2)) {
                    Write-Host "  Line $($i + 1): $($lines[$i].Trim())" -ForegroundColor Yellow
                }
            }
        }
    }
}

# Search for "address" and "contract" in same file
Write-Host "`nSearching for files containing both 'address' and 'contract'..." -ForegroundColor Yellow
if (Test-Path $frontendPath) {
    Get-ChildItem -Path $frontendPath -Recurse -Include "*.tsx", "*.ts", "*.js" | ForEach-Object {
        $content = Get-Content $_.FullName -Raw -ErrorAction SilentlyContinue
        if ($content -and $content.ToLower().Contains("address") -and $content.ToLower().Contains("contract")) {
            Write-Host "Found in: $($_.FullName)" -ForegroundColor Yellow
        }
    }
}

Write-Host "`nSearch completed!" -ForegroundColor Green
