<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend Contract Creation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .log {
            max-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Test Frontend Contract Creation</h1>
    
    <div class="test-section info">
        <h3>Thông tin Test</h3>
        <p>Trang này sẽ test việc tạo hợp đồng từ frontend để kiểm tra lỗi.</p>
        <p><strong>API Gateway URL:</strong> <span id="api-url">http://localhost:8080</span></p>
        <p><strong>Contract Service URL:</strong> <span id="contract-url">http://localhost:8083</span></p>
    </div>

    <div class="test-section">
        <h3>Test Contract Data</h3>
        <pre id="contract-data"></pre>
        <button onclick="testContractCreation()">Test Tạo Hợp Đồng</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="test-section">
        <h3>Test Results</h3>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h3>Console Log</h3>
        <div id="console-log" class="log"></div>
    </div>

    <script>
        // Override console.log to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(type, ...args) {
            const logDiv = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> <span style="color: ${
                type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black'
            };">[${type.toUpperCase()}]</span> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog('log', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog('error', ...args);
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog('warn', ...args);
        };

        // Test contract data
        const testContract = {
            customerId: 1,
            startingDate: "2024-12-20",
            endingDate: "2024-12-31",
            totalAmount: 5000000,
            address: "123 Test Address",
            description: "Test contract from HTML page",
            jobDetails: [
                {
                    jobCategoryId: 1,
                    startDate: "2024-12-20",
                    endDate: "2024-12-31",
                    workLocation: "Test Work Location",
                    workShifts: [
                        {
                            startTime: "08:00",
                            endTime: "17:00",
                            numberOfWorkers: 3,
                            salary: 500000,
                            workingDays: "1,2,3,4,5"
                        }
                    ]
                }
            ]
        };

        // Display contract data
        document.getElementById('contract-data').textContent = JSON.stringify(testContract, null, 2);

        function clearLog() {
            document.getElementById('console-log').innerHTML = '';
            document.getElementById('test-results').innerHTML = '';
        }

        async function testContractCreation() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="info">Đang test...</div>';
            
            console.log('🚀 Starting contract creation test...');
            console.log('Test contract data:', testContract);

            try {
                // Test 1: API Gateway
                console.log('Testing via API Gateway...');
                await testApiEndpoint('http://localhost:8080/api/customer-contract', 'API Gateway');
                
                // Test 2: Direct service
                console.log('Testing via Direct Service...');
                await testApiEndpoint('http://localhost:8083/api/contracts', 'Direct Service');
                
            } catch (error) {
                console.error('Test failed:', error);
                resultsDiv.innerHTML += `<div class="error">Test thất bại: ${error.message}</div>`;
            }
        }

        async function testApiEndpoint(url, name) {
            const resultsDiv = document.getElementById('test-results');
            
            try {
                console.log(`Testing ${name} at ${url}`);
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(testContract)
                });

                console.log(`${name} response status:`, response.status);
                console.log(`${name} response headers:`, Object.fromEntries(response.headers.entries()));

                if (response.ok) {
                    const data = await response.json();
                    console.log(`✅ ${name} SUCCESS:`, data);
                    resultsDiv.innerHTML += `<div class="success">
                        <strong>✅ ${name} THÀNH CÔNG</strong><br>
                        Contract ID: ${data.id}<br>
                        Customer ID: ${data.customerId}<br>
                        Total Amount: ${data.totalAmount}<br>
                        Status: ${data.status}
                    </div>`;
                } else {
                    const errorText = await response.text();
                    console.error(`❌ ${name} ERROR:`, response.status, errorText);
                    resultsDiv.innerHTML += `<div class="error">
                        <strong>❌ ${name} THẤT BẠI</strong><br>
                        Status: ${response.status}<br>
                        Error: ${errorText}
                    </div>`;
                }
            } catch (error) {
                console.error(`❌ ${name} NETWORK ERROR:`, error);
                resultsDiv.innerHTML += `<div class="error">
                    <strong>❌ ${name} LỖI MẠNG</strong><br>
                    Error: ${error.message}
                </div>`;
            }
        }

        // Auto-run test when page loads
        window.addEventListener('load', () => {
            console.log('Page loaded, ready for testing');
        });
    </script>
</body>
</html>
