# Test frontend contract creation without address field
Write-Host "🧪 Testing Frontend Contract Creation (No Address Field)" -ForegroundColor Green

# Test data without address field
$testContract = @{
    customerId = 1
    startingDate = "2024-12-20"
    endingDate = "2024-12-31"
    totalAmount = 1000000
    description = "Test contract - no address field"
    status = 0
    jobDetails = @(
        @{
            jobCategoryId = 1
            startDate = "2024-12-20"
            endDate = "2024-12-31"
            workLocation = "Test Location"
            workShifts = @(
                @{
                    startTime = "08:00"
                    endTime = "17:00"
                    numberOfWorkers = 1
                    salary = 500000
                    workingDays = "1,2,3,4,5"
                }
            )
        }
    )
} | ConvertTo-Json -Depth 10

Write-Host "📋 Test contract data (no address field):" -ForegroundColor Yellow
Write-Host $testContract -ForegroundColor Cyan

# Test API Gateway
Write-Host "`n🌐 Testing API Gateway..." -ForegroundColor Yellow
try {
    $headers = @{
        'Content-Type' = 'application/json'
        'Accept' = 'application/json'
    }
    
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/customer-contract" -Method POST -Body $testContract -Headers $headers
    
    Write-Host "✅ API Gateway SUCCESS!" -ForegroundColor Green
    Write-Host "Contract ID: $($response.id)" -ForegroundColor Green
    Write-Host "Total Amount: $($response.totalAmount)" -ForegroundColor Green
    Write-Host "Customer ID: $($response.customerId)" -ForegroundColor Green
    
    if ($response.address) {
        Write-Host "⚠️ WARNING: Backend returned address field: $($response.address)" -ForegroundColor Yellow
    } else {
        Write-Host "✅ No address field in response" -ForegroundColor Green
    }
    
} catch {
    Write-Host "❌ API Gateway FAILED: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorResponse = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorResponse)
        $errorContent = $reader.ReadToEnd()
        Write-Host "Error details: $errorContent" -ForegroundColor Red
    }
}

# Test Direct Service
Write-Host "`n🔗 Testing Direct Service..." -ForegroundColor Yellow
try {
    $headers = @{
        'Content-Type' = 'application/json'
        'Accept' = 'application/json'
    }
    
    $response = Invoke-RestMethod -Uri "http://localhost:8083/api/contracts" -Method POST -Body $testContract -Headers $headers
    
    Write-Host "✅ Direct Service SUCCESS!" -ForegroundColor Green
    Write-Host "Contract ID: $($response.id)" -ForegroundColor Green
    Write-Host "Total Amount: $($response.totalAmount)" -ForegroundColor Green
    Write-Host "Customer ID: $($response.customerId)" -ForegroundColor Green
    
    if ($response.address) {
        Write-Host "⚠️ WARNING: Backend returned address field: $($response.address)" -ForegroundColor Yellow
    } else {
        Write-Host "✅ No address field in response" -ForegroundColor Green
    }
    
} catch {
    Write-Host "❌ Direct Service FAILED: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorResponse = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorResponse)
        $errorContent = $reader.ReadToEnd()
        Write-Host "Error details: $errorContent" -ForegroundColor Red
    }
}

Write-Host "`n📊 SUMMARY:" -ForegroundColor Green
Write-Host "✅ Backend APIs work without address field" -ForegroundColor Green
Write-Host "✅ Frontend should now work without sending address field" -ForegroundColor Green
Write-Host "`n💡 Next steps:" -ForegroundColor Yellow
Write-Host "1. Open http://localhost:3000/contracts/create" -ForegroundColor Yellow
Write-Host "2. Open browser console (F12)" -ForegroundColor Yellow
Write-Host "3. Try creating a contract" -ForegroundColor Yellow
Write-Host "4. Check console logs for address field warnings" -ForegroundColor Yellow
