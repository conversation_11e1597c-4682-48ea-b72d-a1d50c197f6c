# Test contract creation without address field
Write-Host "Testing contract creation without address field..." -ForegroundColor Green

$testContract = @{
    customerId = 1
    startingDate = "2024-12-20"
    endingDate = "2024-12-31"
    totalAmount = 1000000
    description = "Test contract - no address field"
    status = 0
    jobDetails = @(
        @{
            jobCategoryId = 1
            startDate = "2024-12-20"
            endDate = "2024-12-31"
            workLocation = "Test Location"
            workShifts = @(
                @{
                    startTime = "08:00"
                    endTime = "17:00"
                    numberOfWorkers = 1
                    salary = 500000
                    workingDays = "1,2,3,4,5"
                }
            )
        }
    )
} | ConvertTo-Json -Depth 10

Write-Host "Test contract data:" -ForegroundColor Yellow
Write-Host $testContract -ForegroundColor Cyan

# Test API Gateway
Write-Host "Testing API Gateway..." -ForegroundColor Yellow
try {
    $headers = @{
        'Content-Type' = 'application/json'
        'Accept' = 'application/json'
    }
    
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/customer-contract" -Method POST -Body $testContract -Headers $headers
    
    Write-Host "SUCCESS! Contract ID: $($response.id)" -ForegroundColor Green
    Write-Host "Total Amount: $($response.totalAmount)" -ForegroundColor Green
    
} catch {
    Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Backend works without address field!" -ForegroundColor Green
Write-Host "Now test frontend at: http://localhost:3000/contracts/create" -ForegroundColor Yellow
