// <PERSON><PERSON><PERSON> để verify rằng address field không đ<PERSON><PERSON><PERSON> gửi từ frontend
// <PERSON><PERSON>y trong browser console

console.log('🔍 Verifying no address field is sent from frontend...');

// 1. Monitor all fetch requests
const originalFetch = window.fetch;
window.fetch = function(...args) {
    const [url, options] = args;
    
    if (options && options.method === 'POST' && options.body) {
        try {
            const body = JSON.parse(options.body);
            console.log('🌐 POST Request intercepted:');
            console.log('URL:', url);
            console.log('Body keys:', Object.keys(body));
            console.log('Body:', body);
            
            if ('address' in body) {
                console.error('❌ CRITICAL: address field found in POST request!', body.address);
                console.error('Full body:', body);
            } else {
                console.log('✅ No address field in POST request');
            }
        } catch (e) {
            console.log('🌐 POST Request (non-JSON body):', url);
        }
    }
    
    return originalFetch.apply(this, args);
};

// 2. Monitor XMLHttpRequest
const originalXHRSend = XMLHttpRequest.prototype.send;
XMLHttpRequest.prototype.send = function(data) {
    if (data && typeof data === 'string') {
        try {
            const body = JSON.parse(data);
            console.log('🌐 XHR Request intercepted:');
            console.log('Body keys:', Object.keys(body));
            console.log('Body:', body);
            
            if ('address' in body) {
                console.error('❌ CRITICAL: address field found in XHR request!', body.address);
            } else {
                console.log('✅ No address field in XHR request');
            }
        } catch (e) {
            console.log('🌐 XHR Request (non-JSON data)');
        }
    }
    
    return originalXHRSend.call(this, data);
};

// 3. Check current form state
function checkCurrentFormState() {
    console.log('📋 Checking current form state...');
    
    // Find all input fields with name="address"
    const addressInputs = document.querySelectorAll('input[name="address"]');
    if (addressInputs.length > 0) {
        console.warn(`⚠️ Found ${addressInputs.length} input fields with name="address":`);
        addressInputs.forEach((input, index) => {
            console.log(`Input ${index + 1}:`, input);
            console.log(`Value: "${input.value}"`);
        });
    } else {
        console.log('✅ No input fields with name="address" found');
    }
    
    // Check for any elements with address-related data
    const elementsWithAddress = document.querySelectorAll('[data-address], [data-contract-address]');
    if (elementsWithAddress.length > 0) {
        console.warn(`⚠️ Found ${elementsWithAddress.length} elements with address-related data attributes`);
    } else {
        console.log('✅ No elements with address-related data attributes');
    }
}

// 4. Monitor form submissions
function monitorFormSubmissions() {
    console.log('📋 Setting up form submission monitoring...');
    
    const forms = document.querySelectorAll('form');
    forms.forEach((form, index) => {
        form.addEventListener('submit', function(e) {
            console.log(`📋 Form ${index + 1} submission detected`);
            
            const formData = new FormData(form);
            const formObject = {};
            for (let [key, value] of formData.entries()) {
                formObject[key] = value;
            }
            
            console.log('Form data keys:', Object.keys(formObject));
            console.log('Form data:', formObject);
            
            if ('address' in formObject) {
                console.error('❌ CRITICAL: address field found in form submission!', formObject.address);
            } else {
                console.log('✅ No address field in form submission');
            }
        });
    });
}

// 5. Check React component state (if React DevTools available)
function checkReactState() {
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
        console.log('📋 React DevTools detected - checking component state...');
        
        // Find React fiber nodes
        const reactElements = [];
        function findReactElements(element) {
            const fiberKey = Object.keys(element).find(key => 
                key.startsWith('__reactInternalInstance') || 
                key.startsWith('__reactFiber')
            );
            
            if (fiberKey) {
                const fiber = element[fiberKey];
                if (fiber && fiber.memoizedState) {
                    reactElements.push({
                        element: element,
                        state: fiber.memoizedState
                    });
                }
            }
            
            for (let child of element.children) {
                findReactElements(child);
            }
        }
        
        findReactElements(document.body);
        
        reactElements.forEach((reactEl, index) => {
            if (reactEl.state && typeof reactEl.state === 'object') {
                const stateStr = JSON.stringify(reactEl.state);
                if (stateStr.includes('address')) {
                    console.warn(`⚠️ React component ${index + 1} state contains 'address'`);
                    console.log('State:', reactEl.state);
                }
            }
        });
    } else {
        console.log('📋 React DevTools not available');
    }
}

// Run all checks
console.log('🎯 Running all verification checks...');

checkCurrentFormState();
monitorFormSubmissions();
checkReactState();

console.log('✅ Verification setup complete!');
console.log('💡 Now try to create a contract and watch the console for any address field warnings');
console.log('💡 Functions available:');
console.log('  - checkCurrentFormState() - Check current form state');
console.log('  - monitorFormSubmissions() - Re-setup form monitoring');
console.log('  - checkReactState() - Check React component state');

// Export functions
window.verifyNoAddress = {
    checkCurrentFormState,
    monitorFormSubmissions,
    checkReactState
};
